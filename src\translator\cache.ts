import { TFText } from './translatro'

export class IndexedDBCache {
  private cache: Map<string, Map<string, TFText>> = new Map()
  private DB_NAME = 'translator_db'
  private DB_VERSION = 1
  private DB_STORE_NAME = 'translator_store'
  private db?: IDBDatabase
  private isInitialized = false

  constructor() {
    this.initDB()
  }

  private initDB() {
    const request = indexedDB.open(this.DB_NAME, this.DB_VERSION)
    request.onupgradeneeded = event => {
      const db = (event.target as IDBOpenDBRequest).result
      if (!db.objectStoreNames.contains(this.DB_STORE_NAME)) {
        const store = db.createObjectStore(this.DB_STORE_NAME, { keyPath: ['source', 'toLang'] })
        store.createIndex('toLang', 'toLang', { unique: false })
      }
    }
    request.onsuccess = event => {
      this.db = (event.target as IDBOpenDBRequest).result
      this.initCache()
    }
  }

  private initCache() {
    if (!this.db) {
      this.isInitialized = true
      return
    }

    const store = this.db
      .transaction(this.DB_STORE_NAME, 'readonly')
      .objectStore(this.DB_STORE_NAME)
    const request = store.getAll()

    request.onsuccess = event => {
      const data = (event.target as IDBRequest).result
      data.forEach((item: TFText & { toLang: string }) => {
        const { toLang, ...rest } = item
        if (!this.cache.has(item.source)) {
          this.cache.set(item.source, new Map())
        }
        this.cache.get(item.source)?.set(toLang, rest)
      })
      this.isInitialized = true
    }
  }

  public async waitForInitialization(): Promise<void> {
    if (this.isInitialized) return Promise.resolve()

    return new Promise(resolve => {
      const checkInterval = 100
      const checkInitialization = () => {
        if (this.isInitialized) {
          resolve()
          return
        }
        setTimeout(checkInitialization, checkInterval)
      }
      checkInitialization()
    })
  }

  public set(key: string, value: TFText, toLang: string) {
    const store = this.db
      ?.transaction(this.DB_STORE_NAME, 'readwrite')
      .objectStore(this.DB_STORE_NAME)
    if (store) {
      store.put({ ...value, toLang })
    }

    if (!this.cache.has(key)) {
      this.cache.set(key, new Map())
    }
    this.cache.get(key)?.set(toLang, value)
  }

  public get(text: string, toLang: string) {
    return this.cache.get(text)?.get(toLang)
  }

  public has(text: string, toLang: string) {
    return this.cache.get(text)?.has(toLang) || false
  }

  public clear(toLang: string) {
    const store = this.db
      ?.transaction(this.DB_STORE_NAME, 'readwrite')
      .objectStore(this.DB_STORE_NAME)
    if (store) {
      const index = store.index('toLang')
      const request = index.openKeyCursor(IDBKeyRange.only(toLang))
      request.onsuccess = event => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          store.delete(cursor.primaryKey)
          cursor.continue()
        }
      }
    }

    this.cache.forEach((langMap, text) => {
      langMap.delete(toLang)
      if (langMap.size === 0) {
        this.cache.delete(text)
      }
    })
  }

  public getCachedLanguages(): string[] {
    const languages = new Set<string>()
    this.cache.forEach(langMap => {
      langMap.forEach((_, lang) => languages.add(lang))
    })
    return Array.from(languages)
  }
}

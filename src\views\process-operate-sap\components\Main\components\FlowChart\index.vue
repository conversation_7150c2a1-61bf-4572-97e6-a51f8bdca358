<template>
  <div class="flow-chart">
    <div class="flow-box">
      <FlowChartItem
        v-for="item in props.data"
        :active="props.value == item.id"
        :name="item.topicName"
        :type="item.status"
        :forcast-time="item.forcastTime"
        :superviser="item.superviser"
        :invalid="item.invalid"
        :overdue-duration="(item.overdueDuration as number)"
        :remainder-duration="(item.remainderDuration as number)"
        :reality-duration="(item.realityDuration as number)"
        :key="item.id"
        @click="handleFlowChartItemClick(item)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { IProcess } from '@/types/handle'

import FlowChartItem from './components/FlowChartItem/index.vue'

interface IProps {
  value: number | string
  data: IProcess[]
}

const props = defineProps<IProps>()
const emits = defineEmits(['change'])

const handleFlowChartItemClick = (item: IProcess) => {
  emits('change', item)
}
</script>

<style scoped lang="scss">
.flow-chart {
  display: flex;
  margin-top: 32px;
  margin-bottom: 20px;
  padding-bottom: 6px;
  overflow: hidden;
  overflow-x: auto;
  .flow-box {
    display: flex;
    flex: 1;
    justify-content: center;
  }
  :deep(.flow-chart-item:first-child) {
    margin-left: 0;
  }
}
</style>

<template>
  <div>
    <!-- 表单渲染 -->
    <FormRender v-if="id" ref="formRenderRef" :id="id" type="view" :data="TFFormData(handleNode)" />
    <!-- 附件 -->
    <MilepostAppendix :formData="formData" />
  </div>
</template>

<script setup lang="ts">
import { Ref, ref, inject, markRaw, watch } from 'vue'
import { ApplyFormInfoRes, HandleNodeList } from '@/api'
import FormRender from '@/components/FormRender/index.vue'
import MilepostAppendix from './MilepostAppendix.vue'

const props = defineProps<ApplyFormInfoRes>()
const handleNode = inject<Ref<HandleNodeList>>('handleNode') as Ref<HandleNodeList> // 当前里程碑信息
const formRenderRef = ref<any>()
const setFormRenderRef = inject('setFormRenderRef') as (ref: any) => void

watch(
  () => formRenderRef.value,
  () => {
    setFormRenderRef(formRenderRef.value)
  }
)

const TFFormData = (milepost: HandleNodeList) => {
  // 剔除一些基本用不到又可能会更新的字段，减少表单重新渲染的次数，表单重新渲染会造成页面抖动
  // eslint-disable-next-line prettier/prettier, @typescript-eslint/no-unused-vars
  return markRaw({ envData: milepost, envDefaultFormData: props.formData })
}
</script>

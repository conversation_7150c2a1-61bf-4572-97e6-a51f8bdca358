<template>
  <div class="search-content-container">
    <template v-for="item of search.options" :key="item.componentValueKey">
      <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, inject, onMounted } from 'vue'
import { useStore } from 'vuex'
import { Search } from '@/views/message-template/components/SearchContent/search'
import { cache } from '@/utils'

interface IProps {
  queryData: any
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:queryData'])
const store = useStore()
const allUserList = computed(() => store.state.user.allUser || [])
const search = new Search()
const queryData = computed({
  get: () => props.queryData,
  set: val => emits('update:queryData', val),
})
const processTypeData = inject('processTypeData') as any
const routerName = inject('routerName') as any

const onChange = () =>
  (queryData.value = {
    ...search.getParams(),
    ...{ cacheValue: search.getCacheSearch() },
  })
const configList = [
  {
    componentName: 'FSelect',
    componentValueKey: 'processConfigId',
    componentAttrs: {
      class: 'width160 marginR12 marginB24',
      pressLine: computed(() => '流程类型'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: computed(() => processTypeData.value || []),
      optionFilterProp: 'processName',
      fieldNames: { value: 'id', label: 'processName' },
      onChange: (value, option) => {
        search.setOptions('nodeIdList.componentValue', undefined)
        search.setOptions('nodeIdList.componentAttrs.options', option?.nodes || [])
        search.setOptions('processConfigId.componentArgsValue', option || null)
        onChange()
      },
      onClear: () => {
        search.setOptions('nodeIdList.componentValue', undefined)
        search.setOptions('nodeIdList.componentAttrs.options', [])
        search.setOptions('processConfigId.componentArgsValue', null)
        onChange()
      },
    },
    setComponentValueFormat: async (data: any) => {
      search.setOptions('processConfigId.componentValue', (data || {})?.componentValue || undefined)
      search.setOptions('nodeIdList.componentAttrs.options', (data || {})?.componentArgsValue?.nodes || [])
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'nodeIdList',
    componentAttrs: {
      class: 'width160 marginR12 marginB24',
      pressLine: computed(() => '当前节点'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: [],
      optionFilterProp: 'milepostName',
      fieldNames: { value: 'nodeId', label: 'milepostName' },
      mode: 'multiple',
      maxTagCount: 'responsive',
      onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('nodeIdList.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'creator',
    componentAttrs: {
      class: 'width160 marginR12 marginB24',
      pressLine: computed(() => '发起人'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: allUserList,
      fieldNames: { value: 'uuid', label: 'feiShuName' },
      optionFilterProp: 'feiShuName',
      onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('creator.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'uuidList',
    componentAttrs: {
      class: 'width160 marginR12 marginB24',
      pressLine: computed(() => '项目成员'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      showArrow: true,
      allowClear: true,
      options: allUserList,
      fieldNames: { value: 'uuid', label: 'feiShuName' },
      optionFilterProp: 'feiShuName',
      mode: 'multiple',
      maxTagCount: 'responsive',
      onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('uuidList.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FRangePicker',
    componentValueKey: 'time',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '录入时间'),
      valueFormat: 'YYYY-MM-DD',
      onChange,
    },
    getComponentValueFormat: (value: any) => {
      if (!value || value.length !== 2) return undefined
      return {
        startTime: value[0] + ' 00:00:00',
        endTime: value[1] + ' 23:59:59',
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions(
        'time.componentValue',
        ((data || {})?.componentValue && [data.componentValue[0], data.componentValue[1]]) || undefined
      )
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'queryInput',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '快速搜索'),
      placeholder: computed(() => '请输入流程标题/流程编号'),
      allowClear: true,
      type: 'search-clear',
      onSearch: onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('queryInput.componentValue', (data || {})?.componentValue || undefined)
    },
  },
]

const setSearchConfigFn = async () => {
  await nextTick()
  search.initOptions(configList)
  search.clear()
  const cachData = (cache.get(routerName?.value) && JSON.parse(cache.get(routerName?.value) as string)) || {}
  search.setDefaultSearch(cachData)
  nextTick(() => {
    queryData.value = {
      ...search.getParams(),
      ...{ cacheValue: cachData, init: true },
    }
  })
}

onMounted(() => {
  setSearchConfigFn()
})
</script>

<style lang="scss" scoped>
.search-content-container {
  display: flex;
  flex-wrap: wrap;
}
:deep(.fs-tree-select) {
  margin-bottom: 0;
}
:deep(.fs-input-affix-wrapper) {
  padding: 6px 8px !important;
}
</style>

<template>
  <div class="handle-time-container">
    <div class="user-height" @click="timeClick($event)">
      <span class="icon iconfont iconkaoqingongshiguanli marginR4 fontSize16 color999"></span>
      <span class="color999" style="vertical-align: top">{{ i18n.t('预计完成时间') }}: </span>
    </div>
    <FRangePicker
      v-if="editableData[id] && editableData[id]['timeClick']"
      format="YYYY-MM-DD"
      style="width: 120px; margin-right: 16px"
      :disabled-date="disabledDate"
      @click.stop
      @change="(date: Dayjs) => timePickerChange({forcastTime: date})"
    />
    <span
      class="center-middle user-hover marginR16"
      :class="[!forcastTime && 'color999']"
      @click="timeClick($event)"
      v-else
      >{{
        (taskStartTime &&
          forcastTime &&
          transformDate(taskStartTime, 'YYYY-MM-DD') + ' ~ ' + transformDate(forcastTime, 'YYYY-MM-DD')) ||
        i18n.t('待填')
      }}</span
    >
    <EditTimeModal
      v-model:visible="visible"
      @timePickerChange="timePickerChange"
      :forcastTime="forcastTime"
      :taskStartTime="taskStartTime"
      :contentData="contentData"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref } from 'vue'
import { ITask } from '@/types/handle'
import dayjs, { Dayjs } from 'dayjs'
import { deepClone, transformDate, useI18n } from '@/utils'
import EditTimeModal from './EditTimeModal.vue'

interface IProps {
  editableData: Record<string, ITask>
  id: string | number
  currtMilepostChildren: ITask[]
  forcastTime: any
  taskStartTime: any
  contentData: any
}

const props = defineProps<IProps>()
const i18n = useI18n()
const emits = defineEmits(['update:editableData'])
const editableData = computed({
  get: () => props.editableData,
  set: val => emits('update:editableData', val),
})
const visible = ref<boolean>(false)
const disabledDate = (current: Dayjs) => current && current < dayjs().subtract(1, 'days').endOf('day')
const quickEditHandleTaskTime = inject('quickEditHandleTaskTime') as (
  task: ITask,
  callback: (id: string | number) => void
) => void // 快速编辑
const processConfigInfo = inject<Record<string, any>>('processConfigInfo')

const timeClick = (event: MouseEvent) => {
  event.stopPropagation()
  if (processConfigInfo?.value?.isUpdateCompleteSend === 1) {
    visible.value = true
  } else if (Array.isArray(props.currtMilepostChildren) && props.currtMilepostChildren.length > 0) {
    editableData.value[props.id] = deepClone(props.currtMilepostChildren.find(item => props.id == item.id) as ITask)
    editableData.value[props.id].timeClick = true
  }
}

const timePickerChange = (data: any) => {
  if (Array.isArray(props.currtMilepostChildren) && props.currtMilepostChildren.length > 0) {
    const currTask = deepClone(props.currtMilepostChildren.find(item => props.id == item.id) as ITask)
    const obj = Object.assign({}, currTask, editableData.value[props.id])
    ;(obj.taskStartTime = (data.forcastTime && dayjs(data.forcastTime[0]).format('YYYY-MM-DD') + ' 00:00:00') || null),
      (obj.forcastTime = (data.forcastTime && dayjs(data.forcastTime[1]).format('YYYY-MM-DD') + ' 23:59:59') || null),
      data.forcastTimeRemark &&
        (obj.contentData = JSON.stringify(
          Object.assign({}, (obj.contentData && JSON.parse(obj.contentData as string)) || {}, {
            forcastTimeRemark: data.forcastTimeRemark,
          })
        ))
    // 发送编辑请求
    quickEditHandleTaskTime(obj, () => {
      delete editableData.value[props.id]
    })
  }
}
</script>

<style scoped lang="scss">
.handle-time-container {
  display: inline-block;
  .user-height {
    display: inline-block;
    vertical-align: middle;
    height: 32px;
    line-height: 32px;
    text-align: center;
  }
  .marginR4 {
    margin-right: 4px;
  }
}
</style>

<template>
  <FDrawer v-model:visible="visible" title="流程图" placement="right" width="68vw">
    <FSpin :spinning="componentsLoading">
      <ProcessDesigner v-if="context" :actual-node-list="actualNodeList" :context="context" />
    </FSpin>
  </FDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getFlowChartByInstanceId } from '@/api'
import ProcessDesigner from '@/views/approval-operate/components/ProcessDesigner/index.vue'

const visible = ref<boolean>(false)
const processId = ref<any>()
const componentsLoading = ref<boolean>(false)
const actualNodeList = ref<any[]>([])
const context = ref<any>()

const getFlowChartFn = async () => {
  try {
    componentsLoading.value = true
    const res = await getFlowChartByInstanceId(processId.value)
    console.log('res :>> ', res)
    if (res.code !== 200) throw new Error(res.msg)
    actualNodeList.value = res?.data?.actualNodeList ?? []
    context.value = res?.data?.context
  } finally {
    componentsLoading.value = false
  }
}

const onOpenFn = (id: any) => {
  processId.value = id
  actualNodeList.value = []
  context.value = undefined
  visible.value = true
  getFlowChartFn()
}

defineExpose({ onOpenFn })
</script>

<style scoped lang="scss">
:deep(.process-designer-body) {
  height: calc(100vh - 100px);
}
</style>

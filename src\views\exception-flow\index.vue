<template>
  <div class="modify-board-container">
    <div class="card-content-shadow pt24 pl24 pr24 mb16">
      <SearchContent v-model:query-data="queryData" />
    </div>
    <div class="card-content-shadow pt24 pl24 pr24">
      <div class="card-btn">
        <div class="fei-su-title">异常流程列表</div>
        <div>
          <FButton type="default" class="add-button marginR12" @click="exportData">
            <i class="icontubiao_daochu iconfont marginR5 fontSize14"></i>导出
          </FButton>
          <FButton type="primary" :disabled="selectedRowKeys.length === 0" @click="BatchAll"> 批量催办 </FButton>
        </div>
      </div>
      <FTable
        class="table-warp"
        :columns="columns"
        :loading="loading"
        :data-source="dataList"
        :row-key="(data:any) => data.processInstanceId"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
        :sticky="{ offsetHeader: 0 }"
        :scroll="{ x: 'min-content' }"
        :pagination="{
          total: paging.total,
          current: paging.currPage,
          pageSize: paging.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
        }"
        @change="onPaginationChangeFn"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'expectedEndTime'">
            {{ dayjs(record.expectedEndTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
          <template v-if="column.dataIndex === 'nodeStartTime'">
            {{ dayjs(record.nodeStartTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
          <template v-if="column.dataIndex === 'createdTime'">
            {{ dayjs(record.createdTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
          <template v-if="column.dataIndex === 'processInstanceCode'">
            <span :class="[record?.processInstanceId ? 'code-link' : '']" @click="onJumpDemandDetial(record)">{{
              record?.processInstanceCode ?? '--'
            }}</span>
          </template>

          <template v-if="column.dataIndex === 'currentMilepost'">
            <ProcessDetail
              v-model:value="record.currentMilepost"
              :expanded-ids="[record?.currentMilepost?.id].filter(Boolean)"
            />
            <div class="label-box">
              <span class="label">[责任人]</span>
              <span>{{ record?.currentMilepost?.superviser ?? '--' }}</span>
            </div>
            <div class="label-box">
              <span class="label">[DDL]</span>
              <span>{{
                (record?.currentMilepost?.forcastTime &&
                  transformDate(record?.currentMilepost?.forcastTime, 'YYYY-MM-DD HH:mm:ss')) ??
                '--'
              }}</span>
            </div>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <span class="color-bule" @click="onBatch(record)">催办</span>
          </template>
        </template>
      </FTable>
    </div>
  </div>
</template>
<script setup lang="ts">
import dayjs from 'dayjs'
import { transformDate } from '@/utils'
import { ref, computed, watch, reactive, nextTick, provide } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from '@fs/smart-design'
import { cache } from '@/utils'
import SearchContent from './SearchContent/index.vue'
import ProcessDetail from '@/views/process-staffmeeting-list/components/ProcessDetail/index.vue'

import { getAbnormalOngoingProcessList, exportExceptionalProcesses, urgeExceptionalProcesses } from '@/api'
import { GetTagAndNode } from '@/api'

import { IProcessClassType } from '@/types/handle'
const route = useRoute()
const currProcessTypeId = ref()
const routerName = ref()
const processTypeData = ref<IProcessClassType[]>([])
const currProcessTypeData = computed<any>(
  () => processTypeData.value.find(item => item.id == +currProcessTypeId.value) ?? {}
)
const processConfigIdList = computed<any>(() => {
  if (currProcessTypeId.value != 'all' && !currProcessTypeData.value.id)
    return processTypeData.value.map(item => item.id)
  return undefined
})
const loading = ref(false)
const paging = reactive<any>({ currPage: 1, pageSize: 10 })
const queryData = ref<any>({})
const dataList = ref<any[]>([])

const selectedData = ref<any>([])
const selectedRowKeys = ref<any>([])
const onSelectChange = (selectedRowKey: any[], selectedRow: any[]) => {
  selectedRowKeys.value = selectedRowKey
  selectedData.value = selectedRow
}
const columns = ref([
  { title: '当前负责人', dataIndex: 'superviserName', key: 'superviserName', width: 120 },
  { title: '流程类型', dataIndex: 'processConfigName', key: 'processConfigName', width: 120 },
  { title: '流程编号', dataIndex: 'processInstanceCode', key: 'processInstanceCode', width: 180 },
  { title: '流程名称', dataIndex: 'topicName', key: 'topicName', width: 180 },
  { title: '当前阶段', dataIndex: 'currentMilepost', key: 'currentMilepost', width: 200 },
  { title: '阶段开启时间', dataIndex: 'nodeStartTime', key: 'nodeStartTime', width: 180 },
  { title: '当前阶段停留时间(h)', dataIndex: 'stageStayHours', key: 'stageStayHours', width: 180 },
  { title: '计划完成时间', dataIndex: 'expectedEndTime', key: 'expectedEndTime', width: 160 },
  { title: '流程发起日期', dataIndex: 'createdTime', key: 'createdTime', width: 160 },
  { title: '流程进行天数', dataIndex: 'processRunDays', key: 'processRunDays', width: 140 },
  { title: '操作', dataIndex: 'operation', key: 'operation', width: 80 },
])

// 查询列表
const queryDataList = async () => {
  try {
    loading.value = true
    const data = { ...queryData.value }
    cache.set(
      routerName?.value,
      JSON.stringify({
        ...(data?.cacheValue ?? {}),
        currPage: paging.currPage,
        pageSize: paging.pageSize,
      })
    )
    delete data.cacheValue
    const res = await getAbnormalOngoingProcessList({
      ...data,
      ...paging,
      processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
      hasProcess:
        (processConfigIdList.value && processConfigIdList.value.length > 0) || currProcessTypeId.value == 'all' ? 1 : 0,
    })

    dataList.value = res.data.list || []
    paging.total = res?.data?.totalCount || 0
  } finally {
    selectedRowKeys.value = []
    loading.value = false
  }
}
const onBatch = (record: any) => {
  urgeExceptionalProcesses({
    milepostIds: [record.milepostId],
  }).then(res => {
    message.success('催办成功')
    queryDataList()
  })
}
const BatchAll = () => {
  urgeExceptionalProcesses({
    milepostIds: selectedData.value.map(item => {
      return item.milepostId
    }),
  }).then(res => {
    message.success('批量催办成功')
    queryDataList()
  })
}
const onPaginationChangeFn = (pagination: any) => {
  paging.currPage = pagination.current
  paging.pageSize = pagination.pageSize
  queryDataList()
}

// 查询列表
const onGetSearchData = async (data: any) => {
  queryData.value = data
  paging.currPage = data?.cacheValue?.currPage || 1
  paging.pageSize = data?.cacheValue?.pageSize || 10
  queryDataList()
}

const exportData = async () => {
  if (!dataList.value.length) {
    message.warning('暂无数据可导出！')
    return
  }

  const data = { ...queryData.value }
  delete data.cacheValue
  const res = await exportExceptionalProcesses(data)
  const link = document.createElement('a')
  link.href = URL.createObjectURL(new Blob([res.data]))
  link.download =
    (res?.headers?.['content-disposition']?.split('filename=')?.[1] &&
      decodeURI(res?.headers?.['content-disposition']?.split('filename=')?.[1])) ||
    'modify_export.xlsx'
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  message.success('数据导出成功！')
}
const router = useRouter()
const jumpToDemand = (id: string | number, processTypeId: string | number, targer = false) => {
  const options = { name: 'ProcessDetail', params: { id } }

  if (!targer) router.push(options)
  else window.open(router.resolve(options).href, '_blank')
}

// 需求详情跳转
const onJumpDemandDetial = (record: any, idKey = 'processInstanceId') => {
  record?.[idKey] && jumpToDemand(record[idKey], undefined, true)
}
provide('processTypeData', processTypeData)
provide('routerName', routerName)
provide('currProcessTypeId', currProcessTypeId)
provide('currProcessTypeData', currProcessTypeData)
provide('processConfigIdList', processConfigIdList)

watch(
  () => queryData.value,
  val => {
    onGetSearchData(val)
  },
  { deep: true }
)

const getProcessTypes = async () => {
  try {
    const res = await GetTagAndNode()
    processTypeData.value = res.data
  } catch (error) {
    throw new Error('流程类型请求失败')
  }
}
const handleProcessType = async () => {
  await nextTick()
  if (processConfigIdList.value) {
    processTypeData.value = processTypeData.value.filter(item => item?.groupCode == currProcessTypeId.value)
  }
}
watch(
  () => route.params.type,
  async type => {
    !processTypeData?.value?.length && (await getProcessTypes())
    handleProcessType()
    currProcessTypeId.value = type
    routerName.value = route.name
    const cachData = (cache.get(routerName?.value) && JSON.parse(cache.get(routerName?.value) as string)) || {}
    // const currentCachData = cachData?.[currProcessTypeId?.value] || {}
    // tabValue.value = currentCachData?.type ?? 1
    // pageData.value.pageNum = currentCachData?.pageNum ?? 1
    // pageData.value.pageSize = currentCachData?.pageSize ?? 10
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.modify-board-container {
  .none-container-padding {
    margin-top: -24px;
    margin-left: -4px;
    width: calc(100% + 8px);
  }
  .card-content-shadow {
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .code-link {
    cursor: pointer;
    color: #378eef;
  }
  .mr6 {
    margin-right: 6px;
  }
  .mr4 {
    margin-right: 4px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .error-color {
    color: #f04141;
  }
  .sucess-color {
    color: #2fcc83;
  }
  .empty-content {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .count-info-content {
    line-height: 18px;
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
  :deep(.fs-table-tbody > tr > td) {
    &.fs-table-cell-row-hover {
      background-color: #f1f4f8;
    }
  }
  :deep(.fs-table-column-sorters) {
    justify-content: flex-start;
    .fs-table-column-title {
      flex: 0;
      white-space: nowrap;
    }
  }
}

.type-select {
  display: flex;
  align-items: center;
  background: #fff;

  height: 32px;
  span {
    display: inline-block;
    border-radius: 4px;
    color: #999999;
    padding: 0 16px;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
    cursor: pointer;
    border: 1px solid #dddddd;
    &.active {
      background: #378eef;
      // box-shadow: 0px 2px 4px 0px rgba(17, 24, 39, 0.04);
      color: #fff;
      border: 1px solid #378eef;
    }
    &:first-child {
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }
    &:last-child {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
    }
  }
}
.card-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.color-bule {
  color: #378eef;
  cursor: pointer;
}
.code-link {
  display: inline-block;
  cursor: pointer;
  color: #378eef;
}
</style>

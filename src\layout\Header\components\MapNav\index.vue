<template>
  <div class="map-nav-warpper">
    <div class="map-nav-icon-warpper" @click="visible = true">
      <i class="map-nav-icon" />
    </div>
    <FDrawer v-model:visible="visible" placement="left" width="320">
      <FCollapse v-model:activeKey="active" ghost>
        <template #expandIcon="{ isActive }">
          <div class="map-nav-header">
            <FIcon type="icon-shouqi1" :style="{ transform: isActive ? 'rotate(90deg)' : 'rotate(0deg)' }" />
          </div>
        </template>
        <FCollapsePanel key="1" :header="i18n.t('统一入口')">
          <FEmpty v-show="!sys.length" />
          <ul class="map-nav-list">
            <li class="map-nav-list-item" v-for="item in sys" :key="item.uuid" :title="item.title">
              <i :class="['icon', `icon-${item.icon}`]" />
              <a :href="item.route" target="_blank">
                {{ item.title }}
              </a>
            </li>
          </ul>
        </FCollapsePanel>
        <FCollapsePanel key="2" :header="i18n.t('自定义入口')">
          <FEmpty v-show="!custom.length" />
          <ul class="map-nav-list">
            <li class="map-nav-list-item" v-for="item in custom" :key="item.uuid" :title="item.title">
              <i :class="['icon', item.icon]" />
              <a :href="item.route" target="_blank">
                {{ item.title }}
              </a>
            </li>
          </ul>
        </FCollapsePanel>
      </FCollapse>
    </FDrawer>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue'
import { getMapNav } from '@/api'
import { i18n } from '@/init'
import type { IMapNav } from '@/types/request'

const visible = ref(false)

const active = ref<string[]>(['1', '2'])
const mapNav = ref<IMapNav>({} as IMapNav)
const sys = computed(() => mapNav.value.sys || [])
const custom = computed(() => mapNav.value.custom || [])

onMounted(() => {
  // requestIdleCallback(queryMapNavList)
})

const queryMapNavList = async () => {
  const { data } = await getMapNav()
  mapNav.value = data
}
watch(visible, val => {
  val && !Object.keys(mapNav.value)?.length && queryMapNavList()
})
</script>

<style lang="scss" scoped>
.map-nav-warpper {
  .map-nav-icon-warpper {
    display: flex;
    margin-right: 16px;
    width: 24px;
    height: 24px;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    background-color: #fff;

    &:hover {
      background-color: #f1f4f8;
    }

    .map-nav-icon {
      background-image: url('@/assets/images/map_nav.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      width: 16px;
      height: 16px;
    }
  }
}

:deep(.fs-drawer-body) {
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
}
:deep(.fs-collapse-header) {
  margin: 0 8px 16px;
  padding: 0 0 16px 0 !important;
  line-height: 24px !important;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #eee;
}
:deep(.ant-collapse-content-box) {
  padding: 0 !important;
}
.map-nav-header {
  position: absolute;
  right: 0;
}

.map-nav-list {
  padding: 0;
  .map-nav-list-item {
    display: flex;
    align-items: center;
    padding: 9px 8px;
    margin: 12px 0;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;
    }
    &:first-child {
      margin-top: 0;
    }
    .icon {
      font-size: 20px;
      margin-right: 10px;
    }
    a {
      color: #333;
      font-size: 14px;
      line-height: 22px;
    }
  }
}
</style>

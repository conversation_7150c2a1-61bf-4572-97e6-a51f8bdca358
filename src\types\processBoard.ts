import type { BasicFetchResponse } from './common'

export interface BasicPageParams {
  currPage?: number
  pageSize?: number
  total?: number
}

export interface ProcessListParams extends BasicPageParams {
  projectUuidList?: string[]
  tag?: string
  endTime?: string
  processConfigId?: number
  queryInput?: string
  startTime?: string
  status?: number
  [key: string]: any
}

export interface ProcessItem {
  completionRate: number
  earlyCompletion: number
  earlyCompletionRate: number
  inProgress: number
  name: string
  onTimeCompletion: number
  onTimeCompletionRate: number
  onlineRate: number
  overdue: number
  overdueRate: number
  projectTotal: number
  total: number
  uuid: string
  [key: string]: any
}

export interface ProcessListResponse extends BasicFetchResponse {
  data: {
    list: ProcessItem[]
    total: number
    [key: string]: any
  }
}

export interface MilepostExtendSaveParams {
  evolve?: string
  id?: number
  instanceId: number
  kp?: string
  milepostId: number
  reviewMsg?: string
}

export interface countResponse extends BasicFetchResponse {
  data: {
    ipdComplete?: number
    ipdInprogress?: number
    oneNComplete?: number
    oneNInprogress?: number
    pbgComplete?: number
    pbgInprogress?: number
  }
}

interface SuccessIRes {
  code: number
  msg: string
  traceId: string
  success: boolean
}

interface Node {
  createdTime: string
  id: number
  milepostName: string
  nodeId: string
  nodeMode: number
  parentId: number
  processDefineNodeKey: string
  sort: number
  superviser: string
  tag: string
  updatedTime: string
}

interface Dictionary {
  children: Dictionary[]
  field: string
  label: string
  value: string
}

interface ProcessConfigItme {
  dictionarys: Dictionary[]
  nodes: Node[]
  processConfigId: number
  processConfigName: string
}

export interface ScreenedResponse extends SuccessIRes {
  data: ProcessConfigItme[]
}

interface ISearchItemConfig {
  componentLabel?: any
  componentName: string
  componentValueKey?: string
  componentValue?: any
  componentAttrs?: any
  componentOptionsApi?: any
  getComponentValueFormat?: any
  setComponentValueFormat?: any
  [key: string]: any
}

export interface IOptions {
  [key: string]: ISearchItemConfig
}

export interface ISearchDataConfig {
  options: IOptions
  [key: string]: any
}

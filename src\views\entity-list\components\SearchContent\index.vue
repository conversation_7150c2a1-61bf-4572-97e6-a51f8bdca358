<template>
  <div class="search-content-container">
    <FTooltip
      :visible="visible"
      overlayClassName="cust-search-tip"
      placement="bottomLeft"
      color="#fff"
      :getPopupContainer="(trigger: HTMLElement) => trigger.parentElement"
    >
      <template #title>
        <div class="search-box">
          <SearchItem
            v-model:visible="visible"
            v-model:formState="formState"
            :fieldList="fieldList"
            @getDataFn="getDataFn"
          />
        </div>
      </template>
      <div class="search-tip" @click="visible = true">
        <i class="icontubiao_lvqi iconfont"></i>
        <span>{{ i18n.t('高级搜索') }}</span>
        <span class="num" v-show="selectData.length">{{ selectData.length }}</span>
      </div>
    </FTooltip>
    <SearchLabel v-if="selectData.length" v-model:formState="formState" @getDataFn="getDataFn" />
  </div>
</template>

<script setup lang="ts">
import SearchItem from '../SearchItem/index.vue'
import SearchLabel from '../SearchLabel/index.vue'
import { ref, computed } from 'vue'
import { FieldList } from '@/types/entity'
import { useI18n } from '@/utils'

type propsType = {
  fieldList: FieldList[]
  selectData: any[]
}

const emits = defineEmits(['update:selectData', 'getDetailById'])
const props = defineProps<propsType>()
const i18n = useI18n()
const visible = ref<boolean>(false)
const formState = ref<any[]>([])
const selectData = computed({
  get: () => props.selectData,
  set: val => emits('update:selectData', val),
})

const getDataFn = (data: any) => {
  selectData.value = data.filter((item: any) => item.isSearchValue)
  emits('getDetailById')
}
</script>

<style scoped lang="scss">
.search-content-container {
  .search-tip {
    display: flex;
    align-items: center;
    max-width: 100px;
    height: 18px;
    line-height: 18px;
    color: #378eef;
    cursor: pointer;
    .icontubiao_lvqi {
      margin-right: 4px;
    }
    .num {
      &::before {
        content: '·';
        display: inline-block;
        margin: 0 2px;
        font-weight: bold;
      }
    }
  }
}
:deep(.cust-search-tip) {
  max-width: 100%;
  width: 560px;
  .fs-tooltip-inner {
    padding: 16px;
  }
  .search-box {
    color: #378eef;
  }
}
</style>

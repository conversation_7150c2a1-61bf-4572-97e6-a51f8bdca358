<template>
  <div class="not-found">
    <h1 class="title">404 - 页面未找到</h1>
    <p class="message">您所访问的页面不存在或没有访问权限。</p>
    <p class="fancy-message">很抱歉，您迷失在了未知的领域。</p>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'NotFound',
})
</script>

<style scoped>
.not-found {
  text-align: center;
  margin-top: 100px;
}

.title {
  font-size: 48px;
  color: #ff4081;
}

.message {
  font-size: 24px;
  color: #333;
}

.fancy-message {
  font-size: 18px;
  color: #666;
  margin-top: 20px;
}
</style>

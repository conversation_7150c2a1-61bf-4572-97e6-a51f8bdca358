import { deepClone } from '@/utils'

/**
 * 扁平化树形数据（直接使用组件前缀字段）
 */
export const flattenTree = (data: any[], childrenKey = 'dict_tree_children', idKey = 'dict_tree_id'): any[] => {
  const result: any[] = []

  const traverse = (nodes: any[], level = 0, parentId?: string) => {
    nodes.forEach(node => {
      result.push({
        ...node,
        dict_tree_level: level,
        dict_tree_parent_id: parentId || '',
      })

      if (node[childrenKey] && node[childrenKey].length > 0) {
        traverse(node[childrenKey], level + 1, node[idKey])
      }
    })
  }

  traverse(data)
  return result
}

// 递归处理树形数据的方法 - 只使用组件前缀字段，避免污染外部数据
export const processTreeData = (data: any[], parentType?: string): any[] => {
  if (!Array.isArray(data)) return []
  return data.map((item: any) => {
    const generateId = () => {
      const baseId = item.id || item.field || `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      return `dict_tree_${baseId}`
    }
    const getNodeType = () => {
      if (parentType === 'dict_tree_field') return 'dict_tree_category'
      if (parentType === 'dict_tree_category') return 'dict_tree_item'
      if (parentType === 'dict_tree_item') return 'dict_tree_item'
      return 'dict_tree_field'
    }
    const getNodeName = () => {
      return item.originalName || item.name || undefined
    }
    const processedChildren =
      item.children && Array.isArray(item.children) ? processTreeData(item.children, getNodeType()) : []

    return {
      dict_tree_id: generateId(),
      dict_tree_name: getNodeName(),
      dict_tree_type: getNodeType(),
      dict_tree_children: processedChildren,
      dict_tree_expanded: false,
      dict_tree_original_data: deepClone(item),
      ...item,
    }
  })
}

export const isTreeValid = (tree, requiredFields = ['originalName', 'value']) => {
  for (const node of tree) {
    for (const field of requiredFields) {
      if (!node[field] || String(node[field]).trim() === '') {
        return false
      }
    }
    if (node.dict_tree_children && node.dict_tree_children.length) {
      if (!isTreeValid(node.dict_tree_children, requiredFields)) {
        return false
      }
    }
  }
  return true
}

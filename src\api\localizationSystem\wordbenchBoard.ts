import { request } from '@/utils'
import type {
  ProcessTree,
  TreeParams,
  LabelParams,
  ProcessLabel,
  ListParams,
  UpdateLabelParams,
} from '@/types/localizationSystem/wordbenchBoard'
import { IRes } from '@/types/request'

// 获取对应国家流程
export const getProcess = (params: TreeParams) => {
  return request.get<any>('/api/localization/selectAllLocalizationRelateProcessAndLabel', { params })
}
// 获取流程
export const getProcessList = (params: ListParams) => {
  return request.post<any>('/api/localization/selectProcessByLabel', params)
}
export const submit = (params: LabelParams) => {
  return request.post<any>('/api/localization/addLocalizationLabel', params)
}

export const updateLabelSort = (params: UpdateLabelParams[]) => {
  return request.post<any>('/api/localization/updateLabelSort', params)
}

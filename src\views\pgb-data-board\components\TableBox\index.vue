<template>
  <div class="table-box-container">
    <FTable
      :data-source="list"
      :loading="loading"
      :columns="columns"
      table-layout="fixed"
      :pagination="false"
      :scroll="{ x: '100%' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'projectName'">
          <span style="cursor: pointer; color: #378eef" @click="onJumpDemandDetial(record)">{{
            record.projectName
          }}</span>
        </template>
        <template v-if="['reviewMsg', 'evolve', 'kp'].includes(column.dataIndex)">
          <EditText
            v-model:text="record[column.dataIndex]"
            v-model:status="record[column.dataIndex + '_status']"
            :value-key="column.dataIndex"
            :instance-id="record.instanceId"
            :milepost-id="record.id"
          />
        </template>
        <template v-if="column.dataIndex == 'files'">
          <DownloadFiles
            v-if="record.files.length"
            class="color1890ff cursor"
            :data="record.files"
            @download="download"
            @batch-download="batchDownload"
          />
          <div v-else>--</div>
        </template>
      </template>
    </FTable>
    <div class="fei-su-pagination">
      <FPagination
        v-model:current="page.pageNum"
        v-model:pageSize="page.pageSize"
        :total="page.total"
        @change="onChangeFn"
        show-size-changer
        show-quick-jumper
        :show-total="() => `${i18n.t('共')} ${page.total} ${i18n.t('条')}`"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ProcessItem, BasicPageParams } from '@/types/pgbDataBoard'
import DownloadFiles from './components/DownloadFiles/index.vue'
import { columns } from './tableConfig'
import EditText from './components/EditText/index.vue'
import { useRouter } from 'vue-router'
import { toRefs } from 'vue'
import { download, batchDownload, jumpToDemand } from '@/utils'
import { useI18n } from '@/utils'
const i18n = useI18n()

const router = useRouter()

type propsType = {
  list: ProcessItem[]
  page: BasicPageParams
  loading: boolean
}
const props = withDefaults(defineProps<propsType>(), {
  list: () => [],
  page: () => ({}),
  loading: false,
})

const emits = defineEmits(['onPageChange'])
const { page } = toRefs(props)

const onJumpDemandDetial = (record: any) => {
  jumpToDemand(record.instanceId, record.processConfigId, false, router)
}

const onChangeFn = (current: number, pageSize: number) => {
  page.value.pageNum = current
  page.value.pageSize = pageSize
  emits('onPageChange', page.value)
}
</script>

<style scoped lang="scss">
.table-box-container {
  padding-top: 12px;
  :deep(.urgent-label) {
    display: inline-block;
    height: 18px;
    line-height: 17.5px;
    padding: 0 3px;
    background: #eafaf2;
    color: #2fcc83;
    border-radius: 2px;
    border: 1px solid #2fcc83;
    &.is-urgent {
      color: #f04141;
      border-color: #f04141;
      background: #fdecec;
    }
  }
  :deep(.color333) {
    color: #333;
  }
  :deep(.colorFA8F23) {
    color: #fa8f23;
  }
  :deep(.color2FCC83) {
    color: #2fcc83;
  }
  :deep(.color378EEF) {
    color: #378eef;
  }
  :deep(.colorF04141) {
    color: #f04141;
  }
  :deep(.fs-table-cell) {
    &:empty {
      &::after {
        content: '--';
      }
    }
  }
  :deep(.fs-table-tbody) {
    > tr:hover:not(.fs-table-expanded-row) > td,
    .fs-table-row-hover,
    .fs-table-row-hover > td {
      background: #f1f4f8 !important;
    }
    tr > td {
      background: #fff !important;
    }
    .table-striped > td {
      background: #fbfdff !important;
    }
  }
  :deep(.fs-table-container) {
    &::after {
      box-shadow: none;
    }
  }
  :deep(.fs-table-content) {
    padding-bottom: 4px;
  }
  .fei-su-pagination {
    padding-top: 11px;
  }
  :deep(.popover-select-notes) {
    .fs-popover-inner-content {
      padding: 12px;
    }
  }
}
</style>

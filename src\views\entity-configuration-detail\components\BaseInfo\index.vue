<template>
  <div class="base-info-container">
    <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <FRow :gutter="[24, 0]">
        <FCol :span="8">
          <FFormItem :label="i18n.t('实体名称')" name="name">
            <FInput v-model:value="formState.name" :placeholder="i18n.t('请输入实体名称')" />
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem :label="i18n.t('实体编码')" name="code">
            <FSelect v-model:value="formState.code" :options="entityTables" allow-clear placeholder="请选择实体编码" />
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem :label="i18n.t('实体分类')" name="entityClass">
            <FSelect
              v-model:value="formState.entityClass"
              :options="entityClassLists"
              :field-names="{ label: 'name', value: 'id' }"
              allow-clear
              :placeholder="i18n.t('请选择实体分类')"
            />
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem :label="i18n.t('启用状态')" name="status">
            <FSelect
              v-model:value="formState.status"
              :options="options"
              allow-clear
              :placeholder="i18n.t('请选择启用状态')"
            />
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem :label="i18n.t('实体类型')" name="entityType">
            <FSelect
              v-model:value="formState.entityType"
              :options="entityTypeOptios"
              allow-clear
              :placeholder="i18n.t('请选择实体类型')"
              @change="entityTypeChange"
            />
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem :label="i18n.t('主实体')" name="masterEntityId" v-if="(formState.entityType as number) != 0">
            <FSelect
              v-model:value="formState.masterEntityId"
              :options="masterEntityOptions"
              :field-names="{ label: 'name', value: 'id' }"
              allow-clear
              :placeholder="i18n.t('请选择主实体')"
              @change="masterEntityIdChange"
            />
          </FFormItem>
        </FCol>
        <FCol :span="24">
          <FFormItem :label="i18n.t('备注说明')" name="describe">
            <FTextarea
              v-model:value="formState.describe"
              :placeholder="i18n.t('请输入备注说明')"
              :auto-size="{ minRows: 4 }"
            />
          </FFormItem>
        </FCol>
      </FRow>
    </FForm>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { IEntityFormState, IEntityPageListData } from '@/types/entity'
import { getEntityType, getEntityClass, getEntityTables } from '@/api'
import { useI18n, deepClone } from '@/utils'

interface IProps {
  detailInfo?: IEntityPageListData
}

const props = defineProps<IProps>()
const i18n = useI18n()
const formRef = ref<FormInstance>()
const formState = reactive<IEntityFormState>({
  name: undefined,
  code: undefined,
  entityClass: undefined,
  status: undefined,
  entityType: undefined,
  masterEntityId: undefined,
  masterEntityName: undefined,
  describe: undefined,
  esMaxResultWindow: 5000,
})
const rules: Record<string, Rule[]> = {
  name: [{ required: true, message: i18n.t('请输入实体名称') }],
  code: [{ required: true, message: i18n.t('请输入实体编码') }],
  entityClass: [{ required: true, message: i18n.t('请选择实体分类') }],
  status: [{ required: true, message: i18n.t('请选择启用状态') }],
  entityType: [{ required: true, message: i18n.t('请输入实体类型') }],
  masterEntityId: [{ required: true, message: i18n.t('请选择主实体') }],
}

const entityTypeOptios = computed(() => [
  { label: i18n.t('主实体'), value: 0 },
  { label: i18n.t('明细实体'), value: 1 },
])
const options = computed(() => [
  { label: i18n.t('是'), value: 0 },
  { label: i18n.t('否'), value: 1 },
])
const masterEntityOptions = ref<any>([])
const entityClassLists = ref<any>([])
const entityTables = ref<any>([])

const entityTypeChange = async (val: any) => {
  if (val === 0) return
  masterEntityOptions.value = []
  formState.masterEntityId = undefined
  formState.masterEntityName = undefined
  const res = await getEntityType({ entityType: 0 })
  if (res.code !== 200) throw new Error(res.msg)
  masterEntityOptions.value = res.data || []
}

const masterEntityIdChange = (value: any, option: any) => {
  formState.masterEntityName = option?.name
}

const submitFn = async () => {
  if (!formRef.value) {
    return
  }
  await formRef.value.validate()
  return deepClone(formState)
}

defineExpose({ submitFn })

const getEntityClassLists = async () => {
  const res = await getEntityClass()
  if (res.code !== 200) throw new Error(res.msg)
  entityClassLists.value = (res?.data || []).map((item: any) => {
    item.id = item.id.toString()
    return item
  })
}

const getEntityTablesFn = async () => {
  const res = await getEntityTables()
  if (res.code !== 200) throw new Error(res.msg)
  entityTables.value = (res?.data || []).map((item: any) => ({
    value: item,
    label: item,
  }))
}

onMounted(() => {
  getEntityClassLists()
  getEntityTablesFn()
})

watch(
  () => props.detailInfo,
  val => {
    val?.entityType === 1 && entityTypeChange(1)
    val &&
      Object.entries(formState).forEach(([key, value]) => {
        formState[key as keyof typeof formState] = value || (val[key as keyof typeof val] as any)
      })
  }
)
</script>

<style scoped lang="scss">
.base-info-container {
  :deep(.fs-form-item-control-input-content) {
    height: auto !important;
  }
}
</style>

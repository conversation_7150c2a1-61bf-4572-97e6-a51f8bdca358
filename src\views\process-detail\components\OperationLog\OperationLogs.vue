<template>
  <FTable
    :columns="columns"
    :data-source="operationLogs"
    :loading="loading"
    table-layout="fixed"
    :pagination="pagination"
  />
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { operationRecord } from '@/api/handle'
import { transformDate, useI18n } from '@/utils'
import type { IOperation } from '@/types/handle'

const i18n = useI18n()
const TYPE_MAP = {
  0: i18n.t('创建需求'),
  1: i18n.t('修改'),
  2: i18n.t('删除'),
  3: i18n.t('转派任务'),
  4: i18n.t('完成任务'),
  5: i18n.t('保存'),
  6: i18n.t('办结'),
  7: i18n.t('添加子节点'),
  8: i18n.t('添加子任务'),
  9: i18n.t('审核完成'),
  10: i18n.t('驳回任务'),
  11: i18n.t('转派里程碑'),
  12: i18n.t('完成里程碑'),
  13: i18n.t('编辑子节点'),
  14: i18n.t('关联流程'),
  15: i18n.t('解除关联流程'),
  16: i18n.t('驳回里程碑'),
  18: i18n.t('添加角色'),
  19: i18n.t('修改角色'),
  20: i18n.t('删除角色'),
}

const props = defineProps({
  instanceId: {
    type: [String, Number],
    default: '',
  },
})
const operationLogs = ref<IOperation[]>([])
const loading = ref(false)
const columns = computed(() => [
  { title: i18n.t('创建人'), dataIndex: 'createUser', key: 'createUser' },
  { title: i18n.t('状态'), dataIndex: 'typeName', key: 'typeName' },
  { title: i18n.t('操作内容'), dataIndex: 'content', key: 'content' },
  { title: i18n.t('操作时间'), dataIndex: 'createTime', key: 'createTime' },
])
const pagination = ref({
  pageNo: 1,
  pageSize: 10, // 默认每页显示数量
  showSizeChanger: true, // 显示可改变每页数量
  onChange: (page: number, pageSize: number) => changePage(page, pageSize), //点击页码事件
  total: 0, //总条数
})

onMounted(() => {
  fetchLogs()
})

// 获取操作日志
const fetchLogs = () => {
  loading.value = true
  const parmas = {
    instanceId: props.instanceId,
    pageNum: pagination.value.pageNo,
    pageSize: pagination.value.pageSize,
  }
  operationRecord(parmas).then(res => {
    operationLogs.value = res.data.list.map((item: IOperation) => {
      return {
        ...item,
        createTime: transformDate(item.createTime),
        typeName: TYPE_MAP[item.type as keyof typeof TYPE_MAP],
      }
    })
    pagination.value.total = res.data.totalCount
    loading.value = false
  })
}

// 分页变化
const changePage = (page: number, pageSize: number) => {
  pagination.value.pageNo = page
  pagination.value.pageSize = pageSize
  fetchLogs()
}
</script>
<style lang="scss" scoped>
.info-time-box {
  li {
    display: flex;
    padding-bottom: 0px;
    .info-time {
      display: inline-block;
      color: #666;
      flex-shrink: 0;
      font-size: 12px;
    }
    .info-time-line {
      position: relative;
      margin-left: 20px;
      .ivu-timeline-item-head {
        width: 9px;
        height: 9px;
        top: 2px;
        background-color: #eee;
        border-radius: 50%;
        border: 1px solid transparent;
        position: absolute;
      }
    }
    .ivu-timeline-item-content {
      margin-top: 2px;
      padding: 1px 1px 10px 24px;
      font-size: 14px;
      position: relative;
      top: -3px;
    }
    .el-timeline-item__wrapper {
      top: -1px;
    }
    .line-detial-box {
      .icon {
        cursor: pointer;
        color: #bbb;
      }
      p {
        color: #666;
        font-size: 12px;
        span {
          font-size: 12px;
        }
      }
      .line-detial {
        display: none;
        padding: 0px 10px 0 10px;
        p {
          color: #666;
          font-size: 12px;
          padding-bottom: 0px;
          max-width: 940px;
          span {
            cursor: pointer;
          }
        }
      }
    }
    .ivu-timeline-item-tail {
      left: 4px;
      top: 8px;
      height: 100%;
      border-left: 1px solid #e8eaec;
      position: absolute;
    }
  }
  .ivu-timeline-item:last-child .ivu-timeline-item-tail {
    display: none;
  }
  .ivu-timeline-item.active .line-detial {
    display: block;
  }
}
</style>

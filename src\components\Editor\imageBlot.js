/* eslint-disable */
// 重新扩展自定义 img 标签
import { Quill } from '@vueup/vue-quill'
const BlockEmbed = Quill.import('formats/image')
class ImageBlot extends BlockEmbed {
  static create(value) {
    const node = super.create(value.imgURL)
    if (typeof value === 'string') {
      node.setAttribute('src', this.sanitize(value))
    } else {
      node.setAttribute('src', this.sanitize(value.imgURL))
    }
    node.setAttribute('attr-uuid', value.uuid)
    return node
  }

  static value(domNode) {
    return {
      imgURL: domNode.getAttribute('src'),
      uuid: domNode.getAttribute('attr-uuid'),
    }
  }
}
ImageBlot.blotName = 'image1'
Quill.register({
  'formats/image1': ImageBlot,
})

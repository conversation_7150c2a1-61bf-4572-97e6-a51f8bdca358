import { request } from '@/utils'
import type { IRes, ISearchDataList, ISearchData, IQueryParams } from '@/types/searchList'

// 获取流程搜素配置
export const getProcessSearchPage = (data: IQueryParams): Promise<IRes<ISearchDataList<ISearchData>>> => {
  return request.post('/api/searchModule/getPage', data)
}

// 修改流程搜素配置
export const updateProcessSearchPage = (data: IQueryParams): Promise<IRes> => {
  return request.post('/api/searchModule/update', data)
}

// 新增流程搜素配置
export const saveProcessSearchPage = (data: IQueryParams): Promise<IRes> => {
  return request.post('/api/searchModule/save', data)
}

// 删除
export const deleteProcessSearch = (id: number): Promise<IRes> => {
  return request.get('/api/searchModule/delById/' + id)
}

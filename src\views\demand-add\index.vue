<template>
  <FSpin style="width: 100%; margin-top: 300px" :spinning="loading" :tip="i18n.t('加载中...')" />
  <FButton v-if="fromId" style="margin: 0 0 16px auto" size="small" @click="router.back()">{{
    i18n.t('返回')
  }}</FButton>
  <FormRender v-if="fromId" :id="fromId" type="edit" :data="{ envData: data, envDefaultFormData: formData }" />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import FormRender from '@/components/FormRender/index.vue'
import { getDraftProcessInfoById, getFormByProcessDefineKey } from '@/api'
import { useI18n } from '@/utils'

const i18n = useI18n()

interface IQuery {
  draftId?: string
  labelCode?: string
  localName?: string
  processNo?: string
  keyType?: 'formId'
}

const route = useRoute()
const router = useRouter()
const { id, processDefineKey } = route.params as { id: string; processDefineKey: string }
const { draftId, labelCode, localName, processNo, keyType } = route.query as IQuery
const fromId = ref<string>()
const loading = ref(false)

const formData = ref<Record<string, unknown>>({})
const data = reactive({
  draftId,
  labelCode: labelCode,
  processConfigId: id,
  processDefineKey,
  handleSubmit: () => {
    localName ? router.push({ name: localName }) : router.push({ name: 'ProcessList' })
  },
})

onMounted(() => {
  init()
})

const init = async () => {
  loading.value = true
  initProcessNo()
  await getDraftData()
  await getFormId()
  loading.value = false
}

const initProcessNo = () => {
  processNo && (formData.value.relevanceInstanceCodeList = [processNo])
}

const getDraftData = async () => {
  if (!draftId) return
  const res = await getDraftProcessInfoById(draftId)
  const data = res?.data?.formData ?? {}
  formData.value = typeof data === 'string' ? JSON.parse(res?.data?.formData ?? {}) : data
}

const getFormId = async () => {
  if (keyType && keyType === 'formId') return (fromId.value = processDefineKey)
  const res = await getFormByProcessDefineKey(processDefineKey)
  res.code && (fromId.value = res.data?.id)
}
</script>

import type { BasicFetchResponse } from './common'

export interface BasicPageParams {
  pageNum?: number
  pageSize?: number
  total?: number
}

export interface ProcessListParams extends BasicPageParams {
  bu?: string
  dt?: string
  endTime?: string
  isUrgent?: number
  nodeId?: string
  processConfigId?: number
  queryInput?: string
  startTime?: string
  status?: number
  type?: string
}

interface File {
  name: string
  size: number
  url: string
}

export interface ProcessItem {
  bu: string
  delayRate: number
  dt: string
  evolve: string
  files: File[]
  forcastTime: string
  id: number
  instanceId: number
  isUrgent: number
  kp: string
  overalRejectCount: number
  overallDelayRate: number
  processConfigId: number
  processConfigName: string
  processInstanceCode: string
  project: string
  projectName: string
  rejectCount: number
  reviewMsg: string
  status: number
  superviser: string
  superviserUuid: string
  topicName: string
  type: string
  [key: string]: any
}

export interface ProcessListResponse extends BasicFetchResponse {
  data: {
    list: ProcessItem[]
    total: number
    [key: string]: any
  }
}

export interface MilepostExtendSaveParams {
  evolve?: string
  id?: number
  instanceId: number
  kp?: string
  milepostId: number
  reviewMsg?: string
}

export interface countResponse extends BasicFetchResponse {
  data: {
    ipdComplete?: number
    ipdInprogress?: number
    oneNComplete?: number
    oneNInprogress?: number
    pbgComplete?: number
    pbgInprogress?: number
  }
}

interface SuccessIRes {
  code: number
  msg: string
  traceId: string
  success: boolean
}

interface Node {
  createdTime: string
  id: number
  milepostName: string
  nodeId: string
  nodeMode: number
  parentId: number
  processDefineNodeKey: string
  sort: number
  superviser: string
  tag: string
  updatedTime: string
}

interface Dictionary {
  children: Dictionary[]
  field: string
  label: string
  value: string
}

interface ProcessConfigItme {
  dictionarys: Dictionary[]
  nodes: Node[]
  processConfigId: number
  processConfigName: string
}

export interface ScreenedResponse extends SuccessIRes {
  data: ProcessConfigItme[]
}

interface ISearchItemConfig {
  componentLabel?: any
  componentName: string
  componentValueKey?: string
  componentValue?: any
  componentAttrs?: any
  componentOptionsApi?: any
  getComponentValueFormat?: any
  setComponentValueFormat?: any
  clearComponentValueFn?: any
  [key: string]: any
}

export interface IOptions {
  [key: string]: ISearchItemConfig
}

export interface ISearchDataConfig {
  options: IOptions
  [key: string]: any
}

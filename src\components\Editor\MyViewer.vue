<template>
  <div class="ql-container ql-snow color333">
    <div class="ql-editor" v-html="porps.html" @click="handleElementClick"></div>
    <SeeImage v-model="seeImageObj.flag" :src="seeImageObj.src" />
  </div>
</template>
<script lang="ts" setup>
import { reactive } from 'vue'
import SeeImage from '@/components/SeeImage/index.vue'

const porps = defineProps({ html: { type: String, default: '' } })
const seeImageObj = reactive({ src: '', flag: false })
const handleElementClick = (e: any) => {
  if (e.target.nodeName === 'IMG') {
    seeImageObj.src = e.target.currentSrc
    seeImageObj.flag = true
  }
}
</script>

<style lang="scss" scoped>
.ql-container.ql-snow {
  position: relative;
  border: none !important;
  .ql-editor {
    padding: 0;
    word-break: break-all;
  }
  :deep(.ql-video) {
    width: 100%;
    height: 434px;
  }
  :deep(.fs-image) {
    position: absolute;
  }

  :deep(img) {
    max-width: 200px;
    max-height: 120px;
  }

  p {
    margin: 0;
  }
}
</style>

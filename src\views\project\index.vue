<template>
  <div class="modify-board-container">
    <div class="card-content-shadow pt24 pl24 pr24 mb16">
      <SearchContent v-model:query-data="queryData" />
    </div>
    <ContentBoard
      :trend-update-key="trendUpdateKey"
      @increase-trend-update-key="increaseTrendUpdateKey"
      :count-update-key="countUpdateKey"
      :hours-count-update-key="hoursCountUpdateKey"
      :basic-update-key="basicUpdateKey"
      :basic-statistics="basicStatistics"
      :project-statistics="projectStatistics"
      :process-distribution-data="processDistributionData"
      :trend-data="trendData"
      :project-field-distribution-data="projectFieldDistributionData"
      :stage-completion-data="stageCompletionData"
      :project-distribution-data="projectDistributionData"
      :project-stage-data="projectStageData"
      :project-delivery-cycle-data="projectDeliveryCycleData"
      :project-list="projectList"
      :query-data="queryData"
    />
    <FCard :title="'个人项目统计'">
      <div class="flex space-between mb16">
        <div class="type-select">
          <span :class="{ active: statisticsType === 'creator' }" @click="changeTypeFn('creator')">流程发起人</span>
          <span :class="{ active: statisticsType === 'handler' }" @click="changeTypeFn('handler')">节点处理人</span>
        </div>
        <FButton size="small" @click="exportData">
          <template #icon><i class="iconfont icontubiao_daochu" /></template>
          导出</FButton
        >
      </div>
      <FTable
        class="table-warp"
        :columns="columns"
        :loading="loading"
        :data-source="dataList"
        :row-key="(data:any) => data.id"
        :row-selection="rowSelection"
        :sticky="{ offsetHeader: 0 }"
        :scroll="{ x: 'min-content' }"
        :pagination="{
          total: paging.total,
          current: paging.currPage,
          pageSize: paging.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
        }"
        @change="onPaginationChangeFn"
        :key="updateKey"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'number'">
            {{ index + 1 }}
          </template>
          <template v-if="column.dataIndex === 'projectCompletionRate'"> {{ record.projectCompletionRate }}% </template>
          <template v-if="column.dataIndex === 'projectOnTimeRate'"> {{ record.projectOnTimeRate }}% </template>
          <template v-if="column.dataIndex === 'deliverySatisfaction'">
            {{ record.deliverySatisfaction || 0 }}%
          </template>
        </template>
      </FTable>
    </FCard>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, reactive, nextTick, provide } from 'vue'
import { useRoute } from 'vue-router'
import { message } from '@fs/smart-design'
import { cache } from '@/utils'
import SearchContent from './components/SearchContent/index.vue'
import ContentBoard from './components/ContentBoard/index.vue'
import {
  getBoardData,
  getProcessDistributionData,
  getProjectTrendData,
  getProjectDistributionData,
  getStageCompletionData,
  getProjectStageData,
  getProjectFieldDistributionData,
  getProjectDeliveryCycleData,
  getPersonProjectStatisticsList,
  exportPersonalProjectStatistics,
} from '@/api'
import { GetTagAndNode } from '@/api'
import { IProcessClassType } from '@/types/handle'
const route = useRoute()
const currProcessTypeId = ref()
const routerName = ref()
const processTypeData = ref<IProcessClassType[]>([])
const currProcessTypeData = computed<any>(
  () => processTypeData.value.find(item => item.id == +currProcessTypeId.value) ?? {}
)
const processConfigIdList = computed<any>(() => {
  if (currProcessTypeId.value != 'all' && !currProcessTypeData.value.id)
    return processTypeData.value.map(item => item.id)
  return undefined
})
const loading = ref(false)
const paging = reactive<any>({ currPage: 1, pageSize: 10 })
const queryData = ref<any>({})
const dataList = ref<any[]>([])
const basicStatistics = ref<any>({})
const projectStatistics = ref<any>({})
const trendData = ref<any>({})
const processDistributionData = ref<any>({})
const stageCompletionData = ref<any>({})
const projectStageData = ref<any>({})
const projectDistributionData = ref<any>({})
const projectDeliveryCycleData = ref<any>([])
const projectFieldDistributionData = ref<any>([])
const projectList = ref([])
const statisticsType = ref('creator')

const selectedKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[]) => (selectedKeys.value = selectedRowKeys),
}))
const columns = ref([
  { title: '序号', dataIndex: 'number', key: 'number', width: 80 },
  { title: '人员', dataIndex: 'personName', key: 'personName' },
  { title: '已完成数量', dataIndex: 'completedProjectCount', key: 'completedProjectCount' },
  { title: '项目完成率', dataIndex: 'projectCompletionRate', key: 'projectCompletionRate' },
  { title: '交付满意度', dataIndex: 'deliverySatisfaction', key: 'deliverySatisfaction' },
  { title: '进行中数量', dataIndex: 'inProgressProjectCount', key: 'inProgressProjectCount' },
  { title: '准时完成率', dataIndex: 'projectOnTimeRate', key: 'projectOnTimeRate' },
  { title: '已终止数量', dataIndex: 'terminatedProjectCount', key: 'terminatedProjectCount' },
  { title: '参与任务总数量', dataIndex: 'participatedTaskCount', key: 'participatedTaskCount' },
  { title: '已完成任务总数量', dataIndex: 'completedTaskCount', key: 'completedTaskCount' },
  { title: '延期完成任务数量', dataIndex: 'overdueCompletedTaskCount', key: 'overdueCompletedTaskCount' },
])
const updateKey = ref<number>(Date.now() + Math.random())
const countUpdateKey = ref<number>(Date.now() + Math.random())
const hoursCountUpdateKey = ref<number>(Date.now() + Math.random())
const trendUpdateKey = ref<number>(Date.now() + Math.random())
const basicUpdateKey = ref<number>(Date.now() + Math.random())

const increaseTrendUpdateKey = (data: any) => {
  trendUpdateKey.value = data
}
// 查询列表
const queryDataList = async () => {
  try {
    loading.value = true
    const data = { ...queryData.value, statisticsType: statisticsType.value }
    cache.set(
      routerName?.value,
      JSON.stringify({
        ...(data?.cacheValue ?? {}),
        currPage: paging.currPage,
        pageSize: paging.pageSize,
      })
    )
    delete data.cacheValue
    const res = await getPersonProjectStatisticsList({
      ...data,
      ...paging,
      processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
      hasProcess:
        (processConfigIdList.value && processConfigIdList.value.length > 0) || currProcessTypeId.value == 'all' ? 1 : 0,
    })
    dataList.value = res?.data?.list || []
    paging.total = res?.data?.totalCount || 0
  } finally {
    selectedKeys.value = []
    loading.value = false
    // updateKey.value = Date.now() + Math.random()
  }
}

const onPaginationChangeFn = (pagination: any) => {
  paging.currPage = pagination.current
  paging.pageSize = pagination.pageSize
  queryDataList()
}
const changeTypeFn = (type: string) => {
  statisticsType.value = type
  queryDataList()
}
//查询基础数据
const onGetBasicData = async () => {
  try {
    const data = {
      ...queryData.value,
      processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
      hasProcess:
        (processConfigIdList.value && processConfigIdList.value.length > 0) || currProcessTypeId.value == 'all' ? 1 : 0,
    }
    delete data.cacheValue
    const res = await getBoardData(data)
    basicStatistics.value = res?.data?.basicStatistics
    const hasCompleteArr = [
      { name: '已完成', value: res.data.projectStatistics.completedCount },
      { name: '进行中', value: res.data.projectStatistics.inProgressCount },
      { name: '已终止', value: res.data.projectStatistics.terminatedCount },
    ]
    const noCompleteArr = [
      { name: '逾期完成', value: res.data.projectStatistics.overdueCompletedCount },
      { name: '已完成', value: res.data.projectStatistics.onTimeCompletedCount },
    ]
    res.data.projectStatistics.hasCompleteArr = hasCompleteArr
    res.data.projectStatistics.noCompleteArr = noCompleteArr

    projectStatistics.value = res?.data?.projectStatistics
  } finally {
    basicUpdateKey.value = Date.now() + Math.random()
  }
}
//流程分布
const onGetDistributionData = async () => {
  const data = {
    ...queryData.value,
    processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
    hasProcess:
      (processConfigIdList.value && processConfigIdList.value.length > 0) || currProcessTypeId.value == 'all' ? 1 : 0,
  }
  delete data.cacheValue
  const res = await getProcessDistributionData(data)
  processDistributionData.value = res?.data || {}
  if (queryData.value && queryData.value.processConfigId) {
    fetchBothProjectData()
    onGetStageCompletionData()
    onGetProjectStageData()
  }
}
//获取项目数量趋势数据
const onGetProjectTrendData = async () => {
  try {
    const data = {
      ...queryData.value,
      processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
      hasProcess:
        (processConfigIdList.value && processConfigIdList.value.length > 0) || currProcessTypeId.value == 'all' ? 1 : 0,
    }
    delete data.cacheValue
    const res = await getProjectTrendData(data)
    trendData.value = res?.data || {}
  } finally {
    trendUpdateKey.value = Date.now() + Math.random()
  }
}

//项目分布情况2
const onGetProjectDistributionData = async () => {
  try {
    const data = {
      ...queryData.value,
      processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
      hasProcess:
        (processConfigIdList.value && processConfigIdList.value.length > 0) || currProcessTypeId.value == 'all' ? 1 : 0,
    }
    delete data.cacheValue
    const res = await getProjectDistributionData(data)
    projectDistributionData.value = res?.data || {}
  } finally {
    hoursCountUpdateKey.value = Date.now() + Math.random()
  }
}

//阶段完成情况
const onGetStageCompletionData = async () => {
  try {
    const data = {
      ...queryData.value,
      processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
      hasProcess:
        (processConfigIdList.value && processConfigIdList.value.length > 0) || currProcessTypeId.value == 'all' ? 1 : 0,
    }
    delete data.cacheValue
    const res = await getStageCompletionData(data)
    stageCompletionData.value = res?.data || {}
  } finally {
    countUpdateKey.value = Date.now() + Math.random()
  }
}

//项目所属阶段
const onGetProjectStageData = async () => {
  const data = {
    ...queryData.value,
    processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
    hasProcess:
      (processConfigIdList.value && processConfigIdList.value.length > 0) || currProcessTypeId.value == 'all' ? 1 : 0,
  }
  delete data.cacheValue
  const res = await getProjectStageData(data)
  projectStageData.value = res?.data || {}
}

// 合并数据的函数
function mergeDataByFieldName(deliveryData, distributionData) {
  const mergedData = []

  // 创建分布数据的映射，便于查找
  const distributionMap = {}
  distributionData.value.forEach(item => {
    distributionMap[item.fieldName] = item
  })

  // 合并数据
  deliveryData.value.forEach(item => {
    const fieldName = item.fieldName
    const distribution = distributionMap[fieldName]

    if (distribution) {
      mergedData.push({
        fieldName: fieldName,
        fieldKey: item.fieldKey,
        deliveryCycleData: item.deliveryCycleData,
        valueDistributions: distribution.valueDistributions,
      })
    }
  })

  return mergedData
}
const fetchBothProjectData = async () => {
  const data = {
    ...queryData.value,
    processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
    hasProcess:
      (processConfigIdList.value && processConfigIdList.value.length > 0) || currProcessTypeId.value == 'all' ? 1 : 0,
  }
  delete data.cacheValue

  try {
    const [distributionRes, deliveryCycleRes] = await Promise.all([
      getProjectFieldDistributionData(data),
      getProjectDeliveryCycleData(data),
    ])
    projectFieldDistributionData.value = distributionRes?.data?.fieldDistributions || []
    projectDeliveryCycleData.value = deliveryCycleRes?.data?.fieldGroups || []
    projectList.value = mergeDataByFieldName(projectDeliveryCycleData, projectFieldDistributionData) || []
  } catch (error) {
    console.error('获取项目数据失败:', error)
    projectFieldDistributionData.value = {}
    projectDeliveryCycleData.value = {}
  }
}

// 查询列表
const onGetSearchData = async (data: any) => {
  queryData.value = data
  paging.currPage = data?.cacheValue?.currPage || 1
  paging.pageSize = data?.cacheValue?.pageSize || 10

  onGetBasicData()
  onGetDistributionData()
  onGetProjectTrendData()
  onGetProjectDistributionData()
  await queryDataList()
}

const exportData = async () => {
  if (!dataList.value.length) {
    message.warning('暂无数据可导出！')
    return
  }

  const data = { ...queryData.value, statisticsType: statisticsType.value }
  delete data.cacheValue
  const res = await exportPersonalProjectStatistics(data)
  const link = document.createElement('a')
  link.href = URL.createObjectURL(new Blob([res.data]))
  link.download =
    (res?.headers?.['content-disposition']?.split('filename=')?.[1] &&
      decodeURI(res?.headers?.['content-disposition']?.split('filename=')?.[1])) ||
    'modify_export.xlsx'
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  message.success('数据导出成功！')
}

provide('processTypeData', processTypeData)
provide('routerName', routerName)
provide('currProcessTypeId', currProcessTypeId)
provide('currProcessTypeData', currProcessTypeData)
provide('processConfigIdList', processConfigIdList)

watch(
  () => queryData.value,
  val => {
    onGetSearchData(val)
  },
  { deep: true }
)

const getProcessTypes = async () => {
  try {
    const res = await GetTagAndNode()
    processTypeData.value = res.data
  } catch (error) {
    throw new Error('流程类型请求失败')
  }
}
const handleProcessType = async () => {
  await nextTick()
  if (processConfigIdList.value) {
    processTypeData.value = processTypeData.value.filter(item => item?.groupCode == currProcessTypeId.value)
  }
}
watch(
  () => route.params.type,
  async type => {
    !processTypeData?.value?.length && (await getProcessTypes())
    handleProcessType()
    currProcessTypeId.value = type
    routerName.value = route.name
    const cachData = (cache.get(routerName?.value) && JSON.parse(cache.get(routerName?.value) as string)) || {}
    // const currentCachData = cachData?.[currProcessTypeId?.value] || {}
    // tabValue.value = currentCachData?.type ?? 1
    // pageData.value.pageNum = currentCachData?.pageNum ?? 1
    // pageData.value.pageSize = currentCachData?.pageSize ?? 10
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.modify-board-container {
  .none-container-padding {
    margin-top: -24px;
    margin-left: -4px;
    width: calc(100% + 8px);
  }
  .card-content-shadow {
    background: #ffffff;
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .code-link {
    cursor: pointer;
    color: #378eef;
  }
  .mr6 {
    margin-right: 6px;
  }
  .mr4 {
    margin-right: 4px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .error-color {
    color: #f04141;
  }
  .sucess-color {
    color: #2fcc83;
  }
  .empty-content {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .count-info-content {
    line-height: 18px;
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
  :deep(.fs-table-tbody > tr > td) {
    &.fs-table-cell-row-hover {
      background-color: #f1f4f8;
    }
  }
  :deep(.fs-table-column-sorters) {
    justify-content: flex-start;
    .fs-table-column-title {
      flex: 0;
      white-space: nowrap;
    }
  }
}

.type-select {
  display: flex;
  align-items: center;
  background: #fff;

  height: 32px;
  span {
    display: inline-block;
    border-radius: 4px;
    color: #999999;
    padding: 0 16px;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
    cursor: pointer;
    border: 1px solid #dddddd;
    &.active {
      background: #378eef;
      // box-shadow: 0px 2px 4px 0px rgba(17, 24, 39, 0.04);
      color: #fff;
      border: 1px solid #378eef;
    }
    &:first-child {
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }
    &:last-child {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
    }
  }
}
:deep(.fs-card-bordered) {
  border: none;
}
</style>

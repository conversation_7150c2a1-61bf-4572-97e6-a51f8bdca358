<template>
  <div class="forcast-time">
    <div class="forcast-time-view">
      <p class="__time-data">
        {{ (isSuccess && i18n.t('实际完成时间')) || i18n.t('预计完成时间') }}：
        <FDatePicker v-if="editFlag && isEdit" :value="forcastTime" @change="handleChange" />
        <span v-else :class="[{ '__pointer-cursor': isEdit }]" @click="isEdit && (editFlag = true)">
          {{ milepostTime }}
        </span>
      </p>
      <Tag class="ml4" v-if="milepostDay" :type="(milepostStatus[0] as any)" :text="(milepostDay as string)" />
      <Time class="ml4" v-else :time="(forcastTime as Dayjs)" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, Ref } from 'vue'
import { FModal } from '@fs/smart-design'
import { IProcess } from '@/types/handle'
import { transformDate, useI18n } from '@/utils'
import Tag from '../Tag/index.vue'
import Time from '../Time/index.vue'
import dayjs, { Dayjs } from 'dayjs'
import { updateMilepostTime } from '@/api'

const i18n = useI18n()
const processId = inject<number>('processId') // 流程id
const currtMilepost = inject<Ref<IProcess>>('currtMilepost') // 当前里程碑信息
const setCurrtMilepost = inject('setCurrtMilepost') as (data: IProcess) => void // 设置当前里程碑信息
const getProcessInfo = inject('getProcessInfo') as (callback: any) => void
const editFlag = ref(false)

const isEdit = computed(() => [0, 1, 2].includes(currtMilepost!.value.status) && currtMilepost!.value.milepostRole == 1)
const isSuccess = computed(() => [3, 4, 5].includes(currtMilepost!.value.status))
const forcastTime = computed(() => currtMilepost?.value?.forcastTime && dayjs(currtMilepost?.value?.forcastTime))

const milepostStatus = computed(() => {
  if (!currtMilepost?.value) return ['default', i18n.t('未开始')]
  const milepost = currtMilepost.value
  const status = milepost.status
  if ([2].includes(status) && milepost.overdueDuration) return ['danger', i18n.t('已延期')]
  if ([2].includes(status)) return ['warning', i18n.t('进行中')]
  if ([3, 4, 5].includes(status)) return ['success', i18n.t('已完成')]
  return ['default', i18n.t('未开始')]
})

const milepostDay = computed(() => {
  if (!currtMilepost?.value) return ''
  const milepost = currtMilepost.value
  const status = milepost.status
  if ([2].includes(status) && milepost.remainderDuration && milepost.invalid == 1) return ''
  if ([2].includes(status) && milepost.overdueDuration && milepost.invalid == 1)
    return `${i18n.t('延期')}${milepost.overdueDuration ?? 0}${i18n.t('天')}`
  if ([3, 4, 5].includes(status)) return `${i18n.t('耗时')}${milepost.realityDuration ?? 0}${i18n.t('天')}`
  return ''
})

// 里程碑时间
const milepostTime = computed(() => {
  if (!currtMilepost?.value) return ''
  const milepost = currtMilepost.value
  const status = milepost.status
  const timeType = `MM月DD日`
  if ([2].includes(status)) return `${transformDate(milepost.forcastTime, timeType)}`
  if ([3, 4, 5].includes(status)) return `${transformDate(milepost.completeTime, timeType)}`
  return `${transformDate(milepost.forcastTime, timeType)}`
})

// 时间变更
const handleChange = (value: Dayjs) => {
  const time = transformDate(value, 'YYYY-MM-DD')
  FModal.confirm({
    title: i18n.t('是否确认修改节点预计完成时间？'),
    content: i18n.t('该节点预计完成时间将会变更为 ${time} ，请谨慎操作！', { time }),
    okText: i18n.t('确认'),
    cancelText: i18n.t('取消'),
    onOk: async () => {
      editFlag.value = false
      await updateMilepostTime({
        instanceId: processId as number,
        milepostId: currtMilepost!.value.id,
        forcastTime: value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : null,
      })
      getProcessInfo((processInfo: IProcess[]) => {
        const currMilepost = processInfo.find((item: IProcess) => item.id === currtMilepost!.value.id)
        currMilepost && setCurrtMilepost(currMilepost)
      })
    },
  })
}
</script>

<style scoped lang="scss">
.ml4 {
  margin-left: 4px;
}
.forcast-time {
  display: inline-block;
  margin-left: 8px;

  .forcast-time-view {
    display: flex;
    align-items: center;

    .__time-data {
      margin: 0;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 24px;

      .__pointer-cursor {
        cursor: pointer;
        &:hover {
          color: #1890ff;
        }
      }

      > span {
        color: #333;
      }
    }
  }
}
</style>

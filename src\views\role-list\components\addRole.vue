<template>
  <div class="create-creative role" ref="createCreative">
    <FModal
      v-model:visible="dialogVisible"
      :title="title"
      :width="400"
      @cancel="close"
      :get-container="() => createCreative"
    >
      <div>
        <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical" v-bind="layout">
          <FFormItem :label="i18n.t('角色编码')" name="roleCode">
            <FInput v-model:value="formState.roleCode" :placeholder="i18n.t('请输入角色编码')" />
          </FFormItem>
          <FFormItem :label="i18n.t('角色名称')" name="roleName">
            <FInput v-model:value="formState.roleName" :placeholder="i18n.t('请输入角色名称')" />
          </FFormItem>
          <FFormItem :label="i18n.t('角色类型')" name="type">
            <FSelect
              :placeholder="i18n.t('请选择')"
              v-model:value="formState.type"
              :options="valueTypeList"
              allow-clear
            />
          </FFormItem>
          <FFormItem :label="i18n.t('外部同步类型')" name="isSyn">
            <FSelect
              :placeholder="i18n.t('请选择')"
              v-model:value="formState.isSyn"
              mode="multiple"
              :options="synTypeList"
              allow-clear
            />
          </FFormItem>
          <FFormItem :label="i18n.t('接口地址')" name="url">
            <FInput v-model:value="formState.url" :placeholder="i18n.t('请输入接口地址')" />
          </FFormItem>
          <FFormItem :label="i18n.t('角色说明')" name="roleDesc" class="roleDesc">
            <FTextarea v-model:value="formState.roleDesc" :placeholder="i18n.t('请输入备注')" />
          </FFormItem>
        </FForm>
      </div>
      <template #footer>
        <FConfigProvider :auto-insert-space-in-button="false">
          <FButton key="back" @click="close">{{ i18n.t('取消') }}</FButton>
          <FButton key="submit" type="primary" @click="handleOk(formRef)">{{ i18n.t('确定') }}</FButton>
        </FConfigProvider>
      </template>
    </FModal>
  </div>
</template>
<script setup lang="ts">
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { ref, watch, unref, computed } from 'vue'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { IAddRole } from '@/types/role'
import { deepClone, useI18n } from '@/utils'

const i18n = useI18n()

const formRef = ref<FormInstance>()
const createCreative = ref<HTMLElement>()

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  presentRole: {
    type: Object,
    default: () => ({}),
  },
  show: {
    type: Boolean,
  },
  editTepy: {
    type: Number,
    default: 1,
  },
})
const emit = defineEmits(['submit', 'popupClose'])
const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
}
const synTypeList = computed<any[]>(() => [
  { value: '1', label: i18n.t('同步工时') },
  { value: '2', label: i18n.t('同步mom') },
  { value: '3', label: i18n.t('同步关联流程') },
  { value: '4', label: i18n.t('同步SAP') },
])
const valueTypeList = computed<any[]>(() => [
  { value: 1, label: i18n.t('铁六角') },
  { value: 2, label: i18n.t('轮询') },
  { value: 3, label: i18n.t('表达式') },
  { value: 4, label: i18n.t('抄送') },
  { value: 5, label: i18n.t('接口类型') },
  { value: 6, label: i18n.t('抄送包含创建人') },
  // { value: 5, label: 'mom同步角色' },
])
const formState = ref<IAddRole>({
  roleCode: '',
  roleDesc: '',
  roleName: '',
  url: '',
  type: undefined,
  isSyn: undefined,
})
const dialogVisible = ref(false)
const rules: Record<string, Rule[]> = {
  roleName: [{ required: true, message: i18n.t('请输入角色名称') }],
  roleCode: [{ required: true, message: i18n.t('请输入角色编码') }],
  type: [{ required: true, message: i18n.t('请选择角色类型') }],
}

const close = () => {
  emit('popupClose')
  formRef.value?.resetFields()
}
const handleOk = async (formRef: FormInstance | undefined) => {
  if (!formRef) {
    return
  }
  await formRef
    .validate()
    .then(() => {
      const params = deepClone(unref(formState))
      ;(params.isSyn && (params.isSyn = params.isSyn.join(','))) || (params.isSyn = '0')
      emit('submit', params)
    })
    .catch((e: any) => {
      e
    })
}
watch([() => props.show, () => props.presentRole, () => props.editTepy], newValue => {
  dialogVisible.value = newValue[0]
  if (!newValue[1]) return

  if (newValue[1].roleName && newValue[2] === 2) {
    formState.value.roleCode = newValue[1]?.roleCode ?? ''
    formState.value.roleDesc = newValue[1]?.roleDesc ?? ''
    formState.value.roleName = newValue[1]?.roleName ?? ''
    formState.value.url = newValue[1]?.url ?? ''
    formState.value.type = newValue[1]?.type ?? ''
    formState.value.isSyn =
      (newValue[1]?.isSyn && newValue[1]?.isSyn !== '0' && newValue[1]?.isSyn.split(',')) || undefined
  } else {
    formState.value.roleCode = ''
    formState.value.roleDesc = ''
    formState.value.roleName = ''
    formState.value.url = ''
    formState.value.type = undefined
    formState.value.isSyn = undefined
  }
})
</script>
<style scoped lang="scss">
.role {
  :deep(.fs-modal-body) {
    .fs-form-item .fs-btn {
      padding: 0 24px !important;
    }
    .fs-input {
      padding-left: 8px !important;
    }
  }
  .roleDesc {
    :deep(.fs-form-item-control-input-content) {
      height: auto !important;
    }
  }
}
</style>

<template>
  <div class="fei-su-main">
    <div class="fei-su-main-box">
      <div class="fei-su-main-left">
        <div class="fei-su-left-title">{{ i18n.t('流程类型') }}</div>
        <div class="process-type-box">
          <div
            v-for="(item, index) in processClassifyList"
            :key="index"
            :class="['item', { selectd: item.id === modelValue }]"
            :title="item.processName"
            @click="onSelectHandel(item)"
          >
            {{ item.processName }}
          </div>
        </div>
      </div>
      <div class="fei-su-main-right">
        <div class="select-options">
          <FForm layout="inline" class="select-btn">
            <div class="btn_div_relative">
              <span class="btn_span_absolute">{{ i18n.t('快速搜索') }}</span>
              <FFormItem class="width240">
                <FInput :placeholder="i18n.t('请输入')" v-model:value="searchKey" @press-enter="searchProcess">
                  <template #suffix>
                    <i class="cursor iconfont colord8d8d8 fontSize12" @click="searchProcess">&#xe70e;</i>
                  </template>
                </FInput>
              </FFormItem>
            </div>
          </FForm>
        </div>
        <div class="fei-su-card">
          <div class="card-btn">
            <div class="fei-su-title">{{ i18n.t('流程标签') }}</div>
            <div class="_button-gourp">
              <FButton type="primary" class="marginR12" @click="executeResultFn">
                <i class="iconfont iconyijiantongbu marginR5"></i>{{ i18n.t('执行表达式') }}
              </FButton>
              <FButton type="primary" class="marginR12" @click="handleCopyClick">
                <i class="iconfont marginR5">&#xe7f9;</i>{{ i18n.t('拷贝') }}
              </FButton>
              <FButton type="primary" @click="addProcess">
                <i class="icon-tubiao_tianjia1 iconfont marginR5"></i>{{ i18n.t('新增') }}
              </FButton>
            </div>
          </div>
          <FTable
            :data-source="dataList"
            :columns="columns"
            :pagination="false"
            :loading="loading"
            row-key="id"
            :scroll="{ x: '100%' }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'sort'">{{ index + 1 }}</template>
              <template v-if="column.dataIndex === 'status'">{{
                record.status === 1 ? i18n.t('启用') : i18n.t('禁用')
              }}</template>
              <template v-if="column.dataIndex === 'url'">
                <div>{{ record.url ?? '---' }}</div>
              </template>
              <template v-if="column.dataIndex === 'roleCondition'">
                <div>
                  {{ record.roleCondition ?? '--' }}
                </div>
              </template>
              <template v-if="column.dataIndex === 'isComplete'">
                {{ record.isComplete === 1 ? i18n.t('是') : i18n.t('否') }}
              </template>
              <template v-if="column.dataIndex === 'isSend'">
                {{ record.isSend === 1 ? i18n.t('发送') : i18n.t('不发送') }}
              </template>
              <template v-if="column.dataIndex === 'action'">
                <span
                  class="iconfont-hover iconfont icontubiao_xietongbianji cursor color4677C7 marginR5"
                  :title="i18n.t('编辑')"
                  @click="handleUpdate(record)"
                ></span>
                <FConfigProvider :auto-insert-space-in-button="false">
                  <FPopconfirm
                    :ok-text="i18n.t('确定')"
                    :cancel-text="i18n.t('取消')"
                    @confirm="delhandle(record.id)"
                    placement="topRight"
                    arrow-point-at-center
                    :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode?.parentNode?.parentNode?.parentNode}"
                  >
                    <template #title>
                      <p class="color333 fontWeight600 fontSize14 marginB5">{{ i18n.t('确定删除吗？') }}</p>
                      <div class="color666 fontSize12">{{ i18n.t('删除后不可恢复,请谨慎操作') }}</div>
                    </template>

                    <span class="iconfont icon iconfont-hover cursor marginR5 color4677C7" :title="i18n.t('删除')"
                      >&#xe7a4;</span
                    >
                  </FPopconfirm>
                  <FPopconfirm
                    :ok-text="i18n.t('确定')"
                    :cancel-text="i18n.t('取消')"
                    @confirm="confirm(record.id)"
                    placement="topRight"
                    arrow-point-at-center
                    :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode?.parentNode?.parentNode?.parentNode}"
                  >
                    <template #title>
                      <p v-if="record.status === 1" class="color333 fontSize14 marginB5">
                        {{ i18n.t('确定禁用吗？') }}
                      </p>
                      <p v-else class="color333 fontSize14 marginB5">{{ i18n.t('确定启用吗？') }}</p>
                    </template>
                    <span class="iconfont icon iconfont-hover cursor marginR5 color4677C7" :title="i18n.t('启用/禁用')">
                      &#xe7f5;
                    </span>
                  </FPopconfirm>
                </FConfigProvider>
              </template>
            </template>
            <template #emptyText>
              <img :src="noImgUrl" />
              <p class="colorBBB fontSize12 fei-su-tip">{{ i18n.t('暂无数据，赶快去添加吧~~') }}</p>
            </template>
          </FTable>
          <div class="fei-su-pagination">
            <FPagination
              v-model:current="paging.page"
              @change="showSizeChange"
              v-model:pageSize="paging.pageSize"
              :total="paging.total"
              show-size-changer
              :show-total="() => `${i18n.t('共')} ${paging.total} ${i18n.t('条')}`"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <AddProcess
    v-model:visible="visible"
    :form-state="formState"
    @submit="submitProcess"
    :type="type"
    :title="title"
    :classify-list="processClassifyList"
    :classify-id="modelValue"
  />
  <CopyModel v-model="copyModelFlag" :milepost-data="milepostMap[modelValue] ?? []" @submit="handleCopyConfigSubmit" />
  <ExecuteResultModel v-model:visible="executeResultFlag" />
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import noImgUrl from '@/assets/images/no-data.png'
import AddProcess from './components/AddProcess.vue'
import CopyModel from './components/CopyModel/index.vue'
import ExecuteResultModel from './components/ExecuteResultModel/index.vue'
import { IPage } from '@/types/common'
import {
  ConfigListItem,
  ProcessClassifyItem,
  ConfigListParams,
  UpdataManagerConfigParams,
  ProcessNodeItem,
} from '@/types/processLabelModel'
import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import {
  GetProcess,
  GetConfigList,
  DelManager,
  UpdateStatusManager,
  UpdateProcessLabel,
  CreateProcessLabel,
  copyConfig,
  GetProcessNode,
} from '@/api/label'
import { messageInstance as message } from '@fs/smart-design'
import lodash from 'lodash'
import { useI18n } from '@/utils'

const i18n = useI18n()
const modelValue = ref<any>()
// const addProcessdRef = ref()
const title = ref<string>('')
const type = ref<string>('')
const searchKey = ref<string>('')
const formState = ref<any>()
const paging = ref<IPage>({ page: 1, pageSize: 10, total: 0 })
const loading = ref<boolean>(false)
const dataList = ref<ConfigListItem[]>([])
const processClassifyList = ref<Array<ProcessClassifyItem>>([])
const columns = computed<TableColumnsType>(() => [
  { title: i18n.t('序号'), dataIndex: 'sort', width: 80 },
  { title: i18n.t('里程碑'), dataIndex: 'milepostNodeName', width: 160, ellipsis: true },
  { title: i18n.t('默认任务表单'), dataIndex: 'taskDefaultFormName', width: 160, ellipsis: true },
  { title: i18n.t('移动任务表单'), dataIndex: 'taskMobileFormName', width: 160, ellipsis: true },
  { title: i18n.t('人员表达式'), dataIndex: 'userCondition', width: 160, ellipsis: true },
  { title: i18n.t('角色表达式'), dataIndex: 'roleCondition', width: 160, ellipsis: true },
  { title: i18n.t('抄送人角色表达式'), dataIndex: 'makeCondition', width: 160, ellipsis: true },
  { title: i18n.t('工时表达式'), dataIndex: 'timeCondition', width: 160, ellipsis: true },
  { title: i18n.t('节点定制化组件'), dataIndex: 'formComponent', width: 160, ellipsis: true },
  { title: i18n.t('状态'), dataIndex: 'status', width: 80 },
  { title: i18n.t('自动完成'), dataIndex: 'isComplete', width: 80 },
  { title: i18n.t('发送消息通知'), dataIndex: 'isSend', width: 120 },
  { title: i18n.t('操作'), dataIndex: 'action', fixed: 'right', width: 120 },
])
const visible = ref<boolean>(false)
const executeResultFlag = ref<boolean>(false)
const copyModelFlag = ref<boolean>(false)
const milepostMap = ref<Record<string, ProcessNodeItem[]>>({})

onMounted(() => {
  fetchData()
})

const onSelectHandel = (item: any) => {
  loading.value = true
  modelValue.value = item.id
  const parmas = {
    processConfigId: modelValue.value,
    pageNum: 1,
    pageSize: paging.value.pageSize,
  }
  fetchList(parmas)
  getMilepost(modelValue.value)
}
// 获取流程的里程碑列表
const getMilepost = async (processConfigId: number) => {
  if (milepostMap.value[processConfigId]) return
  const { data = [] } = await GetProcessNode({ processConfigId })
  milepostMap.value[processConfigId] = data
}
const showSizeChange = (current: number, pageSize: number) => {
  const parmas = {
    processConfigId: modelValue.value,
    pageNum: paging.value.page,
    pageSize: pageSize,
  }
  fetchList(parmas)
}
const handleUpdate = (record: any) => {
  // addProcessdRef.value.editHandle('edit', record)
  title.value = i18n.t('编辑')
  type.value = 'edit'
  visible.value = true
  formState.value = { ...record }
}

const executeResultFn = () => {
  executeResultFlag.value = true
}

const handleCopyClick = () => {
  copyModelFlag.value = true
}
const handleCopyConfigSubmit = async (data: { sourceMilepostNodeId: number; targetMilepostNodeIds: number[] }) => {
  const { code, msg } = await copyConfig({ processConfigId: modelValue.value, ...data })
  if (code === 200) {
    copyModelFlag.value = false
    onSelectHandel({ id: modelValue.value })
    message.success(msg)
  }
}
const addProcess = () => {
  title.value = i18n.t('新增')
  type.value = 'add'
  visible.value = true
}
// const cancelHandle = () => {
//   dialogVisible.value = false
// }
const submitProcess = async (obj: UpdataManagerConfigParams) => {
  const query = lodash.cloneDeep(obj)
  const { id } = query
  // data.timeConsuming = Number(data.timeConsuming)
  if (id) {
    const res = await UpdateProcessLabel(query)
    if (res.code === 200) {
      visible.value = false
      const parmas = {
        processConfigId: modelValue.value,
        pageNum: paging.value.page,
        pageSize: paging.value.pageSize,
      }
      fetchList(parmas)
    } else {
      message.warn(res.msg)
    }
  } else {
    let { id, ...dataNew } = query
    const res = await CreateProcessLabel(dataNew)
    if (res.code === 200) {
      visible.value = false
      const parmas = {
        processConfigId: modelValue.value,
        pageNum: paging.value.page,
        pageSize: paging.value.pageSize,
      }
      fetchList(parmas)
    } else {
      message.warn(res.msg)
    }
  }
}
const fetchData = async () => {
  const params = {
    pageNum: 1,
    pageSize: 999,
    source: 1,
  }
  const res = await GetProcess(params)
  if (res.code === 200) {
    processClassifyList.value = res.data.list
    modelValue.value = res.data.list[0].id
    const parmas = {
      processConfigId: modelValue.value,
      pageNum: paging.value.page,
      pageSize: paging.value.pageSize,
    }
    fetchList(parmas)
    getMilepost(modelValue.value)
  }
}
const fetchList = async (params: ConfigListParams) => {
  loading.value = true
  const res = await GetConfigList(params)
  if (res.code === 200) {
    dataList.value = res.data.list
    paging.value.total = res.data?.totalCount ?? 0
    loading.value = false
  } else {
    message.warning(res.msg)
  }
}
const confirm = async (id: number) => {
  const params = {
    id: id,
  }
  const res = await UpdateStatusManager(params)
  if (res.code === 200) {
    const parmas = {
      processConfigId: modelValue.value,
      pageNum: paging.value.page,
      pageSize: paging.value.pageSize,
    }
    fetchList(parmas)
  }
}
const delhandle = async (id: number) => {
  const params = {
    id: id,
  }
  const res = await DelManager(params)
  if (res.code === 200) {
    const parmas = {
      processConfigId: modelValue.value,
      pageNum: paging.value.page,
      pageSize: paging.value.pageSize,
    }
    fetchList(parmas)
  }
}
const searchProcess = () => {
  const parmas = {
    processConfigId: modelValue.value,
    pageNum: 1,
    pageSize: paging.value.pageSize,
    queryInput: searchKey.value,
  }
  fetchList(parmas)
}
watch([() => paging.value.page, () => paging.value.pageSize], (newValue, oldValue) => {
  // console.log(newValue, oldValue)
})
</script>

<style scoped lang="scss">
.fei-su-title {
  color: #333;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 0 !important;
}
.fei-su-pagination {
  display: flex;
  justify-content: flex-end;
  padding: 15px 0 25px;
  span {
    line-height: 32px;
    color: #bbb;
    margin-right: 10px;
  }
}
.fei-su-tip {
  margin-top: -30px;
}
.fei-su-main-box {
  display: flex;
  width: 100%;
  .fei-su-main-left {
    width: 260px;
    margin-right: 15px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
    .fei-su-left-title {
      padding: 16px 24px;
      font-size: 14px;
      border-bottom: 1px solid #eeeeee;
      font-weight: 500;
    }
    .process-type-box {
      margin: 24px 0;
      overflow-y: auto;
      height: calc(100vh - 180px);
      .item {
        height: 32px;
        line-height: 32px;
        cursor: pointer;
        font-size: 12px;
        padding: 0 24px 3px;
        box-sizing: border-box;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .selectd {
        color: #378eef;
        background: rgba(55, 142, 239, 0.08);
      }
    }
  }
  .fei-su-main-right {
    width: calc(100% - 260px);
    .select-options {
      background-color: #fff;
      padding: 24px;
    }
  }
}
.select-btn {
  justify-content: space-between;
}
.card-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.marginR12 {
  margin-right: 12px;
}
.fei-su-tree {
  padding: 14px 24px;
}

:deep(.fs-input-affix-wrapper) {
  padding-left: 8px !important;
}

.btn_span_absolute {
  padding: 0 2px;
}

:deep(.fs-table-tbody) {
  > tr:hover:not(.fs-table-expanded-row) > td,
  .fs-table-row-hover,
  .fs-table-row-hover > td {
    background: #f1f4f8 !important;
  }
}
.fs-table-fixed {
  .fs-table-row-hover,
  .fs-table-row-hover > td {
    background: #f1f4f8 !important;
  }
}
</style>

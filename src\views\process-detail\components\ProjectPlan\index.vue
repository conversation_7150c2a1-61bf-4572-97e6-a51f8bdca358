<template>
  <Plan v-if="invalid" :process-info="props.processInfo" :other="props.other" />
  <PartakeRole :process-info="props.processInfo" :other="props.other" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Plan from './components/Plan/index.vue'
import PartakeRole from './components/PartakeRole/index.vue'
import { IProcess } from '@/types/handle'
import { IProcessRoleAndUser } from '@/types/request'

interface IProps {
  processInfo: IProcess[]
  other: { processRoleInfo: IProcessRoleAndUser[] }
}
const props = defineProps<IProps>()
const emits = defineEmits(['operate'])
const invalid = computed(() => props.processInfo[0]?.invalid)
</script>

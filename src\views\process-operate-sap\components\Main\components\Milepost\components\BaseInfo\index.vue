<template>
  <Collapse :header="i18n.t('基本信息')" v-if="formKey">
    <div style="margin-left: 15px" v-if="concludeMsg">{{ concludeMsg }}</div>
    <!-- 表单渲染 -->
    <FormRender
      v-if="currtMilepost && formKey"
      ref="formRenderRef"
      :id="formKey"
      :type="TFFormType(currtMilepost)"
      :data="TFFormData(currtMilepost)"
    />
    <!-- 附件 -->
    <MilepostAppendix :milepost="currtMilepost" />
  </Collapse>
</template>

<script setup lang="ts">
import { Ref, computed, inject, markRaw, ref, watch, onMounted } from 'vue'
import { IProcess } from '@/types/handle'
import { useI18n } from '@/utils'
import { getConcludeMsg } from '@/api'

import FormRender from '@/components/FormRender/index.vue'
import Collapse from '../Collapse/index.vue'
import MilepostAppendix from '@/views/process-detail/components/HandleInfo/components/MilepostAppendix/index.vue'

const i18n = useI18n()
const formRenderRef = ref<any>()
const setFormRenderRef = inject('setFormRenderRef') as (ref: any) => void
const currtMilepost = inject<Ref<IProcess>>('currtMilepost') as Ref<IProcess> // 当前里程碑信息
const formKey = computed(() => currtMilepost?.value?.formKey || '') // 当前里程碑表单 key
const processId = inject<Ref<number>>('processId') // 流程 id
const concludeMsg = ref()

const TFFormType = (milepost: IProcess) => (milepost.status === 2 && milepost.milepostRole == 1 ? 'edit' : 'view')
const TFFormData = (milepost: IProcess) => {
  // 剔除一些基本用不到又可能会更新的字段，减少表单重新渲染的次数，表单重新渲染会造成页面抖动
  // eslint-disable-next-line prettier/prettier, @typescript-eslint/no-unused-vars
  const {
    completeTime,
    contentData,
    createdTime,
    creator,
    updatedTime,
    nodeStartTime,
    planTime,
    children,
    formData,
    ...data
  } = milepost
  return markRaw({ envData: data, envDefaultFormData: formData })
}

watch(
  () => formRenderRef.value,
  () => {
    setFormRenderRef(formRenderRef.value)
  }
)

const onGetConcludeMsgFn = async (data: any) => {
  const res = await getConcludeMsg(Number(processId.value))
  if (res.code !== 200) throw new Error(res.msg)
  concludeMsg.value = res?.data || ''
}

onMounted(() => {
  currtMilepost?.value?.status === 4 && requestIdleCallback(onGetConcludeMsgFn)
})
</script>

<template>
  <Milepost v-for="milepost in processInfo" :milepost="milepost" :key="milepost.id" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Milepost from './components/Milepost/index.vue'
import type { IProcess } from '@/types/handle'

interface IProps {
  processInfo: IProcess[]
  other: unknown
}

const props = defineProps<IProps>()
const emits = defineEmits(['operate'])
const processInfo = computed(() => props.processInfo.filter(item => ![0, 1, -99999].includes(item.status)))
</script>

<template>
  <FRow :gutter="[24, 0]">
    <FCol v-for="item of currentComponents" :key="item.fieldKey" :span="(item?.colSpan && item?.colSpan(item)) || 6">
      <FFormItem
        class="form-item-box"
        :class="[item?.isDetail ? 'form-item-detail-box' : '']"
        :name="(item.fromKey && [...item.fromKey]) || item.fieldKey"
        :rules="{
          required: !item?.isDetail && item.required,
          validator: (rule, value) => validatorFn(rule, value, item),
          trigger: item?.formItemRuleTrigger ?? 'change',
        }"
      >
        <template #label>
          <div style="display: flex; justify-content: space-between; align-items: center; width: 100%; color: #999999">
            <span class="nowrap" :class="[item?.isDetail ? 'color999' : 'color333']">{{ item.label }}</span>
            <!-- 增强的 tipText 支持 -->
            <div v-if="item?.tipText && !item?.isDetail" style="width: max-content;">
              <!-- 如果是字符串，使用原有的 MoreTextTips 组件 -->
              <MoreTextTips
                v-if="typeof item.tipText === 'string'"
                :line-clamp="1"
                bgColor="#000"
                textColor="#fff"
              >
                <span style="margin-left: 4px">
                  <i class="iconfont icontubiao_tishi_mian" style="margin-right: 2px; font-size: 14px" />
                  <span>{{ item.tipText }}</span>
                </span>
              </MoreTextTips>

              <!-- 如果是对象配置，支持自定义组件 -->
              <component
                v-else-if="typeof item.tipText === 'object' && item.tipText.component"
                :is="item.tipText.component"
                v-bind="item.tipText.props || {}"
                :field-key="item.fieldKey"
                :field-value="formData[item.fieldKey]"
                :field-config="item"
                :form-data="formData"
              />

              <!-- 如果是函数，执行函数并渲染结果 -->
              <MoreTextTips
                v-else-if="typeof item.tipText === 'function'"
                :line-clamp="1"
                bgColor="#000"
                textColor="#fff"
              >
                <span style="margin-left: 4px">
                  <i class="iconfont icontubiao_tishi_mian" style="margin-right: 2px; font-size: 14px" />
                  <span>{{ item.tipText(formData[item.fieldKey], formData, item) }}</span>
                </span>
              </MoreTextTips>
            </div>
          </div>
        </template>
        <component
          :is="item.component"
          v-model:value="formData[item.fieldKey]"
          v-bind="item.componentAttrs || {}"
          :componentConfig="item || {}"
        />
      </FFormItem>
    </FCol>
  </FRow>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { deepClone } from '@/utils'
import { isEmpty, isNotEmpty } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'
import MoreTextTips from '@/components/MoreTextTips/index'

const props = defineProps<{
  components: Record<string, any>
  formData: any
  validatorKeys?: any
  isDetail: boolean
}>()

const emits = defineEmits(['update:formData'])

const { validatorKeys } = toRefs(props)
const formData = computed({
  get: () => props.formData,
  set: value => {
    emits('update:formData', value)
  },
})

const validatorFn = async (rule, value, item) => {
  if (rule?.required && isEmpty(value)) {
    let msg = item?.message || item?.componentAttrs?.placeholder || '请填写表单内容'
    return Promise.reject(msg)
  }
  if (!item?.componentAttrs?.disabled && validatorKeys?.value?.[rule?.field])
    return await validatorKeys?.value?.[rule?.field](rule, value, item)
}

const currentComponents = computed<any>(() => {
  const data = {}
  Object.entries(props?.components ?? {}).forEach(([key, item]) => {
    if (isEmpty(item?.isShowComponent) || (isNotEmpty(item?.isShowComponent) && item?.isShowComponent(item))) {
      data[key] = deepClone(
        Object.assign({}, item, Object.assign({}, item?.componentConfig ?? {}, { isDetail: props.isDetail }))
      )
    }
  })
  return data
})
</script>

<style lang="scss" scoped>
:deep(.form-item-detail-box) {
  .fs-form-item-control-input-content,
  .fs-form-item-control-input {
    min-height: auto !important;
    height: auto !important;
  }
  .fs-form-item-label > label {
    height: auto !important;
  }
}
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
:deep(.custom-textarea) {
  min-height: 88px !important;
}
.nowrap {
  white-space: nowrap;
}
:deep(.form-item-box) {
  .fs-form-item-label > label {
    width: 100%;
  }
}
</style>

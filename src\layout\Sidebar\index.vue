<template>
  <FLayoutSider
    class="layout-sider"
    :collapsed="props.collapse"
    :width="224"
    :collapsed-width="96"
    :trigger="null"
    collapsible
  >
    <div class="layout-sider-content">
      <!-- logo -->
      <div class="home-logo-box" :style="{ padding: props.collapse ? '0 10px' : '0 24px' }">
        <router-link to="/" class="home-logo"></router-link>
        <div v-show="!props.collapse" class="home-split" />
        <span class="home-title" v-show="!props.collapse">{{ i18n.t('流程管理系统') }}</span>
      </div>
      <!-- menu -->
      <div class="home-sider-menu">
        <FMenu :collapsed="props.collapse" :menus="props.menu" @menu-item-click="handleMenuItemClick" />
      </div>
      <!-- footer -->
      <div class="home-sider-footer" :style="{ alignSelf: props.collapse ? 'center' : 'flex-end' }">
        <FTooltip v-if="props.collapse" title="展开">
          <FButton class="home-sider-open" @click="handleCollapse">
            <template #icon><FIcon :type="!props.collapse ? 'icon-quyushouqi' : 'icon-quyuzhankai'" /></template>
          </FButton>
        </FTooltip>
        <FButton v-else class="home-sider-open" @click="handleCollapse">
          <template #icon><FIcon :type="!props.collapse ? 'icon-quyushouqi' : 'icon-quyuzhankai'" /></template>
          收起
        </FButton>
      </div>
    </div>
  </FLayoutSider>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { type IMicroMenu } from '@fs/hooks'
import { i18n } from '@/init'
import FMenu from 'fs-menu'

export interface IMenuItem {
  id: number
  name: string
  type: string
  icon?: string
  path?: string
  appName?: string
  children?: IMenuItem[]
}

interface IProps {
  menu: IMicroMenu[]
  collapse: boolean
}

const props = defineProps<IProps>()
const emits = defineEmits(['on-collapse'])
const router = useRouter()

// 处理菜单点击事件
const handleMenuItemClick = (data: { active: string; activePath: string[]; activeType: string }) => {
  const { active: path, activePath: keyPath, activeType: type } = data
  if (type === 'menus_item-link') {
    window.open(path)
  } else if (path && keyPath && keyPath.length) {
    // const appName = menusPathMap.get(path)?.appName ?? keyPath[0]
    router.push({ path })
    // if (window.__MICRO_APP_NAME__ === appName) {
    //   microSendData(appName, { path })
    // } else {
    //   router.push({ path: `/${appName + path}` })
    // }
  }
}

const handleCollapse = () => {
  emits('on-collapse', !props.collapse)
}
</script>
<style scoped lang="scss">
.layout-sider {
  overflow-y: auto;
  background-color: transparent;
  background: linear-gradient(180deg, #deecfd 0%, #edf4ff 100%);
}

.layout-sider-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.home-logo-box {
  flex: 0 0 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  height: 68px;

  .home-logo {
    width: 50px;
    height: 24px;
    background: url(@/assets/images/logo.svg);
    background-repeat: no-repeat;
    background-size: cover;
  }

  .home-split {
    margin: 0 8px;
    width: 1px;
    height: 16px;
    background-color: #b6c4d3;
  }

  .home-title {
    color: #333;
    font-size: 16px;
    font-weight: 500;
    font-family: PingFangSC, PingFang SC;
    animation: titleAnimation 0.3s ease;
    white-space: nowrap;
  }
}

.home-sider-menu {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.home-sider-footer {
  flex: 0 0 80px;
  padding: 24px 12px;

  .home-sider-open {
    color: #333 !important;
    transition: none !important;

    &::before,
    &::after {
      animation: none !important;
    }
  }
}

:deep(.ant-menu-inline) {
  border-color: white;
}

:deep(.fs-btn) {
  padding: 8px !important;
  border: none !important;
  min-width: auto !important;
}
</style>

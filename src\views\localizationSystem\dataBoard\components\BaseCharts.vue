<template>
  <div class="base-charts" ref="chartRef" />
</template>
<script lang="ts" setup>
import { PropType } from 'vue'
import * as Echarts from 'echarts'
import { toRefs, shallowRef, onMounted, watch, onBeforeUnmount } from 'vue'

const props = defineProps({
  options: {
    type: Object as PropType<Echarts.EChartsCoreOption>,
    default: () => ({}),
  },
})

const charts = shallowRef<Echarts.ECharts>()
const { options } = toRefs(props)
const chartRef = shallowRef()

const setOptions = (opt: Echarts.EChartsCoreOption) => {
  charts.value?.setOption(opt)
  handleResize()
}

const initChart = async () => {
  charts.value = Echarts.init(chartRef.value)
  charts.value.setOption(options.value)
}

const handleResize = () => {
  charts.value?.resize()
}

onMounted(async () => {
  initChart()
  setOptions(options.value)
  window.addEventListener('resize', handleResize)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
watch(
  options,
  () => {
    setOptions(options.value)
  },
  {
    deep: true,
  }
)
defineExpose({
  $charts: charts,
})
</script>
<style lang="scss" scoped>
.base-charts {
  width: 100%;
  height: 100%;
  min-height: 80px;
}
</style>

import { PageDemandParams, PageDemandRes, ICount } from '@/types/processManagement'
import { IRes, IResDataList } from '@/types/request'
import { request } from '@/utils'

export const pageDemandList = async (data: PageDemandParams) => {
  const res = await request.post<IRes<IResDataList<PageDemandRes>>>('/api/sdk/demand/query', data)
  return res as unknown as IRes<IResDataList<PageDemandRes>>
}

export const pageDemandExport = async (data: PageDemandParams) => {
  const res = await request.post<IRes<IResDataList<PageDemandRes>>>('/api/sdk/demand/export', data)
  return res as unknown as IRes<IResDataList<PageDemandRes>>
}

export const getDemandCount = async (data: PageDemandParams) => {
  const res = await request.post<IRes<ICount>>('/api/sdk/demand/count', data)
  return res as unknown as IRes<ICount>
}

export const getDictionary = async () => {
  const res = await request.get<IRes>('/api/sdk/demand/getDictionary')
  return res as unknown as IRes
}

export const getByIdProcessConfig = async (id: string) => {
  const res = await request.get<IRes>(`/api/bpmDefine/getById/${id}`)
  return res as unknown as IRes
}

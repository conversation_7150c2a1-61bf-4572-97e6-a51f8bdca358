<template>
  <div class="cron-editor" :class="{ 'cron-editor-disabled': props.disabled }">
    <div class="cron-editor-header">
      <FTabs v-model:activeKey="activeTab" @change="handleTabChange">
        <FTabPane v-if="props.showSeconds" key="seconds" tab="秒">
          <div class="cron-tab-content">
            <FRadioGroup v-model:value="cronParts.seconds.type" @change="generateCron" :disabled="props.disabled">
              <FRadio value="*">每秒</FRadio>
              <FRadio value="0">0秒</FRadio>
              <FRadio value="/">周期</FRadio>
              <FRadio value=",">指定</FRadio>
            </FRadioGroup>

            <div v-if="cronParts.seconds.type === '/'" class="cron-input-group">
              <span>从</span>
              <FInputNumber v-model:value="cronParts.seconds.start" :min="0" :max="59" @change="generateCron" />
              <span>秒开始，每</span>
              <FInputNumber v-model:value="cronParts.seconds.interval" :min="1" :max="59" @change="generateCron" />
              <span>秒执行一次</span>
            </div>

            <div v-if="cronParts.seconds.type === ','" class="cron-input-group">
              <FSelect
                v-model:value="cronParts.seconds.values"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择具体秒数"
                @change="generateCron"
              >
                <FSelectOption v-for="i in 60" :key="i - 1" :value="i - 1">{{ i - 1 }}</FSelectOption>
              </FSelect>
            </div>
          </div>
        </FTabPane>

        <FTabPane key="minutes" tab="分">
          <div class="cron-tab-content">
            <FRadioGroup v-model:value="cronParts.minutes.type" @change="generateCron">
              <FRadio value="*">每分</FRadio>
              <FRadio value="0">0分</FRadio>
              <FRadio value="/">周期</FRadio>
              <FRadio value=",">指定</FRadio>
            </FRadioGroup>

            <div v-if="cronParts.minutes.type === '/'" class="cron-input-group">
              <span>从</span>
              <FInputNumber v-model:value="cronParts.minutes.start" :min="0" :max="59" @change="generateCron" />
              <span>分开始，每</span>
              <FInputNumber v-model:value="cronParts.minutes.interval" :min="1" :max="59" @change="generateCron" />
              <span>分执行一次</span>
            </div>

            <div v-if="cronParts.minutes.type === ','" class="cron-input-group">
              <FSelect
                v-model:value="cronParts.minutes.values"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择具体分钟"
                @change="generateCron"
              >
                <FSelectOption v-for="i in 60" :key="i - 1" :value="i - 1">{{ i - 1 }}</FSelectOption>
              </FSelect>
            </div>
          </div>
        </FTabPane>

        <FTabPane key="hours" tab="时">
          <div class="cron-tab-content">
            <FRadioGroup v-model:value="cronParts.hours.type" @change="generateCron">
              <FRadio value="*">每小时</FRadio>
              <FRadio value="0">0点</FRadio>
              <FRadio value="/">周期</FRadio>
              <FRadio value=",">指定</FRadio>
            </FRadioGroup>

            <div v-if="cronParts.hours.type === '/'" class="cron-input-group">
              <span>从</span>
              <FInputNumber v-model:value="cronParts.hours.start" :min="0" :max="23" @change="generateCron" />
              <span>时开始，每</span>
              <FInputNumber v-model:value="cronParts.hours.interval" :min="1" :max="23" @change="generateCron" />
              <span>小时执行一次</span>
            </div>

            <div v-if="cronParts.hours.type === ','" class="cron-input-group">
              <FSelect
                v-model:value="cronParts.hours.values"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择具体小时"
                @change="generateCron"
              >
                <FSelectOption v-for="i in 24" :key="i - 1" :value="i - 1">{{ i - 1 }}</FSelectOption>
              </FSelect>
            </div>
          </div>
        </FTabPane>

        <FTabPane key="days" tab="日">
          <div class="cron-tab-content">
            <div class="cron-tab-header">
              <div class="cron-tab-description">
                <p>在 Cron 表达式中，日期和星期是互斥的，必须有一个设置为"不指定"（?）。</p>
                <p>当您选择日期选项时，星期会自动设置为"不指定"。</p>
              </div>
            </div>

            <FRadioGroup v-model:value="cronParts.days.type" @change="handleDayTypeChange">
              <FRadio value="*">每日</FRadio>
              <FRadio value="?">不指定</FRadio>
              <FRadio value="/">周期</FRadio>
              <FRadio value=",">指定</FRadio>
              <FRadio value="L">最后一天</FRadio>
            </FRadioGroup>

            <div v-if="cronParts.days.type === '/'" class="cron-input-group">
              <span>从</span>
              <FInputNumber v-model:value="cronParts.days.start" :min="1" :max="31" @change="generateCron" />
              <span>日开始，每</span>
              <FInputNumber v-model:value="cronParts.days.interval" :min="1" :max="31" @change="generateCron" />
              <span>天执行一次</span>
            </div>

            <div v-if="cronParts.days.type === ','" class="cron-input-group">
              <FSelect
                v-model:value="cronParts.days.values"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择具体日期"
                @change="generateCron"
              >
                <FSelectOption v-for="i in 31" :key="i" :value="i">{{ i }}</FSelectOption>
              </FSelect>
            </div>
          </div>
        </FTabPane>

        <FTabPane key="months" tab="月">
          <div class="cron-tab-content">
            <FRadioGroup v-model:value="cronParts.months.type" @change="generateCron">
              <FRadio value="*">每月</FRadio>
              <FRadio value="/">周期</FRadio>
              <FRadio value=",">指定</FRadio>
            </FRadioGroup>

            <div v-if="cronParts.months.type === '/'" class="cron-input-group">
              <span>从</span>
              <FInputNumber v-model:value="cronParts.months.start" :min="1" :max="12" @change="generateCron" />
              <span>月开始，每</span>
              <FInputNumber v-model:value="cronParts.months.interval" :min="1" :max="12" @change="generateCron" />
              <span>月执行一次</span>
            </div>

            <div v-if="cronParts.months.type === ','" class="cron-input-group">
              <FSelect
                v-model:value="cronParts.months.values"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择具体月份"
                @change="generateCron"
              >
                <FSelectOption v-for="(month, index) in monthNames" :key="index + 1" :value="index + 1">{{
                  month
                }}</FSelectOption>
              </FSelect>
            </div>
          </div>
        </FTabPane>

        <FTabPane key="weeks" tab="周">
          <div class="cron-tab-content">
            <div class="cron-tab-header">
              <div class="cron-tab-description">
                <p>在 Cron 表达式中，日期和星期是互斥的，必须有一个设置为"不指定"（?）。</p>
                <p>当您选择周选项时，日期会自动设置为"不指定"。</p>
              </div>
            </div>

            <FRadioGroup v-model:value="cronParts.weeks.displayType" @change="handleWeekTypeChange">
              <FRadio value="*">每周</FRadio>
              <FRadio value="?">不指定</FRadio>
              <FRadio value=",">指定周几</FRadio>
              <FRadio value="#">第几个周几</FRadio>
            </FRadioGroup>

            <div v-if="cronParts.weeks.displayType === '*'" class="cron-input-group">
              <span>每周的</span>
              <FSelect
                v-model:value="cronParts.weeks.specificDay"
                style="width: 100px"
                placeholder="星期几"
                @change="handleSpecificDayChange"
                :options="weekDayOptions"
              >
              </FSelect>
              <span>（不选则为每天）</span>
            </div>

            <div v-if="cronParts.weeks.displayType === ','" class="cron-input-group">
              <FSelect
                v-model:value="cronParts.weeks.values"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择具体星期"
                @change="handleWeekValuesChange"
                :options="weekDayOptions"
              >
              </FSelect>
            </div>

            <div v-if="cronParts.weeks.displayType === '#'" class="cron-input-group">
              <span>第</span>
              <FInputNumber v-model:value="cronParts.weeks.nth" :min="1" :max="5" @change="handleWeekNthChange" />
              <span>个</span>
              <FSelect
                v-model:value="cronParts.weeks.dayOfWeek"
                style="width: 100px"
                placeholder="星期几"
                @change="handleWeekDayChange"
                :options="weekDayOptions"
              >
              </FSelect>
            </div>
          </div>
        </FTabPane>
      </FTabs>
    </div>

    <div class="cron-editor-footer">
      <!-- 表达式输入框 -->
      <div v-if="props.showExpression" class="cron-expression">
        <span class="label">表达式：</span>
        <FInput
          v-model:value="cronExpression"
          @change="onExpressionChange"
          :status="isExpressionValid ? '' : 'error'"
          :placeholder="props.placeholder"
          :disabled="props.disabled"
        />
      </div>

      <!-- 验证错误提示 -->
      <div class="cron-validation" v-if="!isExpressionValid">
        <FAlert type="error" :message="validationError" banner />
      </div>

      <!-- 表达式描述 -->
      <div v-if="props.showDescription" class="cron-description">
        <span>{{ cronDescription }}</span>
      </div>

      <!-- 未来执行时间 -->
      <div v-if="props.showNextExecutions && isExpressionValid" class="cron-next-executions">
        <div class="next-execution-title">未来{{ props.executionCount }}次执行时间:</div>
        <ul class="next-execution-list" v-if="nextExecutions.length > 0">
          <li v-for="(time, index) in nextExecutions" :key="index">
            {{ time }}
          </li>
        </ul>
        <div class="next-execution-reason" v-else>
          {{ executionReason }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { parseCronToText } from '@/utils/cronParser'
import dayjs from 'dayjs'
import parser from 'cron-parser'

// 定义组件属性
const props = defineProps({
  // Cron 表达式的值
  value: {
    type: String,
    default: '0 0 12 * * ?',
  },
  // 是否禁用编辑器
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否显示表达式输入框
  showExpression: {
    type: Boolean,
    default: true,
  },
  // 是否显示未来执行时间
  showNextExecutions: {
    type: Boolean,
    default: true,
  },
  // 未来执行时间的数量
  executionCount: {
    type: Number,
    default: 5,
  },
  // 默认激活的标签页
  defaultTab: {
    type: String,
    default: 'seconds',
    validator: (value: string) => ['seconds', 'minutes', 'hours', 'days', 'months', 'weeks'].includes(value),
  },
  // 是否显示秒选项卡
  showSeconds: {
    type: Boolean,
    default: true,
  },
  // 是否显示年选项卡
  showDescription: {
    type: Boolean,
    default: true,
  },
  // 表达式输入框的占位符
  placeholder: {
    type: String,
    default: '请输入有效的Cron表达式，例如: 0 0 12 * * ?',
  },
  // 表达式是否有效（用于双向绑定）
  isValid: {
    type: Boolean,
    default: true,
  },
})

// 定义事件
const emit = defineEmits([
  'update:value', // 值更新事件
  'update:isValid', // 更新表达式是否有效
  'change', // 值变化事件
  'tab-change', // 标签页变化事件
  'validation-change', // 验证状态变化事件
  'error', // 错误事件
])

// 状态变量
const activeTab = ref(props.defaultTab)
const cronExpression = ref(props.value)
const isExpressionValid = ref(true)
const validationError = ref('')
const nextExecutions = ref<string[]>([])
const executionReason = ref('无法计算未来执行时间，表达式过于复杂')

// 常量定义
const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
const weekDayNames = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']

// 选项数据
const weekDayOptions = weekDayNames.map((day, index) => ({
  label: day,
  value: index,
}))

// 获取星期几的名称
const getWeekDayName = (day: number): string => {
  // 确保 day 在 0-6 范围内
  const index = ((day % 7) + 7) % 7
  return weekDayNames[index]
}

// Cron表达式各部分的配置
const cronParts = reactive({
  seconds: {
    type: '0',
    start: 0,
    interval: 1,
    values: [],
  },
  minutes: {
    type: '0',
    start: 0,
    interval: 1,
    values: [],
  },
  hours: {
    type: '12',
    start: 0,
    interval: 1,
    values: [],
  },
  days: {
    type: '*',
    start: 1,
    interval: 1,
    values: [],
    workDay: 1,
  },
  months: {
    type: '*',
    start: 1,
    interval: 1,
    values: [],
  },
  weeks: {
    type: '?',
    values: [],
    nth: 1,
    dayOfWeek: 1,
    specificDay: null,
    originalType: null,
    displayType: '?',
  },
})

// 处理标签页切换
const handleTabChange = (key: string) => {
  // 更新激活的标签页
  activeTab.value = key

  // 注意：我们不再自动设置字段为"每日"或"每周"
  // 只在用户明确选择时才改变值

  // 触发标签页变化事件
  emit('tab-change', key)
}

// 处理日类型变更
const handleDayTypeChange = (event: any) => {
  // 从事件对象中获取值
  // 注意：FRadioGroup 的 change 事件直接传递选中的值，而不是事件对象
  const value = typeof event === 'string' ? event : event.target?.value || '*'

  // 保存旧值，用于判断是否需要清空选项
  const oldType = cronParts.days.type

  // 更新实际类型
  cronParts.days.type = String(value)

  // 处理日期和星期的互斥性
  if (value !== '?') {
    // 如果日期不是"不指定"，则将星期设为"不指定"
    cronParts.weeks.type = '?'
    cronParts.weeks.displayType = '?'
    cronParts.weeks.originalType = '?'
    // 清除星期的值
    cronParts.weeks.values = []
    cronParts.weeks.specificDay = null
  }
  // 注意：如果日期设为"不指定"，我们不自动修改星期的值

  // 清除之前的设置 - 确保切换选项时不会保留旧值
  if (oldType !== value) {
    cronParts.days.values = []
    cronParts.days.start = 1
    cronParts.days.interval = 1
  }

  // 生成表达式并计算未来执行时间
  generateCron()
}

// 处理周类型变更
const handleWeekTypeChange = (event: any) => {
  // 从事件对象中获取值
  // 注意：FRadioGroup 的 change 事件直接传递选中的值，而不是事件对象
  const value = typeof event === 'string' ? event : event.target?.value || '*'

  // 保存旧值，用于判断是否需要清空选项
  const oldType = cronParts.weeks.type

  // 更新实际类型和显示类型
  cronParts.weeks.type = String(value)
  cronParts.weeks.displayType = String(value)
  cronParts.weeks.originalType = String(value)

  // 处理日期和星期的互斥性
  if (value !== '?') {
    // 如果星期不是"不指定"，则将日期设为"不指定"
    cronParts.days.type = '?'
    // 清除日期的值
    cronParts.days.values = []
  }
  // 注意：如果星期设为"不指定"，我们不自动修改日期的值

  // 如果类型变更，清除之前的设置
  if (oldType !== value) {
    // 清除之前的设置 - 确保切换选项时不会保留旧值
    cronParts.weeks.specificDay = null
    cronParts.weeks.values = []

    // 设置默认值
    if (value === '#') {
      cronParts.weeks.nth = 1
      cronParts.weeks.dayOfWeek = 1
    }
  }

  // 生成表达式并计算未来执行时间
  generateCron()
}

// 处理周几值变更
const handleWeekValuesChange = (values: number[]) => {
  // 更新周几值
  cronParts.weeks.values = values

  // 确保周几选项正确显示
  cronParts.weeks.type = ','
  cronParts.weeks.displayType = ','
  cronParts.weeks.originalType = ','

  // 将日期设为"不指定"
  cronParts.days.type = '?'
  // 清除日期的值
  cronParts.days.values = []

  // 生成表达式并计算未来执行时间
  generateCron()
}

// 处理第几个周几的"第几个"变更
const handleWeekNthChange = (value: number) => {
  // 更新第几个
  cronParts.weeks.nth = value

  // 确保周几选项正确显示
  cronParts.weeks.type = '#'
  cronParts.weeks.displayType = '#'
  cronParts.weeks.originalType = '#'

  // 将日期设为"不指定"
  cronParts.days.type = '?'
  // 清除日期的值
  cronParts.days.values = []

  // 生成表达式并计算未来执行时间
  generateCron()
}

// 处理第几个周几的"周几"变更
const handleWeekDayChange = (value: number) => {
  // 更新周几
  cronParts.weeks.dayOfWeek = value

  // 确保周几选项正确显示
  cronParts.weeks.type = '#'
  cronParts.weeks.displayType = '#'
  cronParts.weeks.originalType = '#'

  // 将日期设为"不指定"
  cronParts.days.type = '?'
  // 清除日期的值
  cronParts.days.values = []

  // 生成表达式并计算未来执行时间
  generateCron()
}

// 处理每周特定日期变更
const handleSpecificDayChange = (value: number | null) => {
  // 更新特定日期
  cronParts.weeks.specificDay = value

  // 确保周几选项正确显示
  cronParts.weeks.type = '*'
  cronParts.weeks.displayType = '*'
  cronParts.weeks.originalType = '*'

  // 将日期设为"不指定"
  cronParts.days.type = '?'
  // 清除日期的值
  cronParts.days.values = []

  // 生成表达式并计算未来执行时间
  generateCron()
}

// 生成Cron表达式
const generateCron = () => {
  // 确保日和周不能同时为具体值
  if (
    cronParts.days.type !== '?' &&
    cronParts.days.type !== '*' &&
    cronParts.weeks.type !== '?' &&
    cronParts.weeks.type !== '*'
  ) {
    // 根据当前激活的标签页决定哪个字段设置为"不指定"
    if (activeTab.value === 'days') {
      // 保留日期，将星期设为"不指定"
      cronParts.weeks.type = '?'
      cronParts.weeks.displayType = '?'
      cronParts.weeks.originalType = '?'
      // 清除星期的值
      cronParts.weeks.values = []
      cronParts.weeks.specificDay = null
    } else if (activeTab.value === 'weeks') {
      // 保留星期，将日期设为"不指定"
      cronParts.days.type = '?'
      // 清除日期的值
      cronParts.days.values = []
    }
  }

  // 确保至少有一个字段不是"不指定"
  if (cronParts.days.type === '?' && cronParts.weeks.type === '?') {
    // 如果两者都是"不指定"，根据当前激活的标签页决定哪个设为默认值
    if (activeTab.value === 'days') {
      cronParts.days.type = '*' // 设置为"每日"
    } else {
      cronParts.weeks.type = '*' // 设置为"每周"
      cronParts.weeks.displayType = '*'
      cronParts.weeks.originalType = '*'
    }
  }

  // 处理特殊情况
  if (cronParts.weeks.type === '#' || cronParts.weeks.displayType === '#') {
    const dayOfWeek = cronParts.weeks.dayOfWeek ?? 0
    const nth = cronParts.weeks.nth || 1
    // 保存原始类型，以便在UI中仍然显示为"第几个周几"
    cronParts.weeks.originalType = '#'
    cronParts.weeks.displayType = '#'
    cronParts.weeks.type = `${dayOfWeek}#${nth}`
  }
  // 处理每周特定日期
  else if (cronParts.weeks.type === '*' && cronParts.weeks.specificDay !== null) {
    // 保存原始类型，以便在UI中仍然显示为"每周"
    cronParts.weeks.originalType = '*'
    cronParts.weeks.displayType = '*'
    cronParts.weeks.type = cronParts.weeks.specificDay.toString()
  }
  // 处理指定周几列表
  else if (cronParts.weeks.type === ',' || cronParts.weeks.displayType === ',') {
    // 保存原始类型，以便在UI中仍然显示为"指定"
    cronParts.weeks.originalType = ','
    cronParts.weeks.displayType = ','
    if (cronParts.weeks.values && cronParts.weeks.values.length > 0) {
      cronParts.weeks.type = cronParts.weeks.values.join(',')
    } else {
      cronParts.weeks.type = '?'
    }
  }

  // 生成表达式 (6位: 秒 分 时 日 月 周)
  const parts = [
    getPartExpression(cronParts.seconds),
    getPartExpression(cronParts.minutes),
    getPartExpression(cronParts.hours),
    getPartExpression(cronParts.days),
    getPartExpression(cronParts.months),
    getPartExpression(cronParts.weeks),
  ]

  cronExpression.value = parts.join(' ')
  validateExpression(cronExpression.value)

  // 如果表达式有效，计算下一次执行时间
  if (isExpressionValid.value) {
    nextExecutions.value = calculateNextExecutions(cronExpression.value)
  }

  emit('update:value', cronExpression.value)
  emit('change', cronExpression.value)
}

// 获取各部分的表达式
const getPartExpression = (part: any) => {
  // 处理特殊情况
  if (
    part.type &&
    typeof part.type === 'string' &&
    (part.type.includes('W') || part.type.includes('#') || part.type.includes('L'))
  ) {
    return part.type
  }

  switch (part.type) {
    case '*':
    case '?':
    case 'L':
      return part.type
    case '/':
      return `${part.start}/${part.interval}`
    case ',':
      return part.values.length > 0 ? part.values.join(',') : '*'
    default:
      // 如果是数字，直接返回
      if (!isNaN(parseInt(part.type))) {
        return part.type
      }
      return '*'
  }
}

// 解析Cron表达式
const parseCronExpression = (expression: string) => {
  try {
    const parts = expression.split(' ')
    if (parts.length !== 6) {
      validationError.value = '表达式必须包含6个部分（秒 分 时 日 月 周）'
      return false
    }

    // 清除之前的值，但不设置默认类型，让解析过程决定类型
    cronParts.seconds.values = []
    cronParts.minutes.values = []
    cronParts.hours.values = []
    cronParts.days.values = []
    cronParts.months.values = []
    cronParts.weeks.values = []
    cronParts.weeks.specificDay = null
    cronParts.weeks.nth = 1
    cronParts.weeks.dayOfWeek = 1

    // 解析各部分
    parsePartExpression(cronParts.seconds, parts[0])
    parsePartExpression(cronParts.minutes, parts[1])
    parsePartExpression(cronParts.hours, parts[2])
    parsePartExpression(cronParts.days, parts[3])
    parsePartExpression(cronParts.months, parts[4])
    parsePartExpression(cronParts.weeks, parts[5])

    // 处理特殊情况

    // 处理第几个周几 (2#1 - 第一个周一)
    if (typeof cronParts.weeks.type === 'string' && cronParts.weeks.type.includes('#')) {
      const match = cronParts.weeks.type.match(/(\d+)#(\d+)/)
      if (match) {
        cronParts.weeks.dayOfWeek = parseInt(match[1])
        cronParts.weeks.nth = parseInt(match[2])
        cronParts.weeks.originalType = '#'
        cronParts.weeks.displayType = '#'
        cronParts.weeks.type = '#'
      }
    }

    // 处理指定周几列表 (1,3,5 - 周日、二、四)
    else if (typeof cronParts.weeks.type === 'string' && cronParts.weeks.type.includes(',')) {
      cronParts.weeks.values = cronParts.weeks.type.split(',').map(v => parseInt(v))
      cronParts.weeks.originalType = ','
      cronParts.weeks.displayType = ','
      cronParts.weeks.type = ','
    }

    // 解析单个周几 (如 1 表示周日)
    else if (/^[1-7]$/.test(cronParts.weeks.type)) {
      cronParts.weeks.specificDay = parseInt(cronParts.weeks.type)
      cronParts.weeks.originalType = '*'
      cronParts.weeks.displayType = '*'
      cronParts.weeks.type = '*'
    }

    // 确保日期和星期的互斥性
    if (
      cronParts.days.type !== '?' &&
      cronParts.days.type !== '*' &&
      cronParts.weeks.type !== '?' &&
      cronParts.weeks.type !== '*'
    ) {
      // 如果两者都有具体值，优先保留当前激活的标签页对应的值
      if (activeTab.value === 'days') {
        cronParts.weeks.type = '?'
        cronParts.weeks.displayType = '?'
      } else if (activeTab.value === 'weeks') {
        cronParts.days.type = '?'
      }
    }

    // 确保UI组件正确显示
    updateUIDisplay()

    return true
  } catch (e) {
    console.error('解析Cron表达式出错:', e)
    validationError.value = '解析表达式出错: ' + (e instanceof Error ? e.message : String(e))
    return false
  }
}

// 更新UI显示状态
const updateUIDisplay = () => {
  // 处理具体数值的情况
  handleSpecificValues(cronParts.seconds)
  handleSpecificValues(cronParts.minutes)
  handleSpecificValues(cronParts.hours)
  handleSpecificValues(cronParts.months)

  // 确保周几选项正确显示
  if (cronParts.weeks.type === '#' || cronParts.weeks.originalType === '#') {
    cronParts.weeks.displayType = '#'
  } else if (cronParts.weeks.type === ',' || cronParts.weeks.originalType === ',') {
    cronParts.weeks.displayType = ','
  } else if (cronParts.weeks.type === '*' && cronParts.weeks.specificDay !== null) {
    cronParts.weeks.displayType = '*'
  }

  // 确保日期选项正确显示
  if (cronParts.days.type === 'L') {
    // 最后一天特殊处理
    cronParts.days.type = 'L'
  } else if (typeof cronParts.days.type === 'string' && cronParts.days.type.includes('W')) {
    // 工作日特殊处理
    cronParts.days.workDay = parseInt(cronParts.days.type.replace('W', '')) || 1
    cronParts.days.type = 'W'
  } else {
    // 处理日期的具体数值
    handleSpecificValues(cronParts.days)
  }

  // 处理完具体数值后，再次确保日期和星期的互斥性
  ensureDayWeekMutualExclusion()
}

// 确保日期和星期的互斥性
const ensureDayWeekMutualExclusion = () => {
  // 检查日期和星期是否都有具体值
  const dayHasSpecificValue = cronParts.days.type !== '?' && cronParts.days.type !== '*'
  const weekHasSpecificValue =
    cronParts.weeks.type !== '?' &&
    cronParts.weeks.type !== '*' &&
    (cronParts.weeks.specificDay !== null || cronParts.weeks.values.length > 0 || cronParts.weeks.type === '#')

  if (dayHasSpecificValue && weekHasSpecificValue) {
    // 如果两者都有具体值，根据当前激活的标签页决定保留哪个
    if (activeTab.value === 'days') {
      // 保留日期，将星期设为"不指定"
      cronParts.weeks.type = '?'
      cronParts.weeks.displayType = '?'
      cronParts.weeks.originalType = '?'
      cronParts.weeks.values = []
      cronParts.weeks.specificDay = null
    } else if (activeTab.value === 'weeks') {
      // 保留星期，将日期设为"不指定"
      cronParts.days.type = '?'
      cronParts.days.values = []
    } else {
      // 如果当前标签页不是日期或星期，根据表达式决定保留哪个
      // 优先保留日期
      cronParts.weeks.type = '?'
      cronParts.weeks.displayType = '?'
      cronParts.weeks.originalType = '?'
      cronParts.weeks.values = []
      cronParts.weeks.specificDay = null
    }
  }

  // 确保至少有一个字段不是"不指定"
  if (cronParts.days.type === '?' && cronParts.weeks.type === '?') {
    // 如果两者都是"不指定"，根据当前激活的标签页决定哪个设为默认值
    if (activeTab.value === 'days') {
      cronParts.days.type = '*' // 设置为"每日"
    } else {
      cronParts.weeks.type = '*' // 设置为"每周"
      cronParts.weeks.displayType = '*'
      cronParts.weeks.originalType = '*'
    }
  }
}

// 处理具体数值的情况
const handleSpecificValues = (part: any) => {
  // 检查是否是具体数值
  if (
    typeof part.type === 'string' &&
    !isNaN(parseInt(part.type)) &&
    part.type !== '*' &&
    part.type !== '?' &&
    part.type !== '/' &&
    part.type !== ','
  ) {
    // 将具体数值转换为"指定"类型，并设置values数组
    const num = parseInt(part.type)
    part.values = [num]
    part.type = ','
  }
}

// 解析各部分表达式
const parsePartExpression = (part: any, expression: string) => {
  try {
    // 处理特殊情况
    if (
      typeof expression === 'string' &&
      (expression.includes('#') || expression.includes('L') || expression.includes('W'))
    ) {
      part.type = expression

      // 如果是周几部分，设置displayType
      if (part === cronParts.weeks) {
        if (expression.includes('#')) {
          part.displayType = '#'
          part.originalType = '#'
        }
      }
      return
    }

    if (expression === '*' || expression === '?') {
      part.type = expression

      // 如果是周几部分，设置displayType
      if (part === cronParts.weeks) {
        part.displayType = expression
        part.originalType = expression
      }
    } else if (typeof expression === 'string' && expression.includes('/')) {
      part.type = '/'
      const [start, interval] = expression.split('/')
      part.start = parseInt(start) || 0
      part.interval = parseInt(interval) || 1
    } else if (typeof expression === 'string' && expression.includes(',')) {
      part.type = ','
      // 确保所有值都是有效的数字
      const values = expression.split(',').map(v => {
        const num = parseInt(v)
        return isNaN(num) ? 0 : num
      })
      part.values = values

      // 如果是周几部分，设置displayType
      if (part === cronParts.weeks) {
        part.displayType = ','
        part.originalType = ','
      }
    } else if (typeof expression === 'string' && expression.includes('-')) {
      // 处理范围表达式，如 1-5
      // 对于范围表达式，我们可以转换为逗号分隔的列表以便UI显示
      const [start, end] = expression.split('-').map(v => parseInt(v))
      if (!isNaN(start) && !isNaN(end) && start <= end) {
        part.type = ','
        const values = []
        for (let i = start; i <= end; i++) {
          values.push(i)
        }
        part.values = values

        // 如果是周几部分，设置displayType
        if (part === cronParts.weeks) {
          part.displayType = ','
          part.originalType = ','
        }
      } else {
        part.type = expression
      }
    } else {
      // 尝试解析为数字
      const num = parseInt(expression)
      if (!isNaN(num)) {
        // 对于具体数值，我们应该将其设置为"指定"类型，并添加到values数组中
        // 这样UI组件就能正确显示
        if (
          part === cronParts.seconds ||
          part === cronParts.minutes ||
          part === cronParts.hours ||
          part === cronParts.months ||
          part === cronParts.days
        ) {
          // 直接设置为逗号分隔类型，并添加值
          part.type = ','
          part.values = [num]
        } else if (part === cronParts.weeks) {
          // 对于周几，单个数字表示特定的周几
          part.specificDay = num
          part.type = '*'
          part.displayType = '*'
          part.originalType = '*'
        } else {
          part.type = expression
        }
      } else {
        part.type = '*'
      }
    }
  } catch (e) {
    console.error('解析表达式部分出错:', expression, e)
    // 出错时设置为默认值
    part.type = '*'
  }
}

// 验证表达式
const validateExpression = (expression: string) => {
  try {
    if (!expression) {
      isExpressionValid.value = false
      validationError.value = '表达式不能为空'
      return false
    }

    // 检查表达式格式
    const parts = expression.split(' ')
    if (parts.length !== 6) {
      isExpressionValid.value = false
      validationError.value = `表达式必须包含6个部分（秒 分 时 日 月 周），当前有 ${parts.length} 个部分`
      return false
    }

    // 检查日期和星期是否同时指定
    const day = parts[3]
    const week = parts[5]

    if (day !== '?' && week !== '?' && day !== '*' && week !== '*') {
      isExpressionValid.value = false
      validationError.value = '日期和星期不能同时指定具体值，必须有一个为 ?'
      return false
    }

    // 检查各部分是否有效
    const [seconds, minutes, hours, dayOfMonth, month, dayOfWeek] = parts

    // 检查秒 (0-59)
    if (!isValidPartRange(seconds, 0, 59)) {
      isExpressionValid.value = false
      validationError.value = '秒字段无效，有效范围为 0-59'
      return false
    }

    // 检查分 (0-59)
    if (!isValidPartRange(minutes, 0, 59)) {
      isExpressionValid.value = false
      validationError.value = '分钟字段无效，有效范围为 0-59'
      return false
    }

    // 检查时 (0-23)
    if (!isValidPartRange(hours, 0, 23)) {
      isExpressionValid.value = false
      validationError.value = '小时字段无效，有效范围为 0-23'
      return false
    }

    // 检查日 (1-31)
    if (dayOfMonth !== '?' && dayOfMonth !== '*' && !isValidPartRange(dayOfMonth, 1, 31)) {
      isExpressionValid.value = false
      validationError.value = '日期字段无效，有效范围为 1-31'
      return false
    }

    // 检查月 (1-12)
    if (!isValidPartRange(month, 1, 12)) {
      isExpressionValid.value = false
      validationError.value = '月份字段无效，有效范围为 1-12'
      return false
    }

    // 检查周 (0-6)
    if (dayOfWeek !== '?' && dayOfWeek !== '*' && !isValidPartRange(dayOfWeek, 0, 6)) {
      isExpressionValid.value = false
      validationError.value = '星期字段无效，有效范围为 1-7（1=周日，7=周六）'
      return false
    }

    // 使用 cron-parser 的 parse 方法进行校验
    let valid = true
    try {
      // 替换 ? 为 *，因为 cron-parser 不支持 ?
      const parsableExpression = expression.replace(/\?/g, '*')

      // 尝试解析表达式
      parser.parse(parsableExpression)
    } catch (e) {
      console.error('cron-parser 校验失败:', e)
      valid = false

      // 如果前面的基本检查都通过了，但 cron-parser 校验失败，提供更详细的错误信息
      if (!validationError.value) {
        if (e instanceof Error) {
          validationError.value = '表达式格式不正确: ' + e.message
        } else {
          validationError.value = '表达式格式不正确，请检查语法'
        }
      }
    }

    // 更新验证状态
    const oldValid = isExpressionValid.value
    isExpressionValid.value = valid

    // 如果验证状态发生变化，触发验证状态变化事件
    if (oldValid !== valid) {
      emit('validation-change', valid, validationError.value)
      // 更新 isValid 属性
      emit('update:isValid', valid)
    }

    if (!valid) {
      // 如果前面的检查都通过了，但仍然无效，可能是其他原因
      if (!validationError.value) {
        validationError.value = '表达式格式不正确，请检查语法'
      }

      // 触发错误事件
      emit('error', validationError.value)
    } else {
      // 表达式有效，清空错误信息
      validationError.value = ''
    }

    return valid
  } catch (e) {
    console.error('验证表达式出错:', e)
    isExpressionValid.value = false
    validationError.value = '验证表达式出错: ' + (e instanceof Error ? e.message : String(e))
    return false
  }
}

// 检查表达式部分是否在有效范围内
const isValidPartRange = (part: string, min: number, max: number): boolean => {
  try {
    // 处理特殊字符
    if (part === '*' || part === '?') return true

    // 处理列表 (1,2,3)
    if (part.includes(',')) {
      return part.split(',').every(item => isValidPartRange(item, min, max))
    }

    // 处理范围 (1-5)
    if (part.includes('-')) {
      const [start, end] = part.split('-').map(v => parseInt(v))
      return !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end
    }

    // 处理步长 (0/5)
    if (part.includes('/')) {
      const [start, step] = part.split('/')
      return isValidPartRange(start, min, max) && !isNaN(parseInt(step)) && parseInt(step) > 0
    }

    // 处理特殊字符 L, W, #
    if (part.includes('L') || part.includes('W') || part.includes('#')) {
      // 这些特殊字符需要更复杂的验证，这里简化处理
      return true
    }

    // 处理数字
    const num = parseInt(part)
    return !isNaN(num) && num >= min && num <= max
  } catch (e) {
    console.error('验证表达式部分出错:', part, e)
    return false
  }
}

// 监听表达式变化
const onExpressionChange = () => {
  const valid = validateExpression(cronExpression.value)

  if (valid) {
    // 解析表达式到UI
    parseCronExpression(cronExpression.value)

    // 计算未来执行时间
    nextExecutions.value = calculateNextExecutions(cronExpression.value)
  } else {
    nextExecutions.value = []
  }

  emit('update:value', cronExpression.value)
  emit('change', cronExpression.value)
}

// 计算未来执行时间
const calculateNextExecutions = (expression: string, count = props.executionCount): string[] => {
  try {
    if (!isExpressionValid.value) {
      executionReason.value = '表达式格式不正确，无法计算执行时间'
      return []
    }

    // 检查表达式格式
    const parts = expression.split(' ')
    if (parts.length !== 6) {
      executionReason.value = `表达式必须包含6个部分（秒 分 时 日 月 周），当前有 ${parts.length} 个部分`
      return []
    }

    // 处理 Spring Cron 表达式中的特殊字符
    let parsableExpression = expression

    // 替换 ? 为 *，因为 cron-parser 不支持 ?
    parsableExpression = parsableExpression.replace(/\?/g, '*')

    // 处理 L 和 W 特殊字符
    if (parsableExpression.includes('L') || parsableExpression.includes('W')) {
      // 这些特殊字符 cron-parser 可能无法处理，给出提示
      executionReason.value = '表达式包含特殊字符(L/W)，无法准确计算执行时间'
      // 继续尝试解析，但可能会失败
    }

    // 处理 # 特殊字符（第几个周几）
    if (parsableExpression.includes('#')) {
      // 尝试解析 #，但 cron-parser 可能无法处理
      const weekPart = parts[5]
      if (weekPart.includes('#')) {
        const match = weekPart.match(/(\d+)#(\d+)/)
        if (match) {
          const dayOfWeek = parseInt(match[1])
          const nth = parseInt(match[2])
          executionReason.value = `表达式指定了第${nth}个${getWeekDayName(dayOfWeek)}，使用近似计算`
        }
      }
    }

    try {
      // 配置 cron-parser 选项
      const options = {
        currentDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 一年后
        iterator: true,
        dayOfWeekStartIndexZero: true, // 设置为 true，表示星期从 0 开始
        weekStart: 0, // 设置为 0，表示星期从周日开始
      }

      // 尝试使用 cron-parser 解析表达式
      const interval = parser.parse(parsableExpression, options)
      const result: string[] = []

      for (let i = 0; i < count; i++) {
        try {
          const next = interval.next()
          result.push(dayjs(next.toDate()).format('YYYY-MM-DD HH:mm:ss'))
        } catch (e) {
          // 如果无法获取下一个执行时间，跳出循环
          break
        }
      }

      if (result.length > 0) {
        return result
      } else {
        executionReason.value = '无法计算未来执行时间，可能是表达式不正确或没有匹配的日期'
        return []
      }
    } catch (e) {
      console.error('解析表达式失败:', e)

      // 提供详细的错误信息
      if (e instanceof Error) {
        const msg = e.message
        if (msg.includes('Invalid')) {
          executionReason.value = '无效的表达式格式: ' + msg
        } else if (msg.includes('restricted')) {
          executionReason.value = '表达式包含受限制的值: ' + msg
        } else if (msg.includes('out of range')) {
          executionReason.value = '表达式包含超出范围的值: ' + msg
        } else {
          executionReason.value = '解析表达式失败: ' + msg
        }
      } else {
        executionReason.value = '解析表达式失败，未知错误'
      }

      return []
    }
  } catch (e) {
    // 捕获最外层的错误
    console.error('计算下一次执行时间出错:', e)
    executionReason.value = '计算执行时间出错: ' + (e instanceof Error ? e.message : String(e))
    return []
  }
}

// 计算Cron表达式的描述
const cronDescription = computed(() => {
  try {
    if (!isExpressionValid.value) return '表达式格式不正确'
    return parseCronToText(cronExpression.value)
  } catch (e) {
    console.error('解析Cron表达式出错:', e)
    return '表达式解析错误'
  }
})

// 监听 props.value 变化
watch(
  () => props.value,
  newValue => {
    if (newValue !== cronExpression.value) {
      // 处理7位表达式，转换为6位
      const processedValue = (() => {
        const parts = newValue.split(' ')
        return parts.length === 7 ? parts.slice(0, 6).join(' ') : newValue
      })()

      // 更新表达式值
      cronExpression.value = processedValue

      // 验证表达式
      const isValid = validateExpression(processedValue)

      // 更新验证状态
      emit('update:isValid', isValid)

      if (isValid) {
        // 解析表达式到UI组件
        parseCronExpression(processedValue)

        // 根据表达式内容设置合适的激活标签页
        setActiveTabBasedOnExpression(processedValue)

        // 更新UI显示
        updateUIDisplay()

        // 确保日期和星期的互斥性
        ensureDayWeekMutualExclusion()

        // 再次生成表达式，确保互斥性生效
        generateCron()

        // 计算未来执行时间
        nextExecutions.value = calculateNextExecutions(cronExpression.value)

        // 触发变更事件
        emit('change', cronExpression.value)
      } else {
        emit('error', validationError.value)
        nextExecutions.value = []
      }
    }
  }
)

// 初始化处理
// 先检查是否是7位表达式，如果是则转换为6位
const initValue = (() => {
  const parts = props.value.split(' ')
  return parts.length === 7 ? parts.slice(0, 6).join(' ') : props.value
})()

// 设置初始表达式值
cronExpression.value = initValue

// 验证表达式
const isValidFlag = validateExpression(initValue)

// 发出初始验证状态
emit('update:isValid', isValidFlag)

if (isValidFlag) {
  // 解析表达式到UI组件
  parseCronExpression(initValue)

  // 根据表达式内容设置合适的激活标签页
  setActiveTabBasedOnExpression(initValue)

  // 更新UI显示
  updateUIDisplay()

  // 确保日期和星期的互斥性
  ensureDayWeekMutualExclusion()

  // 再次生成表达式，确保互斥性生效
  generateCron()

  // 计算未来执行时间
  nextExecutions.value = calculateNextExecutions(cronExpression.value)
} else {
  emit('error', validationError.value)
}

// 根据表达式内容设置合适的激活标签页
function setActiveTabBasedOnExpression(expression: string) {
  const parts = expression.split(' ')
  if (parts.length !== 6) return

  // 检查各部分是否有特殊值，决定激活哪个标签页

  // 特殊处理日期和星期的互斥性
  const dayPart = parts[3]
  const weekPart = parts[5]

  // 检查日期和星期是否有具体值
  const dayHasSpecificValue = dayPart !== '*' && dayPart !== '?'
  const weekHasSpecificValue = weekPart !== '*' && weekPart !== '?'

  // 优先根据日期和星期的值决定激活哪个标签页
  if (dayHasSpecificValue) {
    // 如果日期有具体值，显示日期标签页
    activeTab.value = 'days'
    return
  } else if (weekHasSpecificValue) {
    // 如果星期有具体值，显示星期标签页
    activeTab.value = 'weeks'
    return
  }

  // 如果秒不是0或*，激活秒标签页
  if (parts[0] !== '0' && parts[0] !== '*' && props.showSeconds) {
    activeTab.value = 'seconds'
    return
  }

  // 如果分钟不是0或*，激活分钟标签页
  if (parts[1] !== '0' && parts[1] !== '*') {
    activeTab.value = 'minutes'
    return
  }

  // 如果小时不是0或*，激活小时标签页
  if (parts[2] !== '0' && parts[2] !== '*') {
    activeTab.value = 'hours'
    return
  }

  // 如果月份不是*，激活月份标签页
  if (parts[4] !== '*') {
    activeTab.value = 'months'
    return
  }

  // 默认显示秒标签页
  activeTab.value = props.showSeconds ? 'seconds' : 'minutes'
}
</script>

<style scoped lang="scss">
.cron-editor {
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
  transition: all 0.3s;
}

.cron-editor-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.cron-tab-content {
  padding: 0 0 16px 0;
}

.cron-input-group {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cron-editor-footer {
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

.cron-expression {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  .label {
    white-space: nowrap;
  }
}

.cron-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
}

.cron-validation {
  margin-bottom: 12px;
}

.cron-next-executions {
  margin-top: 16px;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.next-execution-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.next-execution-list {
  list-style-type: none;
  padding-left: 0;
  margin: 0;
}

.next-execution-list li {
  padding: 4px 0;
  color: #666;
  border-bottom: 1px dashed #eee;
}

.next-execution-list li:last-child {
  border-bottom: none;
}

.cron-tab-description {
  margin: 8px 0 16px;
  padding: 8px 12px;
  background-color: #f0f7ff;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.cron-tab-description p {
  margin: 4px 0;
  color: #333;
  font-size: 14px;
  line-height: 1.5;
}

.next-execution-reason {
  padding: 10px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  background-color: #fffbe6;
  border-radius: 4px;
  border-left: 4px solid #faad14;
}
</style>

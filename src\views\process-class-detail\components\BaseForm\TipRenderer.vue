<template>
  <div style="width: max-content;">
    <!-- 自定义组件类型 -->
    <component
      v-if="normalizedTip.type === 'component'"
      :is="normalizedTip.config.component"
      v-bind="normalizedTip.config.props"
      :field-key="fieldKey"
      :field-value="fieldValue"
      :field-config="fieldConfig"
      :form-data="formData"
    />

    <!-- 文本类型 -->
    <MoreTextTips
      v-else
      v-bind="normalizedTip.config"
    >
      <span style="margin-left: 4px">
        <i class="iconfont icontubiao_tishi_mian" style="margin-right: 2px; font-size: 14px" />
        <span>{{ normalizedTip.text }}</span>
      </span>
    </MoreTextTips>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import MoreTextTips from '@/components/MoreTextTips/index'
import { normalizeTipConfig, type TipText } from './tipTypes'

interface Props {
  tipText: TipText
  fieldKey: string
  fieldValue: any
  fieldConfig: any
  formData: any
}

const props = defineProps<Props>()

// 标准化提示配置
const normalizedTip = computed(() => {
  return normalizeTipConfig(
    props.tipText,
    props.fieldValue,
    props.formData,
    props.fieldConfig
  )
})
</script>

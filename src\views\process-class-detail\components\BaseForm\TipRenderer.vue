<script lang="ts">
import { defineComponent, type PropType } from 'vue'
import { createTipRenderer, type TipText, type RenderContext } from './renderUtils'

// 全局渲染器实例 - 单例模式，避免重复创建
const globalTipRenderer = createTipRenderer()

export default defineComponent({
  name: 'TipRenderer',
  props: {
    tipText: {
      type: [String, Function, Object] as PropType<TipText>,
      required: true
    },
    fieldKey: {
      type: String,
      required: true
    },
    fieldValue: {
      type: null as any,
      default: undefined
    },
    fieldConfig: {
      type: Object,
      default: () => ({})
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },

  // 纯渲染函数实现 - 无模板，无响应式开销
  render() {
    const context: RenderContext = {
      fieldKey: this.fieldKey,
      fieldValue: this.fieldValue,
      fieldConfig: this.fieldConfig,
      formData: this.formData
    }

    // 直接调用渲染器，返回 VNode
    return globalTipRenderer(this.tipText, context)
  }
})
</script>

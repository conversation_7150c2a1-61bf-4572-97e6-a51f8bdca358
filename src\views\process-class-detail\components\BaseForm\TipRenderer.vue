<script lang="ts">
import { defineComponent, type PropType } from 'vue'
import { renderTip, type TipText } from './renderUtils'

export default defineComponent({
  name: 'TipRenderer',
  props: {
    tipText: {
      type: [String, Function, Object] as PropType<TipText>,
      required: true
    },
    fieldKey: {
      type: String,
      required: true
    },
    fieldValue: {
      type: null as any,
      default: undefined
    },
    fieldConfig: {
      type: Object,
      default: () => ({})
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },

  // 简化的渲染函数 - 直接调用核心渲染逻辑
  render() {
    return renderTip(
      this.tipText,
      this.fieldValue,
      this.formData,
      this.fieldConfig
    )
  }
})
</script>

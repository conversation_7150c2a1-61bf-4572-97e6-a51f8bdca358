<template>
  <div class="list-item">
    <span class="code-link" @click="onJumpDemandDetial(data)">{{ data?.processInstanceCode }}</span
    >/
    <span>{{ data?.topicName ?? '--' }}/</span>
    <span class="mr8">{{ data?.tsOwner ?? '--' }}</span>
    <FTooltip
      color="#fff"
      :getPopupContainer="target"
      overlayClassName="cust-relate-process-tooltip"
      @visibleChange="open => handleOpenChange(open, data)"
    >
      <template #title>
        <div class="content-box">
          <div class="header">评估报告产出进展</div>
          <div class="info">
            <FSpin :spinning="!!data?.loading">
              <div class="process-list">
                <div
                  class="flex space-between process-item"
                  v-for="(processItem, processIndex) in data?.processData ?? []"
                  :key="processIndex"
                >
                  <div class="flex left" :title="`${processItem?.topicName}/${processItem?.superviser}`">
                    <img
                      v-if="[3, 4].includes(processItem?.status)"
                      src="@/assets/images/process-icon/completed.svg"
                      class="mr8"
                    />
                    <img
                      v-else-if="[1, 2, 5].includes(processItem?.status)"
                      src="@/assets/images/process-icon/inProgress.svg"
                      class="mr8"
                    />
                    <img
                      v-else-if="[0].includes(processItem?.status)"
                      src="@/assets/images/process-icon/notStarted.svg"
                      class="mr8"
                    />
                    <span class="text-name">{{ processItem?.topicName }}/</span>
                    <span class="text-nowrap">{{ processItem?.superviser }}</span>
                  </div>
                  <span class="color999 text-nowrap">{{
                    (processItem?.completeTime && transformDate(processItem?.completeTime, 'YYYY-MM-DD HH:mm:ss')) ??
                    '--'
                  }}</span>
                </div>
              </div>
            </FSpin>
          </div>
        </div>
      </template>
      <i class="iconfont icontubiao_tishi colorBBB" />
    </FTooltip>
  </div>
</template>

<script setup lang="ts">
import { FSpin } from '@fs/smart-design'
import { ref } from 'vue'
import { getInstanceInfo } from '@/api'
import dayjs from 'dayjs'
import { transformDate, jumpToDemand } from '@/utils'
import { useRouter } from 'vue-router'

interface IProps {
  data: any
}

const router = useRouter()
const props = defineProps<IProps>()
const target = ref(() => document.querySelector('#container'))

const getProcessData = async item => {
  if (
    item?.loading ||
    item?.pending ||
    (item?.initStartTime && dayjs().diff(dayjs(item.initStartTime), 'seconds') <= 10)
  )
    return
  try {
    item.loading = true
    item.pending = true
    item['processData'] = []
    const { data = [] } = await getInstanceInfo({ instanceId: item?.id, isTree: 1 })
    item['processData'] = data
    item.initStartTime = new Date().getTime()
  } finally {
    item.loading = false
    item.pending = false
  }
}

const handleOpenChange = (open: any, item: any) => {
  open && getProcessData(item)
}

// 需求详情跳转
const onJumpDemandDetial = (record: any) => {
  jumpToDemand(record.id, undefined, true, router)
}
</script>

<style lang="scss">
.cust-relate-process-tooltip {
  max-width: 400px;
  width: 400px;
  .fs-tooltip-inner {
    padding: 0;
    .content-box {
      color: #333333;
      .header {
        padding: 12px 16px;
        border-bottom: 1px solid #eeeeee;
        font-weight: 500;
        font-size: 14px;
      }
      .info {
        padding: 16px;
        min-height: 100px;
        .left {
          max-width: 66%;
        }
        .text-name {
          max-width: 84%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .text-nowrap {
          white-space: nowrap;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.mt8 {
  margin-top: 8px;
}
.mr8 {
  margin-right: 8px;
}
.code-link {
  cursor: pointer;
  color: #378eef;
}
.flex {
  display: flex;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.process-item + .process-item {
  margin-top: 8px;
}
.list-item {
  .icontubiao_tishi {
    font-size: 14px;
    line-height: 16px;
  }
}
</style>

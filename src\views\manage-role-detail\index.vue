<template>
  <div class="manage-role-detail">
    <!-- <Breadcrumb class="none-container-padding" /> -->
    <FSpin :spinning="roleConfigLoading">
      <RoleInfo class="marginB24" :roleConfig="roleConfig" :roleId="roleId" :roleConfigLoading="roleConfigLoading" />
    </FSpin>
    <RelevanceInfo />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, provide, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getManageRole } from '@/api'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import RoleInfo from './components/RoleInfo/index.vue'
import RelevanceInfo from './components/RelevanceInfo/index.vue'

const router = useRouter()
const route = useRoute()
const roleConfigLoading = ref<boolean>(false)
const roleConfig = ref<any>({})
const roleId = computed(() => route.params.id as string)
const back = () => {
  router.push({ name: 'manageRole' })
}

const onGetByIdRoleConfig = async () => {
  if (!roleId.value) {
    throw new Error('角色ID不能为空')
  }
  try {
    roleConfigLoading.value = true
    const res = await getManageRole({ id: roleId.value })
    if (res.code !== 200) throw new Error(res.msg)
    roleConfig.value = res?.data ?? {}
  } finally {
    roleConfigLoading.value = false
  }
}

onMounted(() => {
  onGetByIdRoleConfig()
})

provide('roleConfig', roleConfig)
provide('onGetByIdRoleConfig', onGetByIdRoleConfig)
provide('roleId', roleId)
provide('setRoleConfigLoading', (loading: boolean) => {
  roleConfigLoading.value = loading
})
provide('back', back)
</script>

<style lang="scss" scoped>
.manage-role-detail {
  .none-container-padding {
    // margin-top: -20px;
  }
  .card-content-shadow {
    background: #ffffff;
    // box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  :deep(.fs-btn-sm) {
    padding: 0 8px !important;
    font-size: 12px !important;
  }
}
</style>

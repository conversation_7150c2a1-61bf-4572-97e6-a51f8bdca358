<template>
  <FModal v-model:visible="visible" :title="i18n.t('编辑')" :width="400" @cancel="cancelFn" @ok="submitFn">
    <div class="edit-time-box">
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FRow :gutter="[0, 0]">
          <FCol :span="24">
            <FFormItem :label="i18n.t('预计完成时间')" name="forcastTime">
              <FRangePicker v-model:value="formState.forcastTime" style="width: 100%" :disabled-date="disabledDate" />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('原因')" name="forcastTimeRemark">
              <FTextarea
                v-model:value="formState.forcastTimeRemark"
                :auto-size="{ minRows: 4 }"
                class="desc-box"
                placeholder="请输入"
              />
            </FFormItem>
          </FCol>
        </FRow>
      </FForm>
    </div>
  </FModal>
</template>
<script lang="ts" setup>
import { reactive, computed, ref, watch } from 'vue'
import dayjs, { Dayjs } from 'dayjs'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { useI18n } from '@/utils'

interface FormState {
  forcastTime?: Dayjs[]
  forcastTimeRemark?: string
}

interface IProps {
  visible: boolean
  forcastTime: any
  taskStartTime: any
  contentData: any
}

const i18n = useI18n()
const emits = defineEmits(['timePickerChange', 'update:visible'])
const props = defineProps<IProps>()
const disabledDate = (current: Dayjs) => current && current < dayjs().subtract(1, 'days').endOf('day')
const formState = reactive<FormState>({
  forcastTime: undefined,
  forcastTimeRemark: undefined,
})
const rules: Record<string, Rule[]> = {
  forcastTime: [{ required: true, message: i18n.t('请选择预计完成时间') }],
  forcastTimeRemark: [{ required: true, message: i18n.t('请输入备注') }],
}
const formRef = ref<FormInstance>()
const visible = computed({
  get: () => props.visible,
  set: val => emits('update:visible', val),
})

watch(
  () => props.visible,
  val => {
    if (val) {
      formState.forcastTime =
        (props.taskStartTime && props.forcastTime && [dayjs(props.taskStartTime), dayjs(props.forcastTime)]) ||
        undefined
      formState.forcastTimeRemark =
        (props.contentData &&
          JSON.parse(props.contentData as string) &&
          JSON.parse(props.contentData as string).forcastTimeRemark) ||
        undefined
    }
  }
)

const cancelFn = () => {
  formRef.value?.resetFields()
  visible.value = false
}

const submitFn = async () => {
  if (!formRef.value) {
    return
  }
  await formRef.value.validate()
  const params = {
    forcastTime: formState.forcastTime,
    forcastTimeRemark: formState.forcastTimeRemark,
  }
  emits('timePickerChange', params)
  cancelFn()
}
</script>
<style lang="scss" scoped>
.edit-time-box {
  :deep(.fs-form-item-control-input-content) {
    height: auto !important;
  }
}
</style>

import { request } from '@/utils'
import type { IRes, IResData, IClassDataList, IClassData, IClassParams, IProcessCollect } from '@/types/class'
interface IPageParmas {
  pageNum: number // 当前页
  pageSize: number // 每页条数
}
interface IQueryParams extends IPageParmas {
  processName?: null | string
  source?: number
}
export function createClass(data: IClassParams): Promise<IResData> {
  return request.post('/api/bpmDefine/saveProcessConfig', data)
}
export function updateClass(data: IClassParams): Promise<IResData> {
  return request.post('/api/bpmDefine/updateProcessConfig', data)
}
export function getClassList(data: IQueryParams): Promise<IRes<IClassDataList<IClassData>>> {
  return request.post('/api/bpmDefine/getProcess', data)
}
export function getQueryClassList(data: IQueryParams): Promise<IRes<IClassDataList<IClassData>>> {
  return request.post('/api/bpmDefine/querProcess', data)
}
export function getAllClassList(): Promise<IRes<IClassData[]>> {
  return request.get('/api/bpmDefine/getProcessAll')
}
export function getAllCreateProcessList(params = {}): Promise<IRes<IClassData[]>> {
  return request.get('/api/bpmDefine/getCreateProcess', { params })
}
// 查找流程收藏
export const getProcessCollect = async () => {
  const res = await request.get<IRes<IProcessCollect[]>>('/api/processCollect/get')
  return res as unknown as IRes<IProcessCollect[]>
}

// 保存流程收藏
export const saveProcessCollect = async (configId: number) => {
  const res = await request.get<IRes>('/api/processCollect/save?configId=' + configId)
  return res
}

// 删除流程收藏
export const deleteProcessCollect = async (id: number) => {
  const res = await request.get<IRes>('/api/processCollect/delete?id=' + id)
  return res
}

// 获取用户常用流程
export const getProcessCommon = async (size = 3) => {
  const res = await request.get<IRes<IClassData[]>>('/api/bpmDefine/getCommonProcess?size=' + size)
  return res as unknown as IRes<IClassData[]>
}

export const rollbackProcessConfig = async (data: any) => {
  const res = await request.post(`/api/processSyn/rollbackProcessConfig`, data)
  return res as unknown as IRes<any>
}

export const synProcessConfig = async (data: any) => {
  const res = await request.post(`/api/processSyn/synProcessConfig`, data)
  return res as unknown as IRes<any>
}

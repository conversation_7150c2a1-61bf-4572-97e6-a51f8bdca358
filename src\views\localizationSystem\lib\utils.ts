export const getDefaultValue = (
  list: any,
  keyData: any,
  childKey: string,
  parentKey: string,
  defaultName: any,
  getKey: string,
  stopKey: string
) => {
  list.forEach((item: any) => {
    let curKey = item[stopKey]
    if (keyData[parentKey]) {
      keyData[curKey] = JSON.parse(JSON.stringify(keyData[parentKey]))
      keyData[curKey].push(item[getKey])
    } else {
      keyData[curKey] = [item[getKey]]
    }
    if (item[stopKey] === defaultName) {
      return
    } else if (item[childKey] && item[childKey].length) {
      getDefaultValue(item[childKey], keyData, childKey, item[stopKey], defaultName, getKey, stopKey)
    }
  })
  return keyData
}

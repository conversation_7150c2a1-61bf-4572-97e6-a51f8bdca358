<template>
  <div class="export-template-configuration-filed-container">
    <div class="flex space-between align-items marginB16">
      <span class="title color333 fontSize12">导出字段</span>
      <div class="extra-box">
        <FSpace :size="[8]">
          <FButton
            v-if="templateId"
            size="small"
            @click="refFiledModal?.onOpenFn?.('saveExportTemplateField', templateId)"
          >
            <template #icon>
              <i class="iconfont icontubiao_tianjia1 marginR4"></i>
            </template>
            新增字段
          </FButton>
        </FSpace>
      </div>
    </div>

    <FTable
      class="cust-table"
      :loading="loading"
      :row-key="(record:any) => record.id"
      :columns="columns"
      :data-source="dataList"
      :sticky="{ offsetHeader: 0 }"
      :scroll="{ x: 'min-content' }"
      :customRow="customRow"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.key === 'icon'">
          <i class="iconfont icontubiao_tuodong1 color999 cursor"></i>
        </template>
        <template v-if="column.key === 'relatedDataType'">
          <span>{{
            (text === 0 && '流程数据字段') || (text === 1 && '规则字段') || (text === 2 && '系统字段') || '--'
          }}</span>
        </template>
        <template v-if="column.key === 'handle'">
          <FSpace :size="[8]">
            <TipBtn tip-title="编辑">
              <i
                class="iconfont icontubiao_xietongbianji cursor color4677C7 iconfont-hover fontSize14"
                @click="refFiledModal?.onOpenFn?.('updateExportTemplateField', templateId, record)"
              />
            </TipBtn>
            <TipBtn
              has-pop
              tip-title="删除"
              pop-title="确定删除选中的数据吗？"
              @onConfirmFn="onDeleteConfirmFn(record)"
            >
              <i class="iconfont icontubiao_xietongshanchu cursor color4677C7 iconfont-hover fontSize14"></i>
            </TipBtn>
          </FSpace>
        </template>
      </template>
    </FTable>
    <FiledModal ref="refFiledModal" @update-change="onGetByIdExportTemplateConfig" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message } from '@fs/smart-design'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import FiledModal from '../FiledModal/index.vue'
import { getByIdExportTemplateField, updateByIdExportTemplateFieldSort, deleteNodeField } from '@/api'

const props = defineProps<{
  templateId: any
}>()

const refFiledModal = ref()
const loading = ref<boolean>(false)
const columns = ref([
  {
    title: '',
    dataIndex: 'icon',
    key: 'icon',
    width: 50,
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    key: 'sortOrder',
    width: 100,
  },
  {
    title: '字段名称（表头）',
    dataIndex: 'fieldName',
    key: 'fieldName',
  },
  {
    title: '关联数据',
    dataIndex: 'relatedDataType',
    key: 'relatedDataType',
  },
  // {
  //   title: '关联字段',
  //   dataIndex: 'code',
  //   key: 'code',
  // },
  {
    title: '操作',
    dataIndex: 'handle',
    key: 'handle',
    width: 110,
    fixed: 'right',
  },
])
const dataList = ref([])

let dragIndex: any
let draggingRowKey: any
const customRow = (record: any, index: number) => {
  return {
    draggable: true,
    class: {
      'dragging-row': record.id === draggingRowKey,
    },
    onDragstart: e => {
      dragIndex = index
      draggingRowKey = record.id
      e.dataTransfer.effectAllowed = 'move'
    },
    onDragover: e => {
      e.preventDefault()
      const tr = e.currentTarget
      tr.classList.add('drag-over-row')
    },
    onDragleave: e => {
      e.currentTarget.classList.remove('drag-over-row')
    },
    onDrop: async e => {
      e.currentTarget.classList.remove('drag-over-row')
      if (dragIndex === index) return

      const newData = [...dataList.value]
      const movedRow = newData.splice(dragIndex, 1)[0]
      newData.splice(index, 0, movedRow)

      const step = Math.abs(index - dragIndex)
      const minNum = Math.min(index, dragIndex)
      const fields = []
      for (let i = minNum; i <= minNum + step; i++) {
        fields.push({ id: newData[i]?.id, sortOrder: i + 1 })
      }
      try {
        await updateByIdExportTemplateFieldSort(fields)
        dataList.value = newData
        onGetByIdExportTemplateConfig()
        message.success('排序已更新')
      } finally {
        dragIndex = null
        draggingRowKey = null
      }
    },
    onDragend: e => {
      draggingRowKey = null
    },
  }
}

const onDeleteConfirmFn = async (record: any) => {
  const res = await deleteNodeField([record.id], props.templateId)
  if (res.code !== 200) throw new Error(res.msg)
  message.success('删除成功')
  onGetByIdExportTemplateConfig()
}

const onGetByIdExportTemplateConfig = async () => {
  if (!props.templateId) {
    throw new Error('流程配置类ID不能为空')
  }
  try {
    loading.value = true
    const res = await getByIdExportTemplateField(props.templateId)
    if (res.code !== 200) throw new Error(res.msg)
    dataList.value = res?.data ?? []
  } finally {
    loading.value = false
  }
}

watch(
  () => props.templateId,
  newVal => {
    newVal && onGetByIdExportTemplateConfig()
  },
  { immediate: true }
)

defineExpose({
  dataList,
})
</script>

<style lang="scss" scoped>
.export-template-configuration-filed-container {
  .line {
    margin: 16px 0;
    border-bottom: 1px dashed #eee;
  }
  :deep(.dragging-row td) {
    opacity: 0.5;
    background-color: #f5f5f5 !important;
  }

  :deep(.drag-over-row td) {
    background-color: #e6f7ff !important;
    transition: background-color 0.2s ease;
  }
}
.card-content-header {
  padding: 16px 24px;
}
.flex {
  display: flex;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.align-items {
  align-items: center;
}
.padding24 {
  padding: 24px;
}
</style>

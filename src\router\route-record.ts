import type { RouteRecordRaw } from 'vue-router'

export const viewRouter: RouteRecordRaw[] = [
  {
    path: '/demand/add/:id/:processDefineKey',
    name: 'DemandAdd',
    component: () => import(/* webpackChunkName: "DemandAdd" */ '@/views/demand-add/index.vue'),
    meta: { title: '需求新增' },
  },
  {
    path: '/demand/handle/:id',
    name: 'DemandHandle',
    component: () => import(/* webpackChunkName: "DemandHandle" */ '@/views/process-detail/index.vue'),
    meta: { title: '需求处理' },
  },
  {
    path: '/process/detail/:id',
    name: 'ProcessDetail',
    component: () => import(/* webpackChunkName: "ProcessDetail" */ '@/views/process-operate/index.vue'),
    meta: { title: '流程处理' },
  },
  {
    path: '/process/feishu/detail/:processId/:nodeId/:fromId',
    name: 'FeiShuProcessDetail',
    component: () => import(/* webpackChunkName: "FeiShuProcessDetail" */ '@/views/process-operate-feishu/index.vue'),
    meta: { title: '流程处理', hideLayout: true },
  },
  {
    path: '/process/class',
    name: 'ProcessClass',
    component: () => import(/* webpackChunkName: "ProcessClass" */ '@/views/process-class/index.vue'),
    meta: { title: '流程类型' },
  },
  {
    path: '/process/list',
    name: 'ProcessList',
    component: () => import(/* webpackChunkName: "ProcessList" */ '@/views/process-list/index.vue'),
    meta: { title: '流程列表' },
  },
  {
    path: '/process/:type/list',
    name: 'ProcessListOne',
    component: () => import(/* webpackChunkName: "ProcessListOne" */ '@/views/process-list-one/index.vue'),
    meta: { title: '流程列表' },
  },
  {
    path: '/execution/list',
    name: 'ExecutionList',
    component: () => import(/* webpackChunkName: "ExecutionList" */ '@/views/execution-list/index.vue'),
    meta: { title: '执行记录' },
  },
  {
    path: '/automation/list',
    name: 'AutoMationList',
    component: () => import(/* webpackChunkName: "AutoMationList" */ '@/views/trigger/trigger-list.vue'),
    meta: { title: '触发器列表' },
  },
  {
    path: '/excution-edit',
    name: 'ExecutionEdit',
    component: () => import(/* webpackChunkName: "ExecutionEdit" */ '@/views/trigger/edit-list.vue'),
    meta: { title: '添加修改触发器' },
  },
  {
    path: '/process/label',
    name: 'ProcessLabel',
    component: () => import(/* webpackChunkName: "ProcessLabel" */ '@/views/process-label/index.vue'),
    meta: { title: '流程标签' },
  },
  {
    path: '/dictionary',
    name: 'Dictionary',
    component: () => import(/* webpackChunkName: "Dictionary" */ '@/views/dictionary/index.vue'),
    redirect: '/dictionary/home',
    children: [
      {
        path: '/dictionary/home',
        name: 'DictionaryHome',
        component: () => import(/* webpackChunkName: "DictionaryHome" */ '@/views/dictionary/HomeDictonary.vue'),
        meta: { title: '字典管理' },
      },
      {
        path: '/dictionary/search',
        name: 'DictionarySearch',
        component: () => import(/* webpackChunkName: "DictionarySearch" */ '@/views/dictionary/SearchDictonary.vue'),
        meta: { title: '搜索项字典管理' },
      },
    ],
  },
  {
    path: '/workbench',
    name: 'Workbench',
    component: () => import(/* webpackChunkName: "Workbench" */ '@/views/workbench/index.vue'),
    meta: { title: '工作台' },
  },
  {
    path: '/rolelist',
    name: 'rolelist',
    component: () => import(/* webpackChunkName: "rolelist" */ '@/views/role-list/index.vue'),
    meta: { title: '角色列表' },
  },
  {
    path: '/localizationSystem/wordbenchBoard',
    name: 'singapore',
    component: () => import(/* webpackChunkName: "singapore" */ '@/views/localizationSystem/wordbenchBoard/index.vue'),
    meta: { title: '本地化工作台' },
  },
  {
    path: '/localizationSystem/dataBoard',
    name: 'dataBoard',
    component: () => import(/* webpackChunkName: "dataBoard" */ '@/views/localizationSystem/dataBoard/index.vue'),
    meta: { title: '本地化数据看板' },
  },
  {
    path: '/process/setSeachConfig',
    name: 'setSeachConfig',
    component: () => import(/* webpackChunkName: "setSeachConfig" */ '@/views/set-seach-config/index.vue'),
    meta: { title: '配置筛选项列表' },
  },
  {
    path: '/pgb/dataBoard',
    name: 'pgbDataBoard',
    component: () => import(/* webpackChunkName: "pgbDataBoard" */ '@/views/pgb-data-board/index.vue'),
    meta: { title: 'PBG看板' },
  },
  {
    path: '/message-list',
    name: 'messageList',
    component: () => import(/* webpackChunkName: "messageList" */ '@/views/message-list/index.vue'),
    meta: { title: '消息列表' },
  },
  {
    path: '/process-board',
    name: 'processBoard',
    component: () => import(/* webpackChunkName: "processBoard" */ '@/views/process-board/index.vue'),
    meta: { title: '项目看板' },
  },
  {
    path: '/product-remove-list',
    name: 'productRemoveList',
    component: () => import(/* webpackChunkName: "productRemoveList" */ '@/views/product-remove-list/index.vue'),
    meta: { title: '产品停产下架' },
  },
  {
    path: '/entity-configuration-list',
    name: 'entityConfigurationList',
    component: () =>
      import(/* webpackChunkName: "entityConfigurationList" */ '@/views/entity-configuration-list/index.vue'),
    meta: { title: '实体配置' },
  },
  {
    path: '/entity-configuration-detail',
    name: 'entityConfigurationDetail',
    component: () =>
      import(/* webpackChunkName: "entityConfigurationDetail" */ '@/views/entity-configuration-detail/index.vue'),
    meta: { title: '实体详情' },
  },
  {
    path: '/entity-list/:id',
    name: 'entityList',
    component: () => import(/* webpackChunkName: "entityList" */ '@/views/entity-list/index.vue'),
    meta: { title: '实体列表' },
  },
  {
    path: '/publish-log',
    name: 'publishLog',
    component: () => import(/* webpackChunkName: "messageTemplate" */ '@/views/publish-log/index.vue'),
    meta: { title: '发布日志' },
  },
  {
    path: '/message-template',
    name: 'messageTemplate',
    component: () => import(/* webpackChunkName: "messageTemplate" */ '@/views/message-template/index.vue'),
    meta: { title: '消息模板' },
  },
  {
    path: '/process-management/:id/:processDefineKey/:demandType',
    name: 'processManagement',
    component: () => import(/* webpackChunkName: "processManagement" */ '@/views/process-management/index.vue'),
    meta: { title: '需求流程管理' },
  },
  {
    path: '/approval/detail/:id',
    name: 'ApprovalDetail',
    component: () => import('@/views/approval-operate/index.vue'),
    meta: { title: '审批处理' },
  },
  {
    path: '/approval-workbench',
    name: 'approvalWorkbench',
    component: () => import(/* webpackChunkName: "messageTemplate" */ '@/views/approval-workbench/index.vue'),
    meta: { title: '审批工作台' },
  },
  {
    path: '/dashboard/:id',
    name: 'dashboard',
    component: () => import(/* webpackChunkName: "processManagement" */ '@/views/dashboard/index.vue'),
    meta: { title: 'BPM看板' },
  },
  {
    path: '/process/create',
    name: 'processCreate',
    component: () => import(/* webpackChunkName: "processManagement" */ '@/views/process-create/index.vue'),
    meta: { title: '流程创建', hideLayout: true },
  },
  {
    path: '/process/info',
    name: 'processDetail',
    component: () => import(/* webpackChunkName: "processManagement" */ '@/views/process-operate-sap/index.vue'),
    meta: { title: '流程详情', hideLayout: true },
  },
  {
    path: '/process/ipdList',
    name: 'processIpdList',
    component: () => import('@/views/process-ipd-list/index.vue'),
    meta: {
      breadcrumbs: [{ title: '流程管理', path: '/process/list' }, { title: 'IPD流程管理' }],
      title: 'IPD流程管理',
    },
  },
  {
    path: '/process/mmList',
    name: 'processMmList',
    component: () => import('@/views/process-mm-list/index.vue'),
    meta: {
      breadcrumbs: [{ title: '流程管理', path: '/process/list' }, { title: 'MM流程管理' }],
      title: 'MM流程管理',
    },
  },

  {
    path: '/process/class/:id',
    name: 'processClassDetail',
    component: () => import('@/views/process-class-detail/index.vue'),
    meta: {
      breadcrumbs: [{ title: '流程配置', path: '/process/class' }, { title: '流程配置管理' }],
      title: '流程配置管理',
    },
  },
  {
    path: '/process/staffmeetingList',
    name: 'processStaffmeetingList',
    component: () => import('@/views/process-staffmeeting-list/index.vue'),
    meta: {
      breadcrumbs: [{ title: '流程管理', path: '/process/list' }, { title: '员工大会流程管理' }],
      title: '员工大会流程管理',
    },
  },
  {
    path: '/modify-board',
    name: 'modifyBoard',
    component: () => import(/* webpackChunkName: "modifyBoard" */ '@/views/modify-board/index.vue'),
    meta: {
      breadcrumbs: [{ title: '首页', path: '/process/list' }, { title: 'Modify看板' }],
      title: 'Modify看板',
    },
  },
  {
    path: '/project-board/:type',
    name: 'projectBoard',
    component: () => import(/* webpackChunkName: "modifyBoard" */ '@/views/project/index.vue'),
    meta: {
      breadcrumbs: [{ title: '项目看板', path: '/process/list' }, { title: '组织发展流程看板' }],
      title: '组织发展流程看板',
    },
  },
  {
    path: '/process/exception-flow/:type',
    name: 'exceptionFlow',
    component: () => import('@/views/exception-flow/index.vue'),
    meta: {
      breadcrumbs: [{ title: '流程配置', path: '/process/exception-flow' }, { title: '异常流程查询' }],
      title: '异常流程查询',
    },
  },
  {
    path: '/process/list/:type',
    name: 'processListNew',
    component: () => import(/* webpackChunkName: "modifyBoard" */ '@/views/process-list-new/index.vue'),
    meta: {
      // breadcrumbs: [{ title: '首页', path: '/process/list' }, { title: 'Modify看板' }],
      title: '流程列表（新）',
    },
  },
  {
    path: '/manageDictionary',
    name: 'manageDictionary',
    component: () => import('@/views/manage-dictionary/index.vue'),
    meta: {
      breadcrumbs: [{ title: '流程配置', path: '/process/list' }, { title: '字典管理' }],
      title: '字典管理',
    },
  },
  {
    path: '/manageRole',
    name: 'manageRole',
    component: () => import('@/views/manage-role/index.vue'),
    meta: {
      breadcrumbs: [{ title: '首页', path: '/process/list' }, { title: '角色管理' }],
      title: '角色管理',
    },
  },
  {
    path: '/manageRoleDetail/:id',
    name: 'manageRoleDetail',
    component: () => import('@/views/manage-role-detail/index.vue'),
    meta: {
      breadcrumbs: [
        { title: '首页', path: '/process/list' },
        { title: '角色列表', path: '/manageRole' },
        { title: '角色详情' },
      ],
      title: '角色详情',
    },
  },
  {
    path: '/approvalList',
    name: 'approvalList',
    component: () => import('@/views/approval-list/index.vue'),
    meta: {
      breadcrumbs: [{ title: '首页', path: '/process/list' }, { title: '审批列表' }],
      title: '审批列表',
    },
  },
]

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'MainLayout',
    redirect: '/process/list',
    component: () => import('@/layout/index.vue'),
    children: [...viewRouter],
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/403/index.vue'),
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404/index.vue'),
  },
  {
    path: '/:pathMatch(.*)',
    redirect: '/404',
  },
]

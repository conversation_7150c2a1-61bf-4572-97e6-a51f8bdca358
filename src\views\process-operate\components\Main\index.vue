<template>
  <div class="process-operate-main">
    <FlowChart :value="activeMilepostId" :data="(processInfo as IProcess[])" @change="setCurrtMilepost" />
    <Milepost :data="currtMilepost" />
    <Corpus />
  </div>
</template>

<script setup lang="ts">
import { inject, ComputedRef, Ref, computed } from 'vue'
import { IProcess } from '@/types/handle'

import FlowChart from './components/FlowChart/index.vue'
import Milepost from './components/Milepost/index.vue'
import Corpus from './components/Corpus/index.vue'

// const processId = inject<number>('processId') // 流程 id
// const processNo = inject<ComputedRef<string>>('processNo') // 流程编号
// const processName = inject<ComputedRef<string>>('processName') // 流程名称
// const processType = inject<ComputedRef<string>>('processType') // 流程类型
const processInfo = inject<ComputedRef<IProcess[]>>('processInfo') // 流程信息
// const processRoleInfo = inject<Ref<IProcessRoleAndUser[]>>('processRoleInfo') // 流程角色信息

const currtMilepost = inject<Ref<IProcess>>('currtMilepost') // 当前里程碑信息
const setCurrtMilepost = inject('setCurrtMilepost') as (data: IProcess) => void // 设置当前里程碑信息

const activeMilepostId = computed(() => currtMilepost?.value?.id || '') // 当前激活的里程碑 key
</script>

<style scoped lang="scss"></style>

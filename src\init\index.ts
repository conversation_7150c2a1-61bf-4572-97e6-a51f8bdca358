import type { App } from 'vue'
import { message } from '@fs/smart-design'
import { Login, Power, type IRes } from '@fs/hooks'
import { POWER_ENV, debounce, getToken, getUserInfo, logout } from '@/utils'
import store from '@/store'

export const i18n = { t: (str: string) => str }
export let power: Power
export let login: Login

export const setInit = (app: App) => {
  const debouncedErrorMessage = debounce((msg: string) => {
    message.error(msg)
  })

  login = new Login({
    env: POWER_ENV,
    token: () => getToken(),
    isNotInitUpdateToken: true,
  })
  const _fetch = login.fetch
  login.fetch = async <T = null>(input: RequestInfo | URL, init?: RequestInit) => {
    const res = await _fetch<T>(input, init)
    if ((res as IRes).code === 200) return res
    if ((res as IRes).code === 401) logout()
    if ((res as IRes).code !== 200) debouncedErrorMessage('SSO Login Api Error：' + (res as IRes<T>).msg)
    throw new Error((res as IRes<T>).msg)
  }

  power = new Power({
    env: POWER_ENV,
    token: () => getToken(),
    userInfo: () => getUserInfo(),
    appid: 'bpm',
    // isPrecise: true,
  })

  power.interceptor.request = async <T = unknown>(config: T) => {
    ;(config as { headers: Record<string, string> }).headers.token = getToken()
    return config
  }

  power.interceptor.response = res => {
    const { code, msg = '服务器繁忙，请稍后重试！' } = res as IRes
    if (code === 200) return res
    if (code === 401) logout()
    if (code !== 200) debouncedErrorMessage(msg)
    throw new Error(msg)
  }

  app.use(login)
  app.use(power)
}

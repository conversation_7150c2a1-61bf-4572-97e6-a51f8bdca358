import ProcessTip from './ProcessTip.vue'
import DynamicTip from './DynamicTip.vue'

// 使用示例配置
export const exampleFormConfig = {
  // 1. 传统字符串方式（保持向后兼容）
  processName: {
    component: 'CustomInput',
    dataType: 'input',
    fieldKey: 'processName',
    label: '流程名称',
    required: true,
    tipText: '请输入流程的完整名称，建议使用中文描述',
    componentAttrs: {
      placeholder: '请输入流程名称',
    },
  },

  // 2. 函数方式 - 动态生成提示文本
  processDefineKey: {
    component: 'CustomSelect',
    dataType: 'select',
    fieldKey: 'processDefineKey',
    label: '关联流程图',
    required: true,
    tipText: (fieldValue: any, formData: any, fieldConfig: any) => {
      if (!fieldValue) {
        return '请选择关联的流程图，这将决定流程的执行路径'
      }
      return `已选择流程图ID: ${fieldValue}，当前流程将按此图执行`
    },
    componentAttrs: {
      placeholder: '请选择关联流程图',
      options: [], // 这里应该是实际的选项数据
      fieldNames: { label: 'processName', value: 'id' },
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'processName',
    },
  },

  // 3. 自定义组件方式 - 使用 ProcessTip 组件
  processDescription: {
    component: 'CustomTextArea',
    dataType: 'textarea',
    fieldKey: 'processDescription',
    label: '流程描述',
    required: false,
    tipText: {
      component: ProcessTip,
      props: {
        baseText: '流程描述帮助',
        showValue: true,
        valuePrefix: '当前字符数：',
        valueSuffix: '字'
      }
    },
    componentAttrs: {
      placeholder: '请输入流程描述',
      maxlength: 500,
      showCount: true,
    },
  },

  // 4. 复杂自定义组件方式 - 使用 DynamicTip 组件
  processType: {
    component: 'CustomSelect',
    dataType: 'select',
    fieldKey: 'processType',
    label: '流程类型',
    required: true,
    tipText: {
      component: DynamicTip,
      props: {
        title: '流程类型说明',
        text: '类型详情',
        type: 'info',
        showRelatedProcesses: true,
        showValidation: true,
        customSuggestions: [
          '不同类型的流程有不同的审批规则',
          '选择后将自动配置相关参数'
        ]
      }
    },
    componentAttrs: {
      placeholder: '请选择流程类型',
      options: [
        { label: '审批流程', value: 'approval' },
        { label: '业务流程', value: 'business' },
        { label: '系统流程', value: 'system' }
      ],
    },
  },

  // 5. 带有复杂逻辑的函数方式
  processLevel: {
    component: 'CustomSelect',
    dataType: 'select',
    fieldKey: 'processLevel',
    label: '流程级别',
    required: true,
    tipText: (fieldValue: any, formData: any, fieldConfig: any) => {
      const levelMap = {
        1: '一级流程：需要最高级别审批',
        2: '二级流程：需要部门级别审批',
        3: '三级流程：普通审批流程'
      }
      
      if (!fieldValue) {
        return '请选择流程级别，不同级别有不同的审批权限要求'
      }
      
      const description = levelMap[fieldValue] || '未知级别'
      const relatedProcess = formData.processDefineKey ? `，关联流程：${formData.processDefineKey}` : ''
      
      return `${description}${relatedProcess}`
    },
    componentAttrs: {
      placeholder: '请选择流程级别',
      options: [
        { label: '一级', value: 1 },
        { label: '二级', value: 2 },
        { label: '三级', value: 3 }
      ],
    },
  },

  // 6. 条件显示的自定义组件
  processConfig: {
    component: 'CustomInput',
    dataType: 'input',
    fieldKey: 'processConfig',
    label: '流程配置',
    required: false,
    tipText: {
      component: DynamicTip,
      props: {
        title: '配置说明',
        text: '配置帮助',
        type: 'warning',
        customSuggestions: [
          '配置格式应为JSON字符串',
          '留空将使用默认配置'
        ]
      }
    },
    // 只有当流程类型为 'system' 时才显示
    isShowComponent: (item: any, formData: any) => {
      return formData.processType === 'system'
    },
    componentAttrs: {
      placeholder: '请输入JSON格式的配置',
    },
  }
}

// 类型定义
export interface TipTextConfig {
  component: any // Vue 组件
  props?: Record<string, any> // 传递给组件的属性
}

export type TipTextType = 
  | string // 简单字符串
  | ((fieldValue: any, formData: any, fieldConfig: any) => string) // 函数返回字符串
  | TipTextConfig // 自定义组件配置

// 使用示例的辅助函数
export const createTipConfig = (
  component: any, 
  props: Record<string, any> = {}
): TipTextConfig => {
  return {
    component,
    props
  }
}

// 预设的常用提示配置
export const commonTipConfigs = {
  // 必填字段提示
  required: (fieldName: string) => `${fieldName}为必填项，请确保填写完整`,
  
  // 格式验证提示
  format: (format: string) => `请按照${format}格式填写`,
  
  // 长度限制提示
  length: (min?: number, max?: number) => {
    if (min && max) return `长度应在${min}-${max}个字符之间`
    if (min) return `长度不能少于${min}个字符`
    if (max) return `长度不能超过${max}个字符`
    return '请注意字符长度限制'
  },
  
  // 选择项提示
  select: (description: string) => `请选择合适的选项。${description}`,
  
  // 自定义提示组件配置
  customTip: (text: string, type: 'info' | 'warning' | 'error' | 'success' = 'info') => 
    createTipConfig(ProcessTip, { baseText: text, showValue: true }),
  
  // 动态提示组件配置
  dynamicTip: (
    title: string, 
    suggestions: string[] = [], 
    type: 'info' | 'warning' | 'error' | 'success' = 'info'
  ) => createTipConfig(DynamicTip, {
    title,
    text: '查看详情',
    type,
    customSuggestions: suggestions,
    showValidation: true
  })
}

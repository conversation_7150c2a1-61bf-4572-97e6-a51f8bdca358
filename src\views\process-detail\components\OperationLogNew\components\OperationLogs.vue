<template>
  <FSpin :spinning="loading">
    <div class="operation-logs-container">
      <div class="operation-box" v-for="item of operationLogs" :key="item.createTime">
        <div class="time">{{ item.createTime }}</div>
        <div class="line"></div>
        <div class="content">
          <div class="title">
            <span>{{ i18n.t('由') + item.createUser }} {{ item.typeName }}</span>
            <i
              :class="['iconfont', item.show ? 'icontubiao_shanjian1' : 'icontubiao_tianjia21']"
              @click="() => (item.show = !item.show)"
            ></i>
          </div>
          <div v-show="item.show" class="info">{{ item.content }}</div>
        </div>
      </div>
    </div>
  </FSpin>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { operationRecord } from '@/api/handle'
import { transformDate, useI18n } from '@/utils'

interface IOperation {
  content: string
  createTime: string
  createUser: string
  type: number
  [key: string]: any
}

const i18n = useI18n()
const TYPE_MAP = {
  0: i18n.t('创建需求'),
  1: i18n.t('修改'),
  2: i18n.t('删除'),
  3: i18n.t('转派任务'),
  4: i18n.t('完成任务'),
  5: i18n.t('保存'),
  6: i18n.t('办结'),
  7: i18n.t('添加子节点'),
  8: i18n.t('添加子任务'),
  9: i18n.t('审核完成'),
  10: i18n.t('驳回任务'),
  11: i18n.t('转派里程碑'),
  12: i18n.t('完成里程碑'),
  13: i18n.t('编辑子节点'),
  14: i18n.t('关联流程'),
  15: i18n.t('解除关联流程'),
  16: i18n.t('驳回里程碑'),
  18: i18n.t('添加角色'),
  19: i18n.t('修改角色'),
  20: i18n.t('删除角色'),
  21: i18n.t('回滚任务'),
  22: i18n.t('重新排期'),
  23: i18n.t('修改预计完成时间'),
}

const props = defineProps({
  instanceId: {
    type: [String, Number],
    default: '',
  },
})
const operationLogs = ref<IOperation[]>([])
const loading = ref(false)
const pagination = ref({
  pageNo: 1,
  pageSize: 9999, // 默认每页显示数量
})

onMounted(() => {
  fetchLogs()
})

// 获取操作日志
const fetchLogs = async () => {
  try {
    operationLogs.value = []
    loading.value = true
    const parmas = {
      instanceId: props.instanceId,
      pageNum: pagination.value.pageNo,
      pageSize: pagination.value.pageSize,
    }
    const res = await operationRecord(parmas)
    if (res.code === 200) {
      operationLogs.value = res.data.list.map((item: IOperation) => {
        return {
          ...item,
          show: false,
          createTime: transformDate(item.createTime),
          typeName: TYPE_MAP[item.type as keyof typeof TYPE_MAP],
        }
      })
    }
  } finally {
    loading.value = false
  }
}
</script>
<style lang="scss" scoped>
.operation-logs-container {
  min-height: 50px;
  max-height: 260px;
  overflow-y: scroll;
  .operation-box {
    position: relative;
    display: flex;
    padding-bottom: 16px;
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 18px;
    &:last-child {
      padding-bottom: 0;
      .line {
        &::after {
          content: '';
          display: none;
        }
      }
    }
    .line {
      width: 6px;
      height: 6px;
      margin: 6px 16px 0 16px;
      background: #378eef;
      border-radius: 3px;
      &::after {
        content: '';
        position: absolute;
        width: 1px;
        height: calc(100% - 20px);
        margin-top: 12px;
        margin-left: 3px;
        transform: translateX(-50%);
        background: #eeeeee;
      }
    }
    .content {
      .title {
        display: flex;
        align-items: center;
        color: #333333;
        i {
          margin-left: 4px;
          color: #999999;
          cursor: pointer;
        }
      }
      .info {
        padding-top: 4px;
        padding-left: 8px;
        color: #666666;
      }
    }
  }
}
</style>

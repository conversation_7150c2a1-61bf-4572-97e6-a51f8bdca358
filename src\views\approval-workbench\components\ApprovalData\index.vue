<template>
  <FSpin :spinning="approvalDataLoading">
    <FCard class="apprival-data-container" :title="i18n.t('流程诊断')">
      <template #extra>
        <FSelect
          class="cust-select"
          :dropdown-match-select-width="false"
          :placeholder="i18n.t('请选择时段')"
          v-model:value="time"
          :options="timeList"
          @change="getApprovalDataFn"
        />
      </template>
      <FRow :gutter="[16, 16]">
        <FCol :span="24">
          <div class="apprival-box">
            <div class="box">
              <span class="title">{{ i18n.t('流程平均耗时') }}</span>
              <div class="data-content">
                <span class="num">{{ (approvalData?.processAveTime ?? 0).toFixed(2) }}</span>
                <span class="unit no-wrap">{{ i18n.t('小时') }}</span>
                <img
                  v-if="Number((approvalData?.processAveTimeRatio ?? 0).toFixed(2)) > 0"
                  src="../../images/up_red.png"
                />
                <img v-else src="../../images/up_green.png" />
                <span
                  :style="{
                    color: (Number((approvalData?.processAveTimeRatio ?? 0).toFixed(2)) > 0 && '#F04141') || '#2FCC83',
                  }"
                  >{{ (approvalData?.processAveTimeRatio ?? 0).toFixed(2) }}%</span
                >
              </div>
            </div>
            <div class="box paddingL16">
              <span class="line"></span>
              <span class="title">{{ i18n.t('审核平均耗时') }}</span>
              <div class="data-content">
                <span class="num">{{ (approvalData?.auditingAveTime ?? 0).toFixed(2) }}</span>
                <span class="unit no-wrap">{{ i18n.t('小时') }}</span>
                <img
                  v-if="Number((approvalData?.auditingAveRatio ?? 0).toFixed(2)) > 0"
                  src="../../images/up_red.png"
                />
                <img v-else src="../../images/up_green.png" />
                <span
                  :style="{
                    color: (Number((approvalData?.auditingAveRatio ?? 0).toFixed(2)) > 0 && '#F04141') || '#2FCC83',
                  }"
                  >{{ (approvalData?.auditingAveRatio ?? 0).toFixed(2) }}%</span
                >
              </div>
            </div>
          </div>
        </FCol>
        <FCol :span="24">
          <div class="apprival-box">
            <div class="box">
              <span class="title">{{ i18n.t('所有流程完成率') }}</span>
              <div class="data-content">
                <span class="num">{{ (approvalData?.complete ?? 0).toFixed(2) }}</span>
                <span class="unit">%</span>
                <img v-if="Number((approvalData?.completeRatio ?? 0).toFixed(2)) > 0" src="../../images/up_red.png" />
                <img v-else src="../../images/up_green.png" />
                <span
                  :style="{
                    color: (Number((approvalData?.completeRatio ?? 0).toFixed(2)) > 0 && '#F04141') || '#2FCC83',
                  }"
                  >{{ (approvalData?.completeRatio ?? 0).toFixed(2) }}%</span
                >
              </div>
            </div>
            <div class="box paddingL16">
              <span class="line"></span>
              <span class="title">{{ i18n.t('审批中') }}</span>
              <div class="data-content">
                <span class="num">{{ approvalData?.waitDeal ?? 0 }}</span>
                <span class="unit">{{ i18n.t('单') }}</span>
                <FTag :border="false" color="success"
                  >{{ i18n.t('已完结') }} {{ approvalData?.processed ?? 0 }} {{ i18n.t('单') }}</FTag
                >
              </div>
            </div>
          </div>
        </FCol>
        <FCol :span="24">
          <div class="apprival-box">
            <div class="box">
              <span class="title">{{ i18n.t('超48小时待办') }}</span>
              <div class="data-content">
                <span class="num">{{ approvalData?.timeout ?? 0 }}</span>
                <span class="unit">{{ i18n.t('单') }}</span>
              </div>
            </div>
            <div class="box paddingL16">
              <span class="line"></span>
              <span class="title">{{ i18n.t('等待超7天') }}</span>
              <div class="data-content">
                <span class="num">{{ approvalData?.longTimeout ?? 0 }}</span>
                <span class="unit">{{ i18n.t('单') }}</span>
              </div>
            </div>
          </div>
        </FCol>
      </FRow>
    </FCard>
  </FSpin>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from '@/utils'
import { getApprovalDiagnosis, PageApprovalDiagnosisRes } from '@/api'
import dayjs from 'dayjs'

const i18n = useI18n()
const approvalDataLoading = ref<boolean>(false)
const approvalData = ref<PageApprovalDiagnosisRes>()
const time = ref(1)
const timeList = ref([
  { label: i18n.t('近7天'), value: 1 },
  { label: i18n.t('近14天'), value: 2 },
  { label: i18n.t('近一个月'), value: 3 },
  { label: i18n.t('近六个月'), value: 4 },
  { label: i18n.t('全部时间'), value: 5 },
])

const getTimeRange = option => {
  const now = new Date()
  const endTime = new Date(now)
  let startTime = new Date(now)
  switch (option) {
    case 1:
      startTime.setDate(now.getDate() - 7)
      break
    case 2:
      startTime.setDate(now.getDate() - 14)
      break
    case 3:
      startTime.setMonth(now.getMonth() - 1)
      break
    case 4:
      startTime.setMonth(now.getMonth() - 6)
      break
    case 5:
      startTime = new Date(0)
      break
    default:
      throw new Error('Invalid option')
  }
  startTime.setHours(0, 0, 0, 0)
  endTime.setHours(23, 59, 59, 999)
  return {
    startTime: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
  }
}

const getApprovalDataFn = async () => {
  try {
    approvalDataLoading.value = true
    const res = await getApprovalDiagnosis(getTimeRange(time.value))
    if (res.code !== 200) throw new Error(res.msg)
    approvalData.value = res?.data
  } finally {
    approvalDataLoading.value = false
  }
}

onMounted(() => {
  requestIdleCallback(getApprovalDataFn)
})
</script>

<style scoped lang="scss">
.apprival-data-container {
  border-radius: 4px;
  overflow: hidden;
  :deep(.fs-card-body) {
    max-height: 429px;
    overflow-y: auto;
    padding: 16px !important;
  }
  :deep(.cust-select) {
    .fs-select-selector {
      box-shadow: none !important;
      border: none;
    }
  }
  .no-wrap {
    white-space: nowrap;
  }
  .apprival-box {
    display: flex;
    padding: 12px;
    background: #ebf3fd4d;
    border-radius: 4px;
    font-size: 12px;
    .paddingL24 {
      padding-left: 24px;
    }
    .box {
      position: relative;
      flex: 1;
      .line {
        position: absolute;
        top: -10px;
        left: 0;
        height: 100%;
        width: 1px;
        background: #eeeeee;
      }
      .title {
        color: #666666;
      }
      .data-content {
        display: flex;
        flex-wrap: nowrap;
        align-items: baseline;
        margin-top: 8px;
        .num {
          font-size: 20px;
          color: #333333;
        }
        .unit {
          color: #999999;
          margin-right: 8px;
          margin-left: 4px;
        }
        img {
          width: 12px;
          margin-right: 8px;
        }
      }
    }
    .paddingL16 {
      padding-left: 16px;
    }
  }
}
</style>

<template>
  <div class="content-board-container">
    <FSpin size="large" :spinning="countLoading">
      <div class="row-box marginB16">
        <div class="row-box flexW60">
          <div class="board-item shadow-card marginR16 flexW50">
            <div class="title">项目总数</div>
            <BaseEchart :option="projectCoutChartOption" width="100%" height="calc(20vw - 64px)" />
          </div>
          <div class="board-item shadow-card marginR16 flexW50">
            <div class="title">业绩总金额</div>
            <BaseEchart :option="projectMoneyCoutChartOption" width="100%" height="calc(20vw - 64px)" />
          </div>
        </div>

        <div class="board-item shadow-card flexW40">
          <div class="title">需求成单</div>
          <BaseEchart :option="orderCoutChartOption" width="100%" height="calc(20vw - 64px)" />
        </div>
      </div>
      <div class="row-box marginB16">
        <div class="board-item shadow-card marginR16 flexW20">
          <div class="title">项目需求类型</div>
          <BaseEchart :option="projectDemandTypeChartOption" width="100%" height="calc(20vw - 64px)" />
        </div>
        <div class="board-item shadow-card marginR16 flexW20">
          <div class="title">项目研发类型</div>
          <BaseEchart :option="projectDevTypeChartOption" width="100%" height="calc(20vw - 64px)" />
        </div>
        <div class="board-item shadow-card marginR16 flexW20">
          <div class="title">项目难度</div>
          <BaseEchart :option="projectDifficultyTypeChartOption" width="100%" height="calc(20vw - 64px)" />
        </div>
        <div class="board-item shadow-card marginR16 flexW20">
          <div class="title">需求准时交付</div>
          <BaseEchart :option="punctualityOrderCoutChartOption" width="100%" height="calc(20vw - 64px)" />
        </div>
        <div class="board-item shadow-card flexW20">
          <div class="title">验收状态</div>
          <BaseEchart :option="acceptanceOrderCoutChartOption" width="100%" height="calc(20vw - 64px)" />
        </div>
      </div>

      <div class="row-box marginB16">
        <div class="board-item shadow-card flexW33 marginR16">
          <div class="title marginB8">需求节点状态</div>
          <BaseEchart :option="orderCoutBarOption" width="100%" height="calc(20vw - 66px)" :key="countUpdateKey" />
        </div>

        <div class="board-item shadow-card flexW33 marginR16">
          <div class="title row-box justify marginB8">
            <span>需求节点状完成情况</span>
            <div class="type-select">
              <FSelect
                class="cust-select"
                :dropdown-match-select-width="false"
                placeholder="请选择完成状态"
                v-model:value="otherQueryData.nodeStatus"
                :options="nodeStatusList"
                @change="emits('queryDataCount')"
              />
            </div>
          </div>
          <BaseEchart :option="orderHoursBarOption" width="100%" height="calc(20vw - 66px)" :key="countUpdateKey" />
        </div>

        <div class="board-item shadow-card flexW33">
          <FSpin size="large" :spinning="instanceHoursLoading">
            <div class="title row-box justify marginB8">
              <span>平均交付周期</span>
              <div class="type-select">
                <FSelect
                  class="cust-select"
                  :dropdown-match-select-width="false"
                  placeholder="请选择完成状态"
                  v-model:value="otherQueryData.gruopType"
                  :options="gruopTypeList"
                  @change="emits('queryInstanceHoursDataCount')"
                />
              </div>
            </div>
            <BaseEchart
              :option="instanceHoursCountInfoBarOption"
              width="100%"
              height="calc(20vw - 66px)"
              :key="hoursCountUpdateKey"
            />
          </FSpin>
        </div>
      </div>

      <div class="row-box">
        <div class="board-item shadow-card flexW50 marginR16">
          <div class="title row-box justify marginB8">
            <span>业绩趋势</span>
            <div class="type-select">
              <span :class="{ active: otherQueryData.type === 1 }" @click="changeTypeFn(1)">周</span>
              <span :class="{ active: otherQueryData.type === 2 }" @click="changeTypeFn(2)">月</span>
            </div>
          </div>
          <BaseEchart :option="performanceLineOptions" width="100%" height="calc(20vw - 66px)" :key="countUpdateKey" />
        </div>

        <div class="board-item shadow-card flexW50">
          <div class="title row-box justify marginB8">
            <span>项目数量趋势</span>
            <div class="type-select">
              <span :class="{ active: otherQueryData.type === 1 }" @click="changeTypeFn(1)">周</span>
              <span :class="{ active: otherQueryData.type === 2 }" @click="changeTypeFn(2)">月</span>
            </div>
          </div>
          <BaseEchart :option="projectCountLineOptions" width="100%" height="calc(20vw - 66px)" :key="countUpdateKey" />
        </div>
      </div>
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import BaseEchart from '@/components/BaseEchart/index.vue'
import type { EChartsOption } from '@/components/BaseEchart/config'
import {
  pieOptions,
  commonTitle,
  commonLegend,
  getLineOptions,
  getBarOptions,
  formatAmountWithCommas,
} from './chartsOptions'
import { computed, ref } from 'vue'

type propsType = {
  countLoading: boolean
  countInfo: any
  otherQueryData: any
  instanceHoursCountInfo: any
  instanceHoursLoading: boolean
  countUpdateKey: number
  hoursCountUpdateKey: number
}

const props = defineProps<propsType>()
const emits = defineEmits(['update:otherQueryData', 'queryInstanceHoursDataCount', 'queryDataCount'])
const otherQueryData = computed({
  get: () => props.otherQueryData,
  set: val => emits('update:otherQueryData', val),
})
const nodeStatusList = ref([
  { label: '全部', value: 1 },
  { label: '正常完成', value: 2 },
  { label: '延迟完成', value: 3 },
])
const gruopTypeList = ref([
  { label: '业务线', value: 1 },
  { label: '项目难度', value: 2 },
  { label: '项目类型', value: 3 },
  { label: '需求类型', value: 4 },
])

// 项目总数
const projectCoutChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    title: Object.assign({}, commonTitle, {
      text: '总项目 (个)',
      subtext: (props?.countInfo?.moneyInfo ?? []).reduce((sum, item) => sum + (item?.count || 0), 0),
    }),
    series: [
      pieOptions.series?.[0],
      {
        ...pieOptions.series?.[1],
        data: (props?.countInfo?.moneyInfo ?? []).map(item => ({
          name: item?.value || '其它',
          value: item?.count || null,
        })),
      },
      pieOptions.series?.[2],
    ],
  }
})

// 项目总金额
const projectMoneyCoutChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    tooltip: Object.assign({}, pieOptions.tooltip, {
      formatter: (params: any) => {
        // params.marker 是自带的颜色方块图标
        // params.name 是扇区名称 (如 '园区交换机')
        // params.value 是扇区数值 (如 17)
        // params.percent 是扇区百分比 (如 14)
        // 获取当前数据项的颜色
        const itemColor = params.color || '#fff' // 确保有颜色，默认为白色

        // 自定义 marker 的 HTML 结构和样式
        const customMarker = `
          <span style="
            display: inline-block;
            margin-right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 2px;
            background-color: ${itemColor};
            vertical-align: middle;
          "></span>
        `
        return `
          ${customMarker} <span>${params.name}</span><br/>
          <span style="display: inline-block; width: 60px; text-align: left;">金额数</span> <span>${params.value}</span><br/>
          <span style="display: inline-block; width: 60px; text-align: left;">占比</span> <span>${params.percent}%</span>
        `
      },
    }),
    title: Object.assign({}, commonTitle, {
      text: '总金额 (USD)',
      subtext: formatAmountWithCommas(
        (props?.countInfo?.moneyInfo ?? []).reduce((sum, item) => sum + (item?.orderAmt || 0), 0)
      ),
    }),
    series: [
      pieOptions.series?.[0],
      {
        ...pieOptions.series?.[1],
        data: (props?.countInfo?.moneyInfo ?? []).map(item => ({
          name: item?.value || '其它',
          value: item?.orderAmt || null,
        })),
      },
      pieOptions.series?.[2],
    ],
  }
})

// 需求成单
const orderCoutChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    tooltip: Object.assign({}, pieOptions.tooltip, {
      formatter: (params: any) => {
        // params.marker 是自带的颜色方块图标
        // params.name 是扇区名称 (如 '园区交换机')
        // params.value 是扇区数值 (如 17)
        // params.percent 是扇区百分比 (如 14)
        // 获取当前数据项的颜色
        return `
          <span>${params.name}</span><br/>
          <span>${params.marker}</span> <span style="display: inline-block; min-width: 60px; text-align: left;">数量</span> <span>${params.value}</span><br/>
          <span>${params.marker}</span> <span style="display: inline-block; min-width: 60px; text-align: left;">占比</span> <span>${params.percent}%</span>
        `
      },
    }),
    legend: Object.assign({}, commonLegend, { width: '100%', icon: 'circle' }),
    color: ['#378EEF', '#F5CC38', '#FA8F23'],
    series: [
      {
        ...pieOptions.series?.[3],
        data: [
          { value: props.countInfo?.stateInfo?.winCount ?? 0, name: '赢单' },
          // { value: props.countInfo?.stateInfo?.finishedCount ?? 0, name: '已办结' },
          { value: props.countInfo?.stateInfo?.inProgressCount ?? 0, name: '谈单中' },
          { value: props.countInfo?.stateInfo?.loseCount ?? 0, name: '失单' },
        ],
      },
    ],
  }
})

// 项目需求类型
const projectDemandTypeChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    title: Object.assign({}, commonTitle, {
      left: '49%',
      top: '32%',
      text: '总项目 (个)',
      subtext: (props?.countInfo?.typeInfo?.requirement ?? []).reduce((sum, item) => sum + (item?.count || 0), 0),
    }),
    color: ['#2EC881', '#F5CC38', '#37BBEF', '#FA8F23', '#C9CFD5', '#6762FC'],
    series: [
      {
        ...pieOptions.series?.[0],
        center: ['50%', '40%'], // 中上位置
        radius: ['70%', '75%'], // 外环
      },
      {
        ...pieOptions.series?.[1],
        center: ['50%', '40%'], // 中上位置
        radius: ['50%', '70%'], // 主环
        data: (props?.countInfo?.typeInfo?.requirement ?? []).map(item => ({
          value: item?.count || 0,
          name: item?.value || '其它',
        })),
      },
      {
        ...pieOptions.series?.[2],
        center: ['50%', '40%'], // 中上位置
        radius: ['45%', '50%'], // 内环
      },
    ],
  }
})

// 项目研发类型
const projectDevTypeChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    title: Object.assign({}, commonTitle, {
      left: '49%',
      top: '32%',
      text: '总项目 (个)',
      subtext: (props?.countInfo?.typeInfo?.development ?? []).reduce((sum, item) => sum + (item?.count || 0), 0),
    }),
    legend: Object.assign({}, commonLegend, { left: 0, width: 240 }),
    color: ['#37BBEF', '#2EC881', '#F5CC38', '#FA8F23', '#C9CFD5', '#6762FC'],
    series: [
      {
        ...pieOptions.series?.[0],
        center: ['50%', '40%'], // 中上位置
        radius: ['70%', '75%'], // 外环
      },
      {
        ...pieOptions.series?.[1],
        center: ['50%', '40%'], // 中上位置
        radius: ['50%', '70%'], // 主环
        data: (props?.countInfo?.typeInfo?.development ?? []).map(item => ({
          value: item?.count || 0,
          name: item?.value || '其它',
        })),
      },
      {
        ...pieOptions.series?.[2],
        center: ['50%', '40%'], // 中上位置
        radius: ['45%', '50%'], // 内环
      },
    ],
  }
})

// 项目难度
const projectDifficultyTypeChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    title: Object.assign({}, commonTitle, {
      left: '49%',
      top: '32%',
      text: '总项目 (个)',
      subtext: (props?.countInfo?.typeInfo?.difficulty ?? []).reduce((sum, item) => sum + (item?.count || 0), 0),
    }),
    color: ['#37BBEF', '#F5CC38', '#2EC881', '#FA8F23', '#C9CFD5', '#6762FC'],
    series: [
      {
        ...pieOptions.series?.[0],
        center: ['50%', '40%'], // 中上位置
        radius: ['70%', '75%'], // 外环
      },
      {
        ...pieOptions.series?.[1],
        center: ['50%', '40%'], // 中上位置
        radius: ['50%', '70%'], // 主环
        data: (props?.countInfo?.typeInfo?.difficulty ?? []).map(item => ({
          value: item?.count || 0,
          name: item?.value || '其它',
        })),
      },
      {
        ...pieOptions.series?.[2],
        center: ['50%', '40%'], // 中上位置
        radius: ['45%', '50%'], // 内环
      },
    ],
  }
})

// 需求准时交付
const punctualityOrderCoutChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    tooltip: Object.assign({}, pieOptions.tooltip, {
      formatter: (params: any) => {
        return `
          <span>${params.name}</span><br/>
          <span style="display: inline-block; width: 60px; text-align: left;">订单数</span> <span>${params.value}</span><br/>
          <span style="display: inline-block; width: 60px; text-align: left;">占比</span> <span>${params.percent}%</span>
        `
      },
    }),
    title: Object.assign({}, commonTitle, {
      left: '49%',
      top: '32%',
      text: '总订单 (单)',
      subtext: (props.countInfo?.stateInfo?.onTimeCount ?? 0) + (props.countInfo?.stateInfo?.lateCount ?? 0),
    }),
    color: ['#37BBEF', '#F5CC38', '#2EC881', '#FA8F23', '#C9CFD5', '#6762FC'],
    series: [
      {
        ...pieOptions.series?.[0],
        center: ['50%', '40%'], // 中上位置
        radius: ['70%', '75%'], // 外环
      },
      {
        ...pieOptions.series?.[1],
        center: ['50%', '40%'], // 中上位置
        radius: ['50%', '70%'], // 主环
        data: [
          { value: props.countInfo?.stateInfo?.onTimeCount ?? 0, name: '准时交付' },
          // { value: props.countInfo?.stateInfo?.finishedCount ?? 0, name: '已办结' },
          // { value: props.countInfo?.stateInfo?.inProgressCount ?? 0, name: '进行中' },
          { value: props.countInfo?.stateInfo?.lateCount ?? 0, name: '未准时交付' },
        ],
      },
      {
        ...pieOptions.series?.[2],
        center: ['50%', '40%'], // 中上位置
        radius: ['45%', '50%'], // 内环
      },
    ],
  }
})

// 验收状态
const acceptanceOrderCoutChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    tooltip: Object.assign({}, pieOptions.tooltip, {
      formatter: (params: any) => {
        return `
          <span>${params.name}</span><br/>
          <span style="display: inline-block; width: 60px; text-align: left;">数量</span> <span>${params.value}</span><br/>
          <span style="display: inline-block; width: 60px; text-align: left;">占比</span> <span>${params.percent}%</span>
        `
      },
    }),
    title: Object.assign({}, commonTitle, {
      left: '49%',
      top: '32%',
      text: '验收数 (个)',
      subtext: (props?.countInfo?.typeInfo?.acceptance ?? []).reduce((sum, item) => sum + (item?.count || 0), 0),
    }),
    color: ['#37BBEF', '#F5CC38', '#2EC881', '#FA8F23', '#C9CFD5', '#6762FC'],
    series: [
      {
        ...pieOptions.series?.[0],
        center: ['50%', '40%'], // 中上位置
        radius: ['70%', '75%'], // 外环
      },
      {
        ...pieOptions.series?.[1],
        center: ['50%', '40%'], // 中上位置
        radius: ['50%', '70%'], // 主环
        data: (props?.countInfo?.typeInfo?.acceptance ?? []).map(item => ({
          value: item?.count || 0,
          name: item?.value || '其它',
        })),
      },
      {
        ...pieOptions.series?.[2],
        center: ['50%', '40%'], // 中上位置
        radius: ['45%', '50%'], // 内环
      },
    ],
  }
})

// 业绩趋势
const performanceLineOptions = computed(() =>
  getLineOptions({
    title: '业绩趋势',
    yName: '总金额 (USD)',
    xData: (props?.countInfo?.timeOrderAmtInfo ?? []).map(item => item?.year + '/' + item?.time),
    seriesData: (props?.countInfo?.timeOrderAmtInfo ?? []).map(item => ({
      value: item?.orderAmt ?? 0,
      type: otherQueryData.value.type,
      ...item,
    })),
    lineColor: '#378EEF',
    formatter: (params: any) => {
      const [info = {}] = params
      return `
        <span>${info?.data?.year}年</span> <span>${
        info?.data?.type === 1 ? '第' + info?.data?.time + '周' : info?.data?.time + '月'
      }</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left; margin-right: 40px;">总金额 (USD)</span> <span>${
          info.value
        }</span>
      `
    },
  })
)

// 项目数量趋势
const projectCountLineOptions = computed(() =>
  getLineOptions({
    title: '项目数量趋势',
    yName: '项目数 (个)',
    xData: (props?.countInfo?.timeInfo ?? []).map(item => item?.year + '/' + item?.time),
    seriesData: (props?.countInfo?.timeInfo ?? []).map(item => ({
      value: item?.count ?? 0,
      type: otherQueryData.value.type,
      ...item,
    })),
    lineColor: '#2FCC83',
    formatter: (params: any) => {
      const [info = {}] = params
      return `
        <span>${info?.data?.year}年</span> <span>${
        info?.data?.type === 1 ? '第' + info?.data?.time + '周' : info?.data?.time + '月'
      }</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left; margin-right: 40px;">项目数 (个)</span> <span>${
          info.value
        }</span>
      `
    },
  })
)

// 需求节点状态
const orderCoutBarOption = computed(() =>
  getBarOptions({
    title: '需求节点状态',
    unit: '项目数 (个)',
    xData: (props?.countInfo?.nodeInfo ?? []).map(item => item?.node ?? '其它'),
    seriesData: (props?.countInfo?.nodeInfo ?? []).map(item => item?.count ?? 0),
    color: ['#378EEF', '#22C993', '#F5CC38', '#FA8F23'],
    formatter: (params: any) => {
      const [info = {}] = params
      // 自定义 marker 的 HTML 结构和样式
      const itemColor = info.color || '#fff' // 确保有颜色，默认为白色
      const customMarker = `
          <span style="
            display: inline-block;
            margin-right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 2px;
            background-color: ${itemColor};
            vertical-align: middle;
          "></span>
        `
      return `
        <span>项目数 (个)</span><br/>
        ${customMarker}<span style="display: inline-block; width: 60px; text-align: left;">${info.name}</span> <span>${info.value}</span>
      `
    },
  })
)

// 需求节点状完成情况
const orderHoursBarOption = computed(() =>
  getBarOptions({
    title: '需求节点状完成情况',
    unit: '时长 (h)',
    xData: (props?.countInfo?.nodeHours ?? []).map(item => item?.node ?? '其它'),
    seriesData: (props?.countInfo?.nodeHours ?? []).map(item => item?.avgHours ?? 0),
    color: ['#378EEF', '#22C993', '#F5CC38', '#FA8F23'],
    formatter: (params: any) => {
      const [info = {}] = params
      // 自定义 marker 的 HTML 结构和样式
      const itemColor = info.color || '#fff' // 确保有颜色，默认为白色
      const customMarker = `
          <span style="
            display: inline-block;
            margin-right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 2px;
            background-color: ${itemColor};
            vertical-align: middle;
          "></span>
        `
      return `
        <span>时长 (h)</span><br/>
        ${customMarker}<span style="display: inline-block; min-width: 60px; text-align: left;">${info.name}</span> <span>${info.value}</span>
      `
    },
  })
)

// 平均交付周期
const instanceHoursCountInfoBarOption = computed(() =>
  getBarOptions({
    title: '平均交付周期',
    unit: '周期 (w)',
    xData: (props?.instanceHoursCountInfo ?? []).map(item => item?.value ?? '其它'),
    seriesData: (props?.instanceHoursCountInfo ?? []).map(item => item?.avgHours ?? 0),
    color: ['#378EEF', '#22C993', '#F5CC38', '#FA8F23'],
    formatter: (params: any) => {
      const [info = {}] = params
      // 自定义 marker 的 HTML 结构和样式
      const itemColor = info.color || '#fff' // 确保有颜色，默认为白色
      const customMarker = `
          <span style="
            display: inline-block;
            margin-right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 2px;
            background-color: ${itemColor};
            vertical-align: middle;
          "></span>
        `
      return `
        <span>周期 (w)</span><br/>
        ${customMarker}<span style="display: inline-block; min-width: 60px; text-align: left;">${info.name}</span> <span>${info.value}</span>
      `
    },
  })
)

const changeTypeFn = type => {
  otherQueryData.value.type = type
  emits('queryDataCount')
}
</script>

<style scoped lang="scss">
.content-board-container {
  margin-top: 16px;
  .row-box {
    display: flex;
    .board-item {
      width: 100%;
      height: 20vw;
      padding: 16px 20px;
      .title {
        margin-bottom: 6px;
        font-size: 12px;
        font-weight: 500;
        color: #333333;
        line-height: 22px;
      }
    }
    .shadow-card {
      background: #ffffff;
      box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
      border-radius: 4px;
    }
    .line {
      width: 1px;
      height: calc(20vw - 66px);
      margin-bottom: 20px;
      align-self: end;
      background-color: #eeeff2;
    }
    .marginR16 {
      margin-right: 16px;
    }
    .flexW20 {
      flex: 1 1 20%;
    }
    .flexW33 {
      flex: 1 1 33.33333%;
    }
    .flexW32 {
      flex: 1 1 35%;
    }
    .flexW40 {
      flex: 1 1 40%;
    }
    .flexW50 {
      flex: 1 1 50%;
    }
    .flexW60 {
      flex: 1 1 60%;
    }
    .flexW68 {
      flex: 1 1 65%;
    }
    .justify {
      justify-content: space-between;
    }
    .type-select {
      display: flex;
      align-items: center;
      background: #f9fafb;
      border-radius: 4px;
      span {
        color: #999999;
        padding: 0 12px;
        border-radius: 4px;
        cursor: pointer;
        &.active {
          background: #ffffff;
          box-shadow: 0px 2px 4px 0px rgba(17, 24, 39, 0.04);
          color: #378eef;
        }
      }
    }
  }
  .marginB16 {
    margin-bottom: 16px;
  }
  .marginB8 {
    margin-bottom: 8px !important;
  }
  :deep(.cust-select) {
    .fs-select-selector {
      height: 22px;
      box-shadow: none !important;
      border: none;
      .fs-select-selection-item {
        line-height: 22px;
        padding-right: 11px;
      }
    }
    .fs-select-arrow {
      right: 0;
    }
  }
}
</style>

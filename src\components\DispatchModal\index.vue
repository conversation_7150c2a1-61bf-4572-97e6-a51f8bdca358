<template>
  <FModal v-model:visible="value" :title="i18n.t('分派他人')" :width="400" :z-index="1001">
    <div class="dispatch-modal-wrapper">
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FFormItem :label="i18n.t('请选择派发人')" name="superviser">
          <FSelect
            :placeholder="i18n.t('请选择')"
            v-model:value="formState.superviser"
            :field-names="{ label: 'name', value: 'uuid' }"
            :options="personList"
            allow-clear
            show-search
            :filter-option="filterOption"
          />
        </FFormItem>
        <FFormItem :label="i18n.t('备注说明：')" name="transferReason" v-if="props.isRemark">
          <FTextarea v-model:value="formState.transferReason" :rows="4" :placeholder="i18n.t('请输入')" />
        </FFormItem>
      </FForm>
    </div>
    <template #footer>
      <FButton key="back" @click="value = false">{{ i18n.t('取消') }}</FButton>
      <FButton key="submit" type="primary" @click="handleOk(formRef)">{{ i18n.t('确定') }}</FButton>
    </template>
  </FModal>
</template>
<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { useStore } from 'vuex'
import { useI18n } from '@/utils'
const store = useStore()
const personList = computed(() => store.state.user.allUser || [])
interface FormState {
  superviser: null | string
  transferReason: string
}
const i18n = useI18n()
const emit = defineEmits(['submit', 'update:value'])
const props = defineProps({
  value: {
    type: Boolean,
    default: false,
  },
  isRemark: {
    type: Boolean,
    default: false,
  },
})
const value = computed({
  get() {
    return props.value
  },
  set(val: boolean) {
    emit('update:value', val)
  },
})
const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
const rules: Record<string, Rule[]> = {
  superviser: [{ required: true, message: i18n.t('请选择'), trigger: 'change' }],
}
const formRef = ref<FormInstance>()
const formState = reactive<FormState>({
  superviser: null,
  transferReason: '',
})
const handleOk = async (formRef: FormInstance | undefined) => {
  if (!formRef) return
  await formRef.validate()
  emit('submit', formState)
}
</script>
<style lang="scss">
.dispatch-modal-wrapper {
  .fs-form-item {
    margin-bottom: 22px !important;
  }
  div.fs-form-item-control-input-content,
  .fs-form-item-control-input-content textarea.fs-input {
    height: auto !important;
  }

  .fs-modal-footer .fs-btn {
    display: inline-block;
  }
}
</style>

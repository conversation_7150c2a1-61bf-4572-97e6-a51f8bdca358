<template>
  <div class="process-query-form">
    <!-- 筛选条件 -->
    <SearchHead
      :only="(route.params.type as string)"
      :extend="false"
      @search="data => emits('query', data)"
      @on-export-process="key => emits('on-export-process', key)"
    />
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import SearchHead from '@/views/process-list/components/ContentBox/SearchHead.vue'

const route = useRoute()
const emits = defineEmits(['query', 'on-export-process'])
</script>

<style scoped lang="scss">
.process-query-form {
  padding: 8px 20px 0 20px;
  min-height: 72px;
  border-radius: 4px;
  background: #ffffff;
  box-shadow: 0px 5px 15px 0px #dfe2e6cc;
  box-sizing: border-box;
}
</style>

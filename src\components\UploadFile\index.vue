<template>
  <div class="upload-file">
    <FUpload
      :file-list="[]"
      :before-upload="(file: any) => handleUpload(file)"
      style="display: flex; align-item: center"
      class="upload-btn"
    >
      <FPopover trigger="hover" word-wrap overlay-class-name="z-popover">
        <div style="cursor: pointer">
          <slot name="uploadIcon">
            <img src="@/assets/images/upload.png" alt="" style="height: 13px; verticalalign: -2px; margin-right: 4px" />
            <span class="fontSize12" style="color: #378eef">{{ i18n.t('上传附件') }}</span>
          </slot>
        </div>
        <template #content>
          <div class="file-limit-tips">
            1.{{
              i18n.t('支持格式')
            }}：PDF、JPG、JPEG、PNG、DOC、DOCX、PPT、PPTX、XLS、XLSX、TXT、CSV、ZIP、RAR、MP4、BIN、TEST、VSD、VSDX、CNF。<br />
            2. {{ i18n.t('最大文件2G') }}。
          </div>
        </template>
      </FPopover>
    </FUpload>
    <div class="upload-list">
      <!-- 新附件列表 -->
      <template v-if="newFiles.length">
        <div class="upload-item" v-for="(item, index) in newFiles" :key="'newFiles' + index">
          <div>
            <slot name="downloadIcon">
              <span class="iconfont" style="color: #fdb824">&#xe655;</span>
            </slot>
            {{ item.fileName }}
            <span style="color: #bbb" v-if="props.hiddenSize">({{ (item.fileSize / 1024).toFixed() }}KB)</span>
          </div>
          <div v-if="!item.isFormData">
            <span class="iconfont" style="color: #bbb; cursor: pointer; font-size: 14px" @click="delNewFile(index)"
              >&#xe715;</span
            >
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue'
import { messageInstance as message } from '@fs/smart-design'
import { upload } from '@/api/handle'
import { useI18n } from '@/utils'
const i18n = useI18n()
const props = defineProps({
  value: {
    type: [Array, Object],
    default: () => [],
  },
  hiddenSize: {
    type: [Boolean],
    default: () => true,
  },
  limitFileTypes: {
    type: [Array],
    default: () => [
      'PDF',
      'JPG',
      'JPEG',
      'PNG',
      'DOC',
      'DOCX',
      'PPT',
      'PPTX',
      'XLS',
      'XLSX',
      'TXT',
      'CSV',
      'ZIP',
      'RAR',
      'MP4',
      'BIN',
      'TEST',
      'VSD',
      'VSDX',
      'CNF',
    ],
  },
})
const emit = defineEmits(['input'])
const newFiles = ref<any>(props.value)
const delNewFile = (index: number) => {
  newFiles.value.splice(index, 1)
}
const handleUpload = (file: any) => {
  if (!fileLimit(file)) {
    return false
  }
  uploadItem(file)
  return false
}
const uploadItem = (file: any) => {
  let formData = new FormData()
  formData.append('file', file)
  formData.append('isOpen', 'false')
  formData.append('expire', '0')
  upload(formData).then(res => {
    newFiles.value.push({
      fileName: file.name,
      fileSize: file.size,
      resourseKey: res,
    })
  })
}
const fileLimit = (file: any) => {
  const fileType = file.name.replace(/.+\./, '')
  const allowTypes = props.limitFileTypes
  const allowSize = 2 * 1024
  if (allowTypes.indexOf(fileType.toUpperCase()) < 0) {
    message.error(i18n.t('文件类型错误'))
    return false
  }
  if (file.size / (1024 * 1024) > allowSize) {
    message.error(i18n.t('最大文件2G'))
    return false
  } else {
    return true
  }
}
watch(
  () => props.value,
  newVal => {
    newFiles.value = newVal
    emit('input', newVal)
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.upload-file {
  font-size: 12px;
  .upload-btn {
    float: right;
    height: 20px;
    line-height: 20px;
    // margin-top: -34px;
    margin-top: -30px;
  }
}
.upload-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .upload-item {
    width: calc(50% - 10px);
    height: 32px;
    display: flex;
    align-items: center;
    background-color: #f7f8f9;
    justify-content: space-between;
    padding: 0 10px;
    margin-bottom: 10px;
    .iconfont {
      font-size: 12px;
    }
    .spin-box {
      color: #378eef;
    }
    .success-tips {
      color: #3dcca6;
    }
    .error-tips {
      color: #ff4a4a;
    }
    .waiting-tips {
      color: #999999;
    }
    .process-box {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      & > span {
        margin: 0px 4px;
        font-size: 12px;
      }
    }
    .upload-error {
      color: #ff4a4a;
    }
  }
}
.file-limit-tips {
  font-size: 12px;
  font-weight: 400;
  color: #666666;
  line-height: 18px;
}
</style>

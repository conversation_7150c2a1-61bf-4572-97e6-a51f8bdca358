<template>
  <div class="content-board-container">
    <div class="left">
      <div class="title">{{ i18n.t('项目情况') }}</div>
      <FSpin size="large" :spinning="processLoading">
        <BaseEchart :option="projectChartOption" height="324px" />
      </FSpin>
    </div>
    <div class="right">
      <div class="title">{{ i18n.t('任务情况') }}</div>
      <FSpin size="large" :spinning="taskLoading">
        <BaseEchart :option="taskChartOption" height="324px" />
      </FSpin>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseEchart from '@/components/BaseEchart/index.vue'
import type { EChartsOption } from '@/components/BaseEchart/config'
import { barOptions } from './chartsOptions'
import { computed } from 'vue'
import { useI18n } from '@/utils'
const i18n = useI18n()

type propsType = {
  processLoading: boolean
  taskLoading: boolean
  processData: any
  taskData: any
}

const getSplitInterval = (data: number[]) => {
  let max = Math.ceil(Math.max(...data) / 100) * 100
  if (max === 0) {
    max = 100
  }
  return max
}

const props = defineProps<propsType>()
const projectChartOption = computed<EChartsOption>(() => {
  if (props.processData?.projectChart) {
    const numLists = [
      Number(props.processData?.transactDone || 0),
      Number(props.processData?.doing || 0),
      Number(props.processData?.done || 0),
      Number(props.processData?.delayDone || 0),
    ]
    const rateLists = [
      Number(props.processData?.transactDoneData?.ratio || 0),
      Number(props.processData?.doingData?.ratio || 0),
      Number(props.processData?.doneRatio || 0),
      Number(props.processData?.delayDoneRatio || 0),
    ]
    const nummax = getSplitInterval(numLists)
    const ratemax = getSplitInterval(rateLists)
    return {
      xAxis: {
        data: [i18n.t('办结'), i18n.t('进行中'), i18n.t('已完成'), i18n.t('逾期完成')],
      },
      yAxis: [
        {
          min: 0,
          max: nummax,
          splitNumber: 5,
          minInterval: 1,
          interval: nummax / 5,
        },
        {
          min: 0,
          max: ratemax,
          splitNumber: 5,
          minInterval: 1,
          interval: ratemax / 5,
        },
      ],
      series: [
        {
          name: i18n.t('任务数量'),
          data: numLists,
          barWidth: 40,
          barGap: '40%',
          type: 'bar',
        },
        {
          name: i18n.t('占比'),
          data: rateLists,
          barWidth: 40,
          barGap: '40%',
          type: 'line',
          yAxisIndex: 1,
          symbol: 'circle',
          tooltip: {
            valueFormatter: (value: any) => {
              return value + '%'
            },
          },
        },
      ],
    }
  } else {
    return barOptions
  }
})

const taskChartOption = computed<EChartsOption>(() => {
  if (props.taskData?.taskNum) {
    const numLists = [
      Number(props.taskData?.onTime || 0),
      Number(props.taskData?.doing || 0),
      Number(props.taskData?.earlyDone || 0),
      Number(props.taskData?.delayDone || 0),
    ]
    const rateLists = [
      Number(props.taskData?.onTimeRatio || 0),
      Number(props.taskData?.doingData?.ratio || 0),
      Number(props.taskData?.earlyDoneRatio || 0),
      Number(props.taskData?.delayDoneRatio || 0),
    ]
    const nummax = getSplitInterval(numLists)
    const ratemax = getSplitInterval(rateLists)
    return {
      xAxis: {
        data: [i18n.t('按时完成'), i18n.t('进行中'), i18n.t('提前完成'), i18n.t('逾期完成')],
      },
      yAxis: [
        {
          min: 0,
          max: nummax,
          splitNumber: 5,
          minInterval: 1,
          interval: nummax / 5,
        },
        {
          min: 0,
          max: ratemax,
          splitNumber: 5,
          minInterval: 1,
          interval: ratemax / 5,
        },
      ],
      series: [
        {
          name: i18n.t('任务数量'),
          data: numLists,
          barWidth: 40,
          barGap: '40%',
          type: 'bar',
        },
        {
          name: i18n.t('占比'),
          data: rateLists,
          barWidth: 40,
          barGap: '40%',
          type: 'line',
          yAxisIndex: 1,
          symbol: 'circle',
          tooltip: {
            valueFormatter: (value: any) => {
              return value + '%'
            },
          },
        },
      ],
    }
  } else {
    return barOptions
  }
})
</script>

<style scoped lang="scss">
.content-board-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 16px;
  .left,
  .right {
    flex: 1;
    width: calc(50% - 8px);
    min-height: 417px;
    padding: 24px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
    .title {
      margin-bottom: 24px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      line-height: 24px;
    }
  }
  .left {
    margin-right: 16px;
  }
}
</style>

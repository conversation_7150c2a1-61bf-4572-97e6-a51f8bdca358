<template>
  <div class="table-box-container">
    <div class="header">
      <span class="title">{{ i18n.t('个人任务完成情况') }}</span>
      <div class="table-search">
        <FSelect
          v-model:value="queryData.taskSuperviser"
          class="cust-select"
          style="width: 88px; margin-right: 6px"
          placement="bottomRight"
          :placeholder="i18n.t('任务负责人')"
          :options="userList"
          show-arrow
          max-tag-count="responsive"
          mode="multiple"
          allow-clear
          show-search
          option-filter-prop="name"
          :field-names="{ label: 'name', value: 'uuid' }"
          @change="emits('onPageChange', queryData)"
        >
        </FSelect>
        <span class="line"></span>
        <span class="btn" @click="emits('onExportProcess', queryData)">
          <i class="icontubiao_xiazai iconfont"></i>{{ i18n.t('下载') }}
        </span>
      </div>
    </div>
    <FTable
      :data-source="list"
      :loading="loading"
      :columns="columns"
      table-layout="fixed"
      :pagination="false"
      :scroll="{ x: '100%' }"
    >
    </FTable>
    <div class="fei-su-pagination">
      <FPagination
        v-model:current="page.currPage"
        v-model:pageSize="page.pageSize"
        :total="page.total"
        @change="onChangeFn"
        show-size-changer
        show-quick-jumper
        :show-total="() => `${i18n.t('共')} ${page.total} ${i18n.t('条')}`"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ProcessItem, BasicPageParams } from '@/types/processBoard'
import { columns } from './tableConfig'
import { reactive, computed } from 'vue'
import { useI18n } from '@/utils'
import { useStore } from 'vuex'
import { StoreUserType } from '@/store/modules/user'
const i18n = useI18n()
const store = useStore()

type propsType = {
  list: ProcessItem[]
  page: BasicPageParams
  loading: boolean
}
const props = withDefaults(defineProps<propsType>(), {
  list: () => [],
  page: () => ({}),
  loading: false,
})

const emits = defineEmits(['onPageChange', 'onExportProcess', 'update:page'])
const page = computed<BasicPageParams>({
  get: () => props.page,
  set: val => emits('update:page', val),
})
const userList = computed<StoreUserType[]>(() => store.state.user.allUser || [])
const queryData = reactive({
  taskSuperviser: undefined,
})

const onChangeFn = (current: number, pageSize: number) => {
  page.value.currPage = current
  page.value.pageSize = pageSize
  emits('onPageChange', page.value)
}
</script>

<style scoped lang="scss">
.table-box-container {
  background: #ffffff;
  box-shadow: 0px 2px 5px 0px rgba(176, 177, 178, 0.4);
  border-radius: 4px;
  padding: 24px 24px 0 24px;
  margin-top: 12px;
  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      line-height: 22px;
    }
    .btn {
      font-size: 12px;
      font-weight: 400;
      color: #378eef;
      line-height: 18px;
      cursor: pointer;
    }
    .table-search {
      display: flex;
      align-items: center;
      :deep(.cust-select) {
        .fs-select-selector {
          padding: 0;
          box-shadow: none !important;
          border: none;
        }
      }
      .line {
        width: 1px;
        height: 24px;
        margin-right: 12px;
        background-color: #eeeeee;
      }
    }
  }
  :deep(.fs-table-cell) {
    &:empty {
      &::after {
        content: '--';
      }
    }
  }
  :deep(.fs-table-tbody) {
    tr > td {
      background: #fff;
    }
  }
  :deep(.fs-table-container) {
    &::after {
      box-shadow: none;
    }
  }
  :deep(.fs-table-content) {
    padding-bottom: 4px;
  }
  .fei-su-pagination {
    padding-top: 11px;
  }
}
</style>

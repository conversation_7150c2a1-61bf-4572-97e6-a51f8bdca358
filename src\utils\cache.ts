const CACHE_KEY = 'BPM_PLATFORM_CACHE-'

class Cache {
  private prefix: string
  private cache: Map<string, unknown>
  private store: Storage

  constructor(prefix: string) {
    this.prefix = prefix
    this.store = window.localStorage
    this.cache = new Map<string, unknown>()
  }

  private getCacheKey(key: string): string {
    return this.prefix + key
  }

  public get(key: string): unknown {
    const cacheKey = this.getCacheKey(key)
    return this.cache.get(cacheKey) ?? this.store.getItem(cacheKey)
  }

  public set(key: string, value: string, isStore = true): void {
    const cacheKey = this.getCacheKey(key)
    this.cache.set(cacheKey, value)
    isStore && this.store.setItem(cacheKey, value)
  }

  public remove(key: string): void {
    const cacheKey = this.getCacheKey(key)
    this.cache.delete(cacheKey)
    this.store.removeItem(cacheKey)
  }

  public clear(): void {
    this.cache.clear()
    this.store.clear()
  }
}

export const cache = new Cache(CACHE_KEY)

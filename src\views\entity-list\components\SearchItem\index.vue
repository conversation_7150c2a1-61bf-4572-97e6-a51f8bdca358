<template>
  <div class="search-item-container">
    <div class="search-list">
      <div class="list-item" v-for="(item, index) in formState" :key="item.fieldCode">
        <FSelect
          v-model:value="formState[index].fieldCode"
          style="width: 162px"
          :options="fieldOptions"
          allow-clear
          :press-line="i18n.t('筛选字段')"
          :placeholder="i18n.t('请选择筛选字段')"
          :field-names="{ label: 'fieldName', value: 'fieldCode' }"
          @change="(value: any, option: any) => fieldChange(value, option, index)"
          @dropdownVisibleChange="fieldDropdownVisibleChange"
          :getPopupContainer="(triggerNode: Element) => { return triggerNode?.parentNode || null}"
        />
        <FSelect
          v-model:value="formState[index].type"
          style="width: 162px"
          :options="typeList"
          allow-clear
          :press-line="i18n.t('筛选符')"
          :placeholder="i18n.t('请选择筛选符')"
          :getPopupContainer="(triggerNode: Element) => { return triggerNode?.parentNode || null}"
        />
        <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="formState[index].values">
        </component>
        <i class="iconfont icontubiao_shanchu1" @click="onDeteleFn(index)"></i>
      </div>
      <p class="add-text" @click="addSearch"><i class="iconxinzeng iconfont"></i>{{ i18n.t('添加筛选字段') }}</p>
    </div>
    <div class="search-footer">
      <FButton size="small" style="margin-right: 12px" @click="onClose">{{ i18n.t('取消') }}</FButton>
      <FButton size="small" type="primary" @click="onSubmit">{{ i18n.t('确定') }}</FButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FieldList } from '@/types/entity'
import { components } from './config'
import { ref, computed, watch } from 'vue'
import { deepClone, useI18n } from '@/utils'
import { message } from '@fs/smart-design'
type propsType = {
  fieldList: FieldList[]
  visible?: boolean
  formState: any[]
}
const i18n = useI18n()
const props = defineProps<propsType>()
const emits = defineEmits(['update:visible', 'update:formState', 'getDataFn'])
const typeList = ref<any>([
  { label: '等值', value: 'eq' },
  { label: '模糊', value: 'like' },
  { label: '数字范围', value: 'range' },
  { label: '时间范围', value: 'timeRange' },
])
const fieldOptions = ref<any>([])
const formState = computed({
  get: () => props.formState,
  set: val => emits('update:formState', val),
})

const onDeteleFn = (index: number) => {
  formState.value.splice(index, 1)
}

const fieldDropdownVisibleChange = (open: any) => {
  if (open) {
    fieldOptions.value = fieldOptions.value.map((item: FieldList) => {
      const flag = formState.value.some((selectItem: any) => selectItem.fieldCode === item.fieldCode)
      return Object.assign(item, { disabled: flag })
    })
  }
}

const fieldChange = (value: any, option: FieldList, index: number) => {
  if (!option.dataType) {
    message.warning(i18n.t('请先添加数据类型'))
    return
  }
  const component = deepClone(components[option.dataType as keyof typeof components])
  if (!component) {
    message.warning(i18n.t('暂无当前数据类型组件'))
    return
  }
  Object.assign(formState.value[index], component)
  formState.value[index].fieldName = option.fieldName
  if (['selectCode', 'selectMultipleCode'].includes(option.dataType as string)) {
    formState.value[index].componentAttrs.options = option?.values || []
  }
}

const addSearch = () => {
  formState.value.push({
    fieldCode: undefined,
    type: undefined,
    values: undefined,
    componentName: 'FInput',
    componentAttrs: {
      pressLine: i18n.t('目标值'),
      placeholder: i18n.t('请输入目标值'),
      getPopupContainer(triggerNode: Element) {
        return triggerNode?.parentNode || null
      },
      style: {
        width: '162px',
      },
    },
  })
}

const onClose = () => {
  emits('update:visible', false)
}

const onSubmit = () => {
  formState.value = formState.value.map((item: any) => {
    if (item.fieldCode && item.type && item.getComponentValueFormat && item.getComponentValueFormat(item.values)) {
      item.isSearchValue = true
    } else {
      item.isSearchValue = false
    }
    return item
  })
  emits('update:visible', false)
  emits('getDataFn', formState.value)
}

watch(
  () => props.fieldList,
  (val: FieldList) => {
    fieldOptions.value = val
  },
  { immediate: true, deep: true }
)
</script>

<style scoped lang="scss">
.search-item-container {
  .search-list {
    .add-text {
      display: inline-block;
      color: #378eef;
      margin-bottom: 16px;
      cursor: pointer;
      .iconxinzeng {
        font-size: 14px;
        margin-right: 4px;
      }
    }
    .list-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      > div + div {
        margin-left: 8px;
      }
      .icontubiao_shanchu1 {
        margin-left: 12px;
        cursor: pointer;
      }
    }
  }
  .search-footer {
    display: flex;
    justify-content: flex-end;
    .fs-btn-sm {
      font-size: 12px !important;
    }
  }
}
</style>

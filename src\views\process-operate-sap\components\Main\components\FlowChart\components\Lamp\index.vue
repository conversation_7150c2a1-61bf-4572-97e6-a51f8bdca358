<template>
  <i :class="['lamp', LampTypeClassName[props.type as keyof typeof LampTypeClassName]]" />
</template>

<script setup lang="ts">
const LampTypeClassName = {
  0: '',
  1: '',
  2: '__processing',
  3: '__complete',
  4: '__complete',
  5: '__complete',
}

interface IProps {
  type?: number
}

const props = defineProps<IProps>()
</script>

<style scoped lang="scss">
.lamp {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 4px;
  border-radius: 50%;
  background-color: #cbd5e1;

  &.__processing {
    background-color: #fa8f23;
  }

  &.__complete {
    background-color: #2fcc83;
  }
}
</style>

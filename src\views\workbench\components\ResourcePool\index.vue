<template>
  <!-- 用来接收父组件通过属性传递下来的 style 的 -->
  <div class="resource-pool-wrapper">
    <FSpin :spinning="loading" :tip="`${i18n.t('加载中')}...`">
      <FCard :body-style="containerBodyStyle">
        <!-- 标题 -->
        <template #title>
          <div class="card-header">
            <span class="icon iconfont icon-margin">&#xe7d3;</span>
            <h4 class="card-header-title">{{ i18n.t('资源库') }} · {{ total }}</h4>
            <!-- <div class="card-header-right">
              <FButton class="_btn" type="link" size="small" @click="handleSeeAllResourceClick">查看全部资源</FButton>
            </div> -->
          </div>
        </template>

        <!-- 资源列表 -->
        <ResourceTypeList :data="resourceTypeList" @click="handleResourceTypeClick" />

        <!-- 主内容 -->
        <div class="resource-main">
          <div class="resource-header">
            <h5 class="resource-header-title">{{ currResourceData?.name }} · {{ currResourceData?.size }}</h5>
            <div class="resource-header-right">
              <FForm :model="formData" layout="inline">
                <FFormItem>
                  <FCascader
                    class="w240"
                    v-model:value="formData.label"
                    :options="resourceLabelList"
                    :field-names="{ label: 'name', value: 'uuid', children: 'children' }"
                    @change="queryResourceList"
                    allow-clear
                    :placeholder="i18n.t('请选择')"
                  />
                </FFormItem>
                <FFormItem>
                  <FInput
                    v-model:value="formData.key"
                    @blur="queryResourceList"
                    @press-enter="queryResourceList"
                    allow-clear
                    class="w240"
                    :placeholder="i18n.t('请输入')"
                  >
                    <template #suffix>
                      <i v-if="!formData.key" class="iconfont iconsousuo form-search-icon" />
                    </template>
                  </FInput>
                </FFormItem>
              </FForm>
            </div>
          </div>
          <div class="resource-content">
            <FEmpty v-show="!resourceList.length" :image="require(`@/assets/images/empty.png`)" />
            <Resource :data="resourceList" @download="handleDownload" />
            <FPagination
              v-show="pageing.total > 0 && resourceList.length"
              class="resource-pagination"
              v-model:current="pageing.pageNum"
              v-model:page-size="pageing.pageSize"
              :page-size-options="['10', '20', '30', '40', '50']"
              :total="pageing.total"
              :show-total="(total: number) => `${i18n.t('共')} ${total} ${i18n.t('条')}}`"
              @change="onPageChange"
              @show-size-change="onPageShowNumChange"
              show-quick-jumper
              show-size-changer
            />
          </div>
        </div>
      </FCard>
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import ResourceTypeList, { IResourceTypeItem } from '../ResourceTypeList/index.vue'
import Resource from '../Resource/index.vue'
import { getResourceTypeList, getResourceList, getResourceLabel, IResourceParams, downloadResource } from '@/api'
import { IResource, IResourceLabel } from '@/types/request'
import { useI18n } from '@/utils'

interface IFromData {
  key: string | null
  label: string[] | null
  libraries: string | null
}

const i18n = useI18n()

const containerBodyStyle = reactive({ padding: '0 !important', display: 'flex', flexDirection: 'row' })

const resourceList = ref<IResource[]>([])
const resourceTypeList = ref<IResourceTypeItem[]>([])
const resourceLabelList = ref<IResourceLabel[]>([])
const currResourceData = ref<IResourceTypeItem>()
const loading = ref(true)
const pageing = reactive({ pageNum: 1, pageSize: 10, total: 0 })
const formData = reactive<IFromData>({ key: null, label: null, libraries: null })

const resourceListParams = computed<IResourceParams>(() => ({
  key: formData.key,
  label: formData.label?.at(-1) ?? null,
  libraries: formData.libraries,
  ...pageing,
}))
const total = computed(() => resourceTypeList.value.reduce((acc, cur) => acc + cur.size, 0))

onMounted(() => {
  init()
})

// 初始化
const init = async () => {
  queryLabelList()
  const resourceTypeListRes = await getResourceTypeList()
  resourceTypeList.value = (resourceTypeListRes?.data?.libraries || []).map((item: any) => ({
    id: item.uuid,
    name: item.name,
    size: item.count,
    icon: '&#xe7d5',
    class: 'galy',
  }))
  currResourceData.value = resourceTypeList.value[0] || {}
  formData.libraries = currResourceData.value.id as string
  queryResourceList()
}

// 处理下载事件
const handleDownload = async (data: IResource) => {
  try {
    loading.value = true
    const res = await downloadResource(data.file_uuid)
    const url = URL.createObjectURL(res as Blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${data.name}.${data.mime_type}`
    a.click()
    URL.revokeObjectURL(url)
  } finally {
    loading.value = false
  }
}

// 处理资源类型点击事件
const handleResourceTypeClick = (data: IResourceTypeItem) => {
  currResourceData.value = data
  formData.libraries = data.id as string
  queryResourceList()
}

// 处理分页变更事件
const onPageChange = (pageNum: number) => {
  pageing.pageNum = pageNum
  queryResourceList()
}

// 处理分页显示数量变更事件
const onPageShowNumChange = (pageSize: number) => {
  pageing.pageSize = pageSize
  queryResourceList()
}

// 获取标签
const queryLabelList = async () => {
  const resourceLabelListRes = await getResourceLabel()
  resourceLabelList.value = resourceLabelListRes?.data?.labels || []
}

// 查询资源列表
const queryResourceList = async () => {
  try {
    loading.value = true
    const {
      data: { resources = [], paginate = {} },
    } = await getResourceList(resourceListParams.value)
    resourceList.value = resources
    pageing.total = (paginate as { total: number }).total ?? 0
  } catch (error) {
    resourceList.value = []
  }
  loading.value = false
}
</script>

<style lang="scss" scoped>
.iconfont {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  margin-bottom: -1px;
  font-size: 16px;
  vertical-align: text-bottom;
}

.card-header {
  display: flex;
  flex-direction: row;
  align-items: center;

  .card-header-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  ._btn {
    border: none !important;
  }
  .icon-margin {
    width: 22px;
    margin-right: 8px;
    font-size: 22px;
    color: #000;
  }
}

.resource-main {
  flex: 1;
  padding: 24px 14px 16px 16px;

  .resource-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    line-height: 32px;
    margin-bottom: 24px;

    .resource-header-title {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }

  .resource-content {
    max-height: 650px;
    overflow-y: auto;

    .resource-pagination {
      margin-top: 8px;
      padding: 0 8px;
      text-align: right;
    }
    :deep(.fs-empty-description) {
      margin-top: -14px !important;
    }
    :deep(.fs-empty) {
      margin-top: 100px;
    }
  }
}

i.form-search-icon {
  color: #808695;
  font-size: 12px;
}

.w240 {
  width: 240px;
}
</style>

<template>
  <FLayout class="layout-container">
    <Sidebar v-show="isSidebarShow" :menu="currMenu" :collapse="collapse" @on-collapse="toggleCollapse" />
    <FLayout style="background-color: transparent">
      <Header v-show="isHeaderShow" :menus="menus" @on-menu-change="setCurrMenu" />
      <Main></Main>
    </FLayout>
  </FLayout>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { computed } from 'vue'
import useMenuCollapse from '@/hooks/useMenuCollapse'

import Header from './Header/index.vue'
import Sidebar from './Sidebar/index.vue'
import Main from './Main/index.vue'
import useMenu from '@/hooks/useMenu'

const store = useStore()
const [collapse, { toggleCollapse }] = useMenuCollapse()
const { menus, currMenu, setCurrMenu } = useMenu()

const isHeaderShow = computed(() => store.getters.isHeaderShow)
const isSidebarShow = computed(() => store.getters.isSidebarShow)
</script>

<style scoped lang="scss">
.layout-container {
  position: relative;
  height: 100%;
  overflow: hidden;
  background-color: transparent !important;
  background-image: url('@/assets/images/bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
    background-image: linear-gradient(180deg, #e5f1ff 0%, #f9fafc 100%);
    z-index: -1;
    user-select: none;
    pointer-events: none;
  }
}
</style>

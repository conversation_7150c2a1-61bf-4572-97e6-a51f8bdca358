import { request } from '@/utils'
import type {
  IRes,
  IEntityClassData,
  IEntityPageListParams,
  IEntityPageList,
  FieldList,
  IEntityPageListData,
} from '@/types/entity'

export const getEntityClass = async () => {
  const res = await request.get<IRes<IEntityClassData[]>>('/api/datamodel/getEntityClass')
  return res as unknown as IRes<IEntityClassData[]>
}

export const getEntityPageList = async (data: IEntityPageListParams) => {
  const res = await request.post<IRes<IEntityPageList>>('/api/datamodel/getPage', data)
  return res as unknown as IRes<IEntityPageList>
}

export const getFieldConfig = async (id: number) => {
  const res = await request.get<IRes<FieldList[]>>(`/api/datamodelfield/getConfig/${id}`)
  return res as unknown as IRes<FieldList[]>
}

export const getEntityType = async (data: any) => {
  const res = await request.get<IRes<any>>(`/api/datamodel/getEntity`, {
    params: data,
  })
  return res as unknown as IRes<any>
}

export const saveEntityData = async (data: IEntityPageListParams) => {
  const res = await request.post<IRes<any>>('/api/datamodel/save', data)
  return res as unknown as IRes<any>
}

export const updateEntityData = async (data: IEntityPageListParams) => {
  const res = await request.post<IRes<any>>('/api/datamodel/update', data)
  return res as unknown as IRes<any>
}

export const getEntityTables = async () => {
  const res = await request.get<IRes<any>>(`/api/datamodel/getTables`)
  return res as unknown as IRes<any>
}

export const getEntityById = async (id: number) => {
  const res = await request.get<IRes<IEntityPageListData>>(`/api/datamodel/getById/${id}`)
  return res as unknown as IRes<IEntityPageListData>
}

export const delEntityById = async (id: number) => {
  const res = await request.get<IRes<any>>(`/api/datamodel/delById/${id}`)
  return res as unknown as IRes<any>
}

export const saveDatamodelfield = async (data: FieldList) => {
  const res = await request.post<IRes<any>>('/api/datamodelfield/save', data)
  return res as unknown as IRes<any>
}

export const updateDatamodelfield = async (data: FieldList) => {
  const res = await request.post<IRes<any>>('/api/datamodelfield/update', data)
  return res as unknown as IRes<any>
}

export const delEntityFieldById = async (id: number) => {
  const res = await request.get<IRes<any>>(`/api/datamodelfield/delById/${id}`)
  return res as unknown as IRes<any>
}

export const searchDatamodel = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/datamodel/searc', data)
  return res as unknown as IRes<any>
}

export const exportDatamodel = async (data: any) => {
  const res = await request.post<IRes<any>>('api/datamodel/export', data)
  return res as unknown as IRes<any>
}

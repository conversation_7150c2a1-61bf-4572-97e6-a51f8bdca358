import type { Component } from 'vue'

// 基础提示文本类型
export type TipTextString = string

// 动态提示文本函数类型
export type TipTextFunction = (
  fieldValue: any,
  formData: Record<string, any>,
  fieldConfig: Record<string, any>
) => string

// 自定义组件配置类型
export interface TipComponentConfig {
  component: Component | string
  props?: Record<string, any>
}

// 统一的提示类型
export type TipText = TipTextString | TipTextFunction | TipComponentConfig

// 类型守卫函数
export const isTipString = (tip: TipText): tip is TipTextString => {
  return typeof tip === 'string'
}

export const isTipFunction = (tip: TipText): tip is TipTextFunction => {
  return typeof tip === 'function'
}

export const isTipComponent = (tip: TipText): tip is TipComponentConfig => {
  return tip !== null && 
         typeof tip === 'object' && 
         'component' in tip &&
         tip.component !== undefined
}

// 提示配置标准化函数
export const normalizeTipConfig = (
  tipText: TipText,
  fieldValue: any,
  formData: Record<string, any>,
  fieldConfig: Record<string, any>
): { type: 'text' | 'component'; config: any; text?: string } => {
  
  // 自定义组件类型
  if (isTipComponent(tipText)) {
    return {
      type: 'component',
      config: {
        component: tipText.component,
        props: tipText.props || {}
      }
    }
  }
  
  // 函数类型
  if (isTipFunction(tipText)) {
    return {
      type: 'text',
      config: {
        lineClamp: 1,
        bgColor: '#000',
        textColor: '#fff'
      },
      text: tipText(fieldValue, formData, fieldConfig)
    }
  }
  
  // 字符串类型
  if (isTipString(tipText)) {
    return {
      type: 'text',
      config: {
        lineClamp: 1,
        bgColor: '#000',
        textColor: '#fff'
      },
      text: tipText
    }
  }
  
  // 兜底处理
  return {
    type: 'text',
    config: {
      lineClamp: 1,
      bgColor: '#000',
      textColor: '#fff'
    },
    text: String(tipText || '')
  }
}

// 提示配置创建器
export const createTipConfig = {
  // 创建文本提示
  text: (text: string, options?: { bgColor?: string; textColor?: string; lineClamp?: number }): TipComponentConfig => ({
    component: 'MoreTextTips',
    props: {
      lineClamp: options?.lineClamp || 1,
      bgColor: options?.bgColor || '#000',
      textColor: options?.textColor || '#fff',
      text
    }
  }),
  
  // 创建动态文本提示
  dynamic: (fn: TipTextFunction): TipTextFunction => fn,
  
  // 创建自定义组件提示
  component: (component: Component | string, props?: Record<string, any>): TipComponentConfig => ({
    component,
    props: props || {}
  })
}

// 常用提示预设
export const tipPresets = {
  // 必填提示
  required: (fieldName?: string) => 
    createTipConfig.text(`${fieldName || '此字段'}为必填项，请确保填写完整`),
  
  // 格式提示
  format: (format: string) => 
    createTipConfig.text(`请按照${format}格式填写`),
  
  // 长度提示
  length: (min?: number, max?: number) => {
    let text = '请注意字符长度限制'
    if (min && max) text = `长度应在${min}-${max}个字符之间`
    else if (min) text = `长度不能少于${min}个字符`
    else if (max) text = `长度不能超过${max}个字符`
    return createTipConfig.text(text)
  },
  
  // 选择提示
  select: (description: string) => 
    createTipConfig.text(`请选择合适的选项。${description}`),
  
  // 动态值提示
  withValue: (baseText: string, prefix = '当前值：', suffix = '') => 
    createTipConfig.dynamic((fieldValue) => {
      if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
        return `${baseText}（${prefix}${fieldValue}${suffix}）`
      }
      return baseText
    }),
  
  // 条件提示
  conditional: (
    condition: (fieldValue: any, formData: any) => boolean,
    trueTip: TipText,
    falseTip?: TipText
  ) => createTipConfig.dynamic((fieldValue, formData, fieldConfig) => {
    const shouldShowTrue = condition(fieldValue, formData)
    const selectedTip = shouldShowTrue ? trueTip : falseTip
    
    if (!selectedTip) return ''
    
    if (isTipString(selectedTip)) return selectedTip
    if (isTipFunction(selectedTip)) return selectedTip(fieldValue, formData, fieldConfig)
    
    return '' // 组件类型的条件提示需要特殊处理
  })
}

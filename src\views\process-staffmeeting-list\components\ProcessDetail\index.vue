<template>
  <div class="list-item">
    <div class="num-box flex mr4" v-if="value?.taskCompletionRatio">
      <img v-if="[3, 4].includes(value?.status)" src="@/assets/images/process-icon/completed.svg" class="mr4" />
      <img
        v-else-if="[1, 2, 5].includes(value?.status)"
        src="@/assets/images/process-icon/inProgress.svg"
        class="mr4"
      />
      <img v-else-if="[0].includes(value?.status)" src="@/assets/images/process-icon/notStarted.svg" class="mr4" />
      <span>{{ value?.taskCompletionRatio || '0/0' }}</span>
    </div>
    <template v-else>
      <img v-if="[3, 4].includes(value?.status)" src="@/assets/images/process-icon/completed.svg" class="mr8" />
      <img
        v-else-if="[1, 2, 5].includes(value?.status)"
        src="@/assets/images/process-icon/inProgress.svg"
        class="mr8"
      />
      <img v-else-if="[0].includes(value?.status)" src="@/assets/images/process-icon/notStarted.svg" class="mr8" />
    </template>
    <MoreTextTips style="width: auto" :line-clamp="1">
      <span>{{ value?.topicName }}</span>
    </MoreTextTips>
    <!-- <span>{{ value?.topicName }}</span> -->
    <FTooltip
      v-if="value?.instanceId"
      color="#fff"
      :overlayStyle="{ width: '500px', maxWidth: 'none' }"
      :getPopupContainer="target"
      overlayClassName="cust-detail-relate-process-tooltip"
      @visibleChange="open => handleOpenChange(open, value)"
    >
      <template #title>
        <div class="content-box">
          <div class="tip-header">流程进度</div>
          <div class="info">
            <FSpin :spinning="!!value?.loading">
              <div class="process-list">
                <ExpandableTree
                  :items="value?.processData ?? []"
                  has-line
                  :indent="40"
                  :expandedIds="expandedIds || []"
                >
                  <template #label="{ item }">
                    <div class="flex space-between process-item">
                      <div
                        class="flex left"
                        :title="`${item?.topicName ?? item?.taskName ?? '--'}/${item?.superviser ?? '--'}`"
                      >
                        <template v-if="item?.taskName">
                          <img
                            v-if="item?.taskName && [2, 3, 5].includes(item?.status)"
                            src="@/assets/images/process-icon/completed.svg"
                            class="mr4"
                          />
                          <img
                            v-else-if="item?.taskName && [1, 6].includes(item?.status)"
                            src="@/assets/images/process-icon/inProgress.svg"
                            class="mr4"
                          />
                          <img
                            v-else-if="item?.taskName && [null, 0, 4].includes(item?.status)"
                            src="@/assets/images/process-icon/notStarted.svg"
                            class="mr4"
                          />
                        </template>
                        <template v-else>
                          <img
                            v-if="[3, 4].includes(item?.status)"
                            src="@/assets/images/process-icon/completed.svg"
                            class="mr4"
                          />
                          <img
                            v-else-if="[1, 2, 5].includes(item?.status)"
                            src="@/assets/images/process-icon/inProgress.svg"
                            class="mr4"
                          />
                          <img
                            v-else-if="[0].includes(item?.status)"
                            src="@/assets/images/process-icon/notStarted.svg"
                            class="mr4"
                          />
                        </template>
                        <span class="text-name">{{ item?.topicName ?? item?.taskName ?? '--' }}</span>
                        <span class="text-nowrap">/{{ item?.superviser ?? '--' }}</span>
                      </div>
                      <span class="color999 text-nowrap" v-if="item?.taskName">{{
                        (item?.taskCompletedTime && transformDate(item?.taskCompletedTime, 'YYYY-MM-DD HH:mm:ss')) ??
                        '--'
                      }}</span>
                      <span class="color999 text-nowrap" v-else>{{
                        (item?.completeTime && transformDate(item?.completeTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--'
                      }}</span>
                    </div>
                  </template>
                  <template #default="{ item }">
                    <div class="flex space-between process-item">
                      <div
                        class="flex left"
                        :title="`${item?.topicName ?? item?.taskName ?? '--'}/${item?.superviser}`"
                      >
                        <img
                          v-if="[2, 3, 5].includes(item?.status)"
                          src="@/assets/images/process-icon/completed.svg"
                          class="mr4"
                        />
                        <img
                          v-else-if="[1, 6].includes(item?.status)"
                          src="@/assets/images/process-icon/inProgress.svg"
                          class="mr4"
                        />
                        <img
                          v-else-if="[null, 0, 4].includes(item?.status)"
                          src="@/assets/images/process-icon/notStarted.svg"
                          class="mr4"
                        />
                        <span class="text-name">{{ item?.taskName ?? '--' }}</span>
                        <span class="text-nowrap">/{{ item?.superviser ?? '--' }}</span>
                      </div>
                      <span class="color999 text-nowrap">{{
                        (item?.taskCompletedTime && transformDate(item?.taskCompletedTime, 'YYYY-MM-DD HH:mm:ss')) ??
                        '--'
                      }}</span>
                    </div>
                  </template>
                </ExpandableTree>
              </div>
            </FSpin>
          </div>
        </div>
      </template>
      <i class="iconfont icontubiao_tishi colorBBB ml8" />
    </FTooltip>
  </div>
</template>

<script setup lang="ts">
import { FSpin } from '@fs/smart-design'
import { ref, computed } from 'vue'
import { getIpdSubProcessDetails } from '@/api'
import dayjs from 'dayjs'
import { transformDate, jumpToDemand } from '@/utils'
import ExpandableTree from '@/views/process-ipd-list/components/ExpandableTree/index.vue'
import MoreTextTips from '@/components/MoreTextTips/index'
import { handleEmptyChildren } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'

interface IProps {
  value: any
  expandedIds?: (string | number)[]
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:value'])
const value = computed({
  get: () => props?.value ?? {},
  set: val => emits('update:value', val),
})
const target = ref(() => document.querySelector('#container'))

const getProcessData = async item => {
  if (
    item?.loading ||
    item?.pending ||
    (item?.initStartTime && dayjs().diff(dayjs(item.initStartTime), 'seconds') <= 10)
  )
    return
  try {
    item.loading = true
    item.pending = true
    item['processData'] = []
    const { data = [] } = await getIpdSubProcessDetails(item.instanceId)
    item['processData'] = handleEmptyChildren(data)
    item.initStartTime = new Date().getTime()
  } finally {
    item.loading = false
    item.pending = false
  }
}

const handleOpenChange = (open: any, item: any) => {
  open && getProcessData(item)
}
</script>

<style lang="scss">
.cust-detail-relate-process-tooltip {
  max-width: 400px;
  width: 400px;
  .fs-tooltip-inner {
    padding: 0;
    .content-box {
      color: #333333;
      .tip-header {
        padding: 12px 16px;
        border-bottom: 1px solid #eeeeee;
        font-weight: 500;
        font-size: 14px;
      }
      .info {
        padding: 12px 12px 12px 16px;
        margin-right: 4px;
        min-height: 100px;
        max-height: 320px;
        overflow-y: scroll;
        .left {
          max-width: 66%;
          flex: auto;
        }
        .text-name {
          max-width: 145px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .text-nowrap {
          white-space: nowrap;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.mt8 {
  margin-top: 8px;
}
.ml8 {
  margin-left: 8px;
}
.mr4 {
  margin-right: 4px;
}
.mr8 {
  margin-right: 8px;
}
.code-link {
  cursor: pointer;
  color: #378eef;
}
.flex {
  display: flex;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.process-item + .process-item {
  margin-top: 8px;
}
.list-item {
  display: flex;
  align-items: center;
  .icontubiao_tishi {
    font-size: 14px;
    line-height: 16px;
  }
  .num-box {
    align-items: center;
    background: #f8f8f8;
    border-radius: 3px;
    padding: 0 4px;
  }
}
</style>

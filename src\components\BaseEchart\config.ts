import * as Echarts from 'echarts/core'
import {
  DatasetComponent,
  DatasetComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
  TitleComponent,
  TimelineComponentOption,
  DataZoomComponent,
  DataZoomComponentOption,
  GraphicComponent,
  GraphicComponentOption,
} from 'echarts/components'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, PieSeriesOption, BarSeriesO<PERSON>, LineChart, LineSeriesOption } from 'echarts/charts'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

Echarts.use([
  DatasetComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  <PERSON><PERSON>omponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>vasRenderer,
  <PERSON>Layout,
  DataZoomComponent,
  GraphicComponent,
])

export type EChartsOption = Echarts.ComposeOption<
  | DatasetComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LegendComponentOption
  | TimelineComponentOption
  | BarSeriesOption
  | PieSeriesOption
  | LineSeriesOption
  | DataZoomComponentOption
  | GraphicComponentOption
>

export default Echarts

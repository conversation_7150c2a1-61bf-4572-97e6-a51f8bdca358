<template>
  <FModal
    v-model:visible="value"
    title="发布"
    :width="400"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirm-loading="loading"
  >
    <div class="dispatch-modal-wrapper">
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FFormItem label="发布方式" name="type">
          <FRadioGroup
            v-model:value="formState.type"
            :options="publishOptions"
            :disabled="publishOptions.length === 1"
          />
        </FFormItem>
      </FForm>
    </div>
  </FModal>
</template>
<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { deepClone } from '@/utils'
interface FormState {
  type: number
}

interface IProps {
  value: boolean
  publishOptions: any[]
}

const emit = defineEmits(['submit', 'update:value'])
const props = defineProps<IProps>()
const value = computed({
  get() {
    return props.value
  },
  set(val: boolean) {
    emit('update:value', val)
  },
})
const loading = ref(false)
const rules: Record<string, Rule[]> = {
  type: [{ required: true, message: '请选择', trigger: 'change' }],
}
const formRef = ref<FormInstance>()
const formState = reactive<FormState>({
  type: props?.publishOptions[0]?.value,
})
const handleOk = async () => {
  try {
    loading.value = true
    if (!formRef.value) return
    await formRef.value.validate()
    emit('submit', deepClone(formState))
    handleCancel()
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  value.value = false
  formState.type = props?.publishOptions[0]?.value
}
</script>
<style lang="scss">
.dispatch-modal-wrapper {
  .fs-form-item {
    margin-bottom: 22px !important;
  }
  div.fs-form-item-control-input-content,
  .fs-form-item-control-input-content textarea.fs-input {
    height: auto !important;
  }

  .fs-modal-footer .fs-btn {
    display: inline-block;
  }
}
</style>

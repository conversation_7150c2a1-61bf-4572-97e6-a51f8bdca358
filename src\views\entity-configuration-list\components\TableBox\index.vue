<template>
  <div class="table-box-container">
    <div class="header">
      <span class="title">{{ i18n.t('实体列表') }}</span>
      <FButton class="export-btn" type="primary" @click="toDetailInfo(null)">
        <i class="iconxinzeng iconfont"></i>{{ i18n.t('新增实体') }}
      </FButton>
    </div>
    <FTable
      :data-source="list"
      :loading="loading"
      :columns="columns"
      table-layout="fixed"
      :pagination="false"
      :scroll="{ x: '100%' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <span :class="[record.status === 1 && 'off', record.status === 0 && 'open']">{{
            (record.status === 0 && i18n.t('启用')) || (record.status === 1 && i18n.t('停用')) || '--'
          }}</span>
        </template>
        <template v-if="column.dataIndex === 'describe'">
          <MoreTextTips v-if="record.describe" :lineClamp="1">
            {{ record.describe }}
          </MoreTextTips>
        </template>
        <template v-if="column.dataIndex === 'handle'">
          <FTooltip>
            <template #title>{{ i18n.t('详情') }}</template>
            <i
              class="iconfont iconfont-hover cursor color4677C7 icontubiao_chakanxiangqing mr12"
              @click="toDetailInfo(record)"
            ></i>
          </FTooltip>
          <FPopconfirm
            :title="i18n.t('确定操作数据删除吗？')"
            :content="i18n.t('操作删除后可能影响功能，请谨慎操作！')"
            :ok-text="i18n.t('确定')"
            :cancel-text="i18n.t('取消')"
            @confirm="DeleteFilled(record)"
          >
            <span
              class="iconfont iconfont-hover cursor color4677C7 icontubiao_shanchu1 marginR5"
              :title="i18n.t('删除')"
            ></span>
          </FPopconfirm>
        </template>
      </template>
    </FTable>
  </div>
</template>

<script setup lang="ts">
import { IEntityPageListData } from '@/types/entity'
import MoreTextTips from '@/components/MoreTextTips/index'
import { columns } from './tableConfig'
import { useI18n } from '@/utils'
import { useRouter } from 'vue-router'
import { delEntityById } from '@/api'
const i18n = useI18n()

type propsType = {
  list: IEntityPageListData[]
  loading: boolean
}
const props = withDefaults(defineProps<propsType>(), {
  list: () => [],
  loading: false,
})
const emits = defineEmits(['getProcessList'])
const route = useRouter()

const toDetailInfo = (record: any) => {
  const query: any = {}
  record && (query.id = record.id)
  route.push({
    name: 'entityConfigurationDetail',
    query,
  })
}

const DeleteFilled = async (record: any) => {
  if (!record.id) return
  const res = await delEntityById(record.id)
  if (res.code !== 200) throw new Error(res.msg)
  emits('getProcessList')
}
</script>

<style scoped lang="scss">
.table-box-container {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }
  :deep(.fs-table-cell) {
    &:empty {
      &::after {
        content: '--';
      }
    }
    &.fs-table-cell-fix-right-first {
      &:empty {
        &::after {
          content: '';
        }
      }
    }
  }

  .off {
    display: inline-block;
    padding: 0 3px;
    background: #eafaf2;
    border-radius: 2px;
    border: 1px solid #f04141;
    color: #f04141;
    line-height: 18px;
    background: #fdecec;
  }
  .open {
    display: inline-block;
    padding: 0 3px;
    background: #eafaf2;
    border-radius: 2px;
    border: 1px solid #2fcc83;
    color: #2fcc83;
    line-height: 18px;
  }
  .mr12 {
    margin-right: 12px;
  }
}
</style>

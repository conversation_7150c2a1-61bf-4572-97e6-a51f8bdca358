<template>
  <div class="process-mm-list">
    <!-- <Breadcrumb class="none-container-padding" /> -->
    <div class="card-content-shadow pt24 pl24 pr24 mb16">
      <SearchContent v-model:query-data="queryData" />
    </div>
    <div class="card-content-shadow pt24 pl24 pr24 mb24">
      <div class="flex space-between mb16">
        <div class="fw-500 f14">MM流程列表</div>
        <FSpace :size="[12]" wrap class="handle-row-box">
          <FButton class="export-btn" type="default" @click="handleExport">
            <i class="icontubiao_xiazai iconfont"></i>下载数据
          </FButton>
          <FButton type="primary" class="mr6" @click="HandleProcessFn({ key: MM_CONFIG_ID })">
            <template #icon><i class="iconfont icontubiao_tianjia1" /></template>
            创建流程</FButton
          >
          <!-- <component :is="columnsConfig.Operation"></component> -->
        </FSpace>
      </div>
      <FTable
        class="table-warp"
        :columns="columnsConfig.tableColumns"
        :loading="loading"
        :data-source="dataList"
        :row-key="(data:any) => data.id"
        :row-selection="rowSelection"
        :sticky="{ offsetHeader: 0 }"
        :scroll="{ x: 'min-content' }"
        :pagination="{
          total: paging.total,
          current: paging.pageNum,
          pageSize: paging.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
          onChange: onPaginationChangeFn
        }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'creatorInfo'">
            <div class="label-box">
              <span class="label">[录入时间]</span>
              <span>{{
                (record?.createdTime && transformDate(record?.createdTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--'
              }}</span>
            </div>
            <div class="label-box">
              <span class="label">[录入人]</span>
              <span>{{ record?.creatorName ?? '--' }}</span>
            </div>
            <span class="code-link" @click="logModalRef?.onOpenFn(record?.id)">查看日志</span>
          </template>

          <template v-if="column.dataIndex === 'mmInfo'">
            <div class="label-box">
              <span class="label">[MM名称]</span>
              <span>{{ record?.topicName ?? '--' }}</span>
            </div>
            <div class="label-box">
              <span class="label">[流程编码]</span>
              <span :class="[record?.id ? 'code-link' : '']" @click="onJumpDemandDetial(record)">{{
                record?.processInstanceCode ?? '--'
              }}</span>
            </div>
            <div class="label-box">
              <span class="label">[产品线]</span>
              <span>{{ record?.type ?? '--' }}</span>
            </div>
            <div class="label-box">
              <span class="label">[背景]</span>
              <MoreTextTips :line-clamp="2">
                <MyViewer :html="record?.xqText ?? '--'" />
              </MoreTextTips>
            </div>
          </template>

          <template v-if="column.dataIndex === 'mi'">
            <div class="icon-box flex">
              <i :class="[getMiIconInfo(record?.mi ?? []).supplier]" class="iconfont mr4" />
              <span>供应商信息收集</span>
            </div>
            <div class="icon-box flex">
              <i :class="[getMiIconInfo(record?.mi ?? []).competitor]" class="iconfont mr4" />
              <span>竞品信息收集</span>
            </div>
            <div class="icon-box flex">
              <i :class="[getMiIconInfo(record?.mi ?? []).customer]" class="iconfont mr4" />
              <span>客户需求收集</span>
            </div>
          </template>

          <template v-if="column.dataIndex === 'handleKey'">
            <span class="code-link" @click="() => record?.operate && onJumpDemandDetial(record)">{{
              (record?.operate && record?.currentMilepostNod?.topicName) || '--'
            }}</span>
          </template>

          <template v-if="column.dataIndex === 'stpInfo'">
            <div class="label-box" v-for="(item, index) in record?.stpScheme ?? []" :key="index">
              <span class="label">{{ `[方案${index + 1}]` }}</span>
              <span>{{ item?.mm_famc ?? '--' }}</span>
            </div>
            <div class="label-box">
              <span class="label">[STP报告]</span>
              <MoreTextTips :line-clamp="1">
                <span :class="[record?.stpReport ? 'code-link' : '']" @click="onOpenDocument(record?.stpReport)">{{
                  record?.stpReport ?? '--'
                }}</span>
              </MoreTextTips>
            </div>
            <div class="label-box">
              <span class="label">[责任人]</span>
              <span>{{ record?.stpNodeUser ?? '--' }}</span>
            </div>
          </template>

          <template v-if="column.dataIndex === 'planInfo'">
            <div class="icon-box flex">
              <i :class="[getStatusInfo(record?.schemeTask ?? {}).icon]" class="iconfont mr4" />
              <span class="mr4">{{ (record?.schemeTask ?? {})?.taskName ?? '--' }}</span>
              <FTooltip color="#fff" :getPopupContainer="target">
                <i class="iconfont icontubiao_tishi colorBBB" />
                <template #title>
                  <div class="label-box">
                    <span class="label">[处理人]</span>
                    <span>{{ (record?.schemeTask ?? {})?.superviser || '--' }}</span>
                  </div>
                  <div class="label-box">
                    <span class="label">[处理时间]</span>
                    <span>{{
                      ((record?.schemeTask ?? {})?.taskCompletedTime &&
                        transformDate((record?.schemeTask ?? {})?.taskCompletedTime, 'YYYY-MM-DD HH:mm:ss')) ??
                      '--'
                    }}</span>
                  </div>
                </template>
              </FTooltip>
            </div>
            <div class="icon-box flex">
              <i :class="[getStatusInfo(record?.poadmapOutputTask ?? {}).icon]" class="iconfont mr4" />
              <span class="mr4">{{ (record?.poadmapOutputTask ?? {})?.taskName ?? '--' }}</span>
              <FTooltip color="#fff" :getPopupContainer="target">
                <i class="iconfont icontubiao_tishi colorBBB" />
                <template #title>
                  <div class="label-box">
                    <span class="label">[处理人]</span>
                    <span>{{ (record?.poadmapOutputTask ?? {})?.superviser || '--' }}</span>
                  </div>
                  <div class="label-box">
                    <span class="label">[处理时间]</span>
                    <span>{{
                      ((record?.poadmapOutputTask ?? {})?.taskCompletedTime &&
                        transformDate((record?.poadmapOutputTask ?? {})?.taskCompletedTime, 'YYYY-MM-DD HH:mm:ss')) ??
                      '--'
                    }}</span>
                  </div>
                </template>
              </FTooltip>
            </div>
            <div class="icon-box flex">
              <i :class="[getStatusInfo(record?.charterOutputTask ?? {}).icon]" class="iconfont mr4" />
              <span class="mr4">{{ (record?.charterOutputTask ?? {})?.taskName ?? '--' }}</span>
              <FTooltip color="#fff" :getPopupContainer="target">
                <i class="iconfont icontubiao_tishi colorBBB" />
                <template #title>
                  <div class="label-box">
                    <span class="label">[处理人]</span>
                    <span>{{ (record?.charterOutputTask ?? {})?.superviser || '--' }}</span>
                  </div>
                  <div class="label-box">
                    <span class="label">[处理时间]</span>
                    <span>{{
                      ((record?.charterOutputTask ?? {})?.taskCompletedTime &&
                        transformDate((record?.charterOutputTask ?? {})?.taskCompletedTime, 'YYYY-MM-DD HH:mm:ss')) ??
                      '--'
                    }}</span>
                  </div>
                </template>
              </FTooltip>
            </div>
            <div class="label-box">
              <span class="label">[Charter]</span>
              <MoreTextTips :line-clamp="1">
                <span
                  :class="[record?.charterReport ? 'code-link' : '']"
                  @click="onOpenDocument(record?.charterReport)"
                  >{{ record?.charterReport ?? '--' }}</span
                >
              </MoreTextTips>
            </div>
            <div class="label-box">
              <span class="label">[责任人]</span>
              <span>{{ record?.charterUser ?? '--' }}</span>
            </div>
          </template>

          <template v-if="column.dataIndex === 'charterInfo'">
            <div class="label-box flex">
              <span class="label">[评审结果]</span>
              <FTag
                v-if="record?.reviewResult"
                class="mr8"
                size="small"
                :color="
                  record?.reviewResult == '同意' ? 'success' : record?.reviewResult == '不同意' ? 'error' : 'default'
                "
                >{{ record?.reviewResult ?? '--' }}
              </FTag>
              <span v-else class="mr8">--</span>
              <FTooltip color="#fff" :getPopupContainer="target">
                <i class="iconfont icontubiao_tishi colorBBB" />
                <template #title>
                  <div class="label-box">
                    <span class="label">[处理人]</span>
                    <span>{{ (record?.charterNode ?? {})?.superviser || '--' }}</span>
                  </div>
                  <div class="label-box">
                    <span class="label">[处理时间]</span>
                    <span>{{
                      ((record?.charterNode ?? {})?.completeTime &&
                        transformDate((record?.charterNode ?? {})?.completeTime, 'YYYY-MM-DD HH:mm:ss')) ??
                      '--'
                    }}</span>
                  </div>
                </template>
              </FTooltip>
            </div>
            <div class="label-box">
              <span class="label">[评审建议]</span>
              <MoreTextTips :line-clamp="2">
                <MyViewer :html="record?.reviewSuggestion ?? '--'" />
              </MoreTextTips>
            </div>
          </template>

          <template v-if="column.dataIndex === 'charterReportInfo'">
            <div class="label-box">
              <span class="label">[关联IPD流程]</span>
              <span class="empty-content">
                <span
                  style="display: block"
                  v-for="item in record?.ipd ?? []"
                  :key="item.id"
                  class="code-link"
                  @click="onJumpDemandDetial(item)"
                  >{{ item?.processInstanceCode ?? '--' }}</span
                >
              </span>
            </div>
            <div class="label-box">
              <span class="label">[责任人]</span>
              <span>{{ record?.charterTransferUser ?? '--' }}</span>
            </div>
          </template>
          <template v-if="column.dataIndex === 'dataInfo'">
            <div class="label-box">
              <span class="label">[STP报告]</span>
              <MoreTextTips :line-clamp="1">
                <span :class="[record?.stpReport ? 'code-link' : '']" @click="onOpenDocument(record?.stpReport)">{{
                  record?.stpReport ?? '--'
                }}</span>
              </MoreTextTips>
            </div>
            <div class="label-box">
              <span class="label">[Charter报告]</span>
              <MoreTextTips :line-clamp="1">
                <span
                  :class="[record?.charterReport ? 'code-link' : '']"
                  @click="onOpenDocument(record?.charterReport)"
                  >{{ record?.charterReport ?? '--' }}</span
                >
              </MoreTextTips>
            </div>
          </template>
        </template>
      </FTable>
    </div>
    <LogModal ref="logModalRef" />
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, reactive, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { FButton, message } from '@fs/smart-design'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import SearchContent from './components/SearchContent/index.vue'
import LogModal from '@/views/process-ipd-list/components/LogModal/index.vue'
import { tableColumnKeys, useTableColumn } from '@/components/TableOperation/index'
import MoreTextTips from '@/components/MoreTextTips/index'
import MyViewer from '@/components/MoreTextTips/MyViewer.vue'
import { exportProcess, getMmProcessList, ProcessIpdListParams } from '@/api'
import { getByIdProcessConfig } from '@/api/processManagement'
import { cache, transformDate, jumpToDemand, MM_CONFIG_ID } from '@/utils'

const { currentRoute, resolve } = useRouter()
const route = useRoute()
const router = useRouter()
const routerName = computed<any>(() => currentRoute.value?.name)
const loading = ref(false)
const paging = reactive<any>({ pageNum: 1, pageSize: 10, total: 0 })
const dataList = ref<any[]>([])
const selectedKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[]) => (selectedKeys.value = selectedRowKeys),
}))
const columnsConfig = reactive(
  useTableColumn(
    [
      { title: '录入时间/操作日志', dataIndex: 'creatorInfo', key: 'creatorInfo', width: 260 },
      { title: '流程信息', dataIndex: 'mmInfo', key: 'mmInfo', width: 260 },
      { title: '市场洞察（MI）', dataIndex: 'mi', key: 'mi', width: 260 },
      { title: '操作', dataIndex: 'handleKey', key: 'handleKey', width: 180 },
      { title: 'STP评审', dataIndex: 'stpInfo', key: 'stpInfo', width: 260 },
      { title: '产品和技术规划', dataIndex: 'planInfo', key: 'planInfo', width: 260 },
      { title: '路标Charter评审', dataIndex: 'charterInfo', key: 'charterInfo', width: 260 },
      { title: 'Charter报告移交', dataIndex: 'charterReportInfo', key: 'charterReportInfo', width: 210 },
      { title: '资料汇总', dataIndex: 'dataInfo', key: 'dataInfo', width: 210 },
    ],
    tableColumnKeys.processListTable_mm
  )
)
const queryData = ref<ProcessIpdListParams>({})
const logModalRef = ref()
const target = ref(() => document.querySelector('#container'))

const getStatusInfo = data => {
  let info = {
    icon: 'icontubiao_jinggao error-color', // 图标
  }
  if ([2, 3].includes(data?.status)) {
    info.icon = 'icontubiao_chenggong sucess-color'
  }
  return info
}

const getMiIconInfo = (list: any[]) => {
  let info = {
    supplier: 'icontubiao_cuowu error-color', // 图标
    competitor: 'icontubiao_cuowu error-color', // 图标
    customer: 'icontubiao_cuowu error-color', // 图标
  }
  list.forEach(item => {
    if (item?.mm_xqlx === '供应商需求') {
      info.supplier = 'icontubiao_chenggong sucess-color'
    } else if (item?.mm_xqlx === '竞品需求类型') {
      info.competitor = 'icontubiao_chenggong sucess-color'
    } else if (item?.mm_xqlx === '客户需求') {
      info.customer = 'icontubiao_chenggong sucess-color'
    }
  })
  return info
}

const onOpenDocument = (url: any) => {
  if (url) {
    window.open(url, '_blank')
  } else {
    message.error('文档不存在')
  }
}

// 需求详情跳转
const onJumpDemandDetial = (record: any) => {
  record?.id && jumpToDemand(record.id, undefined, true, router)
}

const goCreatePage = (id, processDefineKey) => {
  const { href } = resolve({
    name: 'DemandAdd',
    params: {
      id: id,
      processDefineKey: processDefineKey,
    },
    query: {
      localName: route.name as string,
    },
  })
  window.open(href, '_blank')
}

const HandleProcessFn = async ({ key }) => {
  if (!key) return
  const { data = {} } = (await getByIdProcessConfig(key)) as any
  data?.processDefineKey && goCreatePage(key, data?.processDefineKey)
}

// 查询列表
const queryDataList = async () => {
  try {
    loading.value = true
    const data = { ...queryData.value }
    cache.set(
      routerName?.value,
      JSON.stringify({
        ...(data?.cacheValue ?? {}),
        pageNum: paging.pageNum,
        pageSize: paging.pageSize,
      })
    )
    delete data.cacheValue
    const res = await getMmProcessList(data, paging)
    dataList.value = res?.data?.list || []
    paging.total = res?.data?.totalCount || 0
  } finally {
    selectedKeys.value = []
    loading.value = false
  }
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  paging.pageNum = current
  paging.pageSize = pageSize
  queryDataList()
}

const handleExport = async () => {
  if (!dataList.value.length) {
    return message.warning('当前页面无数据，请重新选择！')
  }
  const query = queryData.value
  const params = {
    processConfigId: MM_CONFIG_ID,
    createUser: query.creator,
    endTime: query.endTime,
    startTime: query.startTime,
    queryInput: query.queryInput,
    type: 0,
  }
  const res = await exportProcess(params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success('下载成功，请在飞书查看！')
}

// 查询列表
const onGetSearchData = (data: any) => {
  queryData.value = data
  paging.pageNum = data?.cacheValue?.pageNum || 1
  paging.pageSize = data?.cacheValue?.pageSize || 10
  queryDataList()
}

watch(
  () => queryData.value,
  val => {
    onGetSearchData(val)
  },
  { deep: true }
)
</script>
<style lang="scss">
p {
  margin: 0;
}
.label-box {
  display: flex;
  color: #333;
  .label {
    margin-right: 4px;
    color: #999;
    white-space: nowrap;
  }
}
</style>
<style scoped lang="scss">
.process-mm-list {
  .none-container-padding {
    margin-top: -20px;
    // margin-left: -20px;
    // width: calc(100% + 40px);
  }
  .card-content-shadow {
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .code-link {
    cursor: pointer;
    color: #378eef;
  }
  .label-box {
    display: flex;
    color: #333;
    .label {
      margin-right: 4px;
      color: #999;
      white-space: nowrap;
    }
  }
  .mr6 {
    margin-right: 6px;
  }
  .mr4 {
    margin-right: 4px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .error-color {
    color: #f04141;
  }
  .sucess-color {
    color: #2fcc83;
  }
  .empty-content {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .count-info-content {
    line-height: 18px;
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
}
</style>

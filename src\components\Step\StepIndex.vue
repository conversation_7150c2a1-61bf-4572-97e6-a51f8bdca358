<template>
  <div class="step-box">
    <div class="step-item" v-for="(item, index) in props.data" :key="item.id">
      <Step :item="item" :data="props.data" :index="index" />
      <slot name="footer" :row="item"></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
import { IProcess } from '@/types/handle'

import Step from './StepBasic.vue' // 基础流程状态

interface IProps {
  data: IProcess[]
}

const props = defineProps<IProps>()
</script>

<style lang="scss" scoped>
.delay {
  background-color: #ff4a4a !important;
}
.step-box {
  //  background-color: #fff;
  line-height: 1;
  display: flex;

  .step-item {
    font-size: 12px;
    padding: 16px 0 0 0;
    width: 220px;
    flex-shrink: 0;
  }
}
</style>

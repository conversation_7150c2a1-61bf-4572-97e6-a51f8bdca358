/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path')
const { defineConfig } = require('@vue/cli-service')
const { sentryWebpackPlugin } = require('@sentry/webpack-plugin')
const { version } = require('./package.json')
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')

const resolve = dir => path.join(__dirname, dir)
const dsn = 'https://<EMAIL>/17'
const baseUrl = {
  localhost: '/',
  pre: 'https://resource-accel.fs.com/backstage/pre/bpm-platform-bpm-project-manage-frontend/',
  test: 'https://resource-accel.fs.com/backstage/test/bpm-platform-bpm-project-manage-frontend/',
  staging: 'https://resource-accel.fs.com/backstage/uat/bpm-platform-bpm-project-manage-frontend/',
  production: 'https://resource-accel.fs.com/backstage/prod/bpm-platform-bpm-project-manage-frontend/',
  m: '/',
  sit: '/',
  compliance: '/',
  localtest: '/',
}

const mode = process.env.VUE_APP_ENV || 'localhost'
const release = `bpm-process@${version}+${mode}`
module.exports = defineConfig({
  publicPath: baseUrl[mode] || '/',
  parallel: false,
  transpileDependencies: true,
  // productionSourceMap: false,
  chainWebpack: config => {
    config.resolve.alias.set('@', resolve('./src'))
  },
  configureWebpack: {
    devtool: process.env.NODE_ENV === 'development' ? 'eval-source-map' : false,
    plugins: [
      // new BundleAnalyzerPlugin(),
      // sentryWebpackPlugin({
      //   url: 'https://sentry.fs.com',
      //   org: 'sentry',
      //   project: 'bpm-process',
      //   authToken:
      //     '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',
      //   telemetry: false,
      //   debug: ['development', 'test'].includes(mode),
      //   // release: {
      //   //   name: release,
      //   //   deploy: { env: mode },
      //   // },
      //   sourcemaps: {
      //     assets: 'dist/js/**',
      //     ignore: ['node_modules'],
      //     filesToDeleteAfterUpload: 'dist/**/*.map',
      //   },
      //   errorHandler: err => console.log('sentry error:', err),
      //   _experiments: {
      //     moduleMetadata: data => {
      //       const { release } = data
      //       return { dsn, release }
      //     },
      //   },
      // }),
    ],
    // externals: {
    //   vue: 'Vue',
    //   dayjs: 'dayjs',
    //   // '@fs/smart-design': 'SmartDesign',
    // },
  },
  devServer: {
    hot: false,
    port: 8300,
    host: 'local.whgxwl.com',
    // https: true,
    headers: { 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Headers': '*' },
    // historyApiFallback: { index: '/index.html' },
    devMiddleware: {
      writeToDisk: false,
    },
  },
})

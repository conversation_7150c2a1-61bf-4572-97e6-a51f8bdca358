<template>
  <div class="appraise-box">
    <span class="label">[{{ title }}]</span>
    <FTag
      v-if="data?.passed"
      class="mr8"
      size="small"
      :color="data?.passed == '1' ? 'success' : data?.passed == '2' ? 'error' : 'default'"
      >{{ data?.passed == '1' ? '通过' : data?.passed == '2' ? '不通过' : '--' }}
    </FTag>
    <span v-else class="mr8">--</span>
    <FTooltip color="#fff" :getPopupContainer="target">
      <template #title>
        <div class="content-box">
          <div class="label-box">
            <span class="label w52">评审主题:</span>
            <span>{{ data?.reviewSubject ?? '--' }}</span>
          </div>
          <div class="label-box mt8">
            <span class="label w52">核心内容:</span>
            <span v-html="data?.coreContent ?? '--'"></span>
          </div>
          <div class="label-box mt8">
            <span class="label w52">评审文件:</span>
            <div>
              <template v-if="data?.reviewFile">
                <div class="file-box" v-for="item in data?.reviewFile ?? []" :key="item.url">
                  <img class="icon-pic mr4" :src="getPicFn(item)" />
                  <span class="text-name" :title="item.fileName">{{ item.fileName }}</span>
                </div>
              </template>
              <span v-else>--</span>
            </div>
          </div>
          <div class="label-box mt8">
            <span class="label w52">评审人:</span>
            <span>{{ data?.reviewerUser ?? '--' }}</span>
          </div>
          <div class="label-box mt8">
            <span class="label w52">评审时间:</span>
            <span>{{ (data?.reviewTime && transformDate(data?.reviewTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--' }}</span>
          </div>
        </div>
      </template>
      <i class="iconfont icontubiao_tishi colorBBB fontSize14" />
    </FTooltip>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { transformDate } from '@/utils'
import pdf from '@/assets/images/pdf.png'
import chm from '@/assets/images/chm.png'
import divPic from '@/assets/images/div.png'
import other from '@/assets/images/other.png'
import png from '@/assets/images/png.png'
import ppt from '@/assets/images/ppt.png'
import excel from '@/assets/images/excel.png'
import word from '@/assets/images/word.png'
import txt from '@/assets/images/txt.png'

interface IProps {
  data: any
  title: string
}

const props = defineProps<IProps>()

const target = ref(() => document.querySelector('#container'))

const getPicFn = (item: any) => {
  const type = item.fileName.substring(item.fileName.lastIndexOf('.') + 1).toLowerCase()
  if (['pdf'].includes(type)) {
    return pdf
  } else if (['chm'].includes(type)) {
    return chm
  } else if (['png', 'jpg'].includes(type)) {
    return png
  } else if (['ppt'].includes(type)) {
    return ppt
  } else if (['txt'].includes(type)) {
    return txt
  } else if (['div'].includes(type)) {
    return divPic
  } else if (['xlsx', 'xlsm', 'xlsb', 'xls'].includes(type)) {
    return excel
  } else if (['doc', 'docm', 'docx', 'dot'].includes(type)) {
    return word
  }
  return other
}
</script>

<style lang="scss" scoped>
.mt8 {
  margin-top: 8px;
}
.mr8 {
  margin-right: 8px;
}
.appraise-box {
  line-height: 1;
  color: #333;
  .w65 {
    width: 65px;
  }
  .w52 {
    width: 52px;
  }
  .label {
    display: inline-block;
    text-align: left;
    margin-right: 8px;
    color: #999;
    white-space: nowrap;
  }
  .iconfont {
    vertical-align: text-bottom;
  }
}

.content-box {
  padding: 6px 8px;
  .label-box {
    display: flex;
    color: #333;
    .label {
      margin-right: 8px;
      color: #999;
      white-space: nowrap;
    }
  }
  .file-box {
    display: flex;
    line-height: 24px;
    align-items: center;
    &:hover {
      background-color: #f1f4f8;
      border-radius: 2px;
      .icontubiao_xiazai {
        display: inline-block;
      }
    }
    &:empty::after {
      content: '--';
    }
    .icon-pic {
      width: 16px;
    }
    .text-name {
      max-width: 84%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }
    .icontubiao_xiazai {
      display: none;
      flex: 1;
      text-align: right;
      font-size: 16px;
      color: #999;
      cursor: pointer;
    }
  }
}
</style>

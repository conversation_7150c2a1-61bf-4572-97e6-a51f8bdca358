<template>
  <div class="bpmn-base-element" :class="[elementConfig?.class || '']">
    <i class="bpmn-base-element-icon" :style="{ backgroundColor: props.themeColor }" />
    <!-- <p class="bpmn-base-element-text">{{ props.text }}</p> -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { BaseNodeModel } from '@logicflow/core'

interface IProps {
  model: BaseNodeModel
  properties: Record<string, unknown>
  themeColor: string
}

const props = defineProps<IProps>()

const elementConfig = computed(() => {
  return {
    class:
      ([0, 1].includes(props?.properties?.custActualNodeStatus as number) && 'bpmn-base-element_wait') ||
      ([2].includes(props?.properties?.custActualNodeStatus as number) && 'bpmn-base-element_warning') ||
      ([3, 4, 5].includes(props?.properties?.custActualNodeStatus as number) && 'bpmn-base-element_success') ||
      'bpmn-base-element_no',
  }
})
</script>

<style scoped lang="scss">
.bpmn-base-element {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 132px;
  height: 54px;
  margin: 4px;
  padding: 16px;
  background-color: #ffffff;
  box-sizing: border-box;
  border-radius: 3px;
  box-shadow: 0px 2px 8px rgba(88, 98, 110, 0.08);
  &_no {
    background-color: #f8f8f8;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 3px;
    > .bpmn-base-element-icon {
      background-color: #e3e3e3 !important;
    }
  }
  &_wait {
    background-color: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 3px;
    > .bpmn-base-element-icon {
      background-color: #cccccc !important;
    }
  }
  &_warning {
    background-color: #fef4e9;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 3px;
    > .bpmn-base-element-icon {
      background-color: #fa8f23 !important;
    }
  }
  &_success {
    background-color: #eafaf2;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 3px;
    > .bpmn-base-element-icon {
      background-color: #2fcc83 !important;
    }
  }

  > .bpmn-base-element-icon {
    width: 22px;
    height: 22px;
    font-size: 16px;
    text-align: center;
    line-height: 22px;
    border-radius: 11px;
    background-color: #378eef;
    background-image: url('~@/assets/images/bpm/bpmn-base-element-icon.svg');
    background-position: center;
    background-repeat: no-repeat;
  }

  > .bpmn-base-element-text {
    margin: 0 0 0 8px;
    line-height: 22px;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC, PingFang SC;
  }
}
</style>

{"type": "form", "body": [{"type": "grid", "columns": [], "id": "u:ae562b17367b", "className": ""}, {"type": "tpl", "tpl": "解决方案与定制研发流程创建", "inline": true, "wrapperComponent": "h2", "id": "u:0b09f8fcf4c6", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "divider", "id": "u:356253a5498e"}, {"type": "tpl", "id": "u:c856524bcc58", "className": "font-bold p-t", "tpl": "Case信息", "inline": true, "wrapperComponent": "h3", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "flex", "className": "p-1", "items": [{"type": "container", "body": [{"type": "input-text", "label": "Case编号", "name": "case_number", "id": "u:a4c21031203d", "value": "${envDefaultFormData.case_number}", "disabled": true, "readOnly": false}, {"type": "select", "label": "前台表单来源", "name": "from_type", "options": [{"label": "详情页", "value": "1"}, {"label": "专题页", "value": "2"}], "id": "u:8a2d00b9056f", "multiple": false, "value": "${envDefaultFormData.from_type}", "hiddenOn": "${!CONTAINS(resource,\"type1\")}"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:65abc8cd1f5b", "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "container", "body": [{"type": "input-datetime", "label": "录入时间", "name": "created_at", "id": "u:8ad738628cf7", "disabled": true, "value": "${envDefaultFormData.created_at}", "inputFormat": "YYYY-MM-DD HH:mm:ss", "placeholder": "请选择日期以及时间", "format": "X", "minDate": "", "maxDate": ""}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:98b72e587ec3"}, {"type": "container", "body": [{"type": "select", "label": "需求来源", "name": "resource", "id": "u:9afa863c5293", "value": "type2", "disabled": true, "options": [{"label": "客户前台提交", "value": "type1"}, {"label": "销售后台提交", "value": "type2"}], "multiple": false}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:08af32f26e3e", "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}], "style": {"position": "relative", "inset": "auto", "flexWrap": "nowrap"}, "direction": "row", "justify": "flex-start", "alignItems": "stretch", "id": "u:2a33fd3b8733", "isFixedHeight": false, "isFixedWidth": false, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "flex", "id": "u:fdc12578a4dd", "className": "p-1", "items": [{"type": "container", "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:f87c1a2ce5b7", "body": [{"type": "input-text", "label": "case主题", "name": "subject", "id": "u:ac324bd427f2", "disabled": true, "value": "${envDefaultFormData.subject}", "hidden": false}, {"type": "textarea", "label": "case 详情", "name": "case_detail", "id": "u:5513cf80600d", "minRows": 3, "maxRows": 20, "static": false, "readOnly": true, "value": "${envDefaultFormData.case_detail || content}", "disabled": true}, {"type": "input-text", "label": "客户详情", "name": "content", "id": "u:6cec0aa8ffce", "hidden": true, "value": "${envDefaultFormData.content}"}], "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}], "style": {"position": "relative", "inset": "auto", "flexWrap": "nowrap"}, "direction": "row", "justify": "flex-start", "alignItems": "stretch", "isFixedHeight": false, "isFixedWidth": false, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "divider", "id": "u:9eeb648e8057"}, {"type": "grid", "columns": [{"id": "u:1111fd2a102d", "body": [{"type": "tpl", "id": "u:c856524bcc58", "tpl": "客户基本信息", "inline": true, "wrapperComponent": "h3", "className": "font-bold p-t", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "service", "body": [], "id": "u:180a87717612", "dsType": "api", "api": {"args": {"options": {}, "api": {"url": "${envUrl}/api/erp/getCustomerInformation", "method": "post", "messages": {}, "headers": {"token": "${envToken}"}, "dataType": "json", "data": {"email": "${email}"}, "adaptor": "if (payload.code != 200) return {}\r\nreturn payload.data"}}, "actionType": "ajax", "outputVar": "emalRes", "url": "${envUrl}/api/erp/getCustomerInformation", "method": "post", "requestAdaptor": "", "adaptor": "if (payload.code != 200) return {}\r\nreturn {\r\n  customers_level: payload?.data?.customer_level,\r\n  customers_industry: payload?.data?.customer_industry,\r\n  countries_name: payload?.data?.customer_country_name,\r\n  country_time: payload?.data?.time_difference,\r\n}", "messages": {}, "dataType": "json", "headers": {"token": "${envToken}"}, "data": {"email": "${envDefaultFormData.custSapData.email}"}, "sendOn": "envDefaultFormData.custSapData.email"}}], "style": {"boxShadow": " 0px 0px 0px 0px transparent"}}], "id": "u:6c599ac5f65d", "className": "m-t m-b"}, {"type": "flex", "id": "u:8e202a47e990", "className": "p-1", "items": [{"type": "container", "body": [{"type": "input-text", "label": "客户邮箱", "name": "email", "id": "u:733b397efd7a", "onEvent": {"blur": {"weight": 0, "actions": [{"args": {"options": {}, "api": {"url": "${envUrl}/api/erp/getCustomerInformation", "method": "post", "messages": {}, "headers": {"token": "${envToken}"}, "dataType": "json", "data": {"email": "${email}"}, "adaptor": "if (payload.code != 200) return {}\r\nreturn payload.data"}}, "actionType": "ajax", "outputVar": "emalRes"}, {"componentId": "u:a1ad6778ddb7", "args": {"value": "${emalRes.customer_level}"}, "actionType": "setValue"}, {"componentId": "u:6160206f0653", "args": {"value": "${emalRes.customers_industry}"}, "actionType": "setValue"}, {"componentId": "u:b6c0bd0b16df", "args": {"value": "${emalRes.customer_country_name}"}, "actionType": "setValue"}, {"args": {"value": "${emalRes.time_difference}"}, "actionType": "setValue", "componentId": "u:a927600af7fe"}, {"componentId": "u:a4c21031203d", "ignoreError": false, "actionType": "setValue", "args": {"value": "${emalRes.companyNumber}"}}]}}, "value": "${envDefaultFormData.email || envDefaultFormData.custSapData.email}", "disabled": false}, {"type": "input-text", "label": "地区时差（h）", "name": "country_time", "id": "u:a927600af7fe", "disabled": true, "value": "${envDefaultFormData.country_time}"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:0347fb190821"}, {"type": "container", "body": [{"type": "input-text", "label": "客户G编码", "name": "company_number", "id": "u:a4c21031203d", "disabled": false, "value": "${envDefaultFormData.company_number || envDefaultFormData.custSapData.companyNumber}", "hidden": false, "required": true}, {"type": "input-text", "label": "国家/地区", "name": "countries_name", "id": "u:b6c0bd0b16df", "disabled": true, "value": "${envDefaultFormData.countries_name}"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:4e92dc3f4e15"}, {"type": "container", "body": [{"type": "input-text", "label": "客户电话", "name": "customers_tel", "id": "u:1252cedacbcc", "value": "${envDefaultFormData.customers_tel}", "disabled": false}, {"type": "input-text", "label": "客户等级", "name": "customers_level", "id": "u:a1ad6778ddb7", "disabled": true, "value": "${envDefaultFormData.customers_level}"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:40d967c4a48f"}], "style": {"position": "relative"}, "direction": "row", "justify": "flex-start", "alignItems": "stretch"}, {"type": "grid", "id": "u:1b851b5c3d5f", "columns": [{"id": "u:8fbc02870717", "body": [{"type": "input-tag", "label": "客户行业", "name": "customers_industry", "id": "u:6160206f0653", "value": "${envDefaultFormData.customers_industry}", "disabled": true, "options": [], "optionsTip": "请选择行业"}]}], "className": "m-t m-b"}, {"type": "grid", "columns": [{"id": "u:782165baee05", "body": [{"type": "select", "id": "u:5ed577af1105", "label": "关联流程", "name": "relevanceInstanceCodeList", "multiple": true, "value": "${envDefaultFormData.relevanceInstanceCodeList}", "checkAll": false, "searchable": true, "source": {"url": "${envUrl}/api/bpmInstance/getInstanceList", "method": "post", "messages": {}, "dataType": "json", "headers": {"token": "${envToken}"}, "data": {"pageNum": 1, "pageSize": 9999, "type": 0}, "adaptor": "if (payload.code !== 200) throw new Error(payload.msg)\n\nconst data = payload.data || {}\nconst list = data.list || []\ndebugger\nreturn list.map(item => ({\n    ...item,\n    label: `${item.processInstanceCode} - ${item.topicName}`\n}))\n"}, "labelField": "label", "valueField": "processInstanceCode", "joinValues": false, "extractValue": true, "hidden": true}], "style": {"boxShadow": " 0px 0px 0px 0px transparent"}}, {"body": [{"type": "input-tag", "label": "询盘状态：", "name": "enquiries_status", "id": "u:c3bdf99f4c16", "value": "${envDefaultFormData.enquiries_status}", "options": [{"label": "初步洽谈", "value": "1"}, {"label": "产品/方案确认", "value": "2"}, {"label": "价格/交期谈判", "value": "3"}, {"label": "付款谈判", "value": "4"}, {"label": "客户失联", "value": "5"}, {"label": "工程延后", "value": "6"}, {"label": "赢单", "value": "7"}, {"label": "失单", "value": "8"}, {"label": "未成交询盘", "value": "9"}, {"label": "过期询盘", "value": "10"}], "optionsTip": "最近您使用的标签", "className": "", "disabled": false, "required": false, "hidden": true}], "id": "u:32a03fe3de82", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}}], "id": "u:d3187c14da55"}, {"type": "grid", "columns": [{"body": [{"type": "divider", "id": "u:0cd01be1a469"}, {"type": "tpl", "tpl": "需求信息", "inline": true, "wrapperComponent": "h3", "id": "u:de20462b9bb4", "className": "font-bold m-t"}, {"type": "grid", "columns": [{"body": [{"type": "tpl", "tpl": "标准产品/解决方案/定制研发项目三层询盘定义：\n", "inline": true, "wrapperComponent": "", "id": "u:b541f67502e3", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent", "font:default": {"color": "#F5A623", "fontSize": "16px", "fontWeight": "500"}}}}], "id": "u:a2fd53d1d2a2"}], "id": "u:c56277fbf071", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "grid", "columns": [{"body": [{"id": "u:2f0e94a77891", "style": {}, "type": "tpl", "tpl": "标准产品：\n针对公司常规产品或服务的询盘，客户需求相对单一、产品品类较少、所购产品可能具有一定的关联性，但不涉及复杂的网络搭配或定制化需求。在处理标准询盘时，通常可以直接根据公司已有的产品规格和服务进行快速交付，无需额外的定制化设计或服务。", "inline": true, "wrapperComponent": "", "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent", "font:default": {"color": "#F5A623"}}}}], "id": "u:0faa36bf2d3b"}], "id": "u:21a87517f529"}, {"type": "grid", "columns": [{"body": [{"type": "tpl", "tpl": "解决方案\n：针对客户行业或群体的需求所设计整体解决方案，包括各种产品和服务的组合，服务包含但不限于规划设计服务、现勘服务、安装服务、部署服务、远程demo测试、技术交流（直接面向客户）、场景测试/组网测试等。", "inline": true, "wrapperComponent": "", "id": "u:d7a9db96dc98", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent", "font:default": {"color": "#F5A623"}}}}], "id": "u:b19eba8a830e", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}}], "id": "u:dbf2286d0946"}, {"type": "grid", "columns": [{"body": [{"type": "tpl", "tpl": "定制研发项目：\n针对客户需求进行定制化设计和实施，包含定制软件开发、系统集成、硬件开模等，有产品研发部门直接参与的项目。", "inline": true, "wrapperComponent": "", "id": "u:23cdb77308ba", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent", "font:default": {"color": "#F5A623"}}}}], "id": "u:91a321fb48b3"}], "id": "u:0015bdf860c6"}, {"type": "grid", "columns": [], "id": "u:b30aac221e5e", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}], "id": "u:75c9371a662c", "columnClassName": "", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}}], "id": "u:f8bd86ec6671", "className": "m-t m-b"}, {"type": "input-text", "id": "u:09e5d100b4bc", "label": "需求主题", "name": "title", "required": true, "value": "${envDefaultFormData.title}", "onEvent": {"change": {"weight": 0, "actions": []}}}, {"type": "input-rich-text", "label": "需求描述", "name": "describe", "receiver": {"method": "post", "url": "${envS3Url}", "data": {"expire": "0", "isOpen": "false"}, "dataType": "form-data", "adaptor": "return {\r\n    code: 200,\r\n    data: {\r\n        link: payload\r\n    }\r\n}"}, "vendor": "<PERSON><PERSON><PERSON>", "id": "u:06f78887f842", "options": {"menubar": true, "height": 400, "plugins": ["advlist", "autolink", "link", "image", "lists", "charmap", "preview", "anchor", "pagebreak", "searchreplace", "wordcount", "visualblocks", "visualchars", "code", "fullscreen", "insertdatetime", "media", "nonbreaking", "table", "emoticons", "template", "help"], "toolbar": "undo redo | formatselect | bold italic backcolor  | alignleft aligncenter alignright alignjustify | link | bullist numlist outdent indent | removeformat"}, "value": "${envDefaultFormData.describe || case_detail || content}<p>请详细描述客户的项目背景，以及需求详细内容</p>", "className": "m-t"}, {"type": "input-file", "label": "", "autoUpload": true, "proxy": false, "uploadType": "fileReceptor", "name": "file", "receiver": {"url": "${envS3Url}", "method": "post", "messages": {}, "dataType": "form-data", "data": {"isOpen": false, "expire": 0}, "adaptor": "const file = api.data.get('file')\r\nreturn { code: 200, data: JSON.stringify({ url: payload, name: file.name, size: file.size }) }", "requestAdaptor": ""}, "id": "u:5b34bbbd3bd9", "btnLabel": "文件上传", "asBlob": false, "formType": "asBlob", "multiple": true, "accept": "", "drag": false, "asBase64": false, "useChunk": false, "bos": "default", "submitType": "asUpload", "description": "支持格式：PDF, JPG, PNG, DOC, DOCX,PPT,PPTX, XLS, XLSX ， TXT，CSV、ZIP、RAR、MP4、Bin、Test、VSD、Cnf；最大2G", "autoFill": {}, "onEvent": {}}, {"type": "grid", "columns": [{"id": "u:ea606bfaff03", "body": [{"type": "grid", "columns": [], "id": "u:97971a679ec4"}, {"type": "nested-select", "label": "产品分类", "name": "prodclass", "onlyChildren": false, "id": "u:f9ad4711e0e6", "searchable": false, "onlyLeaf": true, "multiple": false, "source": {"url": "${envUrl}/api/workbench/getProductType", "method": "get", "messages": {}, "headers": {"token": "${envToken}"}, "adaptor": "if (payload.code !== 200) return []\n\nreturn payload.data\n", "requestAdaptor": "", "cache": 30000}, "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "u:76a0da6871c2", "args": {"value": "${IFS(event.data.value === '4245','type2',event.data.value === '3256','type2',event.data.value === '1071','type2',event.data.value === '3257','type2',event.data.value === '3258','type2',event.data.value === '4243','type2',event.data.value === '4249','type2',event.data.value === '3500','type2',event.data.value === '3501','type2',event.data.value === '3502','type2',event.data.value === '3503','type2',event.data.value === '3255','type2',event.data.value === '4225','type2',event.data.value === '4156','type2',event.data.value === '2968','type2',event.data.value === '4240','type2',event.data.value === '4248','type2',event.data.value === '4237','type2',event.data.value === '4234','type2',event.data.value === '4246','type2',event.data.value === '4252','type2',event.data.value === '4251','type2',event.data.value === '4258','type2',event.data.value === '4239','type2',event.data.value === '4247','type2',event.data.value === '3309','type2',event.data.value === '4241','type2',event.data.value === '4242','type2',event.data.value === '4236','type2',event.data.value === '4238','type2',event.data.value === '4244','type2',event.data.value === '4226','type2',event.data.value === '4054','type2',event.data.value === '4227','type2',event.data.value === '4228','type2',event.data.value === '4089','type3',event.data.value === '3652','type3',event.data.value === '4083','type3',event.data.value === '1159','type3',event.data.value === '3669','type3',event.data.value === '3908','type3',event.data.value === '4067','type3',event.data.value === '1220','type3',event.data.value === '3858','type3',event.data.value === '1360','type3',event.data.value === '3215','type3',event.data.value === '3618','type3',event.data.value === '63','type3',event.data.value === '64','type3',event.data.value === '3428','type3',event.data.value === '2878','type3',event.data.value === '4080','type3',event.data.value === '81','type3',event.data.value === '89','type3',event.data.value === '1668','type3',event.data.value === '83','type3',event.data.value === '4066','type3',event.data.value === '2985','type3',event.data.value === '4129','type3',event.data.value === '3542','type3',event.data.value === '2873','type3',event.data.value === '3331','type3',event.data.value === '3659','type3',event.data.value === '1117','type3',event.data.value === '2869','type3',event.data.value === '1172','type3',event.data.value === '3860','type3',event.data.value === '3801','type3',event.data.value === '2874','type3',event.data.value === '3672','type3',event.data.value === '1367','type3',event.data.value === '2320','type3',event.data.value === '4065','type3',event.data.value === '3831','type3',event.data.value === '65','type3',event.data.value === '3619','type3',event.data.value === '3863','type3',event.data.value === '1789','type3',event.data.value === '4103','type3',event.data.value === '2689','type3',event.data.value === '3684','type3',event.data.value === '3685','type3',event.data.value === '3874','type3',event.data.value === '3660','type3',event.data.value === '4128','type3',event.data.value === '3907','type3',event.data.value === '3609','type3',event.data.value === '3662','type3',event.data.value === '4115','type3',event.data.value === '3389','type3',event.data.value === '2757','type3',event.data.value === '3946','type3',event.data.value === '1175','type3',event.data.value === '1158','type3',event.data.value === '114','type3',event.data.value === '3058','type3',event.data.value === '58','type3',event.data.value === '3803','type3',event.data.value === '49','type3',event.data.value === '3683','type3',event.data.value === '3664','type3',event.data.value === '56','type3',event.data.value === '2758','type3',event.data.value === '87','type3',event.data.value === '3802','type3',event.data.value === '4130','type3',event.data.value === '3964','type3',event.data.value === '3275','type3',event.data.value === '156','type3',event.data.value === '2879','type3',event.data.value === '1115','type3',event.data.value === '1368','type3',event.data.value === '2209','type3',event.data.value === '1114','type3',event.data.value === '3686','type3',event.data.value === '1365','type3',event.data.value === '4206','type3',event.data.value === '1371','type3',event.data.value === '3793','type3',event.data.value === '66','type3',event.data.value === '3962','type3',event.data.value === '2677','type3',event.data.value === '3987','type3',event.data.value === '4208','type3',event.data.value === '2941','type3',event.data.value === '1394','type3',event.data.value === '1392','type3',event.data.value === '1135','type4',event.data.value === '1140','type4',event.data.value === '3919','type4',event.data.value === '3089','type4',event.data.value === '1125','type4',event.data.value === '4092','type4',event.data.value === '897','type4',event.data.value === '3254','type4',event.data.value === '2867','type4',event.data.value === '1324','type4',event.data.value === '901','type4',event.data.value === '2866','type4',event.data.value === '1082','type4',event.data.value === '1194','type4',event.data.value === '220','type4',event.data.value === '1148','type4',event.data.value === '4256','type4',event.data.value === '3856','type4',event.data.value === '1155','type4',event.data.value === '384','type4',event.data.value === '996','type4',event.data.value === '3253','type4',event.data.value === '2981','type4',event.data.value === '3074','type4',event.data.value === '1081','type4',event.data.value === '1083','type4',event.data.value === '2958','type4',event.data.value === '1000','type4',event.data.value === '1023','type4',event.data.value === '1105','type4',event.data.value === '2978','type4',event.data.value === '2906','type4',event.data.value === '2686','type4',event.data.value === '1003','type4',event.data.value === '2975','type4',event.data.value === '4095','type4',event.data.value === '3080','type4',event.data.value === '939','type4',event.data.value === '609','type4',event.data.value === '914','type4',event.data.value === '576','type4',event.data.value === '3125','type4',event.data.value === '1342','type4',event.data.value === '1132','type4',event.data.value === '3087','type4',event.data.value === '3191','type4',event.data.value === '634','type4',event.data.value === '1415','type4',event.data.value === '3216','type4',event.data.value === '974','type4',event.data.value === '4061','type4',event.data.value === '1326','type4',event.data.value === '3081','type4',event.data.value === '1049','type4',event.data.value === '1050','type4',event.data.value === '3507','type4',event.data.value === '1048','type4',event.data.value === '4111','type4',event.data.value === '1051','type4',event.data.value === '1146','type4',event.data.value === '1057','type4',event.data.value === '1052','type4',event.data.value === '1196','type4',event.data.value === '1131','type4',event.data.value === '3362','type4',event.data.value === '1061','type4',event.data.value === '1053','type4',event.data.value === '4221','type4',event.data.value === '4112','type4',event.data.value === '4220','type4',event.data.value === '1190','type4',event.data.value === '975','type4',event.data.value === '2','type4',event.data.value === '3075','type4',event.data.value === '2966','type4',event.data.value === '3334','type4',event.data.value === '3396','type4',event.data.value === '45','type4',event.data.value === '3358','type4',event.data.value === '3359','type4',event.data.value === '3360','type4',event.data.value === '1098','type4',event.data.value === '1067','type4',event.data.value === '1070','type4',event.data.value === '3367','type4',event.data.value === '3054','type4',event.data.value === '4177','type4',event.data.value === '3093','type4',event.data.value === '900','type4',event.data.value === '2967','type4',event.data.value === '2907','type4',event.data.value === '3059','type4',event.data.value === '3319','type4',event.data.value === '3084','type4',event.data.value === '3397','type4',event.data.value === '3398','type4',event.data.value === '3085','type4',event.data.value === '3092','type4',event.data.value === '2963','type4',event.data.value === '3076','type4',event.data.value === '3082','type4',event.data.value === '3535','type4',event.data.value === '3128','type4',event.data.value === '1145','type4',event.data.value === '2687','type4',event.data.value === '593','type4',event.data.value === '594','type4',event.data.value === '3049','type4',event.data.value === '980','type4',event.data.value === '3371','type4',event.data.value === '4136','type4',event.data.value === '3312','type4',event.data.value === '3261','type4',event.data.value === '3262','type4',event.data.value === '3263','type4',event.data.value === '3311','type4',event.data.value === '1134','type4',event.data.value === '1133','type4',event.data.value === '3073','type4',event.data.value === '633','type4',event.data.value === '959','type4',event.data.value === '4044','type4',event.data.value === '3313','type4',event.data.value === '590','type4',event.data.value === '962','type4',event.data.value === '4013','type4',event.data.value === '1126','type4',event.data.value === '1064','type4',event.data.value === '621','type4',event.data.value === '3485','type4',event.data.value === '4107','type4',event.data.value === '4108','type4',event.data.value === '4109','type4',event.data.value === '4110','type4',event.data.value === '613','type4',event.data.value === '4137','type4',event.data.value === '964','type4',event.data.value === '3347','type4',event.data.value === '24','type4',event.data.value === '2951','type4',event.data.value === '1073','type4',event.data.value === '3830','type4',event.data.value === '4082','type1',event.data.value === '4135','type1',event.data.value === '4134','type1',event.data.value === '4193','type1',event.data.value === '3266','type1',event.data.value === '4257','type1')}"}, "actionType": "setValue"}, {"componentId": "u:d58e4b930c75", "actionType": "setValue", "args": {"value": "${IFS(event.data.value === '4245','type2',event.data.value === '3256','type2',event.data.value === '1071','type2',event.data.value === '3257','type2',event.data.value === '3258','type2',event.data.value === '4243','type2',event.data.value === '4249','type2',event.data.value === '3500','type2',event.data.value === '3501','type2',event.data.value === '3502','type2',event.data.value === '3503','type2',event.data.value === '3255','type2',event.data.value === '4225','type2',event.data.value === '4156','type2',event.data.value === '2968','type2',event.data.value === '4240','type2',event.data.value === '4248','type2',event.data.value === '4237','type2',event.data.value === '4234','type2',event.data.value === '4246','type2',event.data.value === '4252','type2',event.data.value === '4251','type2',event.data.value === '4258','type2',event.data.value === '4239','type2',event.data.value === '4247','type2',event.data.value === '3309','type2',event.data.value === '4241','type2',event.data.value === '4242','type2',event.data.value === '4236','type2',event.data.value === '4238','type2',event.data.value === '4244','type2',event.data.value === '4226','type2',event.data.value === '4054','type2',event.data.value === '4227','type2',event.data.value === '4228','type2',event.data.value === '4089','type3',event.data.value === '3652','type3',event.data.value === '4083','type3',event.data.value === '1159','type3',event.data.value === '3669','type3',event.data.value === '3908','type3',event.data.value === '4067','type3',event.data.value === '1220','type3',event.data.value === '3858','type3',event.data.value === '1360','type3',event.data.value === '3215','type3',event.data.value === '3618','type3',event.data.value === '63','type3',event.data.value === '64','type3',event.data.value === '3428','type3',event.data.value === '2878','type3',event.data.value === '4080','type3',event.data.value === '81','type3',event.data.value === '89','type3',event.data.value === '1668','type3',event.data.value === '83','type3',event.data.value === '4066','type3',event.data.value === '2985','type3',event.data.value === '4129','type3',event.data.value === '3542','type3',event.data.value === '2873','type3',event.data.value === '3331','type3',event.data.value === '3659','type3',event.data.value === '1117','type3',event.data.value === '2869','type3',event.data.value === '1172','type3',event.data.value === '3860','type3',event.data.value === '3801','type3',event.data.value === '2874','type3',event.data.value === '3672','type3',event.data.value === '1367','type3',event.data.value === '2320','type3',event.data.value === '4065','type3',event.data.value === '3831','type3',event.data.value === '65','type3',event.data.value === '3619','type3',event.data.value === '3863','type3',event.data.value === '1789','type3',event.data.value === '4103','type3',event.data.value === '2689','type3',event.data.value === '3684','type3',event.data.value === '3685','type3',event.data.value === '3874','type3',event.data.value === '3660','type3',event.data.value === '4128','type3',event.data.value === '3907','type3',event.data.value === '3609','type3',event.data.value === '3662','type3',event.data.value === '4115','type3',event.data.value === '3389','type3',event.data.value === '2757','type3',event.data.value === '3946','type3',event.data.value === '1175','type3',event.data.value === '1158','type3',event.data.value === '114','type3',event.data.value === '3058','type3',event.data.value === '58','type3',event.data.value === '3803','type3',event.data.value === '49','type3',event.data.value === '3683','type3',event.data.value === '3664','type3',event.data.value === '56','type3',event.data.value === '2758','type3',event.data.value === '87','type3',event.data.value === '3802','type3',event.data.value === '4130','type3',event.data.value === '3964','type3',event.data.value === '3275','type3',event.data.value === '156','type3',event.data.value === '2879','type3',event.data.value === '1115','type3',event.data.value === '1368','type3',event.data.value === '2209','type3',event.data.value === '1114','type3',event.data.value === '3686','type3',event.data.value === '1365','type3',event.data.value === '4206','type3',event.data.value === '1371','type3',event.data.value === '3793','type3',event.data.value === '66','type3',event.data.value === '3962','type3',event.data.value === '2677','type3',event.data.value === '3987','type3',event.data.value === '4208','type3',event.data.value === '2941','type3',event.data.value === '1394','type3',event.data.value === '1392','type3',event.data.value === '1135','type4',event.data.value === '1140','type4',event.data.value === '3919','type4',event.data.value === '3089','type4',event.data.value === '1125','type4',event.data.value === '4092','type4',event.data.value === '897','type4',event.data.value === '3254','type4',event.data.value === '2867','type4',event.data.value === '1324','type4',event.data.value === '901','type4',event.data.value === '2866','type4',event.data.value === '1082','type4',event.data.value === '1194','type4',event.data.value === '220','type4',event.data.value === '1148','type4',event.data.value === '4256','type4',event.data.value === '3856','type4',event.data.value === '1155','type4',event.data.value === '384','type4',event.data.value === '996','type4',event.data.value === '3253','type4',event.data.value === '2981','type4',event.data.value === '3074','type4',event.data.value === '1081','type4',event.data.value === '1083','type4',event.data.value === '2958','type4',event.data.value === '1000','type4',event.data.value === '1023','type4',event.data.value === '1105','type4',event.data.value === '2978','type4',event.data.value === '2906','type4',event.data.value === '2686','type4',event.data.value === '1003','type4',event.data.value === '2975','type4',event.data.value === '4095','type4',event.data.value === '3080','type4',event.data.value === '939','type4',event.data.value === '609','type4',event.data.value === '914','type4',event.data.value === '576','type4',event.data.value === '3125','type4',event.data.value === '1342','type4',event.data.value === '1132','type4',event.data.value === '3087','type4',event.data.value === '3191','type4',event.data.value === '634','type4',event.data.value === '1415','type4',event.data.value === '3216','type4',event.data.value === '974','type4',event.data.value === '4061','type4',event.data.value === '1326','type4',event.data.value === '3081','type4',event.data.value === '1049','type4',event.data.value === '1050','type4',event.data.value === '3507','type4',event.data.value === '1048','type4',event.data.value === '4111','type4',event.data.value === '1051','type4',event.data.value === '1146','type4',event.data.value === '1057','type4',event.data.value === '1052','type4',event.data.value === '1196','type4',event.data.value === '1131','type4',event.data.value === '3362','type4',event.data.value === '1061','type4',event.data.value === '1053','type4',event.data.value === '4221','type4',event.data.value === '4112','type4',event.data.value === '4220','type4',event.data.value === '1190','type4',event.data.value === '975','type4',event.data.value === '2','type4',event.data.value === '3075','type4',event.data.value === '2966','type4',event.data.value === '3334','type4',event.data.value === '3396','type4',event.data.value === '45','type4',event.data.value === '3358','type4',event.data.value === '3359','type4',event.data.value === '3360','type4',event.data.value === '1098','type4',event.data.value === '1067','type4',event.data.value === '1070','type4',event.data.value === '3367','type4',event.data.value === '3054','type4',event.data.value === '4177','type4',event.data.value === '3093','type4',event.data.value === '900','type4',event.data.value === '2967','type4',event.data.value === '2907','type4',event.data.value === '3059','type4',event.data.value === '3319','type4',event.data.value === '3084','type4',event.data.value === '3397','type4',event.data.value === '3398','type4',event.data.value === '3085','type4',event.data.value === '3092','type4',event.data.value === '2963','type4',event.data.value === '3076','type4',event.data.value === '3082','type4',event.data.value === '3535','type4',event.data.value === '3128','type4',event.data.value === '1145','type4',event.data.value === '2687','type4',event.data.value === '593','type4',event.data.value === '594','type4',event.data.value === '3049','type4',event.data.value === '980','type4',event.data.value === '3371','type4',event.data.value === '4136','type4',event.data.value === '3312','type4',event.data.value === '3261','type4',event.data.value === '3262','type4',event.data.value === '3263','type4',event.data.value === '3311','type4',event.data.value === '1134','type4',event.data.value === '1133','type4',event.data.value === '3073','type4',event.data.value === '633','type4',event.data.value === '959','type4',event.data.value === '4044','type4',event.data.value === '3313','type4',event.data.value === '590','type4',event.data.value === '962','type4',event.data.value === '4013','type4',event.data.value === '1126','type4',event.data.value === '1064','type4',event.data.value === '621','type4',event.data.value === '3485','type4',event.data.value === '4107','type4',event.data.value === '4108','type4',event.data.value === '4109','type4',event.data.value === '4110','type4',event.data.value === '613','type4',event.data.value === '4137','type4',event.data.value === '964','type4',event.data.value === '3347','type4',event.data.value === '24','type4',event.data.value === '2951','type4',event.data.value === '1073','type4',event.data.value === '3830','type4',event.data.value === '4082','type1',event.data.value === '4135','type1',event.data.value === '4134','type1',event.data.value === '4193','type1',event.data.value === '3266','type1',event.data.value === '4257','type1',event.data.value === '1071','type2',event.data.value === '3256','type2',event.data.value === '3257','type2',event.data.value === '3258','type2',event.data.value === '4243','type2',event.data.value === '4245','type2',event.data.value === '4333','type2',event.data.value === '4240','type2',event.data.value === '4234','type2',event.data.value === '4237','type2',event.data.value === '4248','type2',event.data.value === '4335','type2',event.data.value === '3500','type2',event.data.value === '3255','type2',event.data.value === '3501','type2',event.data.value === '3502','type2',event.data.value === '3503','type2',event.data.value === '4156','type2',event.data.value === '4225','type2',event.data.value === '4359','type2',event.data.value === '4363','type2',event.data.value === '4226','type2',event.data.value === '4227','type2',event.data.value === '4228','type2',event.data.value === '4365','type2',event.data.value === '4367','type2',event.data.value === '4369','type2',event.data.value === '4236','type2',event.data.value === '4238','type2',event.data.value === '4242','type2',event.data.value === '4244','type2',event.data.value === '1368','type2',event.data.value === '2968','type2',event.data.value === '4246','type2',event.data.value === '4249','type2',event.data.value === '4319','type2',event.data.value === '3309','type2',event.data.value === '4239','type2',event.data.value === '4241','type2',event.data.value === '4247','type2',event.data.value === '4251','type2',event.data.value === '4252','type2',event.data.value === '4258','type2',event.data.value === '3086','type2',event.data.value === '4133','type2',event.data.value === '2977','type2',event.data.value === '4104','type2',event.data.value === '4218','type2',event.data.value === '3872','type2',event.data.value === '3616','type2',event.data.value === '3644','type2',event.data.value === '3470','type2',event.data.value === '3375','type2',event.data.value === '4052','type2',event.data.value === '4053','type2',event.data.value === '4305','type2',event.data.value === '3645','type2',event.data.value === '4074','type2',event.data.value === '4091','type2',event.data.value === '4266','type2',event.data.value === '4113','type2',event.data.value === '39','type2',event.data.value === '1181','type2',event.data.value === '1320','type2',event.data.value === '1343','type2',event.data.value === '1402','type2',event.data.value === '3053','type2',event.data.value === '3260','type2',event.data.value === '4040','type2',event.data.value === '4153','type2',event.data.value === '4260','type2',event.data.value === '4261','type2',event.data.value === '4262','type2',event.data.value === '4263','type2',event.data.value === '4306','type2',event.data.value === '54','type2',event.data.value === '4082','type2',event.data.value === '3266','type2',event.data.value === '4134','type2',event.data.value === '4135','type2',event.data.value === '4193','type2',event.data.value === '4257','type2',event.data.value === '4337','type2',event.data.value === '4173','type2',event.data.value === '2887','type2',event.data.value === '3376','type2',event.data.value === '4014','type2',event.data.value === '1150','type2',event.data.value === '2960','type2',event.data.value === '3373','type2',event.data.value === '3374','type2',event.data.value === '3646','type2',event.data.value === '4101','type2',event.data.value === '4275','type2',event.data.value === '1038','type2',event.data.value === '1044','type2',event.data.value === '1045','type2',event.data.value === '1046','type2',event.data.value === '1047','type2',event.data.value === '1186','type2',event.data.value === '3090','type2',event.data.value === '3095','type2',event.data.value === '4231','type2',event.data.value === '4232','type2',event.data.value === '4233','type2',event.data.value === '4056','type2',event.data.value === '3329','type2',event.data.value === '4057','type2',event.data.value === '4058','type2',event.data.value === '4076','type2',event.data.value === '4077','type2',event.data.value === '4088','type2',event.data.value === '4102','type2',event.data.value === '4176','type2',event.data.value === '4230','type2',event.data.value === '4224','type2',event.data.value === '61','type2',event.data.value === '1197','type2',event.data.value === '1049','type3',event.data.value === '1048','type3',event.data.value === '1050','type3',event.data.value === '1051','type3',event.data.value === '1052','type3',event.data.value === '1057','type3',event.data.value === '1131','type3',event.data.value === '1146','type3',event.data.value === '1196','type3',event.data.value === '3507','type3',event.data.value === '4111','type3',event.data.value === '3362','type3',event.data.value === '1053','type3',event.data.value === '1061','type3',event.data.value === '1190','type3',event.data.value === '2','type3',event.data.value === '2966','type3',event.data.value === '3075','type3',event.data.value === '3334','type3',event.data.value === '3396','type3',event.data.value === '4112','type3',event.data.value === '4220','type3',event.data.value === '4221','type3',event.data.value === '975','type3',event.data.value === '3358','type3',event.data.value === '3359','type3',event.data.value === '3360','type3',event.data.value === '45','type3',event.data.value === '1067','type3',event.data.value === '1070','type3',event.data.value === '3054','type3',event.data.value === '3367','type3',event.data.value === '4177','type3',event.data.value === '4351','type3',event.data.value === '1098','type3',event.data.value === '2907','type3',event.data.value === '2967','type3',event.data.value === '3059','type3',event.data.value === '3319','type3',event.data.value === '900','type3',event.data.value === '3093','type3',event.data.value === '3084','type3',event.data.value === '3397','type3',event.data.value === '3398','type3',event.data.value === '1145','type3',event.data.value === '2687','type3',event.data.value === '3082','type3',event.data.value === '3128','type3',event.data.value === '3535','type3',event.data.value === '3076','type3',event.data.value === '4355','type3',event.data.value === '4357','type3',event.data.value === '4353','type3',event.data.value === '1099','type3',event.data.value === '1318','type3',event.data.value === '22','type3',event.data.value === '33','type3',event.data.value === '34','type3',event.data.value === '35','type3',event.data.value === '36','type3',event.data.value === '38','type3',event.data.value === '1321','type3',event.data.value === '19','type3',event.data.value === '21','type3',event.data.value === '48','type3',event.data.value === '51','type3',event.data.value === '52','type3',event.data.value === '55','type3',event.data.value === '3615','type3',event.data.value === '4059','type3',event.data.value === '4060','type3',event.data.value === '1063','type3',event.data.value === '40','type3',event.data.value === '53','type3',event.data.value === '50','type3',event.data.value === '1319','type3',event.data.value === '598','type3',event.data.value === '47','type3',event.data.value === '3083','type3',event.data.value === '2957','type3',event.data.value === '2961','type3',event.data.value === '3368','type3',event.data.value === '3915','type3',event.data.value === '3088','type3',event.data.value === '2868','type3',event.data.value === '2980','type3',event.data.value === '1103','type3',event.data.value === '1156','type3',event.data.value === '4107','type1',event.data.value === '4108','type1',event.data.value === '4109','type1',event.data.value === '4110','type1',event.data.value === '3049','type1',event.data.value === '3312','type1',event.data.value === '3371','type1',event.data.value === '4136','type1',event.data.value === '593','type1',event.data.value === '594','type1',event.data.value === '980','type1',event.data.value === '3261','type1',event.data.value === '3262','type1',event.data.value === '3263','type1',event.data.value === '3311','type1',event.data.value === '4283','type1',event.data.value === '4284','type1',event.data.value === '1133','type1',event.data.value === '1134','type1',event.data.value === '3073','type1',event.data.value === '3313','type1',event.data.value === '4044','type1',event.data.value === '4290','type1',event.data.value === '4371','type1',event.data.value === '4373','type1',event.data.value === '633','type1',event.data.value === '959','type1',event.data.value === '1064','type1',event.data.value === '1126','type1',event.data.value === '3485','type1',event.data.value === '4013','type1',event.data.value === '4298','type1',event.data.value === '590','type1',event.data.value === '621','type1',event.data.value === '962','type1',event.data.value === '3347','type1',event.data.value === '4137','type1',event.data.value === '613','type1',event.data.value === '964','type1',event.data.value === '24','type1',event.data.value === '2951','type1',event.data.value === '1073','type1',event.data.value === '3830','type1')}"}}, {"componentId": "u:4d298e95eecc", "ignoreError": false, "actionType": "setValue", "args": {"value": "${IFS(event.data.value === '4186','type1',event.data.value === '4191','type1',event.data.value === '4192','type1',event.data.value === '4196','type1',event.data.value === '4381','type1',event.data.value === '4189','type1',event.data.value === '4187','type1',event.data.value === '4188','type1',event.data.value === '5179','type1',event.data.value === '4184','type1',event.data.value === '4185','type1',event.data.value === '909','type1',event.data.value === '1197','type1',event.data.value === '4098','type1',event.data.value === '4099','type1',event.data.value === '4182','type1',event.data.value === '628','type1',event.data.value === '837','type1',event.data.value === '3395','type1',event.data.value === '1153','type1',event.data.value === '3318','type1',event.data.value === '1340','type1',event.data.value === '3094','type1',event.data.value === '4096','type1',event.data.value === '3203','type1',event.data.value === '596','type1',event.data.value === '1335','type1',event.data.value === '1316','type1',event.data.value === '3249','type1',event.data.value === '1310','type1',event.data.value === '178','type1',event.data.value === '177','type1',event.data.value === '3925','type1',event.data.value === '179','type1',event.data.value === '3607','type1',event.data.value === '1202','type1',event.data.value === '180','type1',event.data.value === '1333','type1',event.data.value === '1200','type1',event.data.value === '4174','type1',event.data.value === '4175','type1',event.data.value === '1312','type1',event.data.value === '1314','type1',event.data.value === '3770','type1',event.data.value === '1017','type1',event.data.value === '990','type1',event.data.value === '1311','type1',event.data.value === '3077','type1',event.data.value === '1334','type1',event.data.value === '4365','type2',event.data.value === '5125','type2',event.data.value === '1368','type3',event.data.value === '4246','type3',event.data.value === '2968','type3',event.data.value === '4319','type3',event.data.value === '5127','type3',event.data.value === '4369','type3',event.data.value === '4228','type3')}"}}, {"componentId": "u:a88fb3cb2661", "ignoreError": false, "actionType": "setValue", "args": {"value": "${IFS(event.data.value === '4381','A',event.data.value === '5179','A','B')}"}}, {"componentId": "u:6e0c15b671ef", "actionType": "setValue", "args": {"value": "${IFS(event.data.value === '1392','光模块PBU',event.data.value === '3801','光模块PBU',event.data.value === '3672','光模块PBU',event.data.value === '1367','光模块PBU',event.data.value === '2320','光模块PBU',event.data.value === '3908','光模块PBU',event.data.value === '3831','光模块PBU',event.data.value === '4089','光模块PBU',event.data.value === '3652','光模块PBU',event.data.value === '3542','光模块PBU',event.data.value === '4065','光模块PBU',event.data.value === '3858','光模块PBU',event.data.value === '3963','光模块PBU',event.data.value === '1159','光模块PBU',event.data.value === '2874','光模块PBU',event.data.value === '1360','光模块PBU',event.data.value === '3215','光模块PBU',event.data.value === '3618','光模块PBU',event.data.value === '63','光模块PBU',event.data.value === '3428','光模块PBU',event.data.value === '2878','光模块PBU',event.data.value === '64','光模块PBU',event.data.value === '4067','光模块PBU',event.data.value === '4080','光模块PBU',event.data.value === '81','光模块PBU',event.data.value === '89','光模块PBU',event.data.value === '1668','光模块PBU',event.data.value === '83','光模块PBU',event.data.value === '4066','光模块PBU',event.data.value === '2985','光模块PBU',event.data.value === '4129','光模块PBU',event.data.value === '3659','光模块PBU',event.data.value === '1172','光模块PBU',event.data.value === '2873','光模块PBU',event.data.value === '3331','光模块PBU',event.data.value === '1117','光模块PBU',event.data.value === '2869','光模块PBU',event.data.value === '3860','光模块PBU',event.data.value === '65','光模块PBU',event.data.value === '3619','光模块PBU',event.data.value === '3863','光模块PBU',event.data.value === '1789','光模块PBU',event.data.value === '4103','光模块PBU',event.data.value === '2689','光模块PBU',event.data.value === '3684','光模块PBU',event.data.value === '3685','光模块PBU',event.data.value === '3874','光模块PBU',event.data.value === '3660','光模块PBU',event.data.value === '4895','光模块PBU',event.data.value === '3907','光模块PBU',event.data.value === '4897','光模块PBU',event.data.value === '3609','光模块PBU',event.data.value === '4128','光模块PBU',event.data.value === '3662','光模块PBU',event.data.value === '4115','光模块PBU',event.data.value === '3389','光模块PBU',event.data.value === '2757','光模块PBU',event.data.value === '3946','光模块PBU',event.data.value === '1175','光模块PBU',event.data.value === '1158','光模块PBU',event.data.value === '114','光模块PBU',event.data.value === '3058','光模块PBU',event.data.value === '2845','光模块PBU',event.data.value === '920','光模块PBU',event.data.value === '4186','光网络PBU',event.data.value === '4191','光网络PBU',event.data.value === '4192','光网络PBU',event.data.value === '4196','光网络PBU',event.data.value === '4381','光网络PBU',event.data.value === '5329','光网络PBU',event.data.value === '4189','光网络PBU',event.data.value === '4187','光网络PBU',event.data.value === '4188','光网络PBU',event.data.value === '5179','光网络PBU',event.data.value === '4184','光网络PBU',event.data.value === '4185','光网络PBU',event.data.value === '4098','光网络PBU',event.data.value === '4099','光网络PBU',event.data.value === '628','光网络PBU',event.data.value === '837','光网络PBU',event.data.value === '3395','光网络PBU',event.data.value === '1153','光网络PBU',event.data.value === '3318','光网络PBU',event.data.value === '1340','光网络PBU',event.data.value === '3094','光网络PBU',event.data.value === '4096','光网络PBU',event.data.value === '3203','光网络PBU',event.data.value === '596','光网络PBU',event.data.value === '1335','光网络PBU',event.data.value === '1316','光网络PBU',event.data.value === '3249','光网络PBU',event.data.value === '1310','光网络PBU',event.data.value === '178','光网络PBU',event.data.value === '177','光网络PBU',event.data.value === '3925','光网络PBU',event.data.value === '179','光网络PBU',event.data.value === '3607','光网络PBU',event.data.value === '1202','光网络PBU',event.data.value === '180','光网络PBU',event.data.value === '1333','光网络PBU',event.data.value === '1200','光网络PBU',event.data.value === '4174','光网络PBU',event.data.value === '4175','光网络PBU',event.data.value === '1312','光网络PBU',event.data.value === '1314','光网络PBU',event.data.value === '3770','光网络PBU',event.data.value === '1311','光网络PBU',event.data.value === '3077','光网络PBU',event.data.value === '990','光网络PBU',event.data.value === '4245','园区网PBU',event.data.value === '3256','园区网PBU',event.data.value === '1071','园区网PBU',event.data.value === '3257','园区网PBU',event.data.value === '3258','园区网PBU',event.data.value === '4243','园区网PBU',event.data.value === '4333','园区网PBU',event.data.value === '4367','园区网PBU',event.data.value === '5159','园区网PBU',event.data.value === '1368','园区网PBU',event.data.value === '4246','园区网PBU',event.data.value === '2968','园区网PBU',event.data.value === '4319','园区网PBU',event.data.value === '5513','园区网PBU',event.data.value === '4249','园区网PBU',event.data.value === '4240','园区网PBU',event.data.value === '4248','园区网PBU',event.data.value === '4237','园区网PBU',event.data.value === '4234','园区网PBU',event.data.value === '4335','园区网PBU',event.data.value === '4239','园区网PBU',event.data.value === '4247','园区网PBU',event.data.value === '3309','园区网PBU',event.data.value === '4241','园区网PBU',event.data.value === '4242','园区网PBU',event.data.value === '4236','园区网PBU',event.data.value === '4238','园区网PBU',event.data.value === '4244','园区网PBU',event.data.value === '4252','园区网PBU',event.data.value === '4251','园区网PBU',event.data.value === '1150','园区网PBU',event.data.value === '3646','园区网PBU',event.data.value === '3374','园区网PBU',event.data.value === '3500','数据中心PBU',event.data.value === '3501','数据中心PBU',event.data.value === '3502','数据中心PBU',event.data.value === '3503','数据中心PBU',event.data.value === '5451','数据中心PBU',event.data.value === '3255','数据中心PBU',event.data.value === '5279','数据中心PBU',event.data.value === '4225','数据中心PBU',event.data.value === '4359','数据中心PBU',event.data.value === '4363','数据中心PBU',event.data.value === '4365','数据中心PBU',event.data.value === '4887','数据中心PBU',event.data.value === '4889','数据中心PBU',event.data.value === '4891','数据中心PBU',event.data.value === '4156','数据中心PBU',event.data.value === '5127','数据中心PBU',event.data.value === '4369','数据中心PBU',event.data.value === '5407','数据中心PBU',event.data.value === '4228','数据中心PBU',event.data.value === '5405','数据中心PBU',event.data.value === '4227','数据中心PBU',event.data.value === '4226','数据中心PBU',event.data.value === '5131','数据中心PBU',event.data.value === '5129','数据中心PBU',event.data.value === '4258','数据中心PBU',event.data.value === '4082','数据中心PBU',event.data.value === '4337','数据中心PBU',event.data.value === '4135','数据中心PBU',event.data.value === '5183','数据中心PBU',event.data.value === '4134','数据中心PBU',event.data.value === '4193','数据中心PBU',event.data.value === '4257','数据中心PBU',event.data.value === '3266','数据中心PBU',event.data.value === '4173','数据中心PBU',event.data.value === '4014','数据中心PBU',event.data.value === '3376','数据中心PBU',event.data.value === '5169','数据中心PBU',event.data.value === '2887','数据中心PBU',event.data.value === '3373','数据中心PBU',event.data.value === '4275','数据中心PBU',event.data.value === '3086','无线安防PBU',event.data.value === '4133','无线安防PBU',event.data.value === '2977','无线安防PBU',event.data.value === '4104','无线安防PBU',event.data.value === '4218','无线安防PBU',event.data.value === '3872','无线安防PBU',event.data.value === '3470','无线安防PBU',event.data.value === '3375','无线安防PBU',event.data.value === '4052','无线安防PBU',event.data.value === '4053','无线安防PBU',event.data.value === '4305','无线安防PBU',event.data.value === '3645','无线安防PBU',event.data.value === '4074','无线安防PBU',event.data.value === '4091','无线安防PBU',event.data.value === '4266','无线安防PBU',event.data.value === '4113','无线安防PBU',event.data.value === '39','无线安防PBU',event.data.value === '1181','无线安防PBU',event.data.value === '4056','无线安防PBU',event.data.value === '4057','无线安防PBU',event.data.value === '4058','无线安防PBU',event.data.value === '3329','无线安防PBU',event.data.value === '4076','无线安防PBU',event.data.value === '4230','无线安防PBU',event.data.value === '4077','无线安防PBU',event.data.value === '4088','无线安防PBU',event.data.value === '4102','无线安防PBU',event.data.value === '4176','无线安防PBU',event.data.value === '3260','机房布线建设及相关配件PBU',event.data.value === '4306','机房布线建设及相关配件PBU',event.data.value === '1402','机房布线建设及相关配件PBU',event.data.value === '4260','机房布线建设及相关配件PBU',event.data.value === '4261','机房布线建设及相关配件PBU',event.data.value === '5401','机房布线建设及相关配件PBU',event.data.value === '4263','机房布线建设及相关配件PBU',event.data.value === '5403','机房布线建设及相关配件PBU',event.data.value === '1343','机房布线建设及相关配件PBU',event.data.value === '54','机房布线建设及相关配件PBU',event.data.value === '4040','机房布线建设及相关配件PBU',event.data.value === '3053','机房布线建设及相关配件PBU',event.data.value === '4153','机房布线建设及相关配件PBU',event.data.value === '1320','机房布线建设及相关配件PBU',event.data.value === '4262','机房布线建设及相关配件PBU',event.data.value === '2960','机房布线建设及相关配件PBU',event.data.value === '1038','机房布线建设及相关配件PBU',event.data.value === '1044','机房布线建设及相关配件PBU',event.data.value === '1047','机房布线建设及相关配件PBU',event.data.value === '1186','机房布线建设及相关配件PBU',event.data.value === '1045','机房布线建设及相关配件PBU',event.data.value === '1046','机房布线建设及相关配件PBU',event.data.value === '3095','机房布线建设及相关配件PBU',event.data.value === '3090','机房布线建设及相关配件PBU',event.data.value === '1135','机房布线建设及相关配件PBU',event.data.value === '3919','机房布线建设及相关配件PBU',event.data.value === '1140','机房布线建设及相关配件PBU',event.data.value === '3089','机房布线建设及相关配件PBU',event.data.value === '5511','机房布线建设及相关配件PBU',event.data.value === '4092','机房布线建设及相关配件PBU',event.data.value === '1125','机房布线建设及相关配件PBU',event.data.value === '897','机房布线建设及相关配件PBU',event.data.value === '3254','机房布线建设及相关配件PBU',event.data.value === '2867','机房布线建设及相关配件PBU',event.data.value === '1324','机房布线建设及相关配件PBU',event.data.value === '901','机房布线建设及相关配件PBU',event.data.value === '2866','机房布线建设及相关配件PBU',event.data.value === '1082','机房布线建设及相关配件PBU',event.data.value === '1194','机房布线建设及相关配件PBU',event.data.value === '220','机房布线建设及相关配件PBU',event.data.value === '1148','机房布线建设及相关配件PBU',event.data.value === '4256','机房布线建设及相关配件PBU',event.data.value === '3856','机房布线建设及相关配件PBU',event.data.value === '1155','机房布线建设及相关配件PBU',event.data.value === '384','机房布线建设及相关配件PBU',event.data.value === '996','机房布线建设及相关配件PBU',event.data.value === '3253','机房布线建设及相关配件PBU',event.data.value === '2981','机房布线建设及相关配件PBU',event.data.value === '3074','机房布线建设及相关配件PBU',event.data.value === '1081','机房布线建设及相关配件PBU',event.data.value === '1083','机房布线建设及相关配件PBU',event.data.value === '2958','机房布线建设及相关配件PBU',event.data.value === '1000','机房布线建设及相关配件PBU',event.data.value === '1023','机房布线建设及相关配件PBU',event.data.value === '2686','机房布线建设及相关配件PBU',event.data.value === '1105','机房布线建设及相关配件PBU',event.data.value === '2978','机房布线建设及相关配件PBU',event.data.value === '1003','机房布线建设及相关配件PBU',event.data.value === '2975','机房布线建设及相关配件PBU',event.data.value === '4095','机房布线建设及相关配件PBU',event.data.value === '5031','机房布线建设及相关配件PBU',event.data.value === '5033','机房布线建设及相关配件PBU',event.data.value === '5035','机房布线建设及相关配件PBU',event.data.value === '5037','机房布线建设及相关配件PBU',event.data.value === '2906','机房布线建设及相关配件PBU',event.data.value === '3080','机房布线建设及相关配件PBU',event.data.value === '939','机房布线建设及相关配件PBU',event.data.value === '609','机房布线建设及相关配件PBU',event.data.value === '914','机房布线建设及相关配件PBU',event.data.value === '576','机房布线建设及相关配件PBU',event.data.value === '3125','机房布线建设及相关配件PBU',event.data.value === '1342','机房布线建设及相关配件PBU',event.data.value === '1132','机房布线建设及相关配件PBU',event.data.value === '3087','机房布线建设及相关配件PBU',event.data.value === '3191','机房布线建设及相关配件PBU',event.data.value === '634','机房布线建设及相关配件PBU',event.data.value === '1415','机房布线建设及相关配件PBU',event.data.value === '3216','机房布线建设及相关配件PBU',event.data.value === '974','机房布线建设及相关配件PBU',event.data.value === '4061','机房布线建设及相关配件PBU',event.data.value === '1326','机房布线建设及相关配件PBU',event.data.value === '3081','机房布线建设及相关配件PBU',event.data.value === '1049','机房布线建设及相关配件PBU',event.data.value === '1050','机房布线建设及相关配件PBU',event.data.value === '3507','机房布线建设及相关配件PBU',event.data.value === '1048','机房布线建设及相关配件PBU',event.data.value === '4111','机房布线建设及相关配件PBU',event.data.value === '1051','机房布线建设及相关配件PBU',event.data.value === '1146','机房布线建设及相关配件PBU',event.data.value === '1057','机房布线建设及相关配件PBU',event.data.value === '1052','机房布线建设及相关配件PBU',event.data.value === '1196','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '1131','机房布线建设及相关配件PBU',event.data.value === '3362','机房布线建设及相关配件PBU',event.data.value === '1061','机房布线建设及相关配件PBU',event.data.value === '1053','机房布线建设及相关配件PBU',event.data.value === '4221','机房布线建设及相关配件PBU',event.data.value === '4112','机房布线建设及相关配件PBU',event.data.value === '4220','机房布线建设及相关配件PBU',event.data.value === '1190','机房布线建设及相关配件PBU',event.data.value === '975','机房布线建设及相关配件PBU',event.data.value === '2','机房布线建设及相关配件PBU',event.data.value === '3075','机房布线建设及相关配件PBU',event.data.value === '45','机房布线建设及相关配件PBU',event.data.value === '3358','机房布线建设及相关配件PBU',event.data.value === '3359','机房布线建设及相关配件PBU',event.data.value === '3360','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '1098','机房布线建设及相关配件PBU',event.data.value === '1067','机房布线建设及相关配件PBU',event.data.value === '5167','机房布线建设及相关配件PBU',event.data.value === '1070','机房布线建设及相关配件PBU',event.data.value === '3367','机房布线建设及相关配件PBU',event.data.value === '3054','机房布线建设及相关配件PBU',event.data.value === '4177','机房布线建设及相关配件PBU',event.data.value === '4351','机房布线建设及相关配件PBU',event.data.value === '3093','机房布线建设及相关配件PBU',event.data.value === '900','机房布线建设及相关配件PBU',event.data.value === '2967','机房布线建设及相关配件PBU',event.data.value === '2907','机房布线建设及相关配件PBU',event.data.value === '3059','机房布线建设及相关配件PBU',event.data.value === '3319','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '3084','机房布线建设及相关配件PBU',event.data.value === '3397','机房布线建设及相关配件PBU',event.data.value === '3398','机房布线建设及相关配件PBU',event.data.value === '3085','机房布线建设及相关配件PBU',event.data.value === '3092','机房布线建设及相关配件PBU',event.data.value === '2963','机房布线建设及相关配件PBU',event.data.value === '3076','机房布线建设及相关配件PBU',event.data.value === '3082','机房布线建设及相关配件PBU',event.data.value === '3535','机房布线建设及相关配件PBU',event.data.value === '3128','机房布线建设及相关配件PBU',event.data.value === '1145','机房布线建设及相关配件PBU',event.data.value === '2687','机房布线建设及相关配件PBU',event.data.value === '4353','机房布线建设及相关配件PBU',event.data.value === '4355','机房布线建设及相关配件PBU',event.data.value === '4357','机房布线建设及相关配件PBU',event.data.value === '4182','机房布线建设及相关配件PBU',event.data.value === '1017','机房布线建设及相关配件PBU',event.data.value === '593','机房布线建设及相关配件PBU',event.data.value === '594','机房布线建设及相关配件PBU',event.data.value === '3049','机房布线建设及相关配件PBU',event.data.value === '980','机房布线建设及相关配件PBU',event.data.value === '3371','机房布线建设及相关配件PBU',event.data.value === '4136','机房布线建设及相关配件PBU',event.data.value === '5137','机房布线建设及相关配件PBU',event.data.value === '3312','机房布线建设及相关配件PBU',event.data.value === '3261','机房布线建设及相关配件PBU',event.data.value === '3262','机房布线建设及相关配件PBU',event.data.value === '3263','机房布线建设及相关配件PBU',event.data.value === '3311','机房布线建设及相关配件PBU',event.data.value === '4283','机房布线建设及相关配件PBU',event.data.value === '4284','机房布线建设及相关配件PBU',event.data.value === '1134','机房布线建设及相关配件PBU',event.data.value === '1133','机房布线建设及相关配件PBU',event.data.value === '3073','机房布线建设及相关配件PBU',event.data.value === '633','机房布线建设及相关配件PBU',event.data.value === '959','机房布线建设及相关配件PBU',event.data.value === '4371','机房布线建设及相关配件PBU',event.data.value === '4373','机房布线建设及相关配件PBU',event.data.value === '4044','机房布线建设及相关配件PBU',event.data.value === '3313','机房布线建设及相关配件PBU',event.data.value === '4290','机房布线建设及相关配件PBU',event.data.value === '590','机房布线建设及相关配件PBU',event.data.value === '962','机房布线建设及相关配件PBU',event.data.value === '4013','机房布线建设及相关配件PBU',event.data.value === '1126','机房布线建设及相关配件PBU',event.data.value === '4298','机房布线建设及相关配件PBU',event.data.value === '1064','机房布线建设及相关配件PBU',event.data.value === '621','机房布线建设及相关配件PBU',event.data.value === '3485','机房布线建设及相关配件PBU',event.data.value === '4107','机房布线建设及相关配件PBU',event.data.value === '4108','机房布线建设及相关配件PBU',event.data.value === '4109','机房布线建设及相关配件PBU',event.data.value === '4110','机房布线建设及相关配件PBU',event.data.value === '613','机房布线建设及相关配件PBU',event.data.value === '4137','机房布线建设及相关配件PBU',event.data.value === '964','机房布线建设及相关配件PBU',event.data.value === '3347','机房布线建设及相关配件PBU',event.data.value === '24','机房布线建设及相关配件PBU',event.data.value === '2951','机房布线建设及相关配件PBU',event.data.value === '1073','机房布线建设及相关配件PBU',event.data.value === '3830','机房布线建设及相关配件PBU',event.data.value === '1321','机房布线建设及相关配件PBU',event.data.value === '22','机房布线建设及相关配件PBU',event.data.value === '35','机房布线建设及相关配件PBU',event.data.value === '33','机房布线建设及相关配件PBU',event.data.value === '36','机房布线建设及相关配件PBU',event.data.value === '38','机房布线建设及相关配件PBU',event.data.value === '1099','机房布线建设及相关配件PBU',event.data.value === '34','机房布线建设及相关配件PBU',event.data.value === '1318','机房布线建设及相关配件PBU',event.data.value === '55','机房布线建设及相关配件PBU',event.data.value === '19','机房布线建设及相关配件PBU',event.data.value === '21','机房布线建设及相关配件PBU',event.data.value === '51','机房布线建设及相关配件PBU',event.data.value === '48','机房布线建设及相关配件PBU',event.data.value === '52','机房布线建设及相关配件PBU',event.data.value === '4059','机房布线建设及相关配件PBU',event.data.value === '4060','机房布线建设及相关配件PBU',event.data.value === '3615','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '50','机房布线建设及相关配件PBU',event.data.value === '53','机房布线建设及相关配件PBU',event.data.value === '1063','机房布线建设及相关配件PBU',event.data.value === '40','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '47','机房布线建设及相关配件PBU',event.data.value === '1319','机房布线建设及相关配件PBU',event.data.value === '598','机房布线建设及相关配件PBU')}"}}, {"componentId": "u:d932d276430f", "actionType": "setValue", "args": {"value": "${IFS(event.data.value === '5451','AI交换机PDT',event.data.value === '3255','AI交换机PDT',event.data.value === '5279','AI交换机PDT',event.data.value === '4225','AI交换机PDT',event.data.value === '4363','AI交换机PDT',event.data.value === '4365','AI交换机PDT',event.data.value === '4887','AI交换机PDT',event.data.value === '4889','AI交换机PDT',event.data.value === '4891','AI交换机PDT',event.data.value === '4156','AI交换机PDT',event.data.value === '4369','AI交换机PDT',event.data.value === '5407','AI交换机PDT',event.data.value === '4228','AI交换机PDT',event.data.value === '5405','AI交换机PDT',event.data.value === '4082','AI交换机PDT',event.data.value === '4337','AI交换机PDT',event.data.value === '4135','AI交换机PDT',event.data.value === '5183','AI交换机PDT',event.data.value === '4134','AI交换机PDT',event.data.value === '4193','AI交换机PDT',event.data.value === '4257','AI交换机PDT',event.data.value === '3266','AI交换机PDT',event.data.value === '5513','AmpCon-CampusPDT',event.data.value === '4196','DCI_PDT',event.data.value === '4381','DCI_PDT',event.data.value === '5329','DCI_PDT',event.data.value === '4189','DCI_PDT',event.data.value === '4187','DCI_PDT',event.data.value === '4188','DCI_PDT',event.data.value === '5179','DCI_PDT',event.data.value === '837','DCI_PDT',event.data.value === '3395','DCI_PDT',event.data.value === '4096','DCI_PDT',event.data.value === '1200','DCI_PDT',event.data.value === '4174','DCI_PDT',event.data.value === '4175','DCI_PDT',event.data.value === '1312','DCI_PDT',event.data.value === '1314','DCI_PDT',event.data.value === '990','DCI_PDT',event.data.value === '3389','FSBOX_PDT',event.data.value === '3500','IDC交换机PDT',event.data.value === '3501','IDC交换机PDT',event.data.value === '3502','IDC交换机PDT',event.data.value === '3503','IDC交换机PDT',event.data.value === '4359','IDC交换机PDT',event.data.value === '5127','IDC交换机PDT',event.data.value === '4227','IDC交换机PDT',event.data.value === '4226','IDC交换机PDT',event.data.value === '5131','IDC交换机PDT',event.data.value === '5129','IDC交换机PDT',event.data.value === '4258','IDC交换机PDT',event.data.value === '3373','IDC交换机PDT',event.data.value === '4275','IDC交换机PDT',event.data.value === '4186','OLS_PDT',event.data.value === '4191','OLS_PDT',event.data.value === '4192','OLS_PDT',event.data.value === '4184','OLS_PDT',event.data.value === '4185','OLS_PDT',event.data.value === '628','OLS_PDT',event.data.value === '1153','OLS_PDT',event.data.value === '596','OLS_PDT',event.data.value === '1316','OLS_PDT',event.data.value === '3249','OLS_PDT',event.data.value === '178','OLS_PDT',event.data.value === '177','OLS_PDT',event.data.value === '3925','OLS_PDT',event.data.value === '179','OLS_PDT',event.data.value === '3607','OLS_PDT',event.data.value === '1202','OLS_PDT',event.data.value === '180','OLS_PDT',event.data.value === '1333','OLS_PDT',event.data.value === '1311','OLS_PDT',event.data.value === '3077','OLS_PDT',event.data.value === '4098','PON_PDT',event.data.value === '4099','PON_PDT',event.data.value === '3086','企业无线PDT',event.data.value === '4133','企业无线PDT',event.data.value === '2977','企业无线PDT',event.data.value === '4104','企业无线PDT',event.data.value === '4218','企业无线PDT',event.data.value === '3872','企业无线PDT',event.data.value === '3470','企业无线PDT',event.data.value === '3375','企业无线PDT',event.data.value === '2960','光纤布线PDT',event.data.value === '1135','光纤布线PDT',event.data.value === '3919','光纤布线PDT',event.data.value === '1140','光纤布线PDT',event.data.value === '3089','光纤布线PDT',event.data.value === '5511','光纤布线PDT',event.data.value === '4092','光纤布线PDT',event.data.value === '1125','光纤布线PDT',event.data.value === '897','光纤布线PDT',event.data.value === '3254','光纤布线PDT',event.data.value === '2867','光纤布线PDT',event.data.value === '1324','光纤布线PDT',event.data.value === '901','光纤布线PDT',event.data.value === '2866','光纤布线PDT',event.data.value === '1082','光纤布线PDT',event.data.value === '1194','光纤布线PDT',event.data.value === '220','光纤布线PDT',event.data.value === '1148','光纤布线PDT',event.data.value === '4256','光纤布线PDT',event.data.value === '3856','光纤布线PDT',event.data.value === '1155','光纤布线PDT',event.data.value === '384','光纤布线PDT',event.data.value === '996','光纤布线PDT',event.data.value === '3253','光纤布线PDT',event.data.value === '2981','光纤布线PDT',event.data.value === '3074','光纤布线PDT',event.data.value === '1081','光纤布线PDT',event.data.value === '1083','光纤布线PDT',event.data.value === '2958','光纤布线PDT',event.data.value === '1000','光纤布线PDT',event.data.value === '1023','光纤布线PDT',event.data.value === '2686','光纤布线PDT',event.data.value === '1105','光纤布线PDT',event.data.value === '2978','光纤布线PDT',event.data.value === '1003','光纤布线PDT',event.data.value === '2975','光纤布线PDT',event.data.value === '4095','光纤布线PDT',event.data.value === '5031','光纤布线PDT',event.data.value === '5033','光纤布线PDT',event.data.value === '5035','光纤布线PDT',event.data.value === '5037','光纤布线PDT',event.data.value === '2906','光纤布线PDT',event.data.value === '3080','光纤布线PDT',event.data.value === '939','光纤布线PDT',event.data.value === '609','光纤布线PDT',event.data.value === '914','光纤布线PDT',event.data.value === '576','光纤布线PDT',event.data.value === '3125','光纤布线PDT',event.data.value === '1342','光纤布线PDT',event.data.value === '1132','光纤布线PDT',event.data.value === '3087','光纤布线PDT',event.data.value === '3191','光纤布线PDT',event.data.value === '634','光纤布线PDT',event.data.value === '1415','光纤布线PDT',event.data.value === '3216','光纤布线PDT',event.data.value === '974','光纤布线PDT',event.data.value === '4061','光纤布线PDT',event.data.value === '1326','光纤布线PDT',event.data.value === '3081','光纤布线PDT',event.data.value === '1049','光纤布线PDT',event.data.value === '1050','光纤布线PDT',event.data.value === '3507','光纤布线PDT',event.data.value === '1048','光纤布线PDT',event.data.value === '4111','光纤布线PDT',event.data.value === '1051','光纤布线PDT',event.data.value === '1146','光纤布线PDT',event.data.value === '1057','光纤布线PDT',event.data.value === '1052','光纤布线PDT',event.data.value === '1196','光纤布线PDT',event.data.value === '','光纤布线PDT',event.data.value === '1131','光纤布线PDT',event.data.value === '3362','光纤布线PDT',event.data.value === '1061','光纤布线PDT',event.data.value === '1053','光纤布线PDT',event.data.value === '4221','光纤布线PDT',event.data.value === '4112','光纤布线PDT',event.data.value === '4220','光纤布线PDT',event.data.value === '1190','光纤布线PDT',event.data.value === '975','光纤布线PDT',event.data.value === '2','光纤布线PDT',event.data.value === '3075','光纤布线PDT',event.data.value === '45','光纤布线PDT',event.data.value === '3358','光纤布线PDT',event.data.value === '3359','光纤布线PDT',event.data.value === '3360','光纤布线PDT',event.data.value === '','光纤布线PDT',event.data.value === '3319','光纤布线PDT',event.data.value === '','光纤布线PDT',event.data.value === '3084','光纤布线PDT',event.data.value === '3397','光纤布线PDT',event.data.value === '3398','光纤布线PDT',event.data.value === '3085','光纤布线PDT',event.data.value === '3092','光纤布线PDT',event.data.value === '2963','光纤布线PDT',event.data.value === '3076','光纤布线PDT',event.data.value === '3082','光纤布线PDT',event.data.value === '3535','光纤布线PDT',event.data.value === '3128','光纤布线PDT',event.data.value === '1145','光纤布线PDT',event.data.value === '2687','光纤布线PDT',event.data.value === '4182','光纤布线PDT',event.data.value === '1017','光纤布线PDT',event.data.value === '1321','光纤布线PDT',event.data.value === '22','光纤布线PDT',event.data.value === '35','光纤布线PDT',event.data.value === '33','光纤布线PDT',event.data.value === '36','光纤布线PDT',event.data.value === '38','光纤布线PDT',event.data.value === '1099','光纤布线PDT',event.data.value === '34','光纤布线PDT',event.data.value === '1318','光纤布线PDT',event.data.value === '55','光纤布线PDT',event.data.value === '19','光纤布线PDT',event.data.value === '21','光纤布线PDT',event.data.value === '51','光纤布线PDT',event.data.value === '48','光纤布线PDT',event.data.value === '52','光纤布线PDT',event.data.value === '4059','光纤布线PDT',event.data.value === '4060','光纤布线PDT',event.data.value === '3615','光纤布线PDT',event.data.value === '3318','光网络',event.data.value === '1340','光网络',event.data.value === '3094','光网络',event.data.value === '3203','光网络',event.data.value === '1335','光网络',event.data.value === '1310','光网络',event.data.value === '3770','光网络',event.data.value === '4052','安防监控PDT',event.data.value === '4053','安防监控PDT',event.data.value === '4305','安防监控PDT',event.data.value === '3645','安防监控PDT',event.data.value === '4074','安防监控PDT',event.data.value === '4091','安防监控PDT',event.data.value === '4266','安防监控PDT',event.data.value === '4113','安防监控PDT',event.data.value === '39','安防监控PDT',event.data.value === '1181','安防监控PDT',event.data.value === '4056','安防监控PDT',event.data.value === '4057','安防监控PDT',event.data.value === '4058','安防监控PDT',event.data.value === '3329','安防监控PDT',event.data.value === '4076','安防监控PDT',event.data.value === '4230','安防监控PDT',event.data.value === '4077','安防监控PDT',event.data.value === '4088','安防监控PDT',event.data.value === '4102','安防监控PDT',event.data.value === '4176','安防监控PDT',event.data.value === '4242','工业交换机PDT',event.data.value === '4236','工业交换机PDT',event.data.value === '4238','工业交换机PDT',event.data.value === '4244','工业交换机PDT',event.data.value === '1159','常规速率光模块PDT',event.data.value === '2874','常规速率光模块PDT',event.data.value === '1360','常规速率光模块PDT',event.data.value === '3215','常规速率光模块PDT',event.data.value === '3618','常规速率光模块PDT',event.data.value === '63','常规速率光模块PDT',event.data.value === '3428','常规速率光模块PDT',event.data.value === '2878','常规速率光模块PDT',event.data.value === '64','常规速率光模块PDT',event.data.value === '4067','常规速率光模块PDT',event.data.value === '4080','常规速率光模块PDT',event.data.value === '81','常规速率光模块PDT',event.data.value === '89','常规速率光模块PDT',event.data.value === '1668','常规速率光模块PDT',event.data.value === '83','常规速率光模块PDT',event.data.value === '4066','常规速率光模块PDT',event.data.value === '2985','常规速率光模块PDT',event.data.value === '2873','常规速率光模块PDT',event.data.value === '3331','常规速率光模块PDT',event.data.value === '1117','常规速率光模块PDT',event.data.value === '2869','常规速率光模块PDT',event.data.value === '3860','常规速率光模块PDT',event.data.value === '2757','常规速率光模块PDT',event.data.value === '3946','常规速率光模块PDT',event.data.value === '1175','常规速率光模块PDT',event.data.value === '1158','常规速率光模块PDT',event.data.value === '114','常规速率光模块PDT',event.data.value === '3058','常规速率光模块PDT',event.data.value === '4245','接入交换机PDT',event.data.value === '1368','接入交换机PDT',event.data.value === '4240','接入交换机PDT',event.data.value === '4248','接入交换机PDT',event.data.value === '4237','接入交换机PDT',event.data.value === '4234','接入交换机PDT',event.data.value === '4335','接入交换机PDT',event.data.value === '4239','接入交换机PDT',event.data.value === '4247','接入交换机PDT',event.data.value === '3309','接入交换机PDT',event.data.value === '4241','接入交换机PDT',event.data.value === '4173','服务器PDT',event.data.value === '4014','服务器PDT',event.data.value === '3376','服务器PDT',event.data.value === '5169','服务器PDT',event.data.value === '2887','服务器PDT',event.data.value === '3256','核心交换机PDT',event.data.value === '1071','核心交换机PDT',event.data.value === '3257','核心交换机PDT',event.data.value === '3258','核心交换机PDT',event.data.value === '4243','核心交换机PDT',event.data.value === '4333','核心交换机PDT',event.data.value === '4367','核心交换机PDT',event.data.value === '5159','核心交换机PDT',event.data.value === '4246','核心交换机PDT',event.data.value === '2968','核心交换机PDT',event.data.value === '4319','核心交换机PDT',event.data.value === '4249','核心交换机PDT',event.data.value === '4252','核心交换机PDT',event.data.value === '4251','核心交换机PDT',event.data.value === '1150','核心交换机PDT',event.data.value === '3646','核心交换机PDT',event.data.value === '3374','核心交换机PDT',event.data.value === '65','电信光模块PDT',event.data.value === '3619','电信光模块PDT',event.data.value === '3863','电信光模块PDT',event.data.value === '1789','电信光模块PDT',event.data.value === '4103','电信光模块PDT',event.data.value === '2689','电信光模块PDT',event.data.value === '3684','电信光模块PDT',event.data.value === '3685','电信光模块PDT',event.data.value === '3874','电信光模块PDT',event.data.value === '3660','电信光模块PDT',event.data.value === '4895','电信光模块PDT',event.data.value === '3907','电信光模块PDT',event.data.value === '4897','电信光模块PDT',event.data.value === '3609','电信光模块PDT',event.data.value === '4128','电信光模块PDT',event.data.value === '3662','电信光模块PDT',event.data.value === '4115','电信光模块PDT',event.data.value === '2845','电信光模块PDT',event.data.value === '920','电信光模块PDT',event.data.value === '3260','设备电源及配件PDT',event.data.value === '4306','设备电源及配件PDT',event.data.value === '1402','设备电源及配件PDT',event.data.value === '4260','设备电源及配件PDT',event.data.value === '4261','设备电源及配件PDT',event.data.value === '5401','设备电源及配件PDT',event.data.value === '4263','设备电源及配件PDT',event.data.value === '5403','设备电源及配件PDT',event.data.value === '1343','设备电源及配件PDT',event.data.value === '54','设备电源及配件PDT',event.data.value === '4040','设备电源及配件PDT',event.data.value === '3053','设备电源及配件PDT',event.data.value === '4153','设备电源及配件PDT',event.data.value === '1320','设备电源及配件PDT',event.data.value === '4262','设备电源及配件PDT',event.data.value === '1038','设备电源及配件PDT',event.data.value === '1044','设备电源及配件PDT',event.data.value === '1047','设备电源及配件PDT',event.data.value === '1186','设备电源及配件PDT',event.data.value === '1045','设备电源及配件PDT',event.data.value === '1046','设备电源及配件PDT',event.data.value === '3095','设备电源及配件PDT',event.data.value === '3090','设备电源及配件PDT',event.data.value === '1098','铜缆布线PDT',event.data.value === '1067','铜缆布线PDT',event.data.value === '5167','铜缆布线PDT',event.data.value === '1070','铜缆布线PDT',event.data.value === '3367','铜缆布线PDT',event.data.value === '3054','铜缆布线PDT',event.data.value === '4177','铜缆布线PDT',event.data.value === '4351','铜缆布线PDT',event.data.value === '3093','铜缆布线PDT',event.data.value === '900','铜缆布线PDT',event.data.value === '2967','铜缆布线PDT',event.data.value === '2907','铜缆布线PDT',event.data.value === '3059','铜缆布线PDT',event.data.value === '4353','铜缆布线PDT',event.data.value === '4355','铜缆布线PDT',event.data.value === '4357','铜缆布线PDT',event.data.value === '593','铜缆布线PDT',event.data.value === '594','铜缆布线PDT',event.data.value === '3049','铜缆布线PDT',event.data.value === '980','铜缆布线PDT',event.data.value === '3371','铜缆布线PDT',event.data.value === '4136','铜缆布线PDT',event.data.value === '5137','铜缆布线PDT',event.data.value === '3312','铜缆布线PDT',event.data.value === '3261','铜缆布线PDT',event.data.value === '3262','铜缆布线PDT',event.data.value === '3263','铜缆布线PDT',event.data.value === '3311','铜缆布线PDT',event.data.value === '4283','铜缆布线PDT',event.data.value === '4284','铜缆布线PDT',event.data.value === '1134','铜缆布线PDT',event.data.value === '1133','铜缆布线PDT',event.data.value === '3073','铜缆布线PDT',event.data.value === '633','铜缆布线PDT',event.data.value === '959','铜缆布线PDT',event.data.value === '4371','铜缆布线PDT',event.data.value === '4373','铜缆布线PDT',event.data.value === '4044','铜缆布线PDT',event.data.value === '3313','铜缆布线PDT',event.data.value === '4290','铜缆布线PDT',event.data.value === '590','铜缆布线PDT',event.data.value === '962','铜缆布线PDT',event.data.value === '4013','铜缆布线PDT',event.data.value === '1126','铜缆布线PDT',event.data.value === '4298','铜缆布线PDT',event.data.value === '1064','铜缆布线PDT',event.data.value === '621','铜缆布线PDT',event.data.value === '3485','铜缆布线PDT',event.data.value === '4107','铜缆布线PDT',event.data.value === '4108','铜缆布线PDT',event.data.value === '4109','铜缆布线PDT',event.data.value === '4110','铜缆布线PDT',event.data.value === '613','铜缆布线PDT',event.data.value === '4137','铜缆布线PDT',event.data.value === '964','铜缆布线PDT',event.data.value === '3347','铜缆布线PDT',event.data.value === '24','铜缆布线PDT',event.data.value === '2951','铜缆布线PDT',event.data.value === '1073','铜缆布线PDT',event.data.value === '3830','铜缆布线PDT',event.data.value === '50','铜缆布线PDT',event.data.value === '53','铜缆布线PDT',event.data.value === '1063','铜缆布线PDT',event.data.value === '40','铜缆布线PDT',event.data.value === '47','铜缆布线PDT',event.data.value === '1319','铜缆布线PDT',event.data.value === '598','铜缆布线PDT',event.data.value === '1392','高速率光模块PDT',event.data.value === '3801','高速率光模块PDT',event.data.value === '3672','高速率光模块PDT',event.data.value === '1367','高速率光模块PDT',event.data.value === '2320','高速率光模块PDT',event.data.value === '3908','高速率光模块PDT',event.data.value === '3831','高速率光模块PDT',event.data.value === '4089','高速率光模块PDT',event.data.value === '3652','高速率光模块PDT',event.data.value === '3542','高速率光模块PDT',event.data.value === '4065','高速率光模块PDT',event.data.value === '3858','高速率光模块PDT',event.data.value === '3963','高速率光模块PDT',event.data.value === '4129','高速率光模块PDT',event.data.value === '3659','高速率光模块PDT',event.data.value === '1172','高速率光模块PDT')}"}}]}}, "required": true, "value": "${envDefaultFormData.prodclass}"}], "columnClassName": "", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}}, {"body": [{"type": "input-date", "label": "期望交付时间", "name": "desire_time", "id": "u:307c6b56564d", "format": "YYYY-MM-DD", "inputFormat": "YYYY-MM-DD", "value": "${envDefaultFormData.desire_time}"}, {"type": "grid", "columns": [], "id": "u:fc9320c2bc66"}], "id": "u:b5dcd10f2193"}], "id": "u:96eeb38020b9", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "grid", "columns": [], "id": "u:b8d89528ac48", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "grid", "columns": [], "id": "u:cb459c04d548", "className": "m-t m-b"}, {"type": "grid", "id": "u:0f5d456b378a", "columns": []}, {"type": "grid", "columns": [{"body": [{"type": "select", "label": "需求类型", "name": "type", "options": [{"label": "定制研发", "value": "type1", "hiddenOn": "${OR(CONTAINS(demand_type,'type18'),CONTAINS(demand_type,'type19'))}"}, {"label": "AI算力解决方案", "value": "type2"}, {"label": "数据中心解决方案", "value": "type4"}, {"label": "园区无线解决方案", "value": "type5"}, {"label": "园区有线解决方案", "value": "type6"}, {"label": "安防监控解决方案", "value": "type7"}, {"label": "光网络解决方案", "value": "type8"}, {"label": "综合布线解决方案", "value": "type9"}], "id": "u:7ef4f572de27", "multiple": false, "disabledOn": "${CONTAINS(type,\"type1\")}", "static": false, "required": true}, {"type": "input-text", "label": "询盘RQ编号", "name": "enquiries", "id": "u:1d62fdb0a59a", "onEvent": {"blur": {"weight": 0, "actions": [{"args": {"options": {}, "api": {"url": "${envUrl}/api/erp/getInquiryInformation?inquiryNumber=${enquiries}", "method": "get", "messages": {}, "headers": {"token": "${envToken}"}, "adaptor": "\nreturn { data: payload.data || 0 }\n", "requestAdaptor": "", "sendOn": "${enquiries}"}}, "outputVar": "rqRes", "actionType": "ajax", "expression": "${event.data.value}"}, {"args": {"msgType": "warning", "position": "top-right", "closeButton": true, "showIcon": true, "msg": "${`\"${event.data.value}\"是无效编号!`}", "title": "询盘编号"}, "actionType": "toast", "expression": "${event.data.rqRes.data == 0}"}, {"actionType": "ajax", "args": {"options": {}, "api": {"url": "${envUrl}/api/workbench/getRequestQuote?inquiryNumber=${enquiries}", "method": "get", "messages": {}, "headers": {"token": "${envToken}"}, "adaptor": "if (payload.code !== 200) return {}\r\nreturn payload.data", "sendOn": "${enquiries}", "requestAdaptor": ""}}, "outputVar": "quoteRes"}, {"actionType": "setValue", "args": {"value": "${event.data.quoteRes.currency_code}"}, "componentId": "u:50e25d6b3671"}, {"actionType": "setValue", "args": {"value": "${event.data.quoteRes.product_price_total}"}, "componentId": "u:495b8af16830"}]}}, "size": "full", "value": "${envDefaultFormData.enquiries}", "hiddenOn": "${type==\"type14\"||type==\"type15\"||type==\"type14,type15\"||type==\"type15,type14\"}", "requiredOn": "${resource && CONTAINS('type2',resource)}"}, {"type": "input-date", "label": "预计成单时间", "name": "completion_time", "id": "u:307c6b56564d", "format": "YYYY-MM-DD", "inputFormat": "YYYY-MM-DD", "value": "${envDefaultFormData.completion_time}"}, {"type": "tpl", "tpl": "产品线影响子任务分配，请按产品分类进行选择", "inline": true, "wrapperComponent": "", "id": "u:87d3610b23a4", "style": {}, "hiddenOn": "${!OR(CONTAINS(type,'type1,'),CONTAINS(type,'type2,'),CONTAINS(type,'type4,'),CONTAINS(type,'type6,'),ENDSWITH(type,'type1'),ENDSWITH(type,'type2'),ENDSWITH(type,'type4'),ENDSWITH(type,'type6'))}", "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent", "font:default": {"color": "#F5A623"}}}}, {"type": "select", "label": "产品线", "name": "product_type", "options": [{"label": "服务器", "value": "type1"}, {"label": "交换机", "value": "type2"}, {"label": "模块", "value": "type3"}, {"label": "布线", "value": "type4"}, {"label": "其他", "value": "type5"}], "id": "u:76a0da6871c2", "multiple": false, "value": "其他", "required": true, "hiddenOn": "${!OR(CONTAINS(type,'type1,'),CONTAINS(type,'type2,'),CONTAINS(type,'type4,'),CONTAINS(type,'type6,'),ENDSWITH(type,'type1'),ENDSWITH(type,'type2'),ENDSWITH(type,'type4'),ENDSWITH(type,'type6'))}"}, {"type": "input-datetime", "label": "期望安装时间（当地时间）", "name": "desire_time", "id": "u:307c6b56564d", "format": "YYYY-MM-DD", "inputFormat": "YYYY-MM-DD HH:mm:ss", "placeholder": "请选择日期以及时间", "minDate": "", "maxDate": "", "value": "${desire_service_time && DATEMODIFY(desire_service_time, country_time, 'hours') || envDefaultFormData.desire_time || null}", "required": true, "mode": "normal", "hiddenOn": "${!CONTAINS(demand_type,\"type39\")}", "clearValueOnHidden": true}, {"type": "input-text", "label": "城市", "name": "city", "id": "u:50a0c519d25b", "required": true, "hiddenOn": "${!CONTAINS(demand_type,\"type39\")}", "clearValueOnHidden": true, "value": "${envDefaultFormData.city}"}, {"type": "input-text", "label": "市郊", "name": "suburb", "id": "u:c0e0bb4b04a3", "hiddenOn": "${!CONTAINS(demand_type,\"type39\")}", "clearValueOnHidden": true, "value": "${envDefaultFormData.suburb}"}, {"type": "select", "label": "PDT", "name": "ltc_pdt", "id": "u:d932d276430f", "placeholder": "-", "options": [{"label": "AI交换机PDT", "value": "AI交换机PDT"}, {"label": "AmpCon-CampusPDT", "value": "AmpCon-CampusPDT"}, {"label": "DCI_PDT", "value": "DCI_PDT"}, {"label": "FSBOX_PDT", "value": "FSBOX_PDT"}, {"label": "IDC交换机PDT", "value": "IDC交换机PDT"}, {"label": "OLS_PDT", "value": "OLS_PDT"}, {"label": "PON_PDT", "value": "PON_PDT"}, {"label": "企业无线PDT", "value": "企业无线PDT"}, {"label": "光纤布线PDT", "value": "光纤布线PDT"}, {"label": "光网络", "value": "光网络"}, {"label": "安防监控PDT", "value": "安防监控PDT"}, {"label": "工业交换机 PDT", "value": "工业交换机 PDT"}, {"label": "常规速率光模块PDT", "value": "常规速率光模块PDT"}, {"label": "接入交换机PDT", "value": "接入交换机PDT"}, {"label": "服务器PDT", "value": "服务器PDT"}, {"label": "核心交换机PDT", "value": "核心交换机PDT"}, {"label": "电信光模块PDT", "value": "电信光模块PDT"}, {"label": "设备电源及配件PDT", "value": "设备电源及配件PDT"}, {"label": "铜缆布线PDT", "value": "铜缆布线PDT"}, {"label": "高速率光模块PDT", "value": "高速率光模块PDT"}], "multiple": false, "onEvent": {"change": {"weight": 0, "actions": []}}, "width": 160, "hidden": false, "disabled": true}], "id": "u:5f7c8d79677b", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}}, {"id": "u:d1e8a8f8f4d2", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}, "body": [{"type": "tpl", "id": "u:0a545faa3188", "tpl": "可参考查看需求点与资源匹配关系：", "inline": true, "wrapperComponent": "", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent", "font:default": {"color": "var(--colors-warning-5)"}}}, "wrapperCustomStyle": {"color": "#F5A623"}, "hidden": false}, {"type": "link", "value": "http://www.baidu.com/", "id": "u:b8018497faa2", "href": "https://mah2eds8ab.feishu.cn/docx/S2hWdR6MGoEji2xA0BrcDe0bnXc", "body": "需求点说明", "icon": "fa fa-slideshare", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}, "hidden": false}, {"type": "nested-select", "label": "需求点", "name": "demand_type", "id": "u:f6e04c92e407", "onlyChildren": true, "options": [{"label": "功能参数咨询", "value": "type1", "children": [{"label": "硬件参数咨询", "value": "type2"}, {"label": "软件功能咨询", "value": "type3"}]}, {"label": "硬件定制", "value": "type4"}, {"label": "软件功能定制", "value": "type5"}, {"label": "管理平台定制开发", "value": "type6"}, {"label": "软件兼容/适配", "value": "type7"}, {"label": "外观结构定制", "value": "type8"}, {"label": "包装标签定制", "value": "type9"}, {"label": "数字化采购系统", "value": "type10"}, {"label": "产品测试", "value": "type11", "children": [{"label": "软件/平台测试", "value": "type49"}, {"label": "连通性测试", "value": "type13"}, {"label": "性能测试", "value": "type14"}, {"label": "样品测试", "value": "type15"}, {"label": "兼容性测试", "value": "type12"}]}, {"label": "市场主流竞品替代", "value": "type16"}, {"label": "技术视频会议交流", "value": "type17"}, {"label": "解决方案设计", "value": "type18"}, {"label": "方案验证与测试", "value": "type19"}, {"label": "解决方案成功案例", "value": "type20"}, {"label": "Demo体验", "value": "type21"}, {"label": "认证支持", "value": "type22", "children": [{"label": "国家准入认证", "value": "type23"}, {"label": "产品品质认证", "value": "type24"}]}, {"label": "价格", "value": "type25", "children": [{"label": "产品价格", "value": "type26"}, {"label": "软件价格", "value": "type27"}, {"label": "本地服务价格", "value": "type28", "hiddenOn": "true"}]}, {"label": "交期", "value": "type29"}, {"label": "起订量", "value": "type30"}, {"label": "EOL", "value": "type31"}, {"label": "物流服务", "value": "type32", "children": [{"label": "灵活的发货组合", "value": "type34"}, {"label": "定制化制程咨询", "value": "type35"}, {"label": "多样化的物流服务", "value": "type36"}]}, {"label": "全球库存/调仓计划", "value": "type33"}, {"label": "灵活的支付组合", "value": "type37"}, {"label": "服务交付", "value": "type38", "children": [{"label": "FS Care-安装服务", "value": "type39"}, {"label": "配置服务", "value": "type40", "hiddenOn": "true"}, {"label": "客户培训", "value": "type41"}, {"label": "远程配置", "value": "type42"}]}, {"label": "售后服务咨询", "value": "type43", "children": [{"label": "延保服务", "value": "type44"}, {"label": "SLA升级", "value": "type45"}, {"label": "特殊退维修服务", "value": "type46"}, {"label": "备件服务", "value": "type47"}, {"label": "售后远程指导", "value": "type48"}]}], "searchable": false, "onlyLeaf": true, "multiple": true, "required": true, "value": "${envDefaultFormData.demand_type}", "joinValues": true, "hidden": false, "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "u:7ef4f572de27", "ignoreError": false, "actionType": "setValue", "args": {"value": "${IFS(event.data.value === 'type4','type1',event.data.value === 'type5','type1',event.data.value === 'type6','type1',event.data.value === 'type7','type1',event.data.value === 'type18','type2',event.data.value === 'type19','type2')}"}}, {"componentId": "u:0e5bb0d856f5", "ignoreError": false, "actionType": "setValue", "args": {"value": "${IFS(event.data.value === 'type39','1')}"}}]}}, "delimiter": ","}, {"type": "input-number", "label": "预计询盘金额", "name": "esamount", "keyboard": true, "id": "u:495b8af16830", "step": 1, "value": "${envDefaultFormData.esamount || envDefaultFormData.custSapData.ExpectedRevenueAmount}", "precision": 2, "hiddenOn": "${type==\"type14\"||type==\"type15\"||type==\"type14,type15\"||type==\"type15,type14\"}"}, {"type": "nested-select", "label": "产品线2", "name": "ProductLine_type", "onlyChildren": true, "options": [{"label": "光网络", "value": "type1"}, {"label": "数据中心IDC", "value": "type2", "children": [{"label": "IDC", "value": "type4"}, {"label": "AI算力", "value": "type5"}]}, {"label": "园区网", "value": "type3", "children": [{"label": "企业园区", "value": "type6"}, {"label": "SMB与工业", "value": "type7"}]}], "id": "u:4d298e95eecc", "searchable": false, "onlyLeaf": false, "multiple": false}, {"type": "select", "label": "业务线", "name": "business", "id": "u:700214d18864", "options": [{"label": "安防监控 PDT", "value": "安防监控PDT"}, {"label": "铜缆布线 PDT", "value": "铜缆布线PDT"}, {"label": "服务器 PDT", "value": "服务器PDT"}, {"label": "高速率光模块 PDT", "value": "高速率光模块PDT"}, {"label": "光网络 PDT", "value": "光网络PDT"}, {"label": "企业无线 PDT", "value": "企业无线PDT"}, {"label": "企业园区交换机 PDT", "value": "企业园区交换机PDT"}, {"label": "设备电源及配件 PDT", "value": "设备电源及配件PDT"}, {"label": "细分应用光模块 PDT", "value": "细分应用光模块PDT"}, {"label": "中低速率光模块 PDT", "value": "中低速率光模块PDT"}, {"label": "AI交换机 PDT", "value": "AI交换机PDT"}, {"label": "FS BOX PDT", "value": "FS BOXPDT"}, {"label": "IDC交换机 PDT", "value": "IDC交换机PDT"}, {"label": "SMB与工业交换机 PDT", "value": "SMB与工业交换机PDT"}, {"label": "光纤布线 PDT", "value": "光纤布线PDT"}], "multiple": false, "disabled": false}, {"type": "input-datetime", "label": "期望安装时间（北京时间）", "name": "desire_service_time", "id": "u:307c6b56564d", "format": "x", "inputFormat": "YYYY-MM-DD HH:mm:ss", "value": "${desire_time && DATEMODIFY(desire_time, -country_time, 'hours') || envDefaultFormData.desire_service_time || null}", "onEvent": {"change": {"weight": 0, "actions": []}}, "placeholder": "请选择日期以及时间", "minDate": "${NOW()}", "maxDate": "", "required": true, "mode": "normal", "visible": true, "hiddenOn": "${!CONTAINS(demand_type,\"type39\")}", "clearValueOnHidden": true}, {"type": "input-text", "label": "州/省/地区", "name": "state", "id": "u:c6bb5e7e1b40", "required": true, "hiddenOn": "${!CONTAINS(demand_type,\"type39\")}", "clearValueOnHidden": true, "value": "${envDefaultFormData.state}"}, {"type": "select", "label": "FS Care服务类型", "name": "servicetype", "id": "u:0e5bb0d856f5", "options": [{"label": "安装服务", "value": "1"}, {"label": "远程售后服务", "value": "2"}, {"label": "售后现场服务", "value": "3"}, {"label": "硬件提前更换", "value": "4"}], "multiple": false, "value": "${envDefaultFormData.servicetype}", "hidden": true}]}, {"id": "u:bc0e83f556e9", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}, "body": [{"type": "select", "label": "紧急程度", "name": "is<PERSON><PERSON>", "options": [{"label": "一般", "value": "0"}, {"label": "加急", "value": "1"}], "id": "u:5ed577af1105", "multiple": false, "value": "${envDefaultFormData.isUrgent}"}, {"type": "select", "label": "币种", "name": "qrcurrency", "id": "u:50e25d6b3671", "options": [{"label": "USD $", "value": "USD"}, {"label": "CNY ¥", "value": "CNY"}, {"label": "EUR €", "value": "EUR"}, {"label": "GBP £", "value": "GBP"}, {"label": "YEN 円", "value": "YEN"}, {"label": "AUD $", "value": "AUD"}, {"label": "HK $", "value": "HK"}, {"label": "CHF", "value": "CHF"}, {"label": "NOK", "value": "NOK"}, {"label": "DKK", "value": "DKK"}, {"label": "SEK", "value": "SEK"}, {"label": "R $", "value": "R"}, {"label": "C $", "value": "C"}, {"label": "SGD", "value": "SGD"}], "multiple": false, "value": "", "hiddenOn": "${type==\"type14\"||type==\"type15\"||type==\"type14,type15\"||type==\"type15,type14\"}"}, {"type": "select", "label": "是否为OTN", "name": "isotn", "id": "u:a88fb3cb2661", "options": [{"label": "是", "value": "A"}, {"label": "否", "value": "B"}], "multiple": false, "visible": true, "hidden": false, "value": "", "className": "", "disabled": true}, {"type": "select", "label": "是否为IPD", "name": "isipd", "id": "u:c525c760fe20", "options": [{"label": "是", "value": "A"}, {"label": "否", "value": "B"}], "multiple": false, "visible": true, "hidden": false, "value": "${ProductLine_type ==='type1' && 'A' || ProductLine_type ==='type2' && 'A' || ProductLine_type ==='type3' && 'A' || ProductLine_type ==='type4' && 'A' || ProductLine_type ==='type5' && 'A' || ProductLine_type ==='type6' && 'A' || ProductLine_type ==='type7' && 'A' || 'B'}", "className": "", "disabled": true}, {"type": "input-text", "label": "详细地址", "name": "detailed_address", "id": "u:cfe79383ada5", "required": true, "hiddenOn": "${!CONTAINS(demand_type,\"type39\")}", "clearValueOnHidden": true, "value": "${envDefaultFormData.detailed_address}"}, {"type": "input-text", "label": "国家/地区", "name": "country", "id": "u:e82119860f0a", "required": true, "hiddenOn": "${!CONTAINS(demand_type,\"type39\")}", "clearValueOnHidden": true, "value": "${envDefaultFormData.country}"}, {"type": "select", "label": "产品线（PBU）", "name": "mdf_type", "id": "u:6e0c15b671ef", "options": [{"label": "光模块PBU", "value": "光模块PBU"}, {"label": "光网络PBU", "value": "光网络PBU"}, {"label": "园区网PBU", "value": "园区网PBU"}, {"label": "数据中心PBU", "value": "数据中心PBU"}, {"label": "无线安防PBU", "value": "无线安防PBU"}, {"label": "机房布线建设及相关配件PBU", "value": "机房布线建设及相关配件PBU"}], "multiple": false, "placeholder": "-", "hidden": false, "disabled": true}]}], "id": "u:023e848293b4", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "grid", "columns": [{"id": "u:5d346e60f16a", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}, "body": [{"type": "combo", "label": "问答Q号：", "name": "erp_qnumber_arr", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:18e184c4f7ca"}, "items": [{"type": "input-text", "name": "consultNumber", "placeholder": "请输入问答Q号", "id": "u:d09f31dff06f", "onEvent": {"blur": {"weight": 0, "actions": [{"outputVar": "responseResult", "actionType": "ajax", "args": {"options": {}, "api": {"url": "${envUrl}/api/formInterface/getNumberDetail", "method": "get", "requestAdaptor": "", "adaptor": "", "messages": {}, "headers": {"token": "${envToken}"}, "data": {"consultNumber": "${event.data.value}"}}}}, {"script": "/* 自定义JS使用说明：\n  * 1.动作执行函数doAction，可以执行所有类型的动作\n  * 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n  * 3.事件对象event，在doAction之后执行event.stopPropagation();可以阻止后续动作执行\n*/\nconst { props = {} } = context\nconst data = event?.data?.responseData ?? {}\nconsole.log(context,props, event)\nif (data?.url) {\n  doAction({\n    componentId: 'u:5793dd86858b',\n    actionType: 'setValue',\n    args: {\n      value: {\n        consultNumber: data.consultNumber,\n        url: data.url\n      },\n      index: props.index\n    }\n  });\n} else {\n  doAction({\n    componentId: 'u:5793dd86858b',\n    actionType: 'setValue',\n    args: {\n      value: {\n        consultNumber: '',\n        url: ''\n      },\n      index: props.index\n    }\n  });\n  doAction({\n  actionType: 'toast',\n  args: {\n    msg: '当前查询连接为空，请重新输入'\n  }\n});\n}\n", "actionType": "custom"}]}}, "size": "full", "themeCss": {"inputControlClassName": {"padding-and-margin:default": {"marginBottom": "8px"}}, "addOnClassName": {}, "descriptionClassName": {}, "labelClassName": {}}, "inputControlClassName": "inputControlClassName-d09f31dff06f", "addOnClassName": "addOnClassName-d09f31dff06f", "descriptionClassName": "descriptionClassName-d09f31dff06f", "labelClassName": "labelClassName-d09f31dff06f"}, {"type": "link", "value": "http://www.baidu.com/", "id": "u:3927f1fe3534", "href": "${url}", "body": "${url}", "blank": true, "hidden": true}], "id": "u:5793dd86858b", "strictMode": true, "syncFields": [], "tabsMode": false, "minLength": "", "placeholder": "添加问答Q号", "multiLine": true, "mode": "normal", "noBorder": false, "size": "full"}, {"type": "input-text", "label": "商机编号", "name": "sap_opportunity_number", "id": "u:8026030c8b7f", "value": "${envDefaultFormData.sap_opportunity_number || envDefaultFormData.custSapData.opportunityNumber}", "readOnly": true}, {"type": "select", "label": "安防监控类型", "name": "security_monitoring", "options": [{"label": "安防监控", "value": "type1"}, {"label": "视频会议", "value": "type2"}, {"label": "其他", "value": "type3"}], "id": "u:59d55b622522", "value": "其他", "hiddenOn": "${!CONTAINS(type,\"type7\")}", "multiple": false, "required": true}, {"type": "select", "label": "光网络类型", "name": "optical_network_type", "options": [{"label": "光传输", "value": "type1"}, {"label": "光接入", "value": "type2"}], "id": "u:feef8d016039", "value": "", "hiddenOn": "${!CONTAINS(type,\"type8\")}", "multiple": false, "required": true}, {"type": "select", "label": "综合布线类型", "name": "Integrated_wiring_type", "options": [{"label": "铜缆系统", "value": "type1"}, {"label": "光纤系统", "value": "type2"}, {"label": "机柜&配线系统", "value": "type3"}], "id": "u:d58e4b930c75", "value": "", "hiddenOn": "${!CONTAINS(type,\"type9\")}", "multiple": false, "required": true}, {"type": "select", "label": "判断LTC流程", "name": "is_ltc", "id": "u:f74b01d94504", "options": [{"label": "是", "value": "A"}, {"label": "否", "value": "B"}], "multiple": false, "value": "B", "visible": false}]}, {"body": [{"type": "combo", "label": "关联FS单号：", "name": "erp_fsnumber_arr", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:5eb9911f509d"}, "items": [{"type": "input-text", "name": "orderNumber", "placeholder": "请输入FS单号，分单后仅可填写分单号", "id": "u:f8616fdd3b91", "onEvent": {"blur": {"weight": 0, "actions": [{"outputVar": "responseResult", "actionType": "ajax", "args": {"options": {}, "api": {"url": "${envUrl}/api/formInterface/getNumberDetail", "method": "get", "requestAdaptor": "", "adaptor": "", "messages": {}, "headers": {"token": "${envToken}"}, "data": {"orderNumber": "${event.data.value}"}}}}, {"script": "/* 自定义JS使用说明：\n  * 1.动作执行函数doAction，可以执行所有类型的动作\n  * 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n  * 3.事件对象event，在doAction之后执行event.stopPropagation();可以阻止后续动作执行\n*/\nconst { props = {} } = context\nconst data = event?.data?.responseData ?? {}\nconsole.log(context,props, event)\nif (data?.url) {\n  doAction({\n    componentId: 'u:82ec1a4f9f84',\n    actionType: 'setValue',\n    args: {\n      value: {\n        orderNumber: data.orderNumber,\n        url: data.url\n      },\n      index: props.index\n    }\n  });\n} else {\n  doAction({\n    componentId: 'u:82ec1a4f9f84',\n    actionType: 'setValue',\n    args: {\n      value: {\n        orderNumber: '',\n        url: ''\n      },\n      index: props.index\n    }\n  });\n  doAction({\n  actionType: 'toast',\n  args: {\n    msg: '当前查询连接为空，请重新输入'\n  }\n});\n}\n", "actionType": "custom"}]}}, "size": "full", "themeCss": {"inputControlClassName": {"padding-and-margin:default": {"marginBottom": "8px"}}, "addOnClassName": {}, "descriptionClassName": {}, "labelClassName": {}}, "inputControlClassName": "inputControlClassName-d09f31dff06f inputControlClassName-f8616fdd3b91", "addOnClassName": "addOnClassName-d09f31dff06f addOnClassName-f8616fdd3b91", "descriptionClassName": "descriptionClassName-d09f31dff06f descriptionClassName-f8616fdd3b91", "labelClassName": "labelClassName-d09f31dff06f labelClassName-f8616fdd3b91"}, {"type": "link", "value": "http://www.baidu.com/", "id": "u:7d943f79cfe7", "href": "${url}", "body": "${url}", "blank": true, "hidden": true}], "id": "u:82ec1a4f9f84", "strictMode": true, "syncFields": [], "tabsMode": false, "minLength": "", "placeholder": "添加问答Q号", "multiLine": true, "mode": "normal", "noBorder": false, "size": "full"}, {"type": "input-text", "label": "商机ID", "name": "sap_opportunity_id", "id": "u:e53a1325c15a", "value": "${envDefaultFormData.sap_opportunity_id || envDefaultFormData.custSapData.opportunityId}", "readOnly": true}], "id": "u:4427a228fa87", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}}, {"id": "u:761f60f25b82", "style": {"boxShadow": " 0px 0px 0px 0px transparent"}, "body": [{"type": "combo", "label": "zendesk编号", "name": "zendesk_number", "id": "u:5793dd86858b", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:18e184c4f7ca"}, "items": [{"type": "input-text", "name": "consultNumber", "placeholder": "请输入问答Q号", "id": "u:d09f31dff06f", "onEvent": {"blur": {"weight": 0, "actions": [{"outputVar": "responseResult", "actionType": "ajax", "args": {"options": {}, "api": {"url": "${envUrl}/api/formInterface/getNumberDetail", "method": "get", "requestAdaptor": "", "adaptor": "", "messages": {}, "headers": {"token": "${envToken}"}, "data": {"consultNumber": "${event.data.value}"}}}}, {"script": "/* 自定义JS使用说明：\n  * 1.动作执行函数doAction，可以执行所有类型的动作\n  * 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n  * 3.事件对象event，在doAction之后执行event.stopPropagation();可以阻止后续动作执行\n*/\nconst { props = {} } = context\nconst data = event?.data?.responseData ?? {}\nconsole.log(context,props, event)\nif (data?.url) {\n  doAction({\n    componentId: 'u:5793dd86858b',\n    actionType: 'setValue',\n    args: {\n      value: {\n        consultNumber: data.consultNumber,\n        url: data.url\n      },\n      index: props.index\n    }\n  });\n} else {\n  doAction({\n    componentId: 'u:5793dd86858b',\n    actionType: 'setValue',\n    args: {\n      value: {\n        consultNumber: '',\n        url: ''\n      },\n      index: props.index\n    }\n  });\n  doAction({\n  actionType: 'toast',\n  args: {\n    msg: '当前查询连接为空，请重新输入'\n  }\n});\n}\n", "actionType": "custom"}]}}, "size": "full", "themeCss": {"inputControlClassName": {"padding-and-margin:default": {"marginBottom": "8px"}}, "addOnClassName": {}, "descriptionClassName": {}, "labelClassName": {}}, "inputControlClassName": "inputControlClassName-d09f31dff06f", "addOnClassName": "addOnClassName-d09f31dff06f", "descriptionClassName": "descriptionClassName-d09f31dff06f", "labelClassName": "labelClassName-d09f31dff06f"}, {"type": "link", "value": "http://www.baidu.com/", "id": "u:3927f1fe3534", "href": "${url}", "body": "${url}", "blank": true, "hidden": true}], "strictMode": true, "syncFields": [], "tabsMode": false, "minLength": "", "placeholder": "添加zendesk编号", "multiLine": true, "mode": "normal", "noBorder": false, "size": "full"}]}], "id": "u:14eca66fbd90", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent", "padding-and-margin:default": {"marginTop": "24px"}}}}, {"type": "grid", "columns": [{"body": [{"type": "grid", "columns": [{"body": [], "id": "u:39e445ca8eb4", "style": {"boxShadow": " 0px 0px 0px 0px transparent", "marginTop": "20px"}}], "id": "u:18b9acccb0bd", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "input-table", "label": "产品信息", "name": "prodtable", "id": "u:a0ecf174c24c", "columns": [{"label": "*FS 产品ID", "name": "product_id", "quickEdit": {"type": "input-text", "name": "product_id", "id": "u:903c41b30645", "mode": "inline", "value": ""}, "id": "u:7a6da02d1637", "type": "text", "placeholder": "-", "width": 150}, {"label": "产品属性", "name": "productAttrText", "quickEdit": false, "id": "u:205a198e2e6f", "type": "tpl", "placeholder": "-", "width": 300, "tpl": "${(productAttrText || '点击选择产品属性') | raw}", "inline": true, "wrapperComponent": "", "remark": "双击属性可编辑", "onEvent": {"click": {"weight": 0, "actions": [{"actionType": "ajax", "outputVar": "tokenRes", "args": {"api": {"url": "${envUrl}/api/formInterface/reception/getReceptionToken/${product_id}", "method": "get", "messages": {}, "headers": {"token": "${envToken}"}, "dataType": "json"}, "options": {}}}, {"ignoreError": false, "actionType": "dialog", "dialog": {"$ref": "modal-ref-1"}, "expression": "${product_id && envType === 'edit'}", "waitForAction": false, "data": {"shopToken": "${event.data.tokenRes.result}", "product_id": "${product_id}", "tableIndex": "${event.data.__super.__super.__super.index}"}}]}}}, {"type": "text", "label": "型号名", "name": "product_model", "id": "u:e6715209579d", "placeholder": "-", "quickEdit": {"type": "input-text", "name": "product_model", "id": "u:19707fda0973", "mode": "popOver", "value": ""}, "width": 150}, {"label": "需求数量", "name": "product_num", "id": "u:b70e6e035784", "type": "text", "placeholder": "-", "width": 150, "quickEdit": {"type": "input-number", "name": "product_num", "id": "u:9c86c3d5bed8", "mode": "popOver", "keyboard": true, "step": 1}}, {"label": "*产品分类", "name": "prodclass", "id": "u:f9ad4711e0e6", "type": "nested-select", "multiple": false, "placeholder": "-", "width": 450, "required": true, "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "u:d932d276430f", "ignoreError": false, "actionType": "setValue", "args": {"value": "${IFS(event.data.value === '5451','AI交换机PDT',event.data.value === '3255','AI交换机PDT',event.data.value === '5279','AI交换机PDT',event.data.value === '4225','AI交换机PDT',event.data.value === '4363','AI交换机PDT',event.data.value === '4365','AI交换机PDT',event.data.value === '4887','AI交换机PDT',event.data.value === '4889','AI交换机PDT',event.data.value === '4891','AI交换机PDT',event.data.value === '4156','AI交换机PDT',event.data.value === '4369','AI交换机PDT',event.data.value === '5407','AI交换机PDT',event.data.value === '4228','AI交换机PDT',event.data.value === '5405','AI交换机PDT',event.data.value === '4082','AI交换机PDT',event.data.value === '4337','AI交换机PDT',event.data.value === '4135','AI交换机PDT',event.data.value === '5183','AI交换机PDT',event.data.value === '4134','AI交换机PDT',event.data.value === '4193','AI交换机PDT',event.data.value === '4257','AI交换机PDT',event.data.value === '3266','AI交换机PDT',event.data.value === '5513','AmpCon-CampusPDT',event.data.value === '4196','DCI_PDT',event.data.value === '4381','DCI_PDT',event.data.value === '5329','DCI_PDT',event.data.value === '4189','DCI_PDT',event.data.value === '4187','DCI_PDT',event.data.value === '4188','DCI_PDT',event.data.value === '5179','DCI_PDT',event.data.value === '837','DCI_PDT',event.data.value === '3395','DCI_PDT',event.data.value === '4096','DCI_PDT',event.data.value === '1200','DCI_PDT',event.data.value === '4174','DCI_PDT',event.data.value === '4175','DCI_PDT',event.data.value === '1312','DCI_PDT',event.data.value === '1314','DCI_PDT',event.data.value === '990','DCI_PDT',event.data.value === '3389','FSBOX_PDT',event.data.value === '3500','IDC交换机PDT',event.data.value === '3501','IDC交换机PDT',event.data.value === '3502','IDC交换机PDT',event.data.value === '3503','IDC交换机PDT',event.data.value === '4359','IDC交换机PDT',event.data.value === '5127','IDC交换机PDT',event.data.value === '4227','IDC交换机PDT',event.data.value === '4226','IDC交换机PDT',event.data.value === '5131','IDC交换机PDT',event.data.value === '5129','IDC交换机PDT',event.data.value === '4258','IDC交换机PDT',event.data.value === '3373','IDC交换机PDT',event.data.value === '4275','IDC交换机PDT',event.data.value === '4186','OLS_PDT',event.data.value === '4191','OLS_PDT',event.data.value === '4192','OLS_PDT',event.data.value === '4184','OLS_PDT',event.data.value === '4185','OLS_PDT',event.data.value === '628','OLS_PDT',event.data.value === '1153','OLS_PDT',event.data.value === '596','OLS_PDT',event.data.value === '1316','OLS_PDT',event.data.value === '3249','OLS_PDT',event.data.value === '178','OLS_PDT',event.data.value === '177','OLS_PDT',event.data.value === '3925','OLS_PDT',event.data.value === '179','OLS_PDT',event.data.value === '3607','OLS_PDT',event.data.value === '1202','OLS_PDT',event.data.value === '180','OLS_PDT',event.data.value === '1333','OLS_PDT',event.data.value === '1311','OLS_PDT',event.data.value === '3077','OLS_PDT',event.data.value === '4098','PON_PDT',event.data.value === '4099','PON_PDT',event.data.value === '3086','企业无线PDT',event.data.value === '4133','企业无线PDT',event.data.value === '2977','企业无线PDT',event.data.value === '4104','企业无线PDT',event.data.value === '4218','企业无线PDT',event.data.value === '3872','企业无线PDT',event.data.value === '3470','企业无线PDT',event.data.value === '3375','企业无线PDT',event.data.value === '2960','光纤布线PDT',event.data.value === '1135','光纤布线PDT',event.data.value === '3919','光纤布线PDT',event.data.value === '1140','光纤布线PDT',event.data.value === '3089','光纤布线PDT',event.data.value === '5511','光纤布线PDT',event.data.value === '4092','光纤布线PDT',event.data.value === '1125','光纤布线PDT',event.data.value === '897','光纤布线PDT',event.data.value === '3254','光纤布线PDT',event.data.value === '2867','光纤布线PDT',event.data.value === '1324','光纤布线PDT',event.data.value === '901','光纤布线PDT',event.data.value === '2866','光纤布线PDT',event.data.value === '1082','光纤布线PDT',event.data.value === '1194','光纤布线PDT',event.data.value === '220','光纤布线PDT',event.data.value === '1148','光纤布线PDT',event.data.value === '4256','光纤布线PDT',event.data.value === '3856','光纤布线PDT',event.data.value === '1155','光纤布线PDT',event.data.value === '384','光纤布线PDT',event.data.value === '996','光纤布线PDT',event.data.value === '3253','光纤布线PDT',event.data.value === '2981','光纤布线PDT',event.data.value === '3074','光纤布线PDT',event.data.value === '1081','光纤布线PDT',event.data.value === '1083','光纤布线PDT',event.data.value === '2958','光纤布线PDT',event.data.value === '1000','光纤布线PDT',event.data.value === '1023','光纤布线PDT',event.data.value === '2686','光纤布线PDT',event.data.value === '1105','光纤布线PDT',event.data.value === '2978','光纤布线PDT',event.data.value === '1003','光纤布线PDT',event.data.value === '2975','光纤布线PDT',event.data.value === '4095','光纤布线PDT',event.data.value === '5031','光纤布线PDT',event.data.value === '5033','光纤布线PDT',event.data.value === '5035','光纤布线PDT',event.data.value === '5037','光纤布线PDT',event.data.value === '2906','光纤布线PDT',event.data.value === '3080','光纤布线PDT',event.data.value === '939','光纤布线PDT',event.data.value === '609','光纤布线PDT',event.data.value === '914','光纤布线PDT',event.data.value === '576','光纤布线PDT',event.data.value === '3125','光纤布线PDT',event.data.value === '1342','光纤布线PDT',event.data.value === '1132','光纤布线PDT',event.data.value === '3087','光纤布线PDT',event.data.value === '3191','光纤布线PDT',event.data.value === '634','光纤布线PDT',event.data.value === '1415','光纤布线PDT',event.data.value === '3216','光纤布线PDT',event.data.value === '974','光纤布线PDT',event.data.value === '4061','光纤布线PDT',event.data.value === '1326','光纤布线PDT',event.data.value === '3081','光纤布线PDT',event.data.value === '1049','光纤布线PDT',event.data.value === '1050','光纤布线PDT',event.data.value === '3507','光纤布线PDT',event.data.value === '1048','光纤布线PDT',event.data.value === '4111','光纤布线PDT',event.data.value === '1051','光纤布线PDT',event.data.value === '1146','光纤布线PDT',event.data.value === '1057','光纤布线PDT',event.data.value === '1052','光纤布线PDT',event.data.value === '1196','光纤布线PDT',event.data.value === '','光纤布线PDT',event.data.value === '1131','光纤布线PDT',event.data.value === '3362','光纤布线PDT',event.data.value === '1061','光纤布线PDT',event.data.value === '1053','光纤布线PDT',event.data.value === '4221','光纤布线PDT',event.data.value === '4112','光纤布线PDT',event.data.value === '4220','光纤布线PDT',event.data.value === '1190','光纤布线PDT',event.data.value === '975','光纤布线PDT',event.data.value === '2','光纤布线PDT',event.data.value === '3075','光纤布线PDT',event.data.value === '45','光纤布线PDT',event.data.value === '3358','光纤布线PDT',event.data.value === '3359','光纤布线PDT',event.data.value === '3360','光纤布线PDT',event.data.value === '','光纤布线PDT',event.data.value === '3319','光纤布线PDT',event.data.value === '','光纤布线PDT',event.data.value === '3084','光纤布线PDT',event.data.value === '3397','光纤布线PDT',event.data.value === '3398','光纤布线PDT',event.data.value === '3085','光纤布线PDT',event.data.value === '3092','光纤布线PDT',event.data.value === '2963','光纤布线PDT',event.data.value === '3076','光纤布线PDT',event.data.value === '3082','光纤布线PDT',event.data.value === '3535','光纤布线PDT',event.data.value === '3128','光纤布线PDT',event.data.value === '1145','光纤布线PDT',event.data.value === '2687','光纤布线PDT',event.data.value === '4182','光纤布线PDT',event.data.value === '1017','光纤布线PDT',event.data.value === '1321','光纤布线PDT',event.data.value === '22','光纤布线PDT',event.data.value === '35','光纤布线PDT',event.data.value === '33','光纤布线PDT',event.data.value === '36','光纤布线PDT',event.data.value === '38','光纤布线PDT',event.data.value === '1099','光纤布线PDT',event.data.value === '34','光纤布线PDT',event.data.value === '1318','光纤布线PDT',event.data.value === '55','光纤布线PDT',event.data.value === '19','光纤布线PDT',event.data.value === '21','光纤布线PDT',event.data.value === '51','光纤布线PDT',event.data.value === '48','光纤布线PDT',event.data.value === '52','光纤布线PDT',event.data.value === '4059','光纤布线PDT',event.data.value === '4060','光纤布线PDT',event.data.value === '3615','光纤布线PDT',event.data.value === '3318','光网络',event.data.value === '1340','光网络',event.data.value === '3094','光网络',event.data.value === '3203','光网络',event.data.value === '1335','光网络',event.data.value === '1310','光网络',event.data.value === '3770','光网络',event.data.value === '4052','安防监控PDT',event.data.value === '4053','安防监控PDT',event.data.value === '4305','安防监控PDT',event.data.value === '3645','安防监控PDT',event.data.value === '4074','安防监控PDT',event.data.value === '4091','安防监控PDT',event.data.value === '4266','安防监控PDT',event.data.value === '4113','安防监控PDT',event.data.value === '39','安防监控PDT',event.data.value === '1181','安防监控PDT',event.data.value === '4056','安防监控PDT',event.data.value === '4057','安防监控PDT',event.data.value === '4058','安防监控PDT',event.data.value === '3329','安防监控PDT',event.data.value === '4076','安防监控PDT',event.data.value === '4230','安防监控PDT',event.data.value === '4077','安防监控PDT',event.data.value === '4088','安防监控PDT',event.data.value === '4102','安防监控PDT',event.data.value === '4176','安防监控PDT',event.data.value === '4242','工业交换机PDT',event.data.value === '4236','工业交换机PDT',event.data.value === '4238','工业交换机PDT',event.data.value === '4244','工业交换机PDT',event.data.value === '1159','常规速率光模块PDT',event.data.value === '2874','常规速率光模块PDT',event.data.value === '1360','常规速率光模块PDT',event.data.value === '3215','常规速率光模块PDT',event.data.value === '3618','常规速率光模块PDT',event.data.value === '63','常规速率光模块PDT',event.data.value === '3428','常规速率光模块PDT',event.data.value === '2878','常规速率光模块PDT',event.data.value === '64','常规速率光模块PDT',event.data.value === '4067','常规速率光模块PDT',event.data.value === '4080','常规速率光模块PDT',event.data.value === '81','常规速率光模块PDT',event.data.value === '89','常规速率光模块PDT',event.data.value === '1668','常规速率光模块PDT',event.data.value === '83','常规速率光模块PDT',event.data.value === '4066','常规速率光模块PDT',event.data.value === '2985','常规速率光模块PDT',event.data.value === '2873','常规速率光模块PDT',event.data.value === '3331','常规速率光模块PDT',event.data.value === '1117','常规速率光模块PDT',event.data.value === '2869','常规速率光模块PDT',event.data.value === '3860','常规速率光模块PDT',event.data.value === '2757','常规速率光模块PDT',event.data.value === '3946','常规速率光模块PDT',event.data.value === '1175','常规速率光模块PDT',event.data.value === '1158','常规速率光模块PDT',event.data.value === '114','常规速率光模块PDT',event.data.value === '3058','常规速率光模块PDT',event.data.value === '4245','接入交换机PDT',event.data.value === '1368','接入交换机PDT',event.data.value === '4240','接入交换机PDT',event.data.value === '4248','接入交换机PDT',event.data.value === '4237','接入交换机PDT',event.data.value === '4234','接入交换机PDT',event.data.value === '4335','接入交换机PDT',event.data.value === '4239','接入交换机PDT',event.data.value === '4247','接入交换机PDT',event.data.value === '3309','接入交换机PDT',event.data.value === '4241','接入交换机PDT',event.data.value === '4173','服务器PDT',event.data.value === '4014','服务器PDT',event.data.value === '3376','服务器PDT',event.data.value === '5169','服务器PDT',event.data.value === '2887','服务器PDT',event.data.value === '3256','核心交换机PDT',event.data.value === '1071','核心交换机PDT',event.data.value === '3257','核心交换机PDT',event.data.value === '3258','核心交换机PDT',event.data.value === '4243','核心交换机PDT',event.data.value === '4333','核心交换机PDT',event.data.value === '4367','核心交换机PDT',event.data.value === '5159','核心交换机PDT',event.data.value === '4246','核心交换机PDT',event.data.value === '2968','核心交换机PDT',event.data.value === '4319','核心交换机PDT',event.data.value === '4249','核心交换机PDT',event.data.value === '4252','核心交换机PDT',event.data.value === '4251','核心交换机PDT',event.data.value === '1150','核心交换机PDT',event.data.value === '3646','核心交换机PDT',event.data.value === '3374','核心交换机PDT',event.data.value === '65','电信光模块PDT',event.data.value === '3619','电信光模块PDT',event.data.value === '3863','电信光模块PDT',event.data.value === '1789','电信光模块PDT',event.data.value === '4103','电信光模块PDT',event.data.value === '2689','电信光模块PDT',event.data.value === '3684','电信光模块PDT',event.data.value === '3685','电信光模块PDT',event.data.value === '3874','电信光模块PDT',event.data.value === '3660','电信光模块PDT',event.data.value === '4895','电信光模块PDT',event.data.value === '3907','电信光模块PDT',event.data.value === '4897','电信光模块PDT',event.data.value === '3609','电信光模块PDT',event.data.value === '4128','电信光模块PDT',event.data.value === '3662','电信光模块PDT',event.data.value === '4115','电信光模块PDT',event.data.value === '2845','电信光模块PDT',event.data.value === '920','电信光模块PDT',event.data.value === '3260','设备电源及配件PDT',event.data.value === '4306','设备电源及配件PDT',event.data.value === '1402','设备电源及配件PDT',event.data.value === '4260','设备电源及配件PDT',event.data.value === '4261','设备电源及配件PDT',event.data.value === '5401','设备电源及配件PDT',event.data.value === '4263','设备电源及配件PDT',event.data.value === '5403','设备电源及配件PDT',event.data.value === '1343','设备电源及配件PDT',event.data.value === '54','设备电源及配件PDT',event.data.value === '4040','设备电源及配件PDT',event.data.value === '3053','设备电源及配件PDT',event.data.value === '4153','设备电源及配件PDT',event.data.value === '1320','设备电源及配件PDT',event.data.value === '4262','设备电源及配件PDT',event.data.value === '1038','设备电源及配件PDT',event.data.value === '1044','设备电源及配件PDT',event.data.value === '1047','设备电源及配件PDT',event.data.value === '1186','设备电源及配件PDT',event.data.value === '1045','设备电源及配件PDT',event.data.value === '1046','设备电源及配件PDT',event.data.value === '3095','设备电源及配件PDT',event.data.value === '3090','设备电源及配件PDT',event.data.value === '1098','铜缆布线PDT',event.data.value === '1067','铜缆布线PDT',event.data.value === '5167','铜缆布线PDT',event.data.value === '1070','铜缆布线PDT',event.data.value === '3367','铜缆布线PDT',event.data.value === '3054','铜缆布线PDT',event.data.value === '4177','铜缆布线PDT',event.data.value === '4351','铜缆布线PDT',event.data.value === '3093','铜缆布线PDT',event.data.value === '900','铜缆布线PDT',event.data.value === '2967','铜缆布线PDT',event.data.value === '2907','铜缆布线PDT',event.data.value === '3059','铜缆布线PDT',event.data.value === '4353','铜缆布线PDT',event.data.value === '4355','铜缆布线PDT',event.data.value === '4357','铜缆布线PDT',event.data.value === '593','铜缆布线PDT',event.data.value === '594','铜缆布线PDT',event.data.value === '3049','铜缆布线PDT',event.data.value === '980','铜缆布线PDT',event.data.value === '3371','铜缆布线PDT',event.data.value === '4136','铜缆布线PDT',event.data.value === '5137','铜缆布线PDT',event.data.value === '3312','铜缆布线PDT',event.data.value === '3261','铜缆布线PDT',event.data.value === '3262','铜缆布线PDT',event.data.value === '3263','铜缆布线PDT',event.data.value === '3311','铜缆布线PDT',event.data.value === '4283','铜缆布线PDT',event.data.value === '4284','铜缆布线PDT',event.data.value === '1134','铜缆布线PDT',event.data.value === '1133','铜缆布线PDT',event.data.value === '3073','铜缆布线PDT',event.data.value === '633','铜缆布线PDT',event.data.value === '959','铜缆布线PDT',event.data.value === '4371','铜缆布线PDT',event.data.value === '4373','铜缆布线PDT',event.data.value === '4044','铜缆布线PDT',event.data.value === '3313','铜缆布线PDT',event.data.value === '4290','铜缆布线PDT',event.data.value === '590','铜缆布线PDT',event.data.value === '962','铜缆布线PDT',event.data.value === '4013','铜缆布线PDT',event.data.value === '1126','铜缆布线PDT',event.data.value === '4298','铜缆布线PDT',event.data.value === '1064','铜缆布线PDT',event.data.value === '621','铜缆布线PDT',event.data.value === '3485','铜缆布线PDT',event.data.value === '4107','铜缆布线PDT',event.data.value === '4108','铜缆布线PDT',event.data.value === '4109','铜缆布线PDT',event.data.value === '4110','铜缆布线PDT',event.data.value === '613','铜缆布线PDT',event.data.value === '4137','铜缆布线PDT',event.data.value === '964','铜缆布线PDT',event.data.value === '3347','铜缆布线PDT',event.data.value === '24','铜缆布线PDT',event.data.value === '2951','铜缆布线PDT',event.data.value === '1073','铜缆布线PDT',event.data.value === '3830','铜缆布线PDT',event.data.value === '50','铜缆布线PDT',event.data.value === '53','铜缆布线PDT',event.data.value === '1063','铜缆布线PDT',event.data.value === '40','铜缆布线PDT',event.data.value === '47','铜缆布线PDT',event.data.value === '1319','铜缆布线PDT',event.data.value === '598','铜缆布线PDT',event.data.value === '1392','高速率光模块PDT',event.data.value === '3801','高速率光模块PDT',event.data.value === '3672','高速率光模块PDT',event.data.value === '1367','高速率光模块PDT',event.data.value === '2320','高速率光模块PDT',event.data.value === '3908','高速率光模块PDT',event.data.value === '3831','高速率光模块PDT',event.data.value === '4089','高速率光模块PDT',event.data.value === '3652','高速率光模块PDT',event.data.value === '3542','高速率光模块PDT',event.data.value === '4065','高速率光模块PDT',event.data.value === '3858','高速率光模块PDT',event.data.value === '3963','高速率光模块PDT',event.data.value === '4129','高速率光模块PDT',event.data.value === '3659','高速率光模块PDT',event.data.value === '1172','高速率光模块PDT')}"}}, {"componentId": "u:6e0c15b671ef", "ignoreError": false, "actionType": "setValue", "args": {"value": "${IFS(event.data.value === '1392','光模块PBU',event.data.value === '3801','光模块PBU',event.data.value === '3672','光模块PBU',event.data.value === '1367','光模块PBU',event.data.value === '2320','光模块PBU',event.data.value === '3908','光模块PBU',event.data.value === '3831','光模块PBU',event.data.value === '4089','光模块PBU',event.data.value === '3652','光模块PBU',event.data.value === '3542','光模块PBU',event.data.value === '4065','光模块PBU',event.data.value === '3858','光模块PBU',event.data.value === '3963','光模块PBU',event.data.value === '1159','光模块PBU',event.data.value === '2874','光模块PBU',event.data.value === '1360','光模块PBU',event.data.value === '3215','光模块PBU',event.data.value === '3618','光模块PBU',event.data.value === '63','光模块PBU',event.data.value === '3428','光模块PBU',event.data.value === '2878','光模块PBU',event.data.value === '64','光模块PBU',event.data.value === '4067','光模块PBU',event.data.value === '4080','光模块PBU',event.data.value === '81','光模块PBU',event.data.value === '89','光模块PBU',event.data.value === '1668','光模块PBU',event.data.value === '83','光模块PBU',event.data.value === '4066','光模块PBU',event.data.value === '2985','光模块PBU',event.data.value === '4129','光模块PBU',event.data.value === '3659','光模块PBU',event.data.value === '1172','光模块PBU',event.data.value === '2873','光模块PBU',event.data.value === '3331','光模块PBU',event.data.value === '1117','光模块PBU',event.data.value === '2869','光模块PBU',event.data.value === '3860','光模块PBU',event.data.value === '65','光模块PBU',event.data.value === '3619','光模块PBU',event.data.value === '3863','光模块PBU',event.data.value === '1789','光模块PBU',event.data.value === '4103','光模块PBU',event.data.value === '2689','光模块PBU',event.data.value === '3684','光模块PBU',event.data.value === '3685','光模块PBU',event.data.value === '3874','光模块PBU',event.data.value === '3660','光模块PBU',event.data.value === '4895','光模块PBU',event.data.value === '3907','光模块PBU',event.data.value === '4897','光模块PBU',event.data.value === '3609','光模块PBU',event.data.value === '4128','光模块PBU',event.data.value === '3662','光模块PBU',event.data.value === '4115','光模块PBU',event.data.value === '3389','光模块PBU',event.data.value === '2757','光模块PBU',event.data.value === '3946','光模块PBU',event.data.value === '1175','光模块PBU',event.data.value === '1158','光模块PBU',event.data.value === '114','光模块PBU',event.data.value === '3058','光模块PBU',event.data.value === '2845','光模块PBU',event.data.value === '920','光模块PBU',event.data.value === '4186','光网络PBU',event.data.value === '4191','光网络PBU',event.data.value === '4192','光网络PBU',event.data.value === '4196','光网络PBU',event.data.value === '4381','光网络PBU',event.data.value === '5329','光网络PBU',event.data.value === '4189','光网络PBU',event.data.value === '4187','光网络PBU',event.data.value === '4188','光网络PBU',event.data.value === '5179','光网络PBU',event.data.value === '4184','光网络PBU',event.data.value === '4185','光网络PBU',event.data.value === '4098','光网络PBU',event.data.value === '4099','光网络PBU',event.data.value === '628','光网络PBU',event.data.value === '837','光网络PBU',event.data.value === '3395','光网络PBU',event.data.value === '1153','光网络PBU',event.data.value === '3318','光网络PBU',event.data.value === '1340','光网络PBU',event.data.value === '3094','光网络PBU',event.data.value === '4096','光网络PBU',event.data.value === '3203','光网络PBU',event.data.value === '596','光网络PBU',event.data.value === '1335','光网络PBU',event.data.value === '1316','光网络PBU',event.data.value === '3249','光网络PBU',event.data.value === '1310','光网络PBU',event.data.value === '178','光网络PBU',event.data.value === '177','光网络PBU',event.data.value === '3925','光网络PBU',event.data.value === '179','光网络PBU',event.data.value === '3607','光网络PBU',event.data.value === '1202','光网络PBU',event.data.value === '180','光网络PBU',event.data.value === '1333','光网络PBU',event.data.value === '1200','光网络PBU',event.data.value === '4174','光网络PBU',event.data.value === '4175','光网络PBU',event.data.value === '1312','光网络PBU',event.data.value === '1314','光网络PBU',event.data.value === '3770','光网络PBU',event.data.value === '1311','光网络PBU',event.data.value === '3077','光网络PBU',event.data.value === '990','光网络PBU',event.data.value === '4245','园区网PBU',event.data.value === '3256','园区网PBU',event.data.value === '1071','园区网PBU',event.data.value === '3257','园区网PBU',event.data.value === '3258','园区网PBU',event.data.value === '4243','园区网PBU',event.data.value === '4333','园区网PBU',event.data.value === '4367','园区网PBU',event.data.value === '5159','园区网PBU',event.data.value === '1368','园区网PBU',event.data.value === '4246','园区网PBU',event.data.value === '2968','园区网PBU',event.data.value === '4319','园区网PBU',event.data.value === '5513','园区网PBU',event.data.value === '4249','园区网PBU',event.data.value === '4240','园区网PBU',event.data.value === '4248','园区网PBU',event.data.value === '4237','园区网PBU',event.data.value === '4234','园区网PBU',event.data.value === '4335','园区网PBU',event.data.value === '4239','园区网PBU',event.data.value === '4247','园区网PBU',event.data.value === '3309','园区网PBU',event.data.value === '4241','园区网PBU',event.data.value === '4242','园区网PBU',event.data.value === '4236','园区网PBU',event.data.value === '4238','园区网PBU',event.data.value === '4244','园区网PBU',event.data.value === '4252','园区网PBU',event.data.value === '4251','园区网PBU',event.data.value === '1150','园区网PBU',event.data.value === '3646','园区网PBU',event.data.value === '3374','园区网PBU',event.data.value === '3500','数据中心PBU',event.data.value === '3501','数据中心PBU',event.data.value === '3502','数据中心PBU',event.data.value === '3503','数据中心PBU',event.data.value === '5451','数据中心PBU',event.data.value === '3255','数据中心PBU',event.data.value === '5279','数据中心PBU',event.data.value === '4225','数据中心PBU',event.data.value === '4359','数据中心PBU',event.data.value === '4363','数据中心PBU',event.data.value === '4365','数据中心PBU',event.data.value === '4887','数据中心PBU',event.data.value === '4889','数据中心PBU',event.data.value === '4891','数据中心PBU',event.data.value === '4156','数据中心PBU',event.data.value === '5127','数据中心PBU',event.data.value === '4369','数据中心PBU',event.data.value === '5407','数据中心PBU',event.data.value === '4228','数据中心PBU',event.data.value === '5405','数据中心PBU',event.data.value === '4227','数据中心PBU',event.data.value === '4226','数据中心PBU',event.data.value === '5131','数据中心PBU',event.data.value === '5129','数据中心PBU',event.data.value === '4258','数据中心PBU',event.data.value === '4082','数据中心PBU',event.data.value === '4337','数据中心PBU',event.data.value === '4135','数据中心PBU',event.data.value === '5183','数据中心PBU',event.data.value === '4134','数据中心PBU',event.data.value === '4193','数据中心PBU',event.data.value === '4257','数据中心PBU',event.data.value === '3266','数据中心PBU',event.data.value === '4173','数据中心PBU',event.data.value === '4014','数据中心PBU',event.data.value === '3376','数据中心PBU',event.data.value === '5169','数据中心PBU',event.data.value === '2887','数据中心PBU',event.data.value === '3373','数据中心PBU',event.data.value === '4275','数据中心PBU',event.data.value === '3086','无线安防PBU',event.data.value === '4133','无线安防PBU',event.data.value === '2977','无线安防PBU',event.data.value === '4104','无线安防PBU',event.data.value === '4218','无线安防PBU',event.data.value === '3872','无线安防PBU',event.data.value === '3470','无线安防PBU',event.data.value === '3375','无线安防PBU',event.data.value === '4052','无线安防PBU',event.data.value === '4053','无线安防PBU',event.data.value === '4305','无线安防PBU',event.data.value === '3645','无线安防PBU',event.data.value === '4074','无线安防PBU',event.data.value === '4091','无线安防PBU',event.data.value === '4266','无线安防PBU',event.data.value === '4113','无线安防PBU',event.data.value === '39','无线安防PBU',event.data.value === '1181','无线安防PBU',event.data.value === '4056','无线安防PBU',event.data.value === '4057','无线安防PBU',event.data.value === '4058','无线安防PBU',event.data.value === '3329','无线安防PBU',event.data.value === '4076','无线安防PBU',event.data.value === '4230','无线安防PBU',event.data.value === '4077','无线安防PBU',event.data.value === '4088','无线安防PBU',event.data.value === '4102','无线安防PBU',event.data.value === '4176','无线安防PBU',event.data.value === '3260','机房布线建设及相关配件PBU',event.data.value === '4306','机房布线建设及相关配件PBU',event.data.value === '1402','机房布线建设及相关配件PBU',event.data.value === '4260','机房布线建设及相关配件PBU',event.data.value === '4261','机房布线建设及相关配件PBU',event.data.value === '5401','机房布线建设及相关配件PBU',event.data.value === '4263','机房布线建设及相关配件PBU',event.data.value === '5403','机房布线建设及相关配件PBU',event.data.value === '1343','机房布线建设及相关配件PBU',event.data.value === '54','机房布线建设及相关配件PBU',event.data.value === '4040','机房布线建设及相关配件PBU',event.data.value === '3053','机房布线建设及相关配件PBU',event.data.value === '4153','机房布线建设及相关配件PBU',event.data.value === '1320','机房布线建设及相关配件PBU',event.data.value === '4262','机房布线建设及相关配件PBU',event.data.value === '2960','机房布线建设及相关配件PBU',event.data.value === '1038','机房布线建设及相关配件PBU',event.data.value === '1044','机房布线建设及相关配件PBU',event.data.value === '1047','机房布线建设及相关配件PBU',event.data.value === '1186','机房布线建设及相关配件PBU',event.data.value === '1045','机房布线建设及相关配件PBU',event.data.value === '1046','机房布线建设及相关配件PBU',event.data.value === '3095','机房布线建设及相关配件PBU',event.data.value === '3090','机房布线建设及相关配件PBU',event.data.value === '1135','机房布线建设及相关配件PBU',event.data.value === '3919','机房布线建设及相关配件PBU',event.data.value === '1140','机房布线建设及相关配件PBU',event.data.value === '3089','机房布线建设及相关配件PBU',event.data.value === '5511','机房布线建设及相关配件PBU',event.data.value === '4092','机房布线建设及相关配件PBU',event.data.value === '1125','机房布线建设及相关配件PBU',event.data.value === '897','机房布线建设及相关配件PBU',event.data.value === '3254','机房布线建设及相关配件PBU',event.data.value === '2867','机房布线建设及相关配件PBU',event.data.value === '1324','机房布线建设及相关配件PBU',event.data.value === '901','机房布线建设及相关配件PBU',event.data.value === '2866','机房布线建设及相关配件PBU',event.data.value === '1082','机房布线建设及相关配件PBU',event.data.value === '1194','机房布线建设及相关配件PBU',event.data.value === '220','机房布线建设及相关配件PBU',event.data.value === '1148','机房布线建设及相关配件PBU',event.data.value === '4256','机房布线建设及相关配件PBU',event.data.value === '3856','机房布线建设及相关配件PBU',event.data.value === '1155','机房布线建设及相关配件PBU',event.data.value === '384','机房布线建设及相关配件PBU',event.data.value === '996','机房布线建设及相关配件PBU',event.data.value === '3253','机房布线建设及相关配件PBU',event.data.value === '2981','机房布线建设及相关配件PBU',event.data.value === '3074','机房布线建设及相关配件PBU',event.data.value === '1081','机房布线建设及相关配件PBU',event.data.value === '1083','机房布线建设及相关配件PBU',event.data.value === '2958','机房布线建设及相关配件PBU',event.data.value === '1000','机房布线建设及相关配件PBU',event.data.value === '1023','机房布线建设及相关配件PBU',event.data.value === '2686','机房布线建设及相关配件PBU',event.data.value === '1105','机房布线建设及相关配件PBU',event.data.value === '2978','机房布线建设及相关配件PBU',event.data.value === '1003','机房布线建设及相关配件PBU',event.data.value === '2975','机房布线建设及相关配件PBU',event.data.value === '4095','机房布线建设及相关配件PBU',event.data.value === '5031','机房布线建设及相关配件PBU',event.data.value === '5033','机房布线建设及相关配件PBU',event.data.value === '5035','机房布线建设及相关配件PBU',event.data.value === '5037','机房布线建设及相关配件PBU',event.data.value === '2906','机房布线建设及相关配件PBU',event.data.value === '3080','机房布线建设及相关配件PBU',event.data.value === '939','机房布线建设及相关配件PBU',event.data.value === '609','机房布线建设及相关配件PBU',event.data.value === '914','机房布线建设及相关配件PBU',event.data.value === '576','机房布线建设及相关配件PBU',event.data.value === '3125','机房布线建设及相关配件PBU',event.data.value === '1342','机房布线建设及相关配件PBU',event.data.value === '1132','机房布线建设及相关配件PBU',event.data.value === '3087','机房布线建设及相关配件PBU',event.data.value === '3191','机房布线建设及相关配件PBU',event.data.value === '634','机房布线建设及相关配件PBU',event.data.value === '1415','机房布线建设及相关配件PBU',event.data.value === '3216','机房布线建设及相关配件PBU',event.data.value === '974','机房布线建设及相关配件PBU',event.data.value === '4061','机房布线建设及相关配件PBU',event.data.value === '1326','机房布线建设及相关配件PBU',event.data.value === '3081','机房布线建设及相关配件PBU',event.data.value === '1049','机房布线建设及相关配件PBU',event.data.value === '1050','机房布线建设及相关配件PBU',event.data.value === '3507','机房布线建设及相关配件PBU',event.data.value === '1048','机房布线建设及相关配件PBU',event.data.value === '4111','机房布线建设及相关配件PBU',event.data.value === '1051','机房布线建设及相关配件PBU',event.data.value === '1146','机房布线建设及相关配件PBU',event.data.value === '1057','机房布线建设及相关配件PBU',event.data.value === '1052','机房布线建设及相关配件PBU',event.data.value === '1196','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '1131','机房布线建设及相关配件PBU',event.data.value === '3362','机房布线建设及相关配件PBU',event.data.value === '1061','机房布线建设及相关配件PBU',event.data.value === '1053','机房布线建设及相关配件PBU',event.data.value === '4221','机房布线建设及相关配件PBU',event.data.value === '4112','机房布线建设及相关配件PBU',event.data.value === '4220','机房布线建设及相关配件PBU',event.data.value === '1190','机房布线建设及相关配件PBU',event.data.value === '975','机房布线建设及相关配件PBU',event.data.value === '2','机房布线建设及相关配件PBU',event.data.value === '3075','机房布线建设及相关配件PBU',event.data.value === '45','机房布线建设及相关配件PBU',event.data.value === '3358','机房布线建设及相关配件PBU',event.data.value === '3359','机房布线建设及相关配件PBU',event.data.value === '3360','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '1098','机房布线建设及相关配件PBU',event.data.value === '1067','机房布线建设及相关配件PBU',event.data.value === '5167','机房布线建设及相关配件PBU',event.data.value === '1070','机房布线建设及相关配件PBU',event.data.value === '3367','机房布线建设及相关配件PBU',event.data.value === '3054','机房布线建设及相关配件PBU',event.data.value === '4177','机房布线建设及相关配件PBU',event.data.value === '4351','机房布线建设及相关配件PBU',event.data.value === '3093','机房布线建设及相关配件PBU',event.data.value === '900','机房布线建设及相关配件PBU',event.data.value === '2967','机房布线建设及相关配件PBU',event.data.value === '2907','机房布线建设及相关配件PBU',event.data.value === '3059','机房布线建设及相关配件PBU',event.data.value === '3319','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '3084','机房布线建设及相关配件PBU',event.data.value === '3397','机房布线建设及相关配件PBU',event.data.value === '3398','机房布线建设及相关配件PBU',event.data.value === '3085','机房布线建设及相关配件PBU',event.data.value === '3092','机房布线建设及相关配件PBU',event.data.value === '2963','机房布线建设及相关配件PBU',event.data.value === '3076','机房布线建设及相关配件PBU',event.data.value === '3082','机房布线建设及相关配件PBU',event.data.value === '3535','机房布线建设及相关配件PBU',event.data.value === '3128','机房布线建设及相关配件PBU',event.data.value === '1145','机房布线建设及相关配件PBU',event.data.value === '2687','机房布线建设及相关配件PBU',event.data.value === '4353','机房布线建设及相关配件PBU',event.data.value === '4355','机房布线建设及相关配件PBU',event.data.value === '4357','机房布线建设及相关配件PBU',event.data.value === '4182','机房布线建设及相关配件PBU',event.data.value === '1017','机房布线建设及相关配件PBU',event.data.value === '593','机房布线建设及相关配件PBU',event.data.value === '594','机房布线建设及相关配件PBU',event.data.value === '3049','机房布线建设及相关配件PBU',event.data.value === '980','机房布线建设及相关配件PBU',event.data.value === '3371','机房布线建设及相关配件PBU',event.data.value === '4136','机房布线建设及相关配件PBU',event.data.value === '5137','机房布线建设及相关配件PBU',event.data.value === '3312','机房布线建设及相关配件PBU',event.data.value === '3261','机房布线建设及相关配件PBU',event.data.value === '3262','机房布线建设及相关配件PBU',event.data.value === '3263','机房布线建设及相关配件PBU',event.data.value === '3311','机房布线建设及相关配件PBU',event.data.value === '4283','机房布线建设及相关配件PBU',event.data.value === '4284','机房布线建设及相关配件PBU',event.data.value === '1134','机房布线建设及相关配件PBU',event.data.value === '1133','机房布线建设及相关配件PBU',event.data.value === '3073','机房布线建设及相关配件PBU',event.data.value === '633','机房布线建设及相关配件PBU',event.data.value === '959','机房布线建设及相关配件PBU',event.data.value === '4371','机房布线建设及相关配件PBU',event.data.value === '4373','机房布线建设及相关配件PBU',event.data.value === '4044','机房布线建设及相关配件PBU',event.data.value === '3313','机房布线建设及相关配件PBU',event.data.value === '4290','机房布线建设及相关配件PBU',event.data.value === '590','机房布线建设及相关配件PBU',event.data.value === '962','机房布线建设及相关配件PBU',event.data.value === '4013','机房布线建设及相关配件PBU',event.data.value === '1126','机房布线建设及相关配件PBU',event.data.value === '4298','机房布线建设及相关配件PBU',event.data.value === '1064','机房布线建设及相关配件PBU',event.data.value === '621','机房布线建设及相关配件PBU',event.data.value === '3485','机房布线建设及相关配件PBU',event.data.value === '4107','机房布线建设及相关配件PBU',event.data.value === '4108','机房布线建设及相关配件PBU',event.data.value === '4109','机房布线建设及相关配件PBU',event.data.value === '4110','机房布线建设及相关配件PBU',event.data.value === '613','机房布线建设及相关配件PBU',event.data.value === '4137','机房布线建设及相关配件PBU',event.data.value === '964','机房布线建设及相关配件PBU',event.data.value === '3347','机房布线建设及相关配件PBU',event.data.value === '24','机房布线建设及相关配件PBU',event.data.value === '2951','机房布线建设及相关配件PBU',event.data.value === '1073','机房布线建设及相关配件PBU',event.data.value === '3830','机房布线建设及相关配件PBU',event.data.value === '1321','机房布线建设及相关配件PBU',event.data.value === '22','机房布线建设及相关配件PBU',event.data.value === '35','机房布线建设及相关配件PBU',event.data.value === '33','机房布线建设及相关配件PBU',event.data.value === '36','机房布线建设及相关配件PBU',event.data.value === '38','机房布线建设及相关配件PBU',event.data.value === '1099','机房布线建设及相关配件PBU',event.data.value === '34','机房布线建设及相关配件PBU',event.data.value === '1318','机房布线建设及相关配件PBU',event.data.value === '55','机房布线建设及相关配件PBU',event.data.value === '19','机房布线建设及相关配件PBU',event.data.value === '21','机房布线建设及相关配件PBU',event.data.value === '51','机房布线建设及相关配件PBU',event.data.value === '48','机房布线建设及相关配件PBU',event.data.value === '52','机房布线建设及相关配件PBU',event.data.value === '4059','机房布线建设及相关配件PBU',event.data.value === '4060','机房布线建设及相关配件PBU',event.data.value === '3615','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '50','机房布线建设及相关配件PBU',event.data.value === '53','机房布线建设及相关配件PBU',event.data.value === '1063','机房布线建设及相关配件PBU',event.data.value === '40','机房布线建设及相关配件PBU',event.data.value === '','机房布线建设及相关配件PBU',event.data.value === '47','机房布线建设及相关配件PBU',event.data.value === '1319','机房布线建设及相关配件PBU',event.data.value === '598','机房布线建设及相关配件PBU')}"}}]}}, "onlyChildren": false, "searchable": false, "onlyLeaf": true, "source": {"url": "${envUrl}/api/workbench/getProductType", "method": "get", "messages": {}, "headers": {"token": "${envToken}"}, "adaptor": "if (payload.code !== 200) return []\n\nreturn payload.data\n", "requestAdaptor": "", "cache": 30000}}, {"type": "select", "label": "*产品线", "name": "mdf_type", "id": "u:6e0c15b671ef", "placeholder": "-", "options": [{"label": "光模块PBU", "value": "光模块PBU"}, {"label": "光网络PBU", "value": "光网络PBU"}, {"label": "园区网PBU", "value": "园区网PBU"}, {"label": "数据中心PBU", "value": "数据中心PBU"}, {"label": "无线安防PBU", "value": "无线安防PBU"}, {"label": "机房布线建设及相关配件PBU", "value": "机房布线建设及相关配件PBU"}], "multiple": false, "width": 150}, {"label": "*PDT", "name": "ltc_pdt", "id": "u:d932d276430f", "type": "select", "options": [{"label": "AI交换机PDT", "value": "AI交换机PDT"}, {"label": "AmpCon-CampusPDT", "value": "AmpCon-CampusPDT"}, {"label": "DCI_PDT", "value": "DCI_PDT"}, {"label": "FSBOX_PDT", "value": "FSBOX_PDT"}, {"label": "IDC交换机PDT", "value": "IDC交换机PDT"}, {"label": "OLS_PDT", "value": "OLS_PDT"}, {"label": "PON_PDT", "value": "PON_PDT"}, {"label": "企业无线PDT", "value": "企业无线PDT"}, {"label": "光纤布线PDT", "value": "光纤布线PDT"}, {"label": "光网络", "value": "光网络"}, {"label": "安防监控PDT", "value": "安防监控PDT"}, {"label": "工业交换机 PDT", "value": "工业交换机 PDT"}, {"label": "常规速率光模块PDT", "value": "常规速率光模块PDT"}, {"label": "接入交换机PDT", "value": "接入交换机PDT"}, {"label": "服务器PDT", "value": "服务器PDT"}, {"label": "核心交换机PDT", "value": "核心交换机PDT"}, {"label": "电信光模块PDT", "value": "电信光模块PDT"}, {"label": "设备电源及配件PDT", "value": "设备电源及配件PDT"}, {"label": "铜缆布线PDT", "value": "铜缆布线PDT"}, {"label": "高速率光模块PDT", "value": "高速率光模块PDT"}], "multiple": false, "required": true, "onEvent": {"change": {"weight": 0, "actions": []}}, "placeholder": "-", "width": 160}, {"label": "*需求点", "name": "demand_type", "id": "u:f6e04c92e407", "type": "nested-select", "placeholder": "-", "options": [{"label": "功能参数咨询", "value": "type1", "children": [{"label": "硬件参数咨询", "value": "type2"}, {"label": "软件功能咨询", "value": "type3"}]}, {"label": "硬件定制", "value": "type4"}, {"label": "软件功能定制", "value": "type5"}, {"label": "管理平台定制开发", "value": "type6"}, {"label": "软件兼容/适配", "value": "type7"}, {"label": "外观结构定制", "value": "type8"}, {"label": "包装标签定制", "value": "type9"}, {"label": "数字化采购系统", "value": "type10"}, {"label": "产品测试", "value": "type11", "children": [{"label": "软件/平台测试", "value": "type49"}, {"label": "连通性测试", "value": "type13"}, {"label": "性能测试", "value": "type14"}, {"label": "样品测试", "value": "type15"}, {"label": "兼容性测试", "value": "type12"}]}, {"label": "市场主流竞品替代", "value": "type16"}, {"label": "技术视频会议交流", "value": "type17"}, {"label": "解决方案设计", "value": "type18"}, {"label": "方案验证与测试", "value": "type19"}, {"label": "解决方案成功案例", "value": "type20"}, {"label": "Demo体验", "value": "type21"}, {"label": "认证支持", "value": "type22", "children": [{"label": "国家准入认证", "value": "type23"}, {"label": "产品品质认证", "value": "type24"}]}, {"label": "价格", "value": "type25", "children": [{"label": "产品价格", "value": "type26"}, {"label": "软件价格", "value": "type27"}, {"label": "本地服务价格", "value": "type28", "hiddenOn": "true"}]}, {"label": "交期", "value": "type29"}, {"label": "起订量", "value": "type30"}, {"label": "EOL", "value": "type31"}, {"label": "物流服务", "value": "type32", "children": [{"label": "灵活的发货组合", "value": "type34"}, {"label": "定制化制程咨询", "value": "type35"}, {"label": "多样化的物流服务", "value": "type36"}]}, {"label": "全球库存/调仓计划", "value": "type33"}, {"label": "灵活的支付组合", "value": "type37"}, {"label": "服务交付", "value": "type38", "children": [{"label": "FS Care-安装服务", "value": "type39"}, {"label": "配置服务", "value": "type40", "hiddenOn": "true"}, {"label": "客户培训", "value": "type41"}, {"label": "远程配置", "value": "type42"}]}, {"label": "售后服务咨询", "value": "type43", "children": [{"label": "延保服务", "value": "type44"}, {"label": "SLA升级", "value": "type45"}, {"label": "特殊退维修服务", "value": "type46"}, {"label": "备件服务", "value": "type47"}, {"label": "售后远程指导", "value": "type48"}]}], "multiple": true, "required": true, "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "u:7ef4f572de27", "args": {"value": "${IFS(event.data.value === 'type4','type1',event.data.value === 'type5','type1',event.data.value === 'type6','type1',event.data.value === 'type7','type1',event.data.value === 'type18','type2',event.data.value === 'type19','type2')}"}, "actionType": "setValue", "ignoreError": false}]}}, "onlyChildren": true, "searchable": false, "onlyLeaf": true, "joinValues": true, "hidden": false, "delimiter": ","}, {"label": "实验室设备数量", "name": "laboratory", "id": "u:4a9d5e58939e", "type": "input-text", "static": false, "hiddenOn": "${!(CONTAINS(type,\"type14\") || CONTAINS(type,\"type15\"))}", "placeholder": "-", "width": 160}, {"type": "input-text", "id": "u:471b2e0a5136", "label": "客户设备", "name": "client_device", "static": false, "hiddenOn": "${!CONTAINS(type,\"type14\") }", "placeholder": "-", "width": 160}, {"type": "input-text", "label": "检测功能", "name": "detection", "id": "u:673d12f3ed1d", "placeholder": "-", "width": 160, "static": false, "hiddenOn": "${!(CONTAINS(type,\"type14\") || CONTAINS(type,\"type15\"))}"}, {"type": "input-image", "label": "需求产品参考图片", "name": "prodimage", "autoUpload": true, "proxy": false, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:84bd285affb4", "placeholder": "-", "accept": ".jpeg, .jpg, .png, .gif", "multiple": false, "hideUploadButton": false, "fixedSize": false, "receiver": {"url": "${envS3Url}", "method": "post", "messages": {}, "dataType": "form-data", "data": {"isOpen": false, "expire": 0}, "adaptor": "const file = api.data.get('file')\nreturn { code: 200, value: payload }"}}], "addable": true, "footerAddBtn": {"label": "新增", "icon": "fa fa-plus", "id": "u:9783bb3f86cf"}, "strictMode": false, "minLength": 0, "editable": true, "removable": true, "columnsTogglable": false, "needConfirm": false, "value": "${envDefaultFormData.prodtable}", "className": "m-t", "copyable": false, "maxLength": "", "showTableAddBtn": true, "canAccessSuperData": true, "requiredOn": "${CONTAINS('type2',resource) && CONTAINS('type5,type6,type7',demand_type)}", "onEvent": {"rowDbClick": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "dialog", "dialog": {"$ref": "modal-ref-1"}, "expression": "${false}"}]}}}], "id": "u:0af4cc593b5e", "style": {"boxShadow": " 0px 0px 0px 0px transparent", "marginTop": "20px"}}], "id": "u:2aad07c034ae", "style": {}, "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}, {"type": "grid", "columns": [{"id": "u:467e28ca6bf3"}], "id": "u:c20b9115d12d", "className": "m-t-sm"}, {"type": "grid", "columns": [{"body": [], "id": "u:dac2e5db5ecc"}, {"body": [], "id": "u:14aa8dd06322"}], "id": "u:93128ac8075d"}], "id": "u:d8f07bd8dfe5", "title": "", "mode": "normal", "api": {"url": "${envUrl}/api/bpmInstance/createInstance", "method": "post", "messages": {}, "dataType": "json", "headers": {"token": "${envToken}", "envData": "${envData}"}, "data": {"&": "$$", "userId": "${envUserInfo.uuid}", "userName": "${envUserInfo.name}", "processConfigId": "${envData.processConfigId}", "processDefineKey": "${envData.processDefineKey}", "draftId": "${envData.draftId}", "oldFile": "${envDefaultFormData.file}"}, "requestAdaptor": "const { data } = api\r\nconst { \r\n    processConfigId, \r\n    processDefineKey, \r\n    userId,\r\n    draftId,\r\n    userName,\r\n    file,\r\n    oldFile,\r\n    ...formData\r\n} = data\r\n\r\nconst case_number = formData.case_number\r\n\r\nlet currFile = []\r\nif (oldFile && typeof oldFile === 'string') {\r\n    currFile = JSON.parse(`[]`)\r\n} else {\r\n    currFile = oldFile\r\n}\r\nif (file && oldFile) {\r\n    currFile.push(...JSON.parse(`[]`))\r\n} else if (file && !oldFile) {\r\n    currFile = JSON.parse(`[]`)\r\n}\r\n\r\nformData.file = currFile\r\n\r\napi.data = {\r\n    type: 1,\r\n    userId,\r\n    userName,\r\n    topicName: formData.title,\r\n    processConfigId,\r\n    processDefineKey,\r\n    formData: formData,\r\n    contentData: {},\r\n    attachmentData: [],\r\n}\r\n\r\nformData.erp_qnumber_arr && formData.erp_qnumber_arr.length && (formData.erp_qnumber_arr_init = formData.erp_qnumber_arr)  \r\nformData.erp_fsnumber_arr && formData.erp_fsnumber_arr.length && (formData.erp_fsnumber_arr_init = formData.erp_fsnumber_arr)  \r\nformData.relevanceInstanceCodeList && (api.data.relevanceInstanceCodeList = formData.relevanceInstanceCodeList)\r\ndraftId && (api.data.draftId = draftId)\r\ncase_number && (api.data.caseNumber = case_number)\r\n\r\nreturn api", "adaptor": "if (payload.code !== 200) throw new Error(payload.msg)\r\n\r\nconst { headers } = api\r\nconst { envData = {} } = headers\r\n\r\nconst { \r\n    handleSava = () => {},\r\n    handleSubmit = () => {},\r\n    handleDiscard = () => {}\r\n} = envData\r\n\r\nsetTimeout(handleSubmit)\r\n\r\nreturn {}"}, "messages": {"fetchFailed": "初始化失败", "saveSuccess": "保存成功", "saveFailed": "保存失败", "fetchSuccess": "提交成功"}, "static": false, "submitText": "提交", "actions": [{"type": "button-group", "buttons": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"componentId": "u:d8f07bd8dfe5", "args": {}, "actionType": "submit"}]}}, "id": "u:fb894099bed7", "confirmText": "是否确认提交", "level": "primary"}], "id": "u:e5b3a5464887"}, {"type": "button", "label": "保存草稿", "onEvent": {"click": {"actions": [{"args": {"options": {}, "api": {"method": "post", "url": "${envUrl}/api/instanceDraft/save", "messages": {"success": "保存成功", "failed": "保存失败"}, "data": {"oldFile": "${envDefaultFormData.file}", "&": "$$", "userId": "${envUserInfo.uuid}", "userName": "${envUserInfo.name}", "processConfigId": "${envData.processConfigId}", "processDefineKey": "${envData.processDefineKey}", "draftId": "${envData.draftId}"}, "requestAdaptor": "const { data } = api\nconst { \n    processConfigId, \n    processDefineKey, \n    userId,\n    draftId,\n    userName, \n    file,\n    oldFile,\n    ...formData\n} = data\n\nlet currFile = []\nif (oldFile && typeof oldFile === 'string') {\n    currFile = JSON.parse(`[${oldFile}]`)\n} else {\n    currFile = oldFile\n}\nif (file && oldFile) {\n    currFile.push(...JSON.parse(`[${file}]`))\n} else if (file && !oldFile) {\n    currFile = JSON.parse(`[${file}]`)\n}\n\nformData.file = currFile\n\napi.data = {\n    draftId,\n    userId,\n    userName,\n    topicName: formData.title,\n    processConfigId,\n    processDefineKey,\n    formData: formData,\n}\n\nreturn api", "headers": {"token": "${envToken}", "envData": "${envData}"}, "dataType": "json", "adaptor": "if (payload.code !== 200) throw new Error(payload.msg)\n\nconst { headers } = api\nconst { envData = {} } = headers\n\nsetTimeout(envData.handleSubmit)\n\nreturn {}"}}, "outputVar": "responseResult", "actionType": "ajax"}]}}, "id": "u:d4f7596d477c", "level": "success", "className": "m-r", "confirmText": "是否确定保存草稿？"}, {"type": "button", "label": "废弃草稿", "onEvent": {"click": {"actions": [{"args": {"options": {}, "api": {"url": "${envUrl}//api/instanceDraft/delete/${envData.draftId}", "method": "get", "messages": {"success": "删除成功", "failed": "删除失败"}, "headers": {"token": "${envToken}", "envData": "${envData}"}, "adaptor": "if (payload.code !== 200) throw new Error(payload.msg)\n\nconst { headers } = api\nconst { envData = {} } = headers\n\nsetTimeout(envData.handleSubmit)\n\nreturn {}"}}, "outputVar": "responseResult", "actionType": "ajax"}]}}, "id": "u:b707384ed1e6", "level": "danger", "confirmText": "是否确认废弃？", "className": "m-r", "hidden": false, "disabledOn": "${!envData.draftId}"}], "visibleOn": "envType === 'edit'", "debug": true, "name": "nodeForm", "initApi": {"url": "", "method": "get", "messages": {"fetchFailed": "初始化失败", "saveSuccess": "保存成功", "saveFailed": "保存失败", "fetchSuccess": "提交成功"}, "requestAdaptor": "", "adaptor": "const res = payload.data || {}\nconst data = {}\ndata['email'] = res.customer_email\ndata['Qnumber'] = res.Qnumber\ndata['erp_fsnumber_arr'] = res.erp_fsnumber_arr\nreturn data", "headers": {"token": "${token}"}}, "feat": "Edit", "dsType": "api"}
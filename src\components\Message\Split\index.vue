<template>
  <div class="split-wrapper" ref="outerWrapper">
    <div class="prefix-vertical">
      <div :style="{ bottom: `${anotherOffset}%` }" class="top-pane">
        <slot name="top" />
      </div>
      <div class="prefix-trigger-con" :style="{ top: `${offset}%` }" @mousedown="handleMousedown">
        <slot name="trigger">
          <div class="trigger-split-line">
            <i class="iconfont icontubiao_tuodong" />
          </div>
        </slot>
      </div>
      <div :style="{ top: `${offset}%` }" class="bottom-pane">
        <slot name="bottom" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0.5,
  },
  max: {
    type: Number,
    default: 85,
  },
  min: {
    type: Number,
    default: 50,
  },
})

const emits = defineEmits(['update:modelValue', 'on-move-start', 'on-moving', 'on-move-end'])

const offset = ref<number>(0)
const oldOffset = ref<number>(0)
const initOffset = ref<number>(0)
const isMoving = ref<boolean>(false)
const outerWrapper = ref<HTMLElement>()

const anotherOffset = computed(() => {
  return (100 - offset.value) as number
})

const handleMove = (e: any) => {
  let pageOffset = e.pageY
  let offset = pageOffset - initOffset.value
  let outerHeight = outerWrapper?.value?.offsetHeight || 0
  let value = oldOffset.value + (offset / outerHeight) * 100
  if (value < props.min) return
  if (value > props.max) return
  emits('update:modelValue', value)
  emits('on-moving', e)
}

const handleUp = () => {
  isMoving.value = false
  document.removeEventListener('mousemove', handleMove)
  document.removeEventListener('mouseup', handleUp)
  emits('on-move-end')
}

const handleMousedown = (e: any) => {
  initOffset.value = e.pageY
  oldOffset.value = props.modelValue
  isMoving.value = true
  document.addEventListener('mousemove', handleMove)
  document.addEventListener('mouseup', handleUp)
  emits('on-move-start')
}

const computeOffset = () => {
  nextTick(() => {
    offset.value = props.modelValue
  })
}

watch(
  () => props.modelValue,
  val => {
    computeOffset()
  },
  { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
.prefix-vertical {
  position: relative;
  height: 100%;
  .top-pane,
  .prefix-trigger-con,
  .bottom-pane {
    position: absolute;
  }
  .prefix-trigger-con {
    width: 100%;
    position: absolute;
    z-index: 999;
    .trigger-split-line {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      .iconfont {
        cursor: pointer;
        color: #999;
      }
    }
  }
  .top-pane {
    width: 100%;
    bottom: 27.7425%;
    top: 0;
  }
  .bottom-pane {
    width: 100%;
    top: 72.2575%;
    bottom: 0;
  }
}
</style>

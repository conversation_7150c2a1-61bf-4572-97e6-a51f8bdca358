import { request } from '@/utils'
import type {
  IRes,
  IDictionatyData,
  IDictionatyDataList,
  ISearchDictionatyData,
  IDictionaryConfigId,
  IDictionaryConfigIdData,
  SearchDictionary,
} from '@/types/dictionary'

interface IPageParmas {
  pageNum: number // 当前页
  pageSize: number // 每页条数
}
interface IQueryParams extends IPageParmas {
  name: null | string
}
export function createDictionary(data: IDictionatyData): Promise<IRes<IDictionatyData>> {
  return request.post('/api/dictionary/save', data)
}
export function updateDictionary(data: IDictionatyData): Promise<IRes<IDictionatyData>> {
  return request.post('/api/dictionary/update', data)
}
export function getDictionaryList(data: IQueryParams): Promise<IRes<IDictionatyDataList<IDictionatyData>>> {
  return request.post('/api/dictionary/getList', data)
}

export function deleteDictionary(data: any): Promise<IRes<IDictionatyData>> {
  return request.get('/api/dictionary/del', { params: data })
}

export function banDictionary(data: any): Promise<IRes<IDictionatyData>> {
  return request.get('/api/dictionary/ban', {
    params: data,
  })
}

export async function getRootDictionary() {
  const res = await request.get<IRes<IDictionatyData[]>>('/api/dictionary/getRoot')
  return res as unknown as IRes<IDictionatyData[]>
}

export async function getSearchDictionaryList() {
  const res = await request.get<IRes<ISearchDictionatyData[]>>('/api/searchDictionary/getAll')
  return res as unknown as IRes<ISearchDictionatyData[]>
}

export async function getSearchDictionaryFieldList(data: IDictionaryConfigId) {
  const res = await request.post<IRes<IDictionaryConfigIdData<ISearchDictionatyData>>>(
    '/api/searchDictionary/getDictionaryByConfigId',
    data
  )
  return res as unknown as IRes<IDictionaryConfigIdData<ISearchDictionatyData>>
}

export async function addSearchDictionary(data: SearchDictionary) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/add', data)
  return res as unknown as IRes<any>
}

export async function updateSearchDictionary(data: SearchDictionary) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/update', data)
  return res as unknown as IRes<any>
}

export function deleteSearchDictionary(id: any): Promise<IRes<any>> {
  return request.get(`/api/searchDictionary/delete/${id}`)
}

export function selectFieldDictionary(data: any): Promise<IRes<any>> {
  return request.get('/api/searchDictionary/dictionary/selectField', { params: data })
}

export function selectDictionaryPage(data: any): Promise<IRes<any>> {
  return request.post('/api/searchDictionary/dictionary/page', data)
}

export function selectFieldDictionaryItem(field: any): Promise<IRes<any>> {
  return request.get(`/api/searchDictionary/dictionary/getByField/${field}`)
}

export async function queryFieldDictionary(data: any) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/dictionary/query', data)
  return res as unknown as IRes<any>
}

export async function addOneFieldDictionary(data: any) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/dictionary/addOne', data)
  return res as unknown as IRes<any>
}

export async function updateOneFieldDictionary(data: any) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/dictionary/updateOne', data)
  return res as unknown as IRes<any>
}

export async function addBatchFieldDictionary(data: any) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/dictionary/create', data)
  return res as unknown as IRes<any>
}

export async function updateBatchFieldDictionary(data: any) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/dictionary/update', data)
  return res as unknown as IRes<any>
}

export async function getFiledDictionaryList(data: any) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/dictionary/getDictionaryList', data)
  return res as unknown as IRes<any>
}

export function deleteFieldDictionaryItem(id: any): Promise<IRes<any>> {
  return request.get(`/api/searchDictionary/dictionary/delete/${id}`)
}

export function deleteFieldAllDictionaryItem(field: any): Promise<IRes<any>> {
  return request.get(`/api/searchDictionary/dictionary/deleteByField/${field}`)
}

export async function sortFiledDictionaryList(data: any) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/dictionary/sort', data)
  return res as unknown as IRes<any>
}

export const rollbackDictionary = async (data: any) => {
  const res = await request.post(`/api/processSyn/rollbackDictionary`, data)
  return res as unknown as IRes<any>
}

export const synDictionary = async (data: any) => {
  const res = await request.post(`/api/processSyn/synDictionary`, data)
  return res as unknown as IRes<any>
}

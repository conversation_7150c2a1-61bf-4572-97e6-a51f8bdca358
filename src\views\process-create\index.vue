<template>
  <FSpin style="width: 100%; margin-top: 300px" :spinning="loading" :tip="i18n.t('加载中...')" />
  <FormRender v-if="fromId" :id="fromId" type="edit" :data="{ envData: data, envDefaultFormData: formData }" />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import FormRender from '@/components/FormRender/index.vue'
import { getFormByProcessDefineKey, getLtcByNumber } from '@/api'
import { useI18n, BAESURL } from '@/utils'
import { message } from '@fs/smart-design'

const i18n = useI18n()

interface IQuery {
  processConfigId?: string
  processDefineKey?: string
  opportunityId?: string
}

const route = useRoute()
let { processConfigId, processDefineKey, opportunityId } = route.query as IQuery
const fromId = ref<string>()
const loading = ref(false)

const formData = ref<Record<string, unknown>>({ opportunityId })
const data = reactive({
  processConfigId,
  processDefineKey,
  handleSubmit: () => {
    window.open(`${BAESURL}/bpm-manage/process/list`)
  },
})

onMounted(() => {
  init()
})

const init = async () => {
  try {
    if (!opportunityId) {
      message.error(i18n.t('未携带参数，请检查链接是否正确！'))
      throw new Error(i18n.t('未携带参数，请检查链接是否正确！'))
    }
    loading.value = true
    await getFormId()
  } finally {
    loading.value = false
  }
}

const getFormId = async () => {
  const processRes = await getLtcByNumber(opportunityId)
  processRes?.data?.processConfigId && (data.processConfigId = processRes?.data?.processConfigId)
  processRes?.data?.processDefineKey && (data.processDefineKey = processRes?.data?.processDefineKey)
  formData.value['custSapData'] = processRes?.data ?? {}
  if (!data.processConfigId || !data.processDefineKey) {
    message.error(i18n.t('流程ID不存在，请检查链接是否正确！'))
    throw new Error(i18n.t('流程ID不存在，请检查链接是否正确！'))
  }
  const res = await getFormByProcessDefineKey(data.processDefineKey)
  res.code && (fromId.value = res.data?.id)
}
</script>

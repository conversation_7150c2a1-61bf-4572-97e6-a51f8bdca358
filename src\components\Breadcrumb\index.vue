<template>
  <div v-if="breadcrumbList?.length" class="breadcrumb-box mb24">
    <FBreadcrumb>
      <FBreadcrumbItem
        v-for="(item, index) of breadcrumbList"
        :key="index"
        @click="() => index !== breadcrumbList?.length - 1 && push({ path: item?.path ?? '' })"
        >{{ item?.title ?? '' }}</FBreadcrumbItem
      >
    </FBreadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'

interface IProps {
  list?: any
}

const props = defineProps<IProps>()
const { currentRoute } = useRouter()
const breadcrumbList = computed<any>(() => props?.list ?? currentRoute?.value?.meta?.breadcrumbs ?? [])

const { push } = useRouter()
</script>

<style scoped lang="scss">
.breadcrumb-box {
  padding: 12px 24px;
  background: #fbfdff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
}
</style>

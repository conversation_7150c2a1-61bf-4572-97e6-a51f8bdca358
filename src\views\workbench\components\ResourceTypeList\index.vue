<template>
  <div class="resource-list">
    <div
      :class="['resource-list-item', active === item.id ? 'active' : '']"
      v-for="item in props.data"
      :key="item.id"
      @click="() => handleClick(item)"
    >
      <p class="_content">
        <span class="flex">
          <span
            :class="[active === item.id ? '' : 'icon-color']"
            class="icon iconfont icon-margin"
            v-html="item.icon"
          ></span>
          {{ item.name }}
        </span>
        <span class="_tips" :class="item.class">{{ item.size }}</span>
      </p>
    </div>
    <FEmpty v-show="!props.data.length" :image="require(`@/assets/images/empty.png`)" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

export interface IResourceTypeItem {
  id: number | string
  name: string
  size: number
  [key: string]: unknown
}

interface IProps {
  data: IResourceTypeItem[]
}

const props = defineProps<IProps>()
const emits = defineEmits(['click'])
const active = ref(props.data?.[0]?.id)

watch(
  () => props.data,
  () => {
    active.value = props.data?.[0]?.id
  }
)

const handleClick = (data: IResourceTypeItem) => {
  active.value = data.id
  emits('click', data)
}
</script>

<style lang="scss" scoped>
.resource-list {
  width: 232px;
  max-height: 714px;
  padding: 16px 16px 16px 12px;
  border-right: 1px solid #eee;
  box-sizing: border-box;
  overflow-y: auto;

  > .resource-list-item {
    // height: 40px;
    // line-height: 40px;
    // padding-left: 10px;
    padding: 8px 0 8px 10px;
    margin-bottom: 2px;
    border-radius: 4px;
    border-left: 3px solid transparent;
    cursor: pointer;

    ._content {
      display: flex;
      justify-content: space-between;
      margin: 0;
      color: #333;
      font-size: 14px;
      font-weight: 500;
      .icon-margin {
        margin-right: 8px;
        font-size: 18px;
      }
      .icon-color {
        color: transparent;
        background: linear-gradient(to bottom, #a0b1c5, #58626e);
        -webkit-background-clip: text;
        // color: transparent;
      }
      .flex {
        display: flex;
        align-items: self-start;
        max-width: 82%;
        line-height: 18px;
      }
    }

    ._tips {
      float: right;
      // margin-top: 11px;
      width: 22px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      border-radius: 4px;
      color: #ff4a4a;
      font-size: 12px;
      background: #fff1f1;
    }
    .galy {
      color: #bbb;
      background: #fff;
      font-weight: 500;
      font-size: 12px;
    }

    &:hover {
      background-color: #f9fbfd;
    }

    &.active {
      border-left: 3px solid #378eef;
      background-color: #f9fbfd;

      ._content,
      ._tips {
        color: #378eef;
      }

      ._tips {
        background-color: #f9fbfd;
      }
    }
  }
}
</style>

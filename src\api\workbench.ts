import { getToken, request } from '@/utils'
import { IProcessType, IRes, IResource, IResourceLabel, IResourceType } from '@/types/request'

// 获取全部类型的流程实例
export const getAllTypeProcessInstance = async () => {
  const res = await request.get<IRes<IProcessType[]>>('/api/workbench/getInstanceList')
  return res as unknown as IRes<IProcessType[]>
}

// 获取资源库资源类型列表
export const getResourceTypeList = async () => {
  const res = await request.get<IRes<{ libraries: IResourceType[] }>>('/api/workbench/libraries')
  return res as unknown as IRes<{ libraries: IResourceType[] }>
}

export interface IResourceParams {
  key?: string | null
  category?: string | null
  label?: string | null
  libraries?: string | null
  status?: number | null
  pageNum: number
  pageSize: number
}

// 资源列表查询
export const getResourceList = async (params: IResourceParams) => {
  const res = await request.post<IRes<{ paginate: unknown[]; resources: IResource[] }>>('/api/workbench/search', {
    status: 1,
    ...params,
  })
  return res as unknown as IRes<{ paginate: unknown[]; resources: IResource[] }>
}

// 资源标签
export const getResourceLabel = async () => {
  const res = await request.get<IRes<{ labels: IResourceLabel[] }>>('/api/workbench/trees?key=')
  return res as unknown as IRes<{ labels: IResourceLabel[] }>
}

// 资源文件下载
export const downloadResource = async (id: string) => {
  const token = getToken()
  const res = await request.get<unknown>(`/api/workbench/downloadFile/${id}?token=${token}`, { responseType: 'blob' })
  return res as unknown
}

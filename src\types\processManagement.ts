import { BasicPageParams, IRes } from '@/types/request'

export interface PageDemandParams {
  accept?: string
  adopt?: string
  countriesName?: string
  creatorUuid?: string
  currPage?: number
  customersIndustry?: string
  customersLevel?: string
  cycleRating?: string
  demand?: string
  endTime?: string
  instanceIdList?: any[]
  instanceStatus?: number
  level?: string
  pageSize?: number
  pbu?: string
  productLine?: string
  projectUuidList?: any[]
  queryInput?: string
  searchObj?: Record<string, any>
  searchType?: number
  sort?: string
  source?: string
  startTime?: string
  status?: number
  [key: string]: any
}

export interface IRelevanceList {
  createdTime: string
  currentStage: string
  id: number
  instanceId: number
  milepostId: number
  nodeOwner: string
  projectLeader: string
  relevanceInstanceId: number
  relevanceNumber: string
  type: number
  [key: string]: any
}

export interface PageDemandRes {
  accept: string
  acceptanceResults: string
  adopt: string
  competitor: string
  countriesName: string
  customersIndustry: string
  customerslevel: string
  cycleRating: string
  demand: string
  distribute: string
  expectedTime: string
  how: string
  id: number
  level: string
  node: string
  nodeSuperviser: string
  nodeSuperviserUuid: string
  processConfigId: number
  processInstanceCode: string
  productLine: string
  products: string
  realityDuration: number
  relevanceList: IRelevanceList[]
  scene: string
  score: string
  source: string
  sumUp: string
  topicName: string
  what: string
  when: string
  why: string
  zlfile: any[]
  [key: string]: any
}

export interface ICount {
  completed: number
  processed: number
  running: number
  waitDeal: number
  [key: string]: any
}

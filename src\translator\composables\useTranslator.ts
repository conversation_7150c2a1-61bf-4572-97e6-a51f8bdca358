import { ref, onUnmounted } from 'vue'
import { Translator, TranslatorOptions } from '../translatro'

// 全局翻译器实例
let globalTranslator: Translator | null = null

// 初始化翻译器
export function initTranslator(options: TranslatorOptions) {
  if (globalTranslator) {
    console.warn('Translator already initialized')
    return globalTranslator
  }

  console.log('Initializing translator with options:', {
    toLang: options.toLang,
    fromLang: options.fromLang,
    htmlBlackList: options.htmlBlackList,
    attrWhiteList: options.attrWhiteList
  })

  globalTranslator = new Translator(options)

  return globalTranslator
}

// 使用翻译器
export function useTranslator() {
  if (!globalTranslator) {
    throw new Error('Translator not initialized. Please call initTranslator first.')
  }

  const isTranslating = ref(false)
  const currentLang = ref(globalTranslator.toLang)

  // 开始翻译
  const start = () => {
    console.log('Starting translation')
    isTranslating.value = true
    globalTranslator?.setup()
  }

  // 切换目标语言
  const setToLang = (lang: string) => {
    console.log('Setting target language:', lang)
    currentLang.value = lang
    globalTranslator?.setToLang(lang)
  }

  // 还原中文
  const restore = () => {
    console.log('Restoring original text')
    isTranslating.value = false
    globalTranslator?.restore()
  }

  // 停止翻译
  const stop = () => {
    console.log('Stopping translation')
    isTranslating.value = false
    globalTranslator?.stop()
  }

  // 组件卸载时停止翻译
  onUnmounted(() => {
    console.log('Component unmounted, stopping translation')
    stop()
  })

  return {
    isTranslating,
    currentLang,
    start,
    setToLang,
    restore,
    stop
  }
}

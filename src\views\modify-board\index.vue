<template>
  <div class="modify-board-container">
    <!-- <Breadcrumb class="none-container-padding" /> -->
    <div class="card-content-shadow pt24 pl24 pr24 mb16 flex space-between">
      <SearchContent v-model:query-data="queryData" />
      <div class="handle-box ml12 align-self-start">
        <FSpace :size="[8]" wrap>
          <FButton @click="exportModifyData" :loading="downLoading" type="primary">
            <template #icon><i class="iconfont icontubiao_daochu mr4" /></template>
            下载数据</FButton
          >
        </FSpace>
      </div>
    </div>
    <ContentBoard
      v-model:other-query-data="otherQueryData"
      :count-loading="countLoading"
      :instance-hours-loading="instanceHoursLoading"
      :count-info="countInfo"
      :instance-hours-count-info="instanceHoursCountInfo"
      @queryInstanceHoursDataCount="queryInstanceHoursDataCount"
      @queryDataCount="queryDataCount"
      :countUpdateKey="countUpdateKey"
      :hoursCountUpdateKey="hoursCountUpdateKey"
    />
    <div class="card-content-shadow pt20 pl24 pr24 mt16">
      <div class="flex space-between mb16">
        <span class="color333">个人积分排行</span>
        <FButton @click="exportData">
          <template #icon><i class="iconfont icontubiao_daochu" /></template>
          导出</FButton
        >
      </div>
      <FTable
        class="table-warp"
        :columns="columns"
        :loading="loading"
        :data-source="dataList"
        :row-key="(data:any) => data.id"
        :row-selection="rowSelection"
        :sticky="{ offsetHeader: 0 }"
        :scroll="{ x: 'min-content' }"
        :pagination="{
          total: paging.total,
          current: paging.currPage,
          pageSize: paging.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
        }"
        @change="onPaginationChangeFn"
        :key="updateKey"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'top'">
            <img v-if="record?.top === 1" src="@/assets/images/bpm/top1.svg" alt="" srcset="" />
            <img v-else-if="record?.top === 2" src="@/assets/images/bpm/top2.svg" alt="" srcset="" />
            <img v-else-if="record?.top === 3" src="@/assets/images/bpm/top3.svg" alt="" srcset="" />
            <span v-else>{{ record.top }}</span>
          </template>
          <template v-if="['completedRate', 'timeDeliveryRate', 'acceptCountRate'].includes(column.dataIndex)">
            <span>{{ text || 0 }}%</span>
          </template>
          <template v-if="column.dataIndex === 'orderAmount'">
            <span style="color: #fa8f23">{{ formatAmountWithCommas(text || 0) }}</span>
          </template>
        </template>
      </FTable>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { message } from '@fs/smart-design'
import { cache } from '@/utils'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import SearchContent from './components/SearchContent/index.vue'
import ContentBoard from './components/ContentBoard/index.vue'
import {
  getModifyProcessCount,
  getModifyProcessUserTop,
  getModifyProcessExport,
  getModifyProcessInstanceHoursCount,
  downModifyProcessCount,
} from '@/api/processBoard'
import { formatAmountWithCommas } from './components/ContentBoard/chartsOptions'

const { currentRoute } = useRouter()
const routerName = computed<any>(() => currentRoute.value?.name)
const loading = ref(false)
const paging = reactive<any>({ currPage: 1, pageSize: 10, total: 0 })
const queryData = ref<any>({})
const otherQueryData = ref<any>({
  type: 1,
  sort: undefined,
  nodeStatus: 1,
  gruopType: 4,
})
const dataList = ref<any[]>([])
const countInfo = ref<any>({})
const instanceHoursCountInfo = ref<any>([])
const instanceHoursLoading = ref(false)
const countLoading = ref(false)
const downLoading = ref(false)
const selectedKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[]) => (selectedKeys.value = selectedRowKeys),
}))
const columns = ref([
  { title: '排名', dataIndex: 'top', key: 'top', width: 80 },
  { title: '人员', dataIndex: 'userName', key: 'userName', width: 168 },
  { title: '角色', dataIndex: 'roleName', key: 'roleName', width: 168 },
  { title: '积分', dataIndex: 'score', key: 'score', sorter: true, sortOrder: null, width: 80 },
  { title: '需求完成数量', dataIndex: 'completedCount', key: 'completedCount', width: 130 },
  { title: '需求实现率', dataIndex: 'completedRate', key: 'completedRate', width: 130 },
  { title: '需求验收通过率', dataIndex: 'acceptCountRate', key: 'acceptCountRate', width: 130 },
  { title: '需求准时交付率', dataIndex: 'timeDeliveryRate', key: 'timeDeliveryRate', width: 130 },
  { title: '成单总金额 (USD)', dataIndex: 'orderAmount', key: 'orderAmount', width: 130 },
  { title: '成单量（单）', dataIndex: 'winningOrdersCount', key: 'winningOrdersCount', width: 130 },
])
const updateKey = ref<number>(Date.now() + Math.random())
const countUpdateKey = ref<number>(Date.now() + Math.random())
const hoursCountUpdateKey = ref<number>(Date.now() + Math.random())

// 查询列表
const queryDataList = async () => {
  try {
    loading.value = true
    const data = { ...queryData.value, ...otherQueryData.value }
    cache.set(
      routerName?.value,
      JSON.stringify({
        ...(data?.cacheValue ?? {}),
        currPage: paging.currPage,
        pageSize: paging.pageSize,
      })
    )
    delete data.cacheValue
    const res = await getModifyProcessUserTop({ ...data, ...paging })
    dataList.value = res?.data?.list || []
    paging.total = res?.data?.totalCount || 0
  } finally {
    selectedKeys.value = []
    loading.value = false
    updateKey.value = Date.now() + Math.random()
  }
}

const exportModifyData = async () => {
  try {
    downLoading.value = true
    const data = { ...queryData.value, ...otherQueryData.value }
    delete data.cacheValue
    const res = await downModifyProcessCount(data)
    if (res?.data?.type === 'application/json') {
      message.error('服务器出差错了,请稍后重试')
      return
    }
    if (res?.data?.type === 'application/vnd.ms-excel') {
      const link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([res.data]))
      link.download =
        (res?.headers?.['content-disposition']?.split('filename=')?.[1] &&
          decodeURI(res?.headers?.['content-disposition']?.split('filename=')?.[1])) ||
        'modify流程数据下载.xls'
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      message.success('数据导出成功！')
    }
  } finally {
    downLoading.value = false
  }
}

const queryDataCount = async () => {
  try {
    countLoading.value = true
    const data = { ...queryData.value, ...otherQueryData.value }
    delete data.cacheValue
    const res = await getModifyProcessCount(data)
    countInfo.value = res?.data || {}
  } finally {
    countLoading.value = false
    countUpdateKey.value = Date.now() + Math.random()
  }
}

const queryInstanceHoursDataCount = async () => {
  try {
    instanceHoursLoading.value = true
    const data = { ...queryData.value, ...otherQueryData.value }
    delete data.cacheValue
    const res = await getModifyProcessInstanceHoursCount(data)
    instanceHoursCountInfo.value = res?.data || []
  } finally {
    instanceHoursLoading.value = false
    hoursCountUpdateKey.value = Date.now() + Math.random()
  }
}

const onPaginationChangeFn = (pagination: any, filters: any, sorter: any) => {
  paging.currPage = pagination.current
  paging.pageSize = pagination.pageSize

  otherQueryData.value.sort = sorter?.order === 'descend' ? 1 : sorter?.order === 'ascend' ? 2 : undefined
  columns.value = columns.value.map(col => ({
    ...col,
    sortOrder: col.dataIndex === sorter.field ? sorter.order : null,
  }))
  queryDataList()
}

// 查询列表
const onGetSearchData = (data: any) => {
  queryData.value = data
  paging.currPage = data?.cacheValue?.currPage || 1
  paging.pageSize = data?.cacheValue?.pageSize || 10
  queryDataCount()
  queryDataList()
  queryInstanceHoursDataCount()
}

const exportData = async () => {
  if (!dataList.value.length) {
    message.warning('暂无数据可导出！')
    return
  }

  const data = { ...queryData.value }
  delete data.cacheValue
  const res = await getModifyProcessExport(data)
  const link = document.createElement('a')
  link.href = URL.createObjectURL(new Blob([res.data]))
  link.download =
    (res?.headers?.['content-disposition']?.split('filename=')?.[1] &&
      decodeURI(res?.headers?.['content-disposition']?.split('filename=')?.[1])) ||
    'modify_export.xlsx'
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  message.success('数据导出成功！')
}

watch(
  () => queryData.value,
  val => {
    onGetSearchData(val)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.modify-board-container {
  .none-container-padding {
    margin-top: -24px;
    margin-left: -4px;
    width: calc(100% + 8px);
  }
  .card-content-shadow {
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .align-self-start {
    align-self: flex-start;
  }
  .code-link {
    cursor: pointer;
    color: #378eef;
  }
  .mr6 {
    margin-right: 6px;
  }
  .mr4 {
    margin-right: 4px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .error-color {
    color: #f04141;
  }
  .sucess-color {
    color: #2fcc83;
  }
  .empty-content {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .count-info-content {
    line-height: 18px;
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
  :deep(.fs-table-tbody > tr > td) {
    &.fs-table-cell-row-hover {
      background-color: #f1f4f8;
    }
  }
  :deep(.fs-table-column-sorters) {
    justify-content: flex-start;
    .fs-table-column-title {
      flex: 0;
      white-space: nowrap;
    }
  }
}
</style>

<template>
  <div class="content-board-container">
    <div class="row-box board-all shadow-card marginB16">
      <div class="board-item flex flexW33 marginR16 space-between" style="background-color: #f2f7fe">
        <div class="board-item-left">
          <div class="title">项目总数 (个)</div>
          <div class="count">{{ basicStatistics?.totalCount || 0 }}</div>
          <div class="rate">
            <span class="rate-label">环比</span>
            <span :class="(basicStatistics?.ringRatio >= 0 && 'up') || 'down'">
              <img v-if="basicStatistics?.ringRatio >= 0" src="./images/up.png" />
              <img v-else src="./images/down.png" />
              <span>{{ basicStatistics?.ringRatio }}%</span>
            </span>
          </div>
        </div>
        <div class="board-item-right">
          <BaseEchart :option="projectBarOption" width="192px" height="144px" />
        </div>
      </div>
      <div class="board-item flex flexW33 marginR16 space-between" style="background-color: #f3fcf7">
        <div class="board-item-left">
          <div class="title">已完成 (个)</div>
          <div class="count">{{ projectStatistics?.completedCount || 0 }}</div>
        </div>
        <div class="board-item-right">
          <BaseEchart :option="hasCompleteChartOption" width="214px" :key="basicUpdateKey" height="144px" />
        </div>
      </div>
      <div class="board-item flex flexW33 space-between" style="background-color: #feffef">
        <div class="board-item-left">
          <div class="title">逾期完成 (个)</div>
          <div class="count">{{ projectStatistics?.overdueCompletedCount || 0 }}</div>
        </div>
        <div class="board-item-right">
          <BaseEchart :option="noCompleteChartOption" width="214px" :key="basicUpdateKey" height="144px" />
        </div>
      </div>
    </div>

    <div class="row-box marginB16">
      <div class="board-item shadow-card">
        <div class="title row-box justify">
          <span>项目数量趋势</span>
          <div class="type-select">
            <span :class="{ active: trendType === 1 }" @click="changeTrend(1)">周</span>
            <span :class="{ active: trendType === 2 }" @click="changeTrend(2)">月</span>
          </div>
        </div>
        <BaseEchart :option="projectCountLineOptions" width="100%" height="308px" :key="trendUpdateKey" />
      </div>
    </div>
    <div class="row-box marginB16" :class="show ? '' : 'evenstyle'">
      <div class="board-item shadow-card flexW66 marginR16" v-if="show">
        <div class="title marginB8">项目数量分布-1</div>
        <BaseEchart
          :option="projectCountLineDistributionOneOptions"
          width="100%"
          height="288px"
          :key="hoursCountUpdateKey"
        />
      </div>
      <div class="board-item shadow-card flexW33 marginR16" v-if="!show">
        <div class="title row-box justify marginB8">
          <span>阶段完成情况</span>
          <div class="type-select">
            <span :class="{ active: stageType === 1 }" @click="changeStage(1)">小时</span>
            <span :class="{ active: stageType === 2 }" @click="changeStage(2)">天</span>
            <span :class="{ active: stageType === 3 }" @click="changeStage(3)">周</span>
          </div>
        </div>
        <BaseEchart :option="completionOptions" width="100%" height="288px" :key="hoursCountUpdateKey" />
      </div>
      <div class="board-item shadow-card flexW33" :class="!(show || !isListLengthOdd) ? '' : 'marginR16'" v-if="!show">
        <div class="title row-box justify marginB8">
          <span>项目所属阶段</span>
        </div>
        <BaseEchart :option="stageionOptions" width="100%" height="288px" :key="hoursCountUpdateKey" />
      </div>
      <div class="board-item shadow-card flexW33" v-if="show || !isListLengthOdd">
        <div class="title row-box justify" style="margin-bottom: 24px !important">
          <span>项目数量分布-2</span>
          <div class="type-select">
            <FSelect
              class="cust-select"
              :dropdown-match-select-width="false"
              placeholder="请选择"
              v-model:value="projectStatus"
              :options="nodeStatusList"
              @change="changeNode"
            />
          </div>
        </div>
        <BaseEchart
          :option="projectCountLineDistributionTwoOptions"
          width="100%"
          height="288px"
          :key="hoursCountUpdateKey"
        />
      </div>
    </div>
    <div class="row-box require-box" style="flex-wrap: wrap">
      <div class="board-item shadow-card" v-if="!(show || !isListLengthOdd)">
        <div class="title row-box justify" style="margin-bottom: 24px !important">
          <span>项目数量分布-2</span>
          <div class="type-select">
            <FSelect
              class="cust-select"
              :dropdown-match-select-width="false"
              placeholder="请选择"
              v-model:value="projectStatus"
              :options="nodeStatusList"
              @change="changeNode"
            />
          </div>
        </div>
        <BaseEchart
          :option="projectCountLineDistributionTwoOptions"
          width="100%"
          height="288px"
          :key="hoursCountUpdateKey"
        />
      </div>
      <template v-if="!show">
        <div class="board-item shadow-card" v-for="(item, index) in requireList" :key="index + basicUpdateKey">
          <div class="title row-box justify marginB8">
            <span>{{ item.fieldName }}</span>
            <div class="type-select">
              <span :class="{ active: item.requireType === 1 }" @click="changeRequireType(1, index)">小时</span>
              <span :class="{ active: item.requireType === 2 }" @click="changeRequireType(2, index)">天</span>
              <span :class="{ active: item.requireType === 3 }" @click="changeRequireType(3, index)">周</span>
            </div>
          </div>
          <div class="require-item">
            <div>
              <BaseEchart
                :option="getLinRequireOptions(item)"
                width="100%"
                height="287px"
                :key="'bar_' + index + '_' + item.requireType + basicUpdateKey"
              />
            </div>
            <div style="display: flex; align-items: flex-end">
              <BaseEchart :option="getPieOptions(item)" :key="basicUpdateKey" width="100%" height="244px" />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseEchart from '@/components/BaseEchart/index.vue'
import type { EChartsOption } from '@/components/BaseEchart/config'
import {
  pieOptions,
  barOptions,
  commonTitle,
  commonLegend,
  getLineOptions,
  getBarOptions,
  getBarOneOptions,
} from './chartsOptions'
import { computed, ref, watch } from 'vue'

type propsType = {
  trendUpdateKey: number
  countUpdateKey: number
  hoursCountUpdateKey: number
  basicStatistics: any
  projectStatistics: any
  processDistributionData: any
  trendData: any
  projectFieldDistributionData: any
  stageCompletionData: any
  projectDistributionData: any
  projectStageData: any
  projectDeliveryCycleData: any
  projectList: any
  basicUpdateKey: any
  queryData: any
}

const props = defineProps<propsType>()
const emits = defineEmits(['increaseTrendUpdateKey', 'increaseUpdateKey', 'increaseHoursUpdateKey'])

const nodeStatusList = ref([
  { label: '所属区域', value: '所属区域' },
  { label: '所属中心', value: '所属中心' },
  { label: '需求类型', value: '需求类型' },
])

const trendType = ref(1)
const projectStatus = ref('所属区域')
const stageType = ref(1)

const projectBarOption = computed<EChartsOption>(() => {
  return {
    ...barOptions,
    xAxis: {
      data: (props?.basicStatistics?.monthlyCounts ?? []).map(item => item?.month ?? '其它'),
    },
    yAxis: {
      type: 'value',
      show: false,
    },
    series: [
      {
        type: 'bar',
        name: '项目数量',
        data: (props?.basicStatistics?.monthlyCounts ?? []).map(item => item?.count ?? 0),
        barWidth: 18,
        barMinHeight: 3,
        barCategoryGap: 12,
        stack: 'Total',
        label: {
          show: true,
          distance: 2,
          position: 'top',
          color: '#666',
          fontSize: '10px',
        },
      },
    ],
  }
})

// 已完成
const hasCompleteChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    title: Object.assign({}, commonTitle, {
      left: '32%',
      top: '45%',
      text: props.projectStatistics.completionRate + '%',
      textAlign: 'center',
      textStyle: {
        fontSize: '12px',
        color: '#333333',
        fontWeight: 'bold',
      },
      subtextStyle: {
        fontSize: '10px',
        color: '#666666',
        fontWeight: 400,
      },
      subtext: '项目完成率',
    }),
    legend: Object.assign({}, commonLegend, { orient: 'vertical', right: 0, top: '42%' }),
    color: ['#2FCC83', '#82E0B4', '#C6EFDC'],
    series: [
      {
        ...pieOptions.series?.[1],
        center: ['35%', '56%'], // 中上位置
        radius: ['58%', '85%'], // 主环
        data: (props?.projectStatistics?.hasCompleteArr ?? []).map(item => ({
          value: item?.value || 0,
          name: item?.name || '其它',
        })),
      },
    ],
  }
})

//未完成
const noCompleteChartOption = computed<EChartsOption>(() => {
  return {
    ...pieOptions,
    title: Object.assign({}, commonTitle, {
      left: '28%',
      top: '45%',
      text: props.projectStatistics.overdueRate + '%',
      textAlign: 'center',
      textStyle: {
        fontSize: '12px',
        color: '#333333',
        fontWeight: 'bold',
      },
      subtextStyle: {
        fontSize: '10px',
        color: '#666666',
        fontWeight: 400,
      },
      subtext: '逾期完成率',
    }),
    legend: Object.assign({}, commonLegend, { orient: 'vertical', right: 0, top: '45%' }),
    color: ['#D9E000', '#E8EC66'],
    series: [
      {
        ...pieOptions.series?.[1],
        center: ['30%', '56%'], // 中上位置
        radius: ['55%', '85%'], // 主环
        data: (props?.projectStatistics?.noCompleteArr ?? []).map(item => ({
          value: item?.value || 0,
          name: item?.name || '其它',
        })),
      },
    ],
  }
})

// 项目数量趋势
const projectCountLineOptions = computed(() =>
  getLineOptions({
    title: '项目数量趋势',
    yName: '项目数 (个)',
    xData:
      trendType.value === 1
        ? (props?.trendData?.weeklyTrends ?? []).map(item => item?.week)
        : (props?.trendData?.monthlyTrends ?? []).map(item => item?.month),
    seriesData:
      trendType.value === 1
        ? (props?.trendData?.weeklyTrends ?? []).map(item => item?.count ?? 0)
        : (props?.trendData?.monthlyTrends ?? []).map(item => item?.count ?? 0),
    lineColor: '#378EEF',
    formatter: (params: any) => {
      const [info = {}] = params
      return `
        <span>${info?.axisValue}</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left; margin-right: 40px;">项目数 (个)</span> <span>${info.value}</span>
      `
    },
    areaColor: '',
    ellipsisLimit: 0,
  })
)
// 流程分布情况-1
const projectCountLineDistributionOneOptions = computed(() =>
  getLineOptions({
    title: '项目数量趋势55',
    yName: '项目数 (个)',
    xData: (props?.processDistributionData?.processDistributionData ?? []).map(item => item?.processName ?? '其它'),
    seriesData: (props?.processDistributionData?.processDistributionData ?? []).map(item => item?.count ?? 0),
    lineColor: '#2FCC83',
    formatter: (params: any) => {
      const [info = {}] = params
      return `
        <span>${info?.axisValueLabel}</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left; margin-right: 40px;">项目数 (个)</span> <span>${info.value}</span>
      `
    },
    rotate: 35,
    areaColor: ['rgba(47, 204, 131, 1)', 'rgba(255, 255, 255, 0.16)'],
    ellipsisLimit: 4,
    fontSize: 10,
  })
)

// 项目数量分布2
const projectCountLineDistributionTwoOptions = computed(() =>
  getBarOptions({
    title: '需求节点状完成情况',
    unit: '项目数（个）',
    xData:
      projectStatus.value === '所属区域'
        ? (props?.projectDistributionData?.areaDistribution?.distributionData ?? []).map(item => item?.categoryName)
        : projectStatus.value === '所属中心'
        ? (props?.projectDistributionData?.centerDistribution?.distributionData ?? []).map(item => item?.categoryName)
        : (props?.projectDistributionData?.typeDistribution?.distributionData ?? []).map(item => item?.categoryName),
    seriesData:
      projectStatus.value === '所属区域'
        ? (props?.projectDistributionData?.areaDistribution?.distributionData ?? []).map(item => item?.count)
        : projectStatus.value === '所属中心'
        ? (props?.projectDistributionData?.centerDistribution?.distributionData ?? []).map(item => item?.count)
        : (props?.projectDistributionData?.typeDistribution?.distributionData ?? []).map(item => item?.count),
    color: ['#378EEF'],
    formatter: (params: any) => {
      const [info = {}] = params
      return `
        <span>${info?.axisValue}</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left; margin-right: 40px;">项目数 (个)</span> <span>${info.value}</span>
      `
    },
    ellipsisLimit: 3,
  })
)

// 阶段完成情况
const completionOptions = computed(() =>
  getBarOptions({
    title: '需求节点状完成情况',
    unit: `时长 ${stageType.value === 1 ? '(h)' : stageType.value === 2 ? '(天)' : '(周)'}`,
    xData: (props?.stageCompletionData?.stageCompletionData ?? []).map(item => item?.stageName),
    seriesData:
      stageType.value === 1
        ? (props?.stageCompletionData?.stageCompletionData ?? []).map(item => item?.avgCompletionHours)
        : stageType.value === 2
        ? (props?.stageCompletionData?.stageCompletionData ?? []).map(item => item?.avgCompletionDays)
        : (props?.stageCompletionData?.stageCompletionData ?? []).map(item => item?.avgCompletionWeeks),
    color: ['#F5CC38'],
    formatter: (params: any) => {
      const [info = {}] = params
      return `
        <span>${info?.axisValue}</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left; margin-right: 40px;">时长(${
          stageType.value === 1 ? 'h' : stageType.value === 2 ? '天' : '周'
        })</span> <span>${info.value}</span>
      `
    },
    ellipsisLimit: 3,
  })
)
// 项目所属阶段
const stageionOptions = computed(() =>
  getBarOptions({
    title: '需求节点状完成情况',
    unit: '项目数（个）',
    xData: (props?.projectStageData?.stageData ?? []).map(item => item?.stageName ?? '其它'),
    seriesData: (props?.projectStageData?.stageData ?? []).map(item => item?.processCount ?? 0),
    color: ['#2FCC83'],
    formatter: (params: any) => {
      const [info = {}] = params
      return `
        <span>${info?.axisValue}</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left; margin-right: 40px;">项目数 (个)</span> <span>${info.value}</span>
      `
    },
    ellipsisLimit: 3,
  })
)

const changeTrend = type => {
  trendType.value = type
  emits('increaseTrendUpdateKey', props.trendUpdateKey + 1)
}
const changeNode = type => {
  projectStatus.value = type
  emits('increaseHoursUpdateKey', props.hoursCountUpdateKey + 1)
}
const changeStage = type => {
  stageType.value = type
  emits('increaseUpdateKey', props.countUpdateKey + 1)
}
const requireList = ref([])
const changeRequireType = (type, index) => {
  requireList.value[index].requireType = type
}
const show = ref(false)
const isListLengthOdd = ref(false)
//多选单选
watch(
  () => props.queryData.processConfigId,
  val => {
    if (!val) {
      show.value = true
    } else {
      show.value = false
    }
  },
  { deep: true }
)
//奇数偶数
watch(
  () => props.projectList,
  val => {
    requireList.value = val.map(item => {
      return {
        ...item,
        requireType: 1,
      }
    })
    console.log(requireList.value)
    if (requireList.value.length > 0) {
      isListLengthOdd.value = requireList.value.length % 2 !== 0
    } else {
      isListLengthOdd.value = false
    }

    console.log(isListLengthOdd.value)
  }
)
// 动态生成柱状图配置
const getLinRequireOptions = (chartData: any) => {
  const requireType = chartData.requireType // 这里使用当前图表项的requireType
  return getBarOneOptions({
    title: '需求节点状完成情况',
    unit: '',
    xData: chartData?.deliveryCycleData.map(item => item?.valueName ?? '其它'),
    seriesData:
      requireType === 1
        ? chartData?.deliveryCycleData.map(item => item?.avgCompletionHours)
        : requireType === 2
        ? chartData?.deliveryCycleData.map(item => item?.avgCompletionDays)
        : chartData?.deliveryCycleData.map(item => item?.avgCompletionWeeks),
    color: ['#378EEF', '#2FCC83', '#F5CC38', '#FA8F23', '#A6B0BA'],
    formatter: (params: any) => {
      const [info = {}] = params
      return `
        <span>${info?.axisValue}</span><br/>
        <span style="display: inline-block; text-align: left; margin-right: 40px;">平均交付时长 (${
          requireType === 1 ? 'h' : requireType === 2 ? '天' : '周'
        })</span> <span>${info.value}</span>
      `
    },
    barWidth: 18,
    ellipsisLimit: 3,
  })
}

// 动态生成饼图配置
const getPieOptions = (chartData: any) => {
  return {
    ...pieOptions,
    title: Object.assign({}, commonTitle, {
      left: '44%',
      top: '37%',
      text: '总项目 (个)',
      textAlign: 'center',
      textStyle: {
        fontSize: '12px',
        color: '#666',
        fontWeight: 400,
      },
      subtextStyle: {
        fontSize: '14px',
        color: '#333',
        fontWeight: 'bold',
      },
      subtext: chartData?.valueDistributions.reduce((sum, item) => sum + (item?.processCount || 0), 0),
    }),
    legend: Object.assign({}, commonLegend, {
      type: 'scroll',
      orient: 'horizontal',
      bottom: 0,
      height: 130, // 限制图例容器高度
      pageTextStyle: {
        color: '#666',
      },
      pageIconColor: '#666',
      pageIconInactiveColor: '#ddd',
      pageIconSize: 10,
      formatter: function (name) {
        // 文本过长时显示省略号
        const maxLength = 5
        if (name.length > maxLength) {
          return name.substring(0, maxLength) + '...'
        }
        return name
      },
    }),
    color: ['#378EEF', '#2FCC83', '#F5CC38', '#FA8F23', '#A6B0BA'],
    series: [
      {
        ...pieOptions.series?.[1],
        center: ['45%', '44%'], // 中上位置
        radius: ['60%', '85%'], // 主环
        data: (chartData?.valueDistributions ?? []).map(item => ({
          value: item?.processCount || 0,
          name: item?.valueName || '其它',
        })),
      },
    ],
  }
}
</script>

<style scoped lang="scss">
.content-board-container {
  margin-top: 16px;
  .row-box {
    &.board-all {
      padding: 24px 16px;
      background-color: #fff;
      overflow-y: auto;
      border-radius: 4px;
      .board-item {
        padding: 24px 32px 24px 32px;
        height: 200px;
        border-radius: 4px;
        .title {
          font-weight: 400;
          font-size: 14px;
          color: #999;
          line-height: 22px;
          margin-top: 30px;
          margin-bottom: 24px !important;
        }
        .count {
          font-weight: 500;
          font-size: 30px;
          color: #333333;
          line-height: 34px;
          margin-bottom: 8px;
        }
        .rate {
          display: flex;
          line-height: 18px;
          img {
            width: 16px;
            height: 16px;
          }
          .rate-label {
            display: inline-block;
            margin-right: 4px;
            font-size: 12px;
            font-weight: 400;
            color: #666666;
            line-height: 18px;
          }
          .up {
            color: #2fcc83;
          }
          .down {
            color: #f04141;
          }
          img {
            width: 14px;
            margin-right: 4px;
          }
        }
      }
    }
    &.require-box {
      .board-item {
        width: calc(50% - 8px);
        margin-bottom: 16px;
        .title {
          margin-bottom: 0px !important;
        }
        .require-item {
          display: flex;
          justify-content: space-between;
          > div:nth-child(1) {
            width: calc(55% - 17px);
          }
          > div:nth-child(2) {
            width: calc(45% - 17px);
          }
        }
      }
      .board-item:nth-child(2n-1) {
        margin-right: 16px;
      }
    }
    display: flex;
    .board-item {
      width: 100%;
      padding: 24px;
      .title {
        font-size: 16px;
        font-weight: 400;
        color: #333;
        line-height: 24px;
        margin-bottom: 20px !important;
      }
    }
    .shadow-card {
      background: #ffffff;
      border-radius: 4px;
    }
    .line {
      width: 1px;
      height: calc(20vw - 66px);
      margin-bottom: 20px;
      align-self: end;
      background-color: #eeeff2;
    }
    .marginR16 {
      margin-right: 16px;
    }
    .flexW20 {
      flex: 1 1 20%;
    }
    .flexW33 {
      flex: 1 1 33.33333%;
    }
    .flexW32 {
      flex: 1 1 35%;
    }
    .flexW40 {
      flex: 1 1 40%;
    }
    .flexW50 {
      flex: 1 1 50%;
    }
    .flexW60 {
      flex: 1 1 60%;
    }
    .flexW68 {
      flex: 1 1 65%;
    }
    .flexW66 {
      flex: 1 1 66.66666%;
    }
    .justify {
      justify-content: space-between;
    }
    .type-select {
      background: #f9fafb;
      border-radius: 4px;
      height: 20px;
      line-height: 20px;
      span {
        display: inline-block;
        color: #999999;
        padding: 0 15px;
        border-radius: 4px;
        // line-height: 20px;
        font-size: 12px;
        cursor: pointer;
        &.active {
          background: #ffffff;
          box-shadow: 0px 2px 4px 0px rgba(17, 24, 39, 0.04);
          color: #378eef;
        }
      }
    }
  }

  .marginB16 {
    margin-bottom: 16px;
  }
  .marginB8 {
    margin-bottom: 8px !important;
  }
  :deep(.cust-select) {
    .fs-select-selector {
      height: 22px;
      box-shadow: none !important;
      border: none;
      .fs-select-selection-item {
        line-height: 22px;
        padding-right: 11px;
      }
    }
    .fs-select-arrow {
      right: 0;
    }
  }
}
.space-between {
  justify-content: space-between;
}

.evenstyle {
  .board-item {
    width: calc(50% - 8px) !important;
    flex: 1 !important;
  }
}
</style>

import { BasicPageParams, IRes } from '@/types/request'

export interface PageMessageTemplateParams extends BasicPageParams {
  type?: number
  feishuId?: string
  queryInput?: string
  status?: number
}

export interface PageMessageTemplateRes {
  createdTime: string
  createdUserId: string
  createdUserName: string
  feishuId: string
  fields: string
  id: number
  remarks: string
  status: number
  templateCode: string
  templateName: string
  type: number
  updateTime: string
  updateUserId: string
  updateUserName: string
}

export interface MessageTemplateParams {
  templateName?: string
  templateCode?: string
  type?: number
  status?: number
  feishuId?: string
  fields?: string
  remarks?: string
  id?: number
  [key: string]: any
}

<template>
  <div class="board-task-container">
    <BaseCharts :options="options" />
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import BaseCharts from './BaseCharts.vue'
import { Datum } from '@/types/localizationSystem/dataBoard'
import { useI18n } from '@/utils'
const i18n = useI18n()

type propsType = {
  processTaskNumInfo: Datum[]
}

const props = withDefaults(defineProps<propsType>(), {
  processTaskNumInfo: () => [],
})

const options = reactive<any>({
  color: ['#378EEF'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#666',
        type: 'solid',
      },
    },
    backgroundColor: '#000000B3',
    borderColor: 'transparent',
    formatter: function (params: any) {
      return `<div style="color: #fff">${params[0].name}</div>
      <div style="color: #fff"><span style="display: inline-block; margin-right: 24px"><i style="display: inline-block;width: 8px; height: 8px;margin-right:4px;background-color: #378EEFFF;"></i>${i18n.t(
        '任务完成总数'
      )}</span><span>${params[0].value}个</span></div>`
    },
  },
  grid: {
    left: '34px',
    right: '24px',
    top: '10px',
    bottom: '8%',
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#EAEBF0',
      },
    },
    axisLabel: {
      color: '#666',
      interval: 0,
    },
    data: [],
  },
  yAxis: {
    type: 'value',
    minInterval: 1,
    splitLine: {
      lineStyle: {
        color: '#EAEBF0',
        type: 'dashed',
      },
    },
    axisLabel: {
      color: '#666',
    },
  },
  series: {
    data: [],
    barWidth: 25,
    type: 'bar',
  },
})

watch(
  () => props.processTaskNumInfo,
  newVal => {
    options.xAxis.data = newVal.map(item => item.processName)
    options.series.data = newVal.map(item => item.sumProcessTaskCompleteNum || 0)
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.board-task-container {
  width: 100%;
  height: 100%;
}
</style>

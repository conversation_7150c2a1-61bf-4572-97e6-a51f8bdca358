<template>
  <FCard
    class="mb24"
    :title="i18n.t('项目排期')"
    :head-style="COMPONENT_CRAD_BODY_STYLE"
    :body-style="COMPONENT_CRAD_BODY_STYLE"
  >
    <div class="project-plan">
      <FTable :data-source="tableList" :columns="columns">
        <template #bodyCell="{ column, text, index, record }">
          <template v-if="column.dataIndex === 'forcastTime'">
            <div class="forcast-time">
              <FDatePicker
                :value="text"
                :disabled-date="(current: Dayjs) => current && current < (tableList[index - 1].forcastTime || dayjs().subtract(1, 'day'))"
                :disabled="![0, 1, 2].includes(record.status) || !record.superviserRole"
                :allow-clear="false"
                format="YYYY-MM-DD"
                @change="(date: Dayjs) => handleTimeChange(record, date)"
              />
            </div>
          </template>
        </template>
      </FTable>
    </div>
  </FCard>
</template>

<script setup lang="ts">
import { computed, inject } from 'vue'
import dayjs, { Dayjs } from 'dayjs'
import { COMPONENT_CRAD_BODY_STYLE, MILEPOST_STATUS_NAME } from '../../../../config'
import { IProcess } from '@/types/handle'
import { updateMilepostTime } from '@/api'
import { transformDate, useI18n } from '@/utils'

interface IProps {
  processInfo: IProcess[] | undefined
}

const i18n = useI18n()
const props = defineProps<IProps>()
const tableList = computed(() =>
  (props?.processInfo ?? []).map(item => ({
    ...item,
    forcastTime: TFDate(item),
    nodeStartTime: item.nodeStartTime ? transformDate(item.nodeStartTime, 'YYYY-MM-DD') : null,
    completeTime: item.completeTime ? transformDate(item.completeTime, 'YYYY-MM-DD') : null,
    statusName: MILEPOST_STATUS_NAME[item.status as keyof typeof MILEPOST_STATUS_NAME],
    children: null,
  }))
)
const columns = computed(() => [
  { title: i18n.t('节点名称'), dataIndex: 'topicName' },
  { title: i18n.t('开始时间'), dataIndex: 'nodeStartTime', width: 160 },
  { title: i18n.t('预计完成时间'), dataIndex: 'forcastTime', width: 160 },
  { title: i18n.t('实际完成时间'), dataIndex: 'completeTime', width: 160 },
  { title: i18n.t('状态'), dataIndex: 'statusName', width: 80 },
  { title: `${i18n.t('工时')}(${i18n.t('天')})`, dataIndex: 'duration' },
])

const getProcessInfo = inject('getProcessInfo') as () => void

const handleTimeChange = async (data: IProcess, date: Dayjs) => {
  await updateMilepostTime({
    instanceId: data.instanceId,
    milepostId: data.id,
    forcastTime: date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : null,
  })
  getProcessInfo()
}

const TFDate = (data: IProcess) => {
  if (!data.forcastTime && !data.nodeStartTime) return null
  if (data.forcastTime) return dayjs(data.forcastTime)

  return dayjs(data.nodeStartTime).add(data.duration, 'day')
}
</script>

<style lang="scss" scoped>
.mb24 {
  margin-bottom: 24px !important;
}

.project-plan {
  margin-top: 24px;
  margin-bottom: 24px;

  .forcast-time {
    :deep(.fs-picker) {
      width: 100%;
      border-color: transparent;
      background-color: transparent;
      .fs-picker-suffix {
        display: none;
      }
    }

    :deep(.fs-picker-focused) {
      background-color: white;
      .fs-picker-suffix {
        display: inline;
      }
    }

    &:hover {
      :deep(.fs-picker) {
        border-color: #d9d9d9;
        background-color: white;
        .fs-picker-suffix {
          display: inline;
        }
      }
    }

    // :deep(.fs-picker-disabled:hover) {
    //   border-color: transparent;
    //   background-color: transparent;
    //   .fs-picker-suffix {
    //     display: none;
    //   }
    // }
  }
}
</style>

<template>
  <div class="process-list" :key="updateKey">
    <!-- 面包屑 -->
    <!-- <Breadcrumb :data="[i18n.t('首页'), i18n.t('业务流程'), processTypeName]" /> -->

    <!-- 筛选条件 -->
    <QueryForm @query="query" @on-export-process="onExportProcess" />

    <div class="process-table-container">
      <Tap :list="tapList" :disable="disable" @change="tapChange" />
      <div class="process-table-wrapper">
        <CustomTable :type="tapKey" :data-source="processData" @update-table="getProcessesList" :loading="loading" />
        <div class="fei-su-pagination">
          <span class="fontSize12">{{ i18n.t('共') }} {{ paginate.total }} {{ i18n.t('条') }}</span>
          <FPagination
            v-model:current="paginate.pageNum"
            v-model:pageSize="paginate.pageSize"
            :total="paginate.total"
            @change="showSizeChange"
            show-size-changer
          />
        </div>
      </div>
    </div>

    <div class="user-notice" @click="noticeFlag = true">
      <FBadge :dot="unreadMsg.length"><Icon icon="icontubiao_xiaoxitongzhi_lingdang" size="20" color="white" /></FBadge>
      <span class="__text __opacity">{{ i18n.t('消息通知') }}</span>
      <FBadge class="__opacity" :count="unreadMsg.length" />
    </div>

    <FDrawer v-model:visible="noticeFlag" :title="i18n.t('消息通知')" placement="right">
      <FEmpty v-show="!unreadMsg.length" :image="simpleImage" />
      <div class="mssage-container" v-for="item in unreadMsg" :key="item.instanceId + item.sendDate">
        <MessageItem :message-item="(item as any)" :is-goto="true" />
      </div>
    </FDrawer>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, onMounted, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import {
  GetTagAndNode,
  getDraftProcessInfo,
  getProccessQueryCount,
  getProcessList,
  getUnreadMessage,
  exportProcess,
  templateExportProcess,
} from '@/api'
import { IProcessClassType } from '@/types/handle'
import { BasicPageParams } from '@/types/common'
import { ProcessItem } from '@/types/processListModel'
import { ISeeMessage } from '@/types/request'

import Breadcrumb from './components/Breadcrumb/index.vue'
import QueryForm from './components/QueryForm/index.vue'
import Icon from '@/components/Icon/index.vue'
import Tap from '@/views/process-list/components/ContentBox/TapBox.vue'
import CustomTable from '@/views/process-list/components/ContentBox/CustomTable.vue'
import MessageItem from '@/components/Message/MessageItem.vue'

import simpleImage from '@/assets/images/empty.png'
import { messageInstance as message } from '@fs/smart-design'
import { useI18n } from '@/utils'

const i18n = useI18n()
const store = useStore()
const route = useRoute()
const updateKey = ref<number>(Date.now() + Math.random())
const cacheKey = computed(() => {
  if (route.name === 'ProcessList') return 'all'
  if (route.params?.type) return route.params?.type
  return 'noCache'
})

const loading = ref<boolean>(false)
const noticeFlag = ref<boolean>(false)
const formData = ref<Record<string, unknown>>({})
const unreadMsg = ref<ISeeMessage[]>([])

const processTypeId = computed(() => route.params.type as string)
const currProcessType = computed(() => processTypeData.value.find(item => item.id == +processTypeId.value))
const processTypeName = computed(() => currProcessType.value?.processName ?? '')

const processTypeData = ref<IProcessClassType[]>([])

const processData = ref<ProcessItem[]>([])
const paginate = ref<BasicPageParams>({ pageNum: 1, pageSize: 10, total: 0 })

const tapKey = ref<number>(1)
const disable = ref<boolean>(false)
const taps = computed(() => [
  { label: i18n.t('全部'), value: 0, id: 0 },
  { label: i18n.t('待我处理'), value: 0, id: 1 },
  { label: i18n.t('我发起的'), value: 0, id: 2 },
  { label: i18n.t('即将参与'), value: 0, id: 3 },
  { label: i18n.t('进行中'), value: 0, id: 4 },
  { label: i18n.t('已完成'), value: 0, id: 5 },
  { label: i18n.t('流程终止'), value: 0, id: 6 },
  { label: i18n.t('草稿'), value: 0, id: 7 },
])

const reminds = ref({
  completed: 0,
  joinSoon: 0,
  meStart: 0,
  running: 0,
  total: 0,
  waitDeal: 0,
  completion: 0,
  draftNum: 0,
})

const tapList = computed(() => {
  const list = JSON.parse(JSON.stringify(taps.value))
  list[0].value = reminds.value.total
  list[1].value = reminds.value.waitDeal
  list[2].value = reminds.value.meStart
  list[3].value = reminds.value.joinSoon
  list[4].value = reminds.value.running
  list[5].value = reminds.value.completed
  list[6].value = reminds.value.completion
  list[7].value = reminds.value.draftNum
  return list
})

const tapChange = (data: number) => {
  tapKey.value = data
  query()
}

watch(
  () => route.path,
  () => {
    if (route.path) {
      updateKey.value = Date.now() + Math.random()
      const cache = store.getters['local/getLocalSearchData']
      if (cache !== undefined && cache !== null) {
        const localSearchData = cache[cacheKey.value as string] ?? {}
        ;(localSearchData.type || localSearchData.type === 0) && (tapKey.value = localSearchData.type)
      }
    }
  }
)
onBeforeMount(() => {
  const cache = store.getters['local/getLocalSearchData']
  if (cache !== undefined && cache !== null) {
    const localSearchData = cache[cacheKey.value as string] ?? {}
    ;(localSearchData.type || localSearchData.type === 0) && (tapKey.value = localSearchData.type)
  }
})

onMounted(() => {
  getProcessTypes()
  initUnreadMessage()
})

const showSizeChange = (current: number, pageSize: number) => {
  paginate.value.pageSize = pageSize
  getProcessesList()
}

const exportConfig = {
  common: exportProcess,
  exportTemplate: templateExportProcess,
}
const onExportProcess = async (exportType = 'common') => {
  if (!processData.value.length) {
    message.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const query = formData.value
  const params = { type: tapKey.value, ...query, processConfigId: +processTypeId.value }
  const res = await exportConfig[exportType](params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('下载成功，请在飞书查看！'))
}

const query = (val?: any) => {
  val && (formData.value = val)
  if (tapKey.value !== 7) getProcessesList()
  else getDraftList()
}

const getProcessTypes = async () => {
  try {
    const res = await GetTagAndNode()
    processTypeData.value = res.data
  } catch (error) {
    throw new Error(i18n.t('流程类型请求失败'))
  }
}

// 查询草稿
const getDraftList = async () => {
  loading.value = true
  processData.value = []

  const query = formData.value
  const params = { type: tapKey.value, ...query, processConfigId: +processTypeId.value }

  const [resProcessList, resCount] = await Promise.all([
    getDraftProcessInfo(params, paginate.value),
    getProccessQueryCount(params),
  ])
  reminds.value = resCount.data || {}
  processData.value = (resProcessList.data?.list ?? []) as unknown as ProcessItem[]
  paginate.value.total = resProcessList.data?.totalCount ?? 0
  loading.value = false
  const cacheValue = {
    ...params,
    ...{ pageNum: paginate.value.pageNum, pageSize: paginate.value.pageSize },
  }
  store.commit('local/SET_LOCAL_SEARCH_DATA', { [cacheKey.value as string]: cacheValue })
}

// 查询流程列表数据
const getProcessesList = async () => {
  loading.value = true
  processData.value = []

  const query = formData.value
  const params = { type: tapKey.value, ...query, processConfigId: +processTypeId.value }

  const [resProcessList, resCount] = await Promise.all([
    getProcessList(params, paginate.value),
    getProccessQueryCount(params),
  ])
  reminds.value = resCount.data || {}
  processData.value = resProcessList.data?.list ?? []
  paginate.value.total = resProcessList.data?.totalCount ?? 0
  loading.value = false
  const cacheValue = {
    ...params,
    ...{ pageNum: paginate.value.pageNum, pageSize: paginate.value.pageSize },
  }
  store.commit('local/SET_LOCAL_SEARCH_DATA', { [cacheKey.value as string]: cacheValue })
}

// 初始化未读信息
const initUnreadMessage = async () => {
  const { data = [] } = await getUnreadMessage({ processConfigId: processTypeId.value })
  unreadMsg.value = data
}
</script>

<style scoped lang="scss">
.process-list {
  .process-table-container {
    margin-top: 20px;
    background-color: white;
  }

  .process-table-wrapper {
    margin: 0 24px;
  }
}

.user-notice {
  position: absolute;
  display: flex;
  align-items: center;
  top: 240px;
  right: 0;
  width: 40px;
  height: 40px;
  padding-left: 10px;
  background: #5fa4f2;
  box-shadow: 0px 6px 14px 2px #58626e2e;
  border-radius: 4px 0px 0px 4px;
  box-sizing: border-box;
  transition: width 0.3s ease-in-out;

  &:hover {
    width: 140px;

    .__opacity {
      opacity: 1;
    }
  }

  > .__text {
    color: white;
    font-size: 16px;
    margin: 0 4px 0 6px;
    font-weight: 500;
    line-height: 1;
    overflow: hidden;
    white-space: nowrap;
  }

  .__opacity {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }
}

.fei-su-pagination {
  padding: 20px 0 !important;
}
</style>

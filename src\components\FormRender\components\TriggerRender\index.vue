<template>
  <div class="trigger-render">
    <div :id="id"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, markRaw, nextTick, watch } from 'vue'
import { init } from '@fs/trigger-engine'
import type { EngineCore, ISchema } from '@fs/trigger-core'
import { fetcher } from '../..'
import { uniqueId } from '@fs/trigger-utils'

interface IProps {
  schema: ISchema
  globalData: Record<string, unknown>
}

const props = defineProps<IProps>()
const id = ref(uniqueId('trigger'))
const scoped = ref<EngineCore>()
const initFlag = ref(false)

onMounted(() => {
  createEngine()
})

watch(
  () => props.globalData,
  newVal => {
    scoped.value?.updateData(newVal)
  }
)

watch(
  () => props.schema,
  newVal => {
    scoped.value?.updateSchema(newVal)
  }
)

const createEngine = () => {
  if (initFlag.value) return

  const data = (markRaw(props.globalData).data ?? {}) as Record<string, unknown>

  const engine = init({
    mode: 'view',
    selector: `#${id.value}`,
    schema: props.schema,
    data: markRaw(data),
    width: '100%',
    height: 'auto',
    fetcher,
  })

  scoped.value = engine
  initFlag.value = true

  console.log('scoped', scoped.value)

  nextTick(() => engine.render())
}

const getComponentByName = (name: string) => {
  const engine = scoped.value
  if (!engine) return console.warn('引擎实例不存在，请联系管理员查看！')
  const $doc = engine.document

  if (!$doc.nodesMap.has('ProcessForm')) return console.warn('表单组件不存在，请联系管理员查看！')
  const $form = $doc.nodesMap.get('ProcessForm')

  const $formRef = $form.componentRef
  if (!$formRef) return console.warn('表单组件实例不存在，请联系管理员查看！')

  return {
    submit: async () => {
      try {
        await ($formRef as any)?.validate?.()
        const data = ($formRef as any)?.formData?.()
        return data || {}
      } catch (error) {
        return Promise.reject(error)
      }
    },
    doAction: () => ({ code: 200 }),
  }
}

defineExpose({
  getScoped: () => ({
    scoped: scoped.value,
    getComponentByName,
  }),
})
</script>

<style scoped lang="scss">
.trigger-render {
  width: 100%;
  background-color: white;
}

:deep(.fs-form-item-control-input-content) {
  height: 100% !important;
}
</style>

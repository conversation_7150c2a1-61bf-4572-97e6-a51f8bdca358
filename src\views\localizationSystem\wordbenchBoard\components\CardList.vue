<template>
  <div class="card-list-container">
    <FSpin class="cust-spin" size="large" fix :spinning="loading">
      <header class="header">
        <FTooltip
          overlay-class-name="cust-desc-tip"
          color="#fff"
          :title="listInfo.ext || '--'"
          placement="top"
          :get-popup-container="popConfirmAttribute.getPopupContainer"
        >
          <div class="left">
            <span class="text">{{ listInfo.labelName }}</span
            ><span>·{{ totalCount }}</span>
          </div>
        </FTooltip>
        <div class="right">
          <i class="iconfont icontubiao_tianjia3" @click="goCreatePage"></i>
          <i :class="['iconfont', showList ? 'iconjiantoushang' : 'iconjiantouxia']" @click="handleShowList"></i>
        </div>
      </header>
      <div class="cust-scroll" v-show="showList" ref="box">
        <div class="list-item" @click="onJumpDemandDetial(item.id)" v-for="item in list" :key="item.id">
          <p class="msg">
            <span :class="['level', item.formData.isUrgent === '1' ? 'level1' : 'level2']">{{
              item.formData.isUrgent === '1' ? 'P0' : 'P1'
            }}</span>
            <span class="title" :title="item.topicName">{{ item.topicName }}</span>
          </p>
          <p class="num">{{ item.processInstanceCode }}</p>
          <div class="info">
            <p>
              <span :class="['status', getStatus(item).class]" :title="getStatus(item).label">{{
                getStatus(item).label
              }}</span>
              <span class="label" :title="item.currentMilepost.topicName">{{ item.currentMilepost.topicName }}</span>
              <span :class="['time', getTime(item).class]">{{ getTime(item).label }}</span>
            </p>
            <p style="margin-bottom: 0">
              <img class="avatar" :src="defaultfImg" />
              <span v-if="item.currentMilepost.superviser" class="name" :title="item.currentMilepost.superviser">{{
                item.currentMilepost.superviser
              }}</span>
            </p>
          </div>
        </div>
      </div>
    </FSpin>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, reactive, onMounted, onBeforeUnmount } from 'vue'
import { getProcessList } from '@/api/localizationSystem/wordbenchBoard'
import type { ListParams } from '@/types/localizationSystem/wordbenchBoard'
import { useRouter, useRoute } from 'vue-router'
import dayjs from 'dayjs'
import defaultfImg from '@/assets/images/head.png'
import { useI18n } from '@/utils'
const i18n = useI18n()

const props = defineProps({
  defaultShow: { type: Boolean, default: false },
  listInfo: { type: Object, default: () => ({}) },
})

const popConfirmAttribute = reactive({
  getPopupContainer: () => document.querySelector('.wordbench-board-container') as HTMLElement,
})
const router = useRouter()
const route = useRoute()
let list = ref<any[]>([])
let loading = ref<boolean>(false)
let showList = ref<boolean>(false)
let pageSize = ref<number>(10)
let pageNum = ref<number>(1)
let totalCount = ref<number>(0)
let noData = ref<boolean>(false)
const box = ref<HTMLElement | null>(null)

watch(
  () => props.defaultShow,
  val => {
    showList.value = val
  },
  { deep: true, immediate: true }
)
watch(
  () => props.listInfo,
  val => {
    pageSize.value = val.pageSize
    pageNum.value = val.pageNum
    list.value = val.list
    noData.value = !!val.noData
    totalCount.value = val.totalCount
  },
  { deep: true, immediate: true }
)

const handleReachBottom = async () => {
  try {
    loading.value = true
    if (noData.value && list.value.length) return
    const params: ListParams = {
      labelCode: props.listInfo.labelCode,
      pageSize: pageSize.value,
      pageNum: pageNum.value,
    }
    const res = await getProcessList(params)
    list.value = list.value.concat(res.data.list)
    if (res.data.totalCount > pageNum.value * pageSize.value) {
      pageNum.value++
    } else {
      noData.value = true
    }
    if (!list.value.length) {
      showList.value = false
    }
  } finally {
    loading.value = false
  }
}

const lazyLoading = (e: Event) => {
  const el = e.target as HTMLElement
  const scrollTop = el.scrollTop || 0
  const windowHeight = el.clientHeight
  const scrollHeight = el.scrollHeight
  if (scrollTop + windowHeight >= scrollHeight - 10 && !loading.value) {
    handleReachBottom()
  }
}

const goCreatePage = () => {
  const { href } = router.resolve({
    name: 'DemandAdd',
    params: {
      id: props.listInfo.addProcessConfigId,
      processDefineKey: props.listInfo.addProcessDefinekey,
    },
    query: {
      labelCode: props.listInfo.labelCode,
      localName: route.name as string,
    },
  })
  window.open(href, '_blank')
}

const onJumpDemandDetial = (id: any) => {
  const { href } = router.resolve({
    name: 'DemandHandle',
    params: { id },
  })
  window.open(href, '_blank')
}

const handleShowList = () => {
  showList.value = !showList.value
  if (!list.value.length) handleReachBottom()
}

const getStatus = (item: any) => {
  let attr: { class: string; label: string } = {
    class: '',
    label: '--',
  }
  if (item.status === 0) {
    attr = {
      label: i18n.t('进行中'),
      class: 'ongoing',
    }
  } else if (item.status === 1) {
    attr = {
      label: i18n.t('已完成'),
      class: 'complete',
    }
  } else if (item.status === 2) {
    attr = {
      label: i18n.t('已办结'),
      class: 'done',
    }
  }
  return attr
}

const getTime = (item: any) => {
  let attr: { class: string; label: string }
  // const currentNode = item?.currentMilepost || {}
  if (item.status === 0) {
    if (item?.completeTime) {
      if (dayjs(item.completeTime).diff(dayjs(new Date()), 'day') < 0) {
        attr = {
          label: i18n.t('已延期'),
          class: 'postpone',
        }
      } else {
        if (dayjs(item.completeTime).diff(dayjs(new Date()), 'day') > 1) {
          attr = {
            label: i18n.t('剩余') + Math.ceil(dayjs(item.completeTime).diff(dayjs(new Date()), 'day')) + i18n.t('天'),
            class: 'surplus',
          }
        } else {
          attr = {
            label: i18n.t('今天截止'),
            class: 'end-day',
          }
        }
      }
    } else {
      attr = {
        label: '--',
        class: 'wait',
      }
    }
  } else {
    attr = {
      label: (item.completeTime && dayjs(item.completeTime).format('M月DD日')) || '--',
      class: 'complete',
    }
  }
  return attr
}

onMounted(() => {
  box.value?.addEventListener('scroll', lazyLoading)
})

onBeforeUnmount(() => {
  box.value?.removeEventListener('scroll', lazyLoading)
})
</script>
<style lang="scss" scoped>
.card-list-container {
  width: calc(100% + -6px);
  position: relative;
  .header {
    display: flex;
    justify-content: space-between;
    padding: 11px 24px;
    margin-top: 8px;
    background: #ffffff;
    .left {
      display: flex;
      // flex: 1;
      max-width: 100%;
      height: 22px;
      line-height: 22px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      overflow: hidden;
      .text {
        width: auto;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -webkit-text-overflow: ellipsis;
      }
    }
    .right {
      display: flex;
    }
    .iconfont {
      width: 16px;
      height: 16px;
      color: #666666;
      cursor: pointer;
      &:first-child {
        margin-right: 8px;
      }
      &:hover {
        color: #333;
      }
    }
  }
  .cust-scroll {
    max-height: calc(88vh - 345px);
    width: calc(100% + 6px);
    overflow-y: scroll;
    &::-webkit-scrollbar {
      visibility: hidden;
      background: none;
    }
    &::-webkit-scrollbar-thumb {
      visibility: hidden;
      background: #eeeeee;
    }
    &::-webkit-scrollbar-track {
      background: none;
    }
    &:hover {
      &::-webkit-scrollbar {
        visibility: visible;
      }
    }
    &:hover {
      &::-webkit-scrollbar-thumb {
        visibility: visible;
      }
    }
    .list-item {
      position: relative;
      font-size: 12px;
      padding: 20px 24px 20px 24px;
      background-color: #fff;
      color: #666;
      cursor: pointer;
      &:first-child {
        border-top: 1px solid #eeeeee;
        margin-top: 0;
      }
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        width: calc(100% - 48px);
        border-bottom: 1px dashed #eeeeee;
      }
      &:last-child::after {
        border: none;
      }
      .msg {
        display: flex;
        color: #333333;
        margin-bottom: 0;
        .level {
          display: inline-block;
          height: 18px;
          line-height: 18px;
          font-weight: 500;
          padding: 0 4px;
          margin-right: 4px;
          border-radius: 2px;
          &.level1 {
            color: #f04141;
            background-color: #fdecec;
          }
          &.level2 {
            color: #fa830a;
            background-color: #fef4e9;
          }
          &.level3 {
            color: #febc2e;
            background-color: #fef4e9;
          }
        }
        .title {
          display: inline-block;
          height: 18px;
          line-height: 18px;
          font-weight: 500;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -webkit-text-overflow: ellipsis;
          &:hover {
            color: #378eef;
          }
        }
      }
      .num {
        height: 18px;
        line-height: 18px;
        margin-top: 2px;
        margin-bottom: 8px;
        color: #999;
      }
      .info {
        display: flex;
        flex-direction: column;
        margin-top: 8px;
        > p {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          .label {
            display: inline-block;
            max-width: 80px;
            height: 18px;
            line-height: 18px;
            padding: 0 4px;
            margin-right: 4px;
            background: #f8f8f8;
            border-radius: 2px;
            font-weight: 400;
            color: #999999;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            -webkit-text-overflow: ellipsis;
          }
          .status {
            width: 44px;
            height: 18px;
            margin-right: 4px;
            line-height: 18px;
            font-size: 12px;
            font-weight: 500;
            background: #fef4e9;
            border-radius: 2px;
            text-align: center;
            &.ongoing {
              color: #fa8f23;
              background-color: #fef4e9;
            }
            &.complete {
              color: #eafaf2;
              background-color: #2fcc83;
            }
            &.done {
              color: #378eef;
              background-color: #ebf3fd;
            }
          }
          .status {
            width: 44px;
            height: 18px;
            margin-right: 4px;
            line-height: 18px;
            font-size: 12px;
            font-weight: 500;
            background: #fef4e9;
            border-radius: 2px;
            text-align: center;
            &.ongoing {
              color: #fa8f23;
              background-color: #fef4e9;
            }
            &.complete {
              color: #eafaf2;
              background-color: #2fcc83;
            }
            &.done {
              color: #378eef;
              background-color: #ebf3fd;
            }
          }
          .time {
            height: 18px;
            line-height: 18px;
            padding: 0 4px;
            margin-right: 4px;
            border-radius: 2px;
            white-space: nowrap;
            &.complete {
              color: #999999;
              background-color: #f8f8f8;
            }
            &.wait {
              color: #999999;
              background-color: #f8f8f8;
            }
            &.postpone {
              color: #f04141;
              background-color: #fdecec;
            }
            &.surplus {
              color: #999999;
              background-color: #f8f8f8;
            }
            &.end-day {
              color: #ffa142;
              background-color: #fff8f1;
            }
          }
        }
        .name {
          display: inline-block;
          width: auto;
          height: 18px;
          line-height: 18px;
          color: #bbbbbb;
          font-size: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -webkit-text-overflow: ellipsis;
        }
        .avatar {
          width: 17px;
          height: 17px;
          margin-right: 4px;
        }
      }
    }
  }
  :deep(.cust-spin) {
    height: auto;
    bottom: -10px;
    opacity: 0.6;
    background-color: rgba(255, 255, 255, 0.22);
    .ivu-spin-main {
      top: 96%;
    }
  }
}
</style>

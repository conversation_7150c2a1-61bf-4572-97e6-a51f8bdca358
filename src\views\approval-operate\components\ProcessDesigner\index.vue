<template>
  <div class="process-designer-body">
    <div class="tip-box">
      <span class="tip-item">
        <span class="icon-tip success-tip"></span>
        {{ i18n.t('已完成') }}
      </span>
      <span class="tip-item">
        <span class="icon-tip warning-tip"></span>
        {{ i18n.t('进行中') }}
      </span>
      <span class="tip-item">
        <span class="icon-tip wait-tip"></span>
        {{ i18n.t('未开始') }}
      </span>
      <span class="tip-item">
        <span class="icon-tip no-tip"></span>
        {{ i18n.t('省略流程') }}
      </span>
    </div>

    <div class="attr-panel" v-if="edgeAction">
      <FCard>
        <div class="attr-panel-header">
          <p>属性面板</p>
        </div>
        <div class="attr-panel-content">
          <FForm>
            <FRow :gutter="8">
              <FCol :span="8">
                <FFormItem label="默认跳线">
                  <FInput
                    :bordered="false"
                    :value="edgeAction?.data?.properties?.['activiti:defaultflow'] ? '是' : '否'"
                  />
                </FFormItem>
              </FCol>
              <FCol :span="8">
                <FFormItem label="跳线条件">
                  <FInput :bordered="false" :value="edgeAction?.data?.properties?.['conditionsequenceflow'] || '无'" />
                </FFormItem>
              </FCol>
              <FCol :span="8">
                <FFormItem label="描述">
                  <FInput :bordered="false" :value="edgeAction?.data?.properties?.['description'] || '无'" />
                </FFormItem>
              </FCol>
            </FRow>
          </FForm>
        </div>
      </FCard>
    </div>

    <div id="designer" style="height: calc(100% - 34px)" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import LogicFlow, { BaseNodeModel, BaseEdgeModel, NodeData } from '@logicflow/core'
import { Menu, SelectionSelect, MiniMap } from '@logicflow/extension'
import BpmnElement from './extension/BpmnElements'
import Zoom from './extension/Zoom'
import { HandleNodeList } from '@/api'
import { useI18n } from '@/utils'

LogicFlow.use(Menu)
LogicFlow.use(MiniMap)
LogicFlow.use(SelectionSelect)
LogicFlow.use(BpmnElement)
LogicFlow.use(Zoom)

interface IProcessDesigner {
  actualNodeList?: HandleNodeList[]
  context?: any
}

const i18n = useI18n()
const props = defineProps<IProcessDesigner>()
const lf = ref<LogicFlow>()
const edgeAction = ref<BaseEdgeModel>()

onMounted(() => {
  const $designer = document.getElementById('designer')
  if (!$designer) throw new Error('设计器容器不存在！')
  if (!props?.context) throw new Error('流程信息不存在！')

  const context = props?.context
  // context.edges = (context?.edges ?? []).filter(item => (props?.actualNodeList ?? []).find(actualItem => actualItem.nodeId === item.sourceNodeId) && (props?.actualNodeList ?? []).find(actualItem => actualItem.nodeId === item.targetNodeId))
  // context.nodes = (context?.nodes ?? []).filter(item => (props?.actualNodeList ?? []).find(actualItem => actualItem.nodeId === item.id)).map(item => {
  context.nodes = (context?.nodes ?? []).map(item => {
    const custActualNodeStatus =
      ((props?.actualNodeList ?? []).find(actualItem => actualItem.nodeId === item.id) ?? {})?.status ?? undefined
    item.properties && (item.properties.custActualNodeStatus = custActualNodeStatus)
    return item
  })
  lf.value = new LogicFlow({
    container: $designer,
    isSilentMode: true,
    edgeType: 'bpmn:sequenceFlow',
    grid: { type: 'dot', config: { color: '#B6C4D3' } },
    edgeGenerator: (sourceNode: NodeData) => {
      if (sourceNode.type === 'bpmn:childrenTask') return 'bpmn:childrenTaskFlow'
    },
  })
  lf?.value?.render(context)

  lf?.value?.translateCenter()

  lf?.value?.fitView()

  // 点击空白处
  lf?.value?.on('blank:click', e => {
    edgeAction.value = undefined
  })

  // 点击节点
  lf?.value?.on('node:click', (node: BaseNodeModel) => {
    edgeAction.value = undefined
  })

  // lf?.value?.on('node:delete', e => {
  //   // console.log('e node:delete :>> ', e)
  // })

  // 点击连线
  lf?.value?.on('edge:click', (edge: BaseEdgeModel) => {
    edgeAction.value = edge
  })

  // lf?.value?.on('edge:delete', e => {
  //   // console.log('edge:delete :>> ', e)
  // })

  // lf?.value?.on('selection:selected', (selection: Array<BaseNodeModel | BaseEdgeModel>) => {
  //   // 框选
  //   // const nodes = selection.filter(item => item instanceof BaseNodeModel) as BaseNodeModel[]
  //   // const edges = selection.filter(item => item instanceof BaseEdgeModel) as BaseEdgeModel[]
  //   // console.log('框选 selection:selection', selection, nodes, edges)
  // })
})

onUnmounted(() => {
  lf?.value?.destroy?.()
})
</script>

<style scoped lang="scss">
.process-designer-body {
  position: relative;
  flex: 1;
  height: calc(100vh - 316px);
  padding: 16px 24px 24px;
  background: #ffffff;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  .tip-box {
    margin-bottom: 16px;
    .tip-item {
      margin-right: 32px;
      .icon-tip {
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }
      .success-tip {
        background: #2fcc83;
      }
      .warning-tip {
        background: #fa8f23;
      }
      .wait-tip {
        background: #cccccc;
      }
      .no-tip {
        background: #e3e3e3;
      }
    }
  }

  .attr-panel {
    position: absolute;
    width: calc(100% - 48px);
    bottom: 24px;
    z-index: 1;
    box-sizing: border-box;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);

    .attr-panel-header {
      > p {
        font-size: 16px;
      }
    }
  }
}
</style>

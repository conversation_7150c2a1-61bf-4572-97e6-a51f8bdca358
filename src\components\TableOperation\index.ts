import { ColumnType } from '@fs/smart-design/dist/ant-design-vue_es/table'
export interface IColunmProps {
  columns: any[]
  curColumnsCount?: number
  onChange?: (columns: any[]) => void
  onReset?: () => void
  [key: string]: any
}

export interface FColumnType extends ColumnType {
  key: string
  checked?: boolean
  name?: string
}
export type FColumnGroup = Record<'leftList' | 'list' | 'rightList', FColumnType[]>

/** 浅拷贝赋值列 */
export const cloneColumns = columns => {
  return columns.map(item => {
    if (item.checked !== false) item.checked = true
    return { ...item }
  })
}

export * from './tableColumnKeys'
export { default as useTableColumn } from './hooks/useTableColumn'

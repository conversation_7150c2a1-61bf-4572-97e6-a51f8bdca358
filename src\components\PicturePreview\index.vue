<template>
  <FImage
    ref="aImage"
    :width="200"
    :src="src"
    :style="{ display: 'none' }"
    :preview="{
      visible: imgVisible,
      onVisibleChange: setVisible,
    }"
  />
</template>
<script setup lang="ts">
import { ref } from 'vue'
const src = ref('')
const imgVisible = ref<boolean>(false)

const setVisible = (value: boolean): void => {
  imgVisible.value = value
}

const showImage = (e: any) => {
  if (e.target.nodeName === 'IMG') {
    src.value = e.target.currentSrc
    setVisible(true)
    console.log(src.value)
  }
  return
}
defineExpose({
  showImage,
})
</script>

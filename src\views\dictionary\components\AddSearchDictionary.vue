<template>
  <div class="create-creative" ref="createCreative">
    <FModal
      v-model:visible="dialogVisible"
      :title="title"
      :width="400"
      @cancel="close"
      :get-container="() => createCreative"
    >
      <div>
        <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical" v-bind="layout">
          <FFormItem :label="i18n.t('父级字典')" name="parentId">
            <FTreeSelect
              v-model:value="formState.parentId"
              :placeholder="i18n.t('请选择父级字典')"
              :tree-data="treeData"
              :fieldNames="{ label: 'name', value: 'id' }"
              allow-clear
            >
            </FTreeSelect>
          </FFormItem>
          <FFormItem :label="i18n.t('字典字段')" name="field">
            <FInput v-model:value="formState.field" :placeholder="i18n.t('请输入字典字段')" />
          </FFormItem>
          <FFormItem :label="i18n.t('字典名称')" name="name">
            <FInput v-model:value="formState.name" :placeholder="i18n.t('请输入字典名称')" />
          </FFormItem>
          <FFormItem :label="i18n.t('字典值')" name="value">
            <FInput v-model:value="formState.value" :placeholder="i18n.t('请输入字典值')" />
          </FFormItem>
          <FFormItem :label="i18n.t('描述信息')" name="describe">
            <FInput v-model:value="formState.describe" :placeholder="i18n.t('请输入描述信息')" />
          </FFormItem>
        </FForm>
      </div>
      <template #footer>
        <FConfigProvider :auto-insert-space-in-button="false">
          <FButton key="back" @click="close">{{ i18n.t('取消') }}</FButton>
          <FButton key="submit" type="primary" @click="handleOk(formRef)">{{ i18n.t('确定') }}</FButton>
        </FConfigProvider>
      </template>
    </FModal>
  </div>
</template>
<script setup lang="ts">
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { ref, reactive, watch } from 'vue'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { SearchDictionary } from '@/types/dictionary'
import { useI18n } from '@/utils'

const i18n = useI18n()
const formRef = ref<FormInstance>()
const createCreative = ref<HTMLElement>()

const props = defineProps({
  type: { type: String, default: '' },
  title: { type: String, default: '' },
  treeData: { type: Array, default: () => [] },
  record: { type: Object, default: () => ({}) },
  show: { type: Boolean },
})
const emit = defineEmits(['submit', 'popupClose'])
const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
}

const formState = reactive<SearchDictionary>({
  parentId: undefined,
  name: undefined,
  value: undefined,
  field: undefined,
  describe: undefined,
  status: 1,
})
const dialogVisible = ref(false)
const rules: Record<string, Rule[]> = {
  field: [{ required: true, message: i18n.t('请输入字典字段') }],
  name: [{ required: true, message: i18n.t('请输入字典名称') }],
  value: [{ required: true, message: i18n.t('请输入字典值') }],
}

const close = () => {
  emit('popupClose')
}
const handleOk = async (formRef: FormInstance | undefined) => {
  if (!formRef) {
    return
  }
  await formRef
    .validate()
    .then(() => {
      emit('submit', formState)
    })
    .catch((e: any) => {
      // console.log(e)
    })
}
watch([() => props.show, () => props.type], newValue => {
  dialogVisible.value = newValue[0]
  formRef.value?.resetFields()
  if (newValue[1] == 'edit') {
    formState.name = props.record.name
    formState.value = props.record.value
    formState.parentId = props.record.parentId || undefined
    formState.id = props.record.id
    formState.field = props.record.field
    formState.processConfigId = props.record.processConfigId
    formState.describe = props.record.describe
  } else {
    formState.name = undefined
    formState.value = undefined
    formState.parentId = undefined
    formState.id = undefined
    formState.field = undefined
    formState.processConfigId = undefined
    formState.describe = undefined
  }
})
</script>
<style scoped lang="scss">
:deep(.fs-modal-body) {
  .fs-form-item .fs-btn {
    padding: 0 24px !important;
  }
  .fs-input {
    padding-left: 8px !important;
  }
}
</style>

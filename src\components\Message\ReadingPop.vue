<template>
  <div class="reading-box-wrap">
    <FPopover trigger="hover" word-wrap v-if="props.readingList.length > 0">
      <span v-if="props.reading" class="is-read">{{ props.readingList.length }}{{ i18n.t('人已读') }}</span>
      <span v-else class="no-read">{{ props.readingList.length }}{{ i18n.t('人未读') }}</span>

      <template #content>
        <div>
          <span class="read-name" v-for="(read, index) in props.readingList" :key="index"
            >{{ (read as any).receiver }}
            <span v-if="readingList.length - 1 != index">、</span>
          </span>
          <span class="read-status" v-if="props.readingList.length > 0">
            <template v-if="props.reading">{{ i18n.t('已读') }}</template>
            <template v-else>{{ i18n.t('未读') }}</template>
          </span>
        </div>
      </template>
    </FPopover>
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from '@/utils'
const props = defineProps({
  readingList: {
    type: Array,
    default: () => [],
  },
  reading: {
    type: Boolean,
  },
})

const i18n = useI18n()
</script>
<style lang="scss" scoped>
.reading-box-wrap {
  .is-read {
    color: #2fcc83;
    padding-right: 10px;
  }
  .no-read {
    color: #999999;
  }
}
.read-name {
  font-size: 12px;
  padding-right: 4px;
  color: #666666;
  display: inline-block;
  word-wrap: break-word;
}
.read-status {
  color: #bbbbbb;
  font-size: 12px;
}
</style>

/**
 * @description 子应用数据结构
 * @interface IApplication
 * @property {string} parent 父应用名称
 * @property {string} title 应用标题
 * @property {string} name 应用名称
 * @property {string} host 应用域名
 * @property {string} route 应用路由
 * @property {string} path 应用路由路径
 */
export interface IApplication {
  id: string
  title: string
  name: string
  host: string
  route: string
}

//
// 公共表格组件
// --------------------------------

import type { ITask } from '@/types/handle'

export enum TableColumnTypeKeys {
  default = 'default', // 默认
  custom = 'custom', // 自定义
  operate = 'operate', // 操作
}

// 表格列配置
export interface ITableColumn {
  field?: string // 字段名称
  label: string // 字段标签
  type: TableColumnTypeKeys // 字段类型
  props: Record<string, unknown> // 字段属性
  component?: unknown // 组件名称
}

// 分页
export interface IPage {
  page: number // 当前页
  pageSize: number // 每页条数
  total: number // 总条数
}
export interface BasicFetchResponse {
  code: number
  msg: string
}
export interface BasicPageParams {
  pageNum: number
  pageSize: number
  total?: number
}

export interface UserInfo {
  avatar: string
  created_at: string
  email: string
  group_uuid: string
  id: number
  name: string
  updated_at: string
  uuid: string
}

export interface IModel<T = ITask> {
  flag: boolean
  loading?: boolean
  data: T
  [key: string]: unknown
}

export interface IHistoryRecord {
  fullPath: string
  title: string
}

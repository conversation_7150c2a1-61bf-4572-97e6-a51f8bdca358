<template>
  <FSpin :spinning="loading" :tip="`${i18n.t('加载中')}...`">
    <FCard :body-style="containerBodyStyle">
      <!-- 标题 -->
      <template #title>
        <div class="card-header">
          <div class="flex">
            <span class="icon iconfont icon-margin">&#xe7d1;</span>
            <h4 class="card-header-title">{{ `${i18n.t('我的工作台')} · ${total}` }}</h4>
          </div>
          <div class="card-header-right">
            <FButton class="_btn" type="link" size="small" @click="handleSeeAllProcessClick">{{
              i18n.t('查看全部流程')
            }}</FButton>
          </div>
        </div>
      </template>

      <!-- 资源列表 -->
      <ResourceTypeList :data="resourceData" @click="handleResourceTypeClick" />

      <!-- 主内容 -->
      <div class="workbench-main">
        <div class="workbench-header">
          <h5 class="workbench-header-title">{{ currResourceData?.name }} · {{ currResourceData?.size }}</h5>
          <div class="workbench-header-right">
            <FButton type="primary" @click="createProcessFlag = true">
              <i class="iconxinzeng iconfont marginR5 fontSize14"></i>{{ i18n.t('新建需求') }}
            </FButton>
          </div>
        </div>
        <div class="workbench-content">
          <!-- 流程列表 -->
          <CustomTable :data-source="processData" />
          <div class="fei-su-pagination" style="padding-bottom: 0">
            <span class="fontSize12">{{ i18n.t('共') }} {{ paginate.total }} {{ i18n.t('条') }}</span>
            <FPagination
              v-model:current="paginate.pageNum"
              v-model:pageSize="paginate.pageSize"
              :total="paginate.total"
              @change="showSizeChange"
              show-size-changer
            />
          </div>

          <!-- 新增弹框 -->
          <CreateProcessModal v-model="createProcessFlag" :is-detail="false" />
        </div>
      </div>
    </FCard>
  </FSpin>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import ResourceTypeList, { IResourceTypeItem } from '../ResourceTypeList/index.vue'
import CustomTable from '../../../process-list/components/ContentBox/CustomTable.vue'
import CreateProcessModal from '@/components/CreateProcessModal/index.vue'
import { getAllTypeProcessInstance, getProcessList } from '@/api'
import { IProcess } from '@/types/handle'
import { BasicPageParams } from '@/types/common'
import { useI18n } from '@/utils'

const i18n = useI18n()
const router = useRouter()
const containerBodyStyle = reactive({ padding: '0 !important', display: 'flex', flexDirection: 'row' })

const processData = ref<IProcess[]>([])
const resourceData = ref<IResourceTypeItem[]>([])
const currResourceData = ref<IResourceTypeItem>()
const createProcessFlag = ref(false)
const loading = ref(true)
const paginate = ref<BasicPageParams>({ pageNum: 1, pageSize: 10, total: 0 })

const total = computed(() => resourceData.value.reduce((acc, cur) => acc + cur.size, 0))

onMounted(() => {
  init()
})

// 初始化
const iconList: Record<string, string> = {
  MM市场管理: '&#xe7e7;',
  MTL流程: '&#xe7de;',
  IPD流程: '&#xe7d8;',
  '1N流程': '&#xe7db;',
  LTC流程: '&#xe7e2;',
  'OTD-现场交付': '&#xe7e4;',
  ITR流程: '&#xe7df;',
  供应链开发流程: '&#xe7d7;',
  CS流程: '&#xe7e6;',
  人力资源流程: '&#xe7dc;',
  财务流程: '&#xe7e3;',
  DSTE流程: '&#xe7e9;',
  备用流程: '&#xe7e8;',
}

const init = async () => {
  const { data = [] } = await getAllTypeProcessInstance()
  resourceData.value = (data ?? []).map(item => ({
    id: item.processConfigId,
    name: item.processName,
    size: item.instanceCount,
    process: item.instanceList,
    icon: iconList[item.processName] ?? iconList['备用流程'],
  }))
  currResourceData.value = resourceData.value?.[0] || {}
  // processData.value = data?.[0]?.instanceList || []
  handleResourceTypeClick(currResourceData.value)
  // loading.value = false
}

// 查看全部流程
const handleSeeAllProcessClick = () => {
  router.push({ name: 'ProcessList' })
}

// 资源列表点击事件
const handleResourceTypeClick = async (data: IResourceTypeItem) => {
  try {
    loading.value = true
    if (!data?.id) return
    currResourceData.value = data
    const res = (await getProcessList({ processConfigId: +data.id, type: 1 }, paginate.value)) as any
    processData.value = res.data?.list ?? []
    paginate.value.total = res.data?.totalCount ?? 0
  } finally {
    loading.value = false
  }
  // currResourceData.value = data
  // processData.value = data.process as IProcess[]
}

const showSizeChange = (current: number, pageSize: number) => {
  paginate.value.pageSize = pageSize
  handleResourceTypeClick(currResourceData.value)
}
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  .card-header-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  ._btn {
    border: none !important;
    padding: 0 !important;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .icon-margin {
    margin-right: 8px;
    font-size: 22px;
    color: #000;
  }
}

.workbench-main {
  flex: 1;
  padding: 16px 24px 16px 16px;

  .workbench-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    line-height: 32px;
    margin-bottom: 16px;

    .workbench-header-title {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }

  .workbench-content {
    max-height: 650px;
    overflow-y: auto;
  }
  .marginR8 {
    margin-right: 8px;
  }
}
</style>

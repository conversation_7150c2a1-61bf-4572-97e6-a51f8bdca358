<template>
  <FCard :head-style="COMPONENT_CRAD_BODY_STYLE" :body-style="COMPONENT_CRAD_BODY_STYLE">
    <template #title>{{ i18n.t('操作记录') }}</template>
    <OperationLogs style="margin-top: 20px" :instance-id="instanceId" />
  </FCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { COMPONENT_CRAD_BODY_STYLE } from '../../config'
import OperationLogs from './OperationLogs.vue'
import type { IProcess } from '@/types/handle'
import { useI18n } from '@/utils'

interface IProps {
  processInfo: IProcess[]
  other: unknown
}

const i18n = useI18n()
const porps = defineProps<IProps>()
const instanceId = computed(() => porps.processInfo[0]?.instanceId)
</script>

<style lang="scss" scoped></style>

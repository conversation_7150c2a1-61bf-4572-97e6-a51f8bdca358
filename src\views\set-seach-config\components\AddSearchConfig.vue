<template>
  <FModal
    v-model:visible="dialogVisible"
    :title="title"
    :width="720"
    :confirm-loading="submitLoading"
    @cancel="close"
    @ok="handleOk(formRef)"
    :bodyStyle="{ maxHeight: '70vh', overflow: 'scroll' }"
  >
    <div class="process-search-modal-wrapper">
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FRow :gutter="[24, 0]">
          <FCol :span="12">
            <FFormItem :label="i18n.t('字段标识')" name="field">
              <!-- <JsonData v-model:value="formState.fieldJson" :min="1" :max="1" /> -->
              <FInput v-model:value="formState.field" :maxlength="25" :placeholder="i18n.t('请输入字段标识')" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('字段名称')" name="name">
              <FInput v-model:value="formState.name" :maxlength="25" :placeholder="i18n.t('请输入字段名称')" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('检索类型')" name="searchType">
              <FSelect
                :placeholder="i18n.t('请选择')"
                v-model:value="formState.searchType"
                :options="searchTypeList"
                :field-names="{ label: 'name', value: 'id' }"
                allow-clear
              />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('组件类型')" name="moduleType">
              <FSelect
                :placeholder="i18n.t('请选择')"
                v-model:value="formState.moduleType"
                :options="moduleTypeList"
                :field-names="{ label: 'name', value: 'id' }"
                allow-clear
              />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('数据源')" name="url">
              <FInput v-model:value="formState.url" :placeholder="i18n.t('请输入')" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('值类型')" name="valueType">
              <FSelect
                :placeholder="i18n.t('请选择')"
                v-model:value="formState.valueType"
                :options="valueTypeList"
                allow-clear
              />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem label="映射字典">
              <FSelect
                :placeholder="i18n.t('请选择')"
                v-model:value="dictValue"
                :options="filedDictionaryList"
                optionFilterProp="label"
                show-search
                allow-clear
              />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('是否启用')" name="status">
              <FSwitch v-model:checked="formState.status" :checkedValue="1" :unCheckedValue="0" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('是否折叠')" name="fold">
              <FSwitch v-model:checked="formState.fold" :checkedValue="1" :unCheckedValue="0" />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('组件配置')" name="propsConfig" class="fei-su-textarea">
              <FTextarea
                v-model:value="formState.propsConfig"
                style="min-height: 88px"
                :placeholder="i18n.t('请输入组件配置')"
              />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('备注')" name="remarks" class="fei-su-textarea">
              <FTextarea
                v-model:value="formState.remarks"
                style="min-height: 88px"
                :maxlength="100"
                :placeholder="i18n.t('请输入备注')"
              />
            </FFormItem>
          </FCol>
        </FRow>
      </FForm>
    </div>
  </FModal>
</template>
<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from 'vue'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { ITitle } from '@/types/searchList'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { useI18n, deepClone } from '@/utils'
import { selectFieldDictionary } from '@/api'
// import JsonData from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomJsonData/components/JsonData/index.vue'

const i18n = useI18n()

interface Itme {
  id: string
  name: string
}

interface FormState {
  field?: string
  id: number | undefined
  moduleType: string | undefined
  name: string
  processConfigId: number | undefined
  remarks: string
  searchType: string | undefined
  status: number
  url: string
  valueType: number | undefined
  propsConfig: string | undefined
  fold: number | undefined
  fieldJson: string
}
const formRef = ref<FormInstance>()
const props = defineProps({
  title: { type: String, default: '' },
  show: { type: Boolean },
  record: { type: Object, default: () => ({}) },
  submitLoading: { type: Boolean },
})
const emit = defineEmits(['submit', 'popupClose'])

const formState = reactive<FormState>({
  field: '',
  id: undefined,
  moduleType: undefined,
  name: '',
  processConfigId: undefined,
  remarks: '',
  searchType: undefined,
  status: 0,
  url: '',
  valueType: undefined,
  propsConfig: undefined,
  fold: 1,
  fieldJson: '',
})
const dictValue = ref<string | number | undefined>()
const dialogVisible = ref<boolean>(false)
const valueTypeList = computed<any[]>(() => [
  { value: 1, label: i18n.t('字符串类型') },
  { value: 2, label: i18n.t('数字类型') },
])
const searchTypeList = computed<Itme[]>(() => [
  { id: 'eq', name: i18n.t('等值') },
  { id: 'like', name: i18n.t('模糊') },
  { id: 'gt', name: i18n.t('大于') },
  { id: 'lt', name: i18n.t('小于') },
  { id: 'range', name: i18n.t('范围') },
])
const moduleTypeList = computed<Itme[]>(() => [
  { id: 'ONE_LINE_INPUT', name: i18n.t('单行文本框') },
  { id: 'RANGE_NUMBER', name: i18n.t('区间数字') },
  { id: 'SELECT', name: i18n.t('下拉框') },
  { id: 'MULTIPLE_SELECT', name: i18n.t('下拉多选框') },
  { id: 'TREE_SELECT', name: i18n.t('树形下拉框') },
  { id: 'DATE_PICKER', name: i18n.t('时间日期') },
  { id: 'RANGE_PICKER', name: i18n.t('区间日期') },
  { id: 'SYSTEM_FIELDS', name: i18n.t('系统字段') },
])

// const validatorKeys = reactive<Record<string, any>>({
//   fieldJson: async rule => {
//     await nextTick()
//     const data = formState?.[rule?.field]
//     if (data && Object.keys(JSON.parse(data)).some(key => !key.trim())) {
//       return Promise.reject('key不能为空格')
//     }
//     return Promise.resolve()
//   },
// })

const rules: Record<string, Rule[]> = {
  // fieldJson: [{ required: true, validator: validatorKeys.fieldJson }],
  field: [{ required: true, message: '请输入字段标识' }],
  name: [{ required: true, message: i18n.t('请输入字段名称') }],
  searchType: [{ required: true, message: i18n.t('请选择组件检索类型') }],
  moduleType: [{ required: true, message: i18n.t('请选择组件类型') }],
  url: [{ required: false, message: i18n.t('请输入数据源') }],
  status: [{ required: true, message: i18n.t('请选择是否启用') }],
  fold: [{ required: true, message: i18n.t('请选择是否启用') }],
  remarks: [{ required: true, message: i18n.t('请选择备注') }],
  valueType: [{ required: true, message: i18n.t('请选择值类型') }],
}
const filedDictionaryList = ref([])

const close = () => {
  formRef.value?.resetFields()
  Object.assign(formState, {
    field: '',
    id: undefined,
    moduleType: '',
    name: '',
    processConfigId: undefined,
    remarks: '',
    searchType: '',
    status: 0,
    url: '',
    valueType: undefined,
    propsConfig: undefined,
    fold: 1,
    fieldJson: '',
  })
  dictValue.value = undefined
  emit('popupClose')
}
const handleOk = async (formRef: FormInstance | undefined) => {
  if (!formRef) {
    return
  }
  await formRef.validate()
  const params = deepClone(formState)
  params['fieldJson'] = JSON.stringify({ [params.field]: dictValue?.value ?? '' })
  delete params.field
  emit('submit', params, props.title)
}

watch([() => props.show, () => props.title], newValue => {
  formRef.value?.resetFields()
  dialogVisible.value = newValue[0]
  if (newValue[1] == ITitle.edit) {
    Object.assign(formState, props.record)
    dictValue.value =
      (props?.record?.fieldJson && JSON.parse(props.record.fieldJson)?.[props?.record?.field]) || undefined
  } else {
    Object.assign(formState, {
      field: '',
      id: undefined,
      moduleType: undefined,
      name: '',
      processConfigId: undefined,
      remarks: '',
      searchType: undefined,
      status: 0,
      url: '',
      valueType: undefined,
      propsConfig: undefined,
      fold: 1,
      fieldJson: '',
    })
    dictValue.value = undefined
  }
})

const getFiledDictionaryListFn = async () => {
  const res = await selectFieldDictionary({ input: '' })
  filedDictionaryList.value = (res?.data ?? []).map(item => ({ label: item?.title, value: item?.field }))
}

onMounted(() => {
  requestIdleCallback(getFiledDictionaryListFn)
})
</script>

<style scoped lang="scss">
.process-search-modal-wrapper {
  :deep(.fs-form-item-control-input-content) {
    height: auto !important;
  }
}
</style>

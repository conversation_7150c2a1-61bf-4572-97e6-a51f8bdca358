<template>
  <div class="search-content-container">
    <div class="content-box flex" :class="[showMoreFlag && 'show-more']">
      <div class="left-box" ref="searchcontentBox">
        <template v-for="item of search.options" :key="item.componentValueKey">
          <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue" />
        </template>
      </div>
      <div class="right-box flex">
        <div class="more-box flex ml4" v-show="showMoreBtn" @click="showMoreFlag = !showMoreFlag">
          <span>
            {{ (showMoreFlag && i18n.t('收起')) || i18n.t('展开') }}
          </span>
          <i class="icon iconjiantouxia" :class="[(showMoreFlag && 'iconjiantoushang') || 'iconjiantouxia']"></i>
        </div>
        <div class="more-box flex ml8" v-show="search.clearFlag" @click="onClearFn">
          <span>
            {{ i18n.t('清空全部') }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from '@/utils'
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { Search } from './search'
import usePublish from '@/hooks/usePublish'

const { hasEnvPublish } = usePublish()

interface IProps {
  queryData: any
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:queryData'])
const i18n = useI18n()
const searchcontentBox = ref<HTMLElement>()
const showMoreFlag = ref<boolean>(false)
const showMoreBtn = ref<boolean>(false)
const search = new Search()
const configList = [
  {
    componentName: 'FSelect',
    componentValueKey: 'type',
    componentAttrs: {
      class: 'width120 mr12 marginB24',
      pressLine: computed(() => i18n.t('飞书通知')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      optionFilterProp: 'label',
      allowClear: true,
      options: computed(() => [{ label: i18n.t('飞书通知'), value: 1 }]),
      onChange: (value: any, option: any) => {
        queryData.value = search.getParams()
      },
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'status',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('状态')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      optionFilterProp: 'label',
      allowClear: true,
      options: computed(() => [
        { label: i18n.t('禁用'), value: 0 },
        { label: i18n.t('启用'), value: 1 },
      ]),
      onChange: (value: any, option: any) => {
        queryData.value = search.getParams()
      },
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'publishStatus',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('发布状态')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      optionFilterProp: 'label',
      allowClear: true,
      options: computed(() => [
        { label: i18n.t('已发布'), value: 1 },
        { label: i18n.t('未发布'), value: 0 },
      ]),
      onChange: (value: any, option: any) => {
        queryData.value = search.getParams()
      },
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'feishuId',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => i18n.t('飞书模板ID')),
      placeholder: computed(() => i18n.t('请输入')),
      allowClear: true,
      type: 'search-clear',
      onSearch: () => {
        queryData.value = search.getParams()
      },
      onClear: () => {
        queryData.value = search.getParams()
      },
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'queryInput',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => i18n.t('快速搜索')),
      placeholder: computed(() => i18n.t('请输入')),
      allowClear: true,
      type: 'search-clear',
      onSearch: () => {
        queryData.value = search.getParams()
      },
      onClear: () => {
        queryData.value = search.getParams()
      },
    },
  },
]
const queryData = computed({
  get: () => props.queryData,
  set: val => emits('update:queryData', val),
})

const onClearFn = () => {
  search.clear()
  nextTick(() => {
    queryData.value = search.getParams()
  })
}

const handleResize = () => {
  const { clientHeight = 0 } = searchcontentBox.value || {}
  if (clientHeight > 57) {
    showMoreBtn.value = true
  } else {
    showMoreFlag.value = false
    showMoreBtn.value = false
  }
}
const setSearchConfigFn = () => {
  search.initOptions(configList)
  !hasEnvPublish?.value && search.deteleOptions(['publishStatus'])
  search.clear()
  showMoreFlag.value = false
  nextTick(() => {
    handleResize()
    queryData.value = search.getParams()
  })
}

onMounted(() => {
  setSearchConfigFn()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.search-content-container {
  .content-box {
    align-items: flex-start;
    padding-top: 6px;
    max-height: 57px;
    overflow: hidden;
    :deep(.fs-input-affix-wrapper) {
      padding: 6px 8px !important;
    }
    &.show-more {
      max-height: 100%;
    }
    .right-box {
      height: 30px;
      align-items: center;
      color: #378eef;
      cursor: pointer;
      .more-box {
        align-items: center;
      }
      span {
        white-space: nowrap;
      }
    }
    :deep(.tip-color) {
      border-color: #378eef;
    }
  }
}
</style>

<template>
  <FTooltip
    color="#fff"
    overlay-class-name="cust-flow-tip"
    :overlay-style="{ width: 'auto', maxWidth: 'none' }"
    placement="right"
    :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
  >
    <div :class="['flow-chart-item', props.active ? 'active' : '']">
      <Lamp :type="props.type" />
      <span>{{ props.name }}</span>
      <span v-if="[4, 5].includes(type)" :class="['tag', statusData[type as keyof typeof statusData]?.class || '']">
        {{ statusData[type as keyof typeof statusData]?.label || '--' }}
      </span>
    </div>
    <template #title>
      <div class="flow-box">
        <div class="title-box">
          <Lamp :type="props.type" />
          <span class="name">{{ name }}</span>
          <span
            v-if="statusData[type as keyof typeof statusData]"
            :class="['tag', statusData[type as keyof typeof statusData]?.class || '']"
          >
            {{ statusData[type as keyof typeof statusData]?.label || '--' }}
          </span>
        </div>
        <div class="time-box" v-if="Number(invalid)">
          <span class="label">{{ i18n.t('预计完成时间') }}：</span>
          <span>{{ (forcastTime && dayjs(forcastTime).format('YYYY-MM-DD')) || '--' }}</span>
          <span v-if="getTime()" :class="['time', getTime()?.class]">{{ getTime()?.label }}</span>
        </div>
        <div class="superviser">
          <img class="avatar" :src="defaultfImg" />
          <span class="name" :title="superviser">{{ superviser }}</span>
        </div>
      </div>
    </template>
  </FTooltip>
</template>

<script setup lang="ts">
import Lamp from '../Lamp/index.vue'
import dayjs from 'dayjs'
import defaultfImg from '@/assets/images/head.png'
import { computed } from 'vue'
import { useI18n } from '@/utils'
const i18n = useI18n()

interface IProps {
  name: string
  type: number
  active?: boolean
  forcastTime: string
  superviser: string
  invalid: number
  overdueDuration?: number | null
  remainderDuration?: number | null
  realityDuration?: number | null
}

const props = defineProps<IProps>()

const statusData = computed(() => ({
  0: {
    class: 'wait',
    label: i18n.t('未开始'),
  },
  1: {
    class: 'processing',
    label: i18n.t('进行中'),
  },
  2: {
    class: 'processing',
    label: i18n.t('进行中'),
  },
  3: {
    class: 'success',
    label: i18n.t('已完成'),
  },
  4: {
    class: 'close',
    label: i18n.t('已办结'),
  },
  5: {
    class: 'delay',
    label: i18n.t('延期'),
  },
}))

const getTime = () => {
  let attr: { class: string; label: string } | undefined
  if ([2].includes(props.type)) {
    if (props.overdueDuration) {
      attr = {
        label: i18n.t('延期') + props.overdueDuration + i18n.t('天'),
        class: 'delay',
      }
    } else {
      attr = {
        label: i18n.t('剩余') + props.remainderDuration + i18n.t('天'),
        class: 'surplus',
      }
    }
  }
  if ([3, 4, 5].includes(props.type)) {
    attr = {
      label: i18n.t('耗时') + props.realityDuration + i18n.t('天'),
      class: 'success',
    }
  }
  return attr
}
</script>

<style scoped lang="scss">
.cust-flow-tip {
  .flow-box {
    padding: 10px 8px;
    .title-box {
      margin-bottom: 16px;
      .name {
        display: inline-block;
        height: 22px;
        font-size: 14px;
        color: #333;
        line-height: 22px;
      }
    }
    .time-box {
      height: 18px;
      font-size: 12px;
      font-weight: 400;
      color: #333;
      line-height: 18px;
      .label {
        padding-left: 0;
        font-weight: 400;
        color: #999;
      }
      .time {
        display: inline-block;
        height: 24px;
        line-height: 24px;
        padding: 0 8px;
        margin-left: 14px;
        border-radius: 3px;
        white-space: nowrap;
        &.surplus {
          color: #fa8f23;
          background: #fef4e9;
        }
        &.delay {
          color: #f04141;
          background: #fdecec;
        }
        &.success {
          color: #2fcc83;
          background: #eafaf2;
        }
      }
    }
    .superviser {
      display: flex;
      margin-top: 12px;
      .avatar {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
      .name {
        display: inline-block;
        height: 18px;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
      }
    }
  }
}
.tag {
  display: inline-block;
  height: 18px;
  padding: 0 8px;
  margin-left: 8px;
  line-height: 18px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
  box-sizing: border-box;
}
.wait {
  color: #666666;
  background: #f8f8f8;
}
.processing {
  color: #fa8f23;
  background: #fef4e9;
}
.success {
  color: #2fcc83;
  background: #eafaf2;
}
.close {
  background: #eafaf2;
  border: 1px solid #d5f4e6;
  color: #2fcc83;
  line-height: 17px;
}
.delay {
  border: 1px solid #fcd9d9;
  background: #fdecec;
  color: #f04141;
}
.flow-chart-item {
  position: relative;
  display: flex;
  height: 40px;
  line-height: 1;
  padding: 0 12px;
  margin-left: 42px;
  align-items: center;
  color: #333;
  font-size: 14px;
  border-radius: 6px;
  border: 1px solid #e1e8f0;
  box-sizing: border-box;
  background-color: white;
  white-space: nowrap;
  cursor: pointer;
  white-space: nowrap;

  &::after {
    position: absolute;
    right: calc(100% + 1px);
    width: 42px;
    border-bottom: 1px dashed #cbd5e1;
    content: '';
  }

  &::before {
    position: absolute;
    left: -4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 1px solid white;
    background-color: #cbd5e1;
    content: '';
  }

  &:hover {
    border: 1px solid #5fa4f2;
  }

  &:first-child {
    &::after,
    &::before {
      display: none;
    }
  }

  &.active {
    border: 1px solid #378eef;
  }

  .__tag {
    margin: 0 0 0 8px !important;
    height: 18px;
    line-height: 18px;
    color: #f04141;
    border-radius: 3px;
    border: 1px solid #fcd9d9;
    background-color: #fcd9d9;
  }
}
</style>

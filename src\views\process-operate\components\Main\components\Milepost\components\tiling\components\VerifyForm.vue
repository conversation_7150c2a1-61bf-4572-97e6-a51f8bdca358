<template>
  <!--审核人表单-->
  <div class="review-container">
    <div class="review-header marginB16">
      <span :class="['color999', 'marginR5', !props.task.approverRoleCode ? 'marginR24' : '']"
        >{{ i18n.t('审核人') }}:</span
      >
      <span class="review-person color333">
        {{ props.task.approver }}
        <span class="marginR24 center-middle" v-if="props.task.approverRoleCode">
          - &nbsp;{{ transformRoleText(props.task.approverRoleCode) }}
        </span>
      </span>
      <span class="color999 marginR5">{{ i18n.t('审核结论') }}：</span>
      <FRadioGroup v-model:value="formState.isPass">
        <FRadio :value="1">{{ i18n.t('通过') }}</FRadio>
        <FRadio :value="0">{{ i18n.t('不通过') }}</FRadio>
      </FRadioGroup>
    </div>
    <div class="review-body">
      <!--多行文本域-->
      <FForm layout="vertical" class="marginB16">
        <FFormItem :label="i18n.t('评审说明')" name="contentData" class="fs-textarea">
          <FTextarea v-model:value="formState.verifyDesc" :placeholder="i18n.t('请输入')" />
        </FFormItem>
      </FForm>
      <!--提交按钮-->
      <FButton type="primary" @click="onSubmitReview(props.task)" class="review-submit">{{ i18n.t('提交') }}</FButton>
    </div>
    <div class="review-footer" v-if="props.task.formKey">
      <div class="footer-tip">
        <span class="blue-line"></span>
        <span class="color333 fontWeigtht500 tip-info">{{ i18n.t('处理信息') }}</span>
      </div>
      <div class="footer-form">
        <!--查看态表单渲染-->
        <TaskForm :task="props.task" v-if="!!props.task.formKey"></TaskForm>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import TaskForm from './TaskForm.vue'
import { ITask } from '@/types/handle'
import { transformFieldIdToLabel } from '@/utils/filter'
import { IProcessRoleAndUser } from '@/types/request'
import { inject, reactive } from 'vue'
import { checkMission, rejectMission } from '@/api'
import { messageInstance as message } from '@fs/smart-design'
import { useI18n } from '@/utils'

interface IProps {
  task: ITask
  role?: IProcessRoleAndUser[]
  verifyDesc: string
}
interface FormState {
  verifyDesc: string
  isPass: string | number
}

const i18n = useI18n()
const props = defineProps<IProps>()
const formState = reactive<FormState>({ verifyDesc: props.verifyDesc, isPass: 1 })
const paramsWrapper = inject('paramsWrapper') as (data: any) => any
const initToDoList = inject('initToDoList') as () => void
const refresh = inject('refresh') as () => void
const transformRoleText = (text: number | string) => {
  return transformFieldIdToLabel(props.role as Array<any>, 'roleCode', 'roleName', text)
}
const onSubmitReview = (task: ITask) => {
  let action, data
  if (formState.isPass) {
    action = checkMission
    data = paramsWrapper({
      id: task.id,
      contentData: { verifyDesc: formState.verifyDesc },
    })
  } else {
    action = rejectMission
    data = {
      id: task.id,
      contentData: { verifyDesc: formState.verifyDesc },
    }
  }
  action(data).then(async res => {
    if (res.code == 200) {
      message.success(i18n.t('保存成功'))
      await refresh()
      initToDoList && (await initToDoList())
    } else {
      message.error(res.msg)
    }
  })
}
</script>

<style lang="scss" scoped>
.review-container {
  .review-header {
    .review-person {
      margin-right: 52px;
    }
  }
  .review-body {
    .review-submit {
      margin-bottom: 32px;
    }
  }
  .review-footer {
    .footer-tip {
      .blue-line {
        display: inline-block;
        width: 2px;
        height: 12px;
        border: 1px solid #378eef;
        margin-right: 3px;
      }
      .tip-info {
        margin-bottom: -4px;
      }
    }
    .footer-form {
      margin-left: -12px;
    }
  }
}
</style>

<template>
  <div class="process-detail-data-container">
    <FDrawer
      v-model:visible="visible"
      :get-container="target"
      :title="i18n.t('资料详情')"
      :width="960"
      placement="right"
    >
      <div class="header">
        <span class="title">{{ info?.docName ?? '--' }}</span>
        <span class="line"></span>
        <span>
          <span>{{ i18n.t('资料ID：') }}</span>
          <span>{{ info?.docNumber ?? '--' }}</span>
        </span>
        <span class="line"></span>
        <span>
          <span>{{ i18n.t('创建人：') }}</span>
          <span>{{ info?.creatBy ?? '--' }}</span>
        </span>
        <span class="line"></span>
        <span>{{ (info?.creatTime && transformDate(info.creatTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--' }}</span>
        <span class="line"></span>
        <FTag :border="false" :color="(info.status === 0 && 'error') || (info.status === 1 && 'success')">
          {{ (info.status === 0 && i18n.t('已禁用')) || (info.status === 1 && i18n.t('已启用')) }}
        </FTag>
      </div>
      <div class="nav">
        <span
          :class="['nav-item', (navActive === item.value && 'nav-active') || '']"
          v-for="item in navList"
          :key="item.value"
          @click="handleNavFn(item)"
          >{{ item.label }}</span
        >
      </div>
      <FTable
        :data-source="list"
        :columns="columns"
        :loading="tableLoading"
        table-layout="fixed"
        :scroll="{ x: '100%', y: 'calc(100vh - 484px)' }"
        :pagination="{
          total: pagination.total,
          current: pagination.currPage,
          pageSize: pagination.pageSize,
          showTotal: (total: number) => `${i18n.t('共')}${total}${i18n.t('条')}`,
          showQuickJumper: true,
          onChange: onPaginationChangeFn
        }"
      >
        <template #bodyCell="{ text, record, column }">
          <template v-if="['updateTime'].includes(column.dataIndex)">
            <span>{{ (text && transformDate(text, 'YYYY-MM-DD HH:mm:ss')) || '--' }}</span>
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <TipBtn :tip-title="i18n.t('下载')">
              <i class="iconfont icontubiao_xiazai cursor hover-btn" @click="downloadFn(record?.docFile ?? [])"></i>
            </TipBtn>
          </template>
        </template>
      </FTable>
    </FDrawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { getDocumentPageByGroupIdList } from '@/api'
import { useI18n, transformDate, download, batchDownload } from '@/utils'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'

const i18n = useI18n()
const navList = ref([{ label: i18n.t('迭代信息'), value: 'IterationTable' }])
const navActive = ref('IterationTable')
const info = ref<any>({})
const target = ref(() => document.querySelector('.process-detail-data-container'))
const visible = ref(false)
const tableLoading = ref<boolean>(false)
const list = ref<any[]>([])
const pagination = reactive({
  total: 0,
  currPage: 1,
  pageSize: 10,
})
const columns = ref([
  {
    title: i18n.t('文件名称'),
    dataIndex: 'docName',
    width: 90,
  },
  {
    title: i18n.t('版本号'),
    dataIndex: 'version',
    width: 120,
  },
  {
    title: i18n.t('操作人'),
    dataIndex: 'updateBy',
    width: 100,
  },
  {
    title: i18n.t('操作时间'),
    dataIndex: 'updateTime',
    width: 130,
  },
  {
    title: i18n.t('操作'),
    dataIndex: 'handle',
    width: 60,
  },
])

const handleNavFn = item => {
  navActive.value = item.value
}

const downloadFn = list => {
  const fileList = [],
    linkList = []
  list.forEach(item => {
    item.url && fileList.push(item)
    item.link && linkList.push(item)
  })
  fileList?.length === 1 && download(fileList[0].url, fileList[0].fileName)
  fileList?.length > 1 && batchDownload(fileList.map(item => ({ url: item.url, fileName: item.fileName })))
}

const onGetListFn = async () => {
  try {
    tableLoading.value = true
    const params = {
      correlateInstanceId: info?.value?.correlateInstanceId || undefined,
      groupId: info?.value?.groupId || undefined,
      currPage: pagination.currPage,
      pageSize: pagination.pageSize,
    }
    const res = await getDocumentPageByGroupIdList(params)
    list.value = res?.data?.list || []
    pagination.total = res?.data?.totalCount || 0
  } finally {
    tableLoading.value = false
  }
}

const onPaginationChangeFn = (page: number, pageSize: number) => {
  pagination.currPage = page
  pagination.pageSize = pageSize
  onGetListFn()
}

const open = data => {
  visible.value = true
  info.value = data
  onGetListFn()
}
defineExpose({
  open,
})
</script>
<style lang="scss" scoped>
.process-detail-data-container {
  .header {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    font-size: 12px;
    color: #333333;
    margin-bottom: 24px;
    .title {
      font-weight: bold;
      font-size: 14px;
    }
    .line {
      width: 1px;
      height: 18px;
      margin: 0 8px;
      background-color: #eeeeee;
    }
  }
  .nav {
    margin-bottom: 16px;
    border-bottom: 1px solid #eeeeee;
    .nav-item {
      position: relative;
      display: inline-block;
      padding: 5px 16px;
      background: #f8f8f8;
      border-radius: 3px 3px 0px 0px;
      border: 1px solid #eeeeee;
      border-bottom: none;
      margin-right: 4px;
      cursor: pointer;
      &.nav-active {
        background: #ffffff;
        &::after {
          content: '';
          position: absolute;
          left: 0;
          bottom: -1px;
          width: 100%;
          height: 1px;
          background-color: #ffffff;
        }
      }
    }
  }
  :deep(.fs-drawer-title) {
    font-weight: bold;
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  :deep(.fs-table-tbody) {
    .fs-table-cell {
      &:empty {
        &::after {
          content: '--';
        }
      }
      &.fs-table-cell-fix-right-first {
        &:empty {
          &::after {
            content: '';
          }
        }
      }
    }
  }
  :deep(.fs-pagination) {
    margin-bottom: 0;
  }
}
</style>

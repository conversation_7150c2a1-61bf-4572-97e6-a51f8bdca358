<template>
  <div class="field-detail-container">
    <div class="header">
      <FInput
        style="width: 240px"
        :press-line="i18n.t('快速搜索')"
        allow-clear
        :placeholder="i18n.t('字段名称/字段编码')"
        type="search-clear"
        @search="onSearch"
        @clear="onClear"
      />
      <FButton size="small" class="add-btn" @click="addFieldFn">
        <i class="iconxinzeng iconfont"></i>{{ i18n.t('新增字段') }}
      </FButton>
    </div>
    <FTable
      :data-source="filterFieldList"
      :loading="tableLoading"
      :columns="columns"
      table-layout="fixed"
      :pagination="false"
      :scroll="{ x: '100%', y: 'calc(100vh - 360px)' }"
    >
      <template #bodyCell="{ column, record }">
        <template
          v-if="['status', 'isUpdate', 'isNull', 'isPrimaryKey', 'isSystem', 'isShow'].includes(column.dataIndex)"
        >
          <span :class="[record[column.dataIndex] === 1 && 'off', record[column.dataIndex] === 0 && 'open']">{{
            (record[column.dataIndex] === 0 && i18n.t('是')) || (record[column.dataIndex] === 1 && i18n.t('否')) || '--'
          }}</span>
        </template>
        <template v-if="column.dataIndex === 'handle'">
          <FTooltip>
            <template #title>{{ i18n.t('编辑') }}</template>
            <i
              class="iconfont iconfont-hover cursor color4677C7 icontubiao_xietongbianji mr12"
              @click="editFieldFn(record)"
            ></i>
          </FTooltip>
          <FPopconfirm
            :title="i18n.t('确定操作数据删除吗？')"
            :content="i18n.t('操作删除后可能影响功能，请谨慎操作！')"
            :ok-text="i18n.t('确定')"
            :cancel-text="i18n.t('取消')"
            @confirm="DeleteFilled(record)"
          >
            <span
              class="iconfont iconfont-hover cursor color4677C7 icontubiao_shanchu1 marginR5"
              :title="i18n.t('删除')"
            ></span>
          </FPopconfirm>
        </template>
      </template>
    </FTable>
    <AddFieldModal :type="type" v-model:visible="visible" :data="fieldData" @getFieldList="getFieldList" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message } from '@fs/smart-design'
import { FieldList } from '@/types/entity'
import { useI18n, deepClone } from '@/utils'
import { columns } from './tableConfig'
import { getFieldConfig, delEntityFieldById } from '@/api'
import AddFieldModal from './components/AddFieldModal/index.vue'

type propsType = {
  id?: number | string
}

const i18n = useI18n()
const props = defineProps<propsType>()
const emits = defineEmits(['update:loading'])
const search = ref<string>('')
const allFieldList = ref<FieldList[]>([])
const filterFieldList = ref<FieldList[]>([])
const tableLoading = ref<boolean>(false)
const type = ref<boolean>(false) // true 新增 false 编辑
const visible = ref<boolean>(false)
const fieldData = ref<FieldList>() // true 新增 false 编辑

const onSearch = (searchValue: string) => {
  search.value = searchValue
  filterFieldList.value = allFieldList.value.filter(
    item =>
      (item.fieldName as string).toLowerCase().indexOf(searchValue.toLowerCase()) >= 0 ||
      (item.fieldCode as string).toLowerCase().indexOf(searchValue.toLowerCase()) >= 0
  )
}

const onClear = () => {
  filterFieldList.value = deepClone(allFieldList.value)
}

const addFieldFn = () => {
  visible.value = true
  type.value = true
  fieldData.value = { entityId: Number(props.id) }
}

const editFieldFn = (record: FieldList) => {
  visible.value = true
  type.value = false
  fieldData.value = record
}

const DeleteFilled = async (record: FieldList) => {
  const res = await delEntityFieldById(record.id as number)
  if (res.code !== 200) throw new Error(res.msg)
  getFieldList()
  message.success(i18n.t('删除成功'))
}

const getFieldList = async () => {
  try {
    tableLoading.value = true
    const res = await getFieldConfig(props.id as number)
    allFieldList.value = res?.data || []
    onSearch(search.value)
  } finally {
    tableLoading.value = false
  }
}

watch(
  () => props.id,
  val => {
    val && getFieldList()
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.field-detail-container {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    .add-btn {
      padding: 0 6px !important;
      font-size: 12px !important;
      i {
        font-size: 14px;
      }
    }
  }
  .mr12 {
    margin-right: 12px;
  }
}
</style>

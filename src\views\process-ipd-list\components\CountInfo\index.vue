<template>
  <div class="count-info-content">
    <div class="flex">
      <img src="@/assets/images/process-icon/completed.svg" class="mr8" />
      <span>已完成</span>
      <span class="code-link" @click="onJumpDemandList">（{{ data?.completedCount ?? 0 }}条）</span>
    </div>
    <div class="flex">
      <img src="@/assets/images/process-icon/inProgress.svg" class="mr8" />
      <span>进行中</span>
      <span class="code-link" @click="onJumpDemandList">（{{ data?.inProgressCount ?? 0 }}条）</span>
    </div>
    <div class="flex">
      <img src="@/assets/images/process-icon/notStarted.svg" class="mr8" />
      <span>待开始</span>
      <span class="code-link" @click="onJumpDemandList">（{{ data?.notStartedCount ?? 0 }}条）</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { BAESURL } from '@/utils'
import { useStore } from 'vuex'
interface IProps {
  data: any
  processInstanceCode: any
}

const props = defineProps<IProps>()
const store = useStore()

const onJumpDemandList = () => {
  const cache = store.getters['local/getLocalSearchData'] || {}
  const localSearchData = cache[props?.data?.processConfigId] ?? {}
  store.commit('local/SET_LOCAL_SEARCH_DATA', {
    [props?.data?.processConfigId]: Object.assign({}, localSearchData, {
      processConfigId: props?.data?.processConfigId,
      searchObj: Object.assign({}, localSearchData?.searchObj ?? {}, {
        ['like:syn_ipd_number:1']: props?.processInstanceCode,
      }),
    }),
  })
  window.open(`${BAESURL}/bpm-manage/process/${props?.data?.processConfigId}/list`)
}
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.code-link {
  cursor: pointer;
  color: #378eef;
}
.mr8 {
  margin-right: 8px;
}
</style>

<template>
  <span>
    <FPopover trigger="hover" overlay-class-name="popover-select-notes">
      <template #content>
        <div class="file-box">
          <p class="fontS12" style="margin-bottom: 5px">{{ i18n.t('附件') }}</p>
          <div v-for="file in props.data" :key="file.resourceKey" style="margin-bottom: 5px">
            <a class="file-ellipsis fontS12">
              <span class="iconfont fontS12 file-icon">&#xe655;</span> {{ file.fileName }}
            </a>
          </div>
        </div>
      </template>
      <slot name="icon">
        <a> <span class="iconfont fontS12">&#xe656;</span> {{ i18n.t('附件') }} </a>
      </slot>
    </FPopover>
  </span>
</template>
<script setup lang="ts">
import { useI18n } from '@/utils'

const i18n = useI18n()
const props = defineProps({
  item: {
    type: Object,
    default: null,
  },
  index: {
    type: [String, Number],
    default: '',
  },
  data: {
    type: [Object, Array],
    default: () => [],
  },
})
</script>
<style lang="scss"></style>

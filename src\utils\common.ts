import dayjs from 'dayjs'
import { message } from '@fs/smart-design'
import { FEISHU_APPID, GOTO_URL, isFeishu, initMicroApp, getPower, logout, S3URL } from '.'

import type { IRes } from '@fs/hooks'
import { unref, type Ref } from 'vue'
import type { Router } from 'vue-router'
import { downloadFileList } from '@/api'

export const updatePower = async () => {
  try {
    const power = getPower()
    await power.getUserPower()
    await initMicroApp(power)
  } catch (errRes: unknown) {
    // 如果获取权限失败，清除 token 和 ticket，跳转到 uums 登陆
    if ((errRes as IRes).code === 401) logout()
  }
}

// 飞书授权
export const feishuOAuth = () => {
  const url = encodeURI(`${window.location.protocol}//${window.location.host}?redirect=${window.location.pathname}`)
  window.location.href = `https://open.feishu.cn/open-apis/authen/v1/authorize?app_id=${FEISHU_APPID}&redirect_uri=${url}`
}

export const gotoSSO = () => {
  if (isFeishu) return feishuOAuth()

  const { origin, pathname = '' } = window.location
  const redirectUrl = encodeURIComponent(`${origin}${pathname}`)
  const url = `${GOTO_URL}?redirect_url=${redirectUrl}`
  window.location.href = url
}

export const useI18n = () => ({
  t: (key: string) => key,
})

// 时间转换器
export const transformDate = (
  date: number | string | dayjs.Dayjs | null | undefined,
  format = 'YYYY-MM-DD HH:mm:ss'
) => {
  if (!date) return '--'
  return dayjs(date).format(format)
}

// 获取 html 片段中的文本
export const getHTMLSpingtext = (str: string) => {
  const $div = document.createElement('div')
  $div.innerHTML = str
  return $div.textContent || $div.innerText || '--'
}

// 深拷贝
export const deepClone = <T extends Record<string, any>>(obj: T): T => {
  if (!obj || typeof obj !== 'object') return obj

  let clonedObj: T
  if (Array.isArray(obj)) {
    clonedObj = obj.map(item => deepClone(item)) as unknown as T
  } else {
    clonedObj = {} as T
    for (const key in obj) {
      clonedObj[key] = deepClone(obj[key])
    }
  }
  return clonedObj
}

//防抖函数
// eslint-disable-next-line @typescript-eslint/ban-types
export const debounce = <T extends Function>(fn: T, timeout: number | Ref<number> = 300, immediate = false) => {
  let timmer: number | null
  const wait = unref(timeout)
  return (...args: unknown[]) => {
    timmer && clearTimeout(timmer)
    if (immediate) {
      if (!timmer) fn(...args)
      timmer = setTimeout(() => (timmer = null), wait)
    } else {
      timmer = setTimeout(() => fn(...args), wait)
    }
  }
}

// 文件预览
export const previewFile = (url: string, name: string) => {
  const httpUrl = `${S3URL}/api/s3/getFileByUrl?url=${encodeURIComponent(url)}&filename=${encodeURIComponent(
    name
  )}&type=1`
  window.open(httpUrl, '_blank')
}

const limitFileTypes = ['PDF', 'JPG', 'JPEG', 'PNG', 'DOC', 'DOCX', 'PPT', 'PPTX', 'XLS', 'XLSX', 'TXT', 'ZIP', 'RAR']

export const fileLimit = (file: any) => {
  const fileType = file.name.replace(/.+\./, '')
  const allowTypes = limitFileTypes
  const allowSize = 2 * 1024
  if (allowTypes.indexOf(fileType.toUpperCase()) < 0) {
    message.error('文件类型错误')
    return false
  }
  if (file.size / (1024 * 1024) > allowSize) {
    message.error('最大文件2G')
    return false
  } else {
    return true
  }
}

// 文件下载
export const download = (url: string, name: string) => {
  const httpUrl = `${S3URL}/api/s3/getFileByUrl?url=${encodeURIComponent(url)}&filename=${encodeURIComponent(name)}`
  window.open(httpUrl, '_blank')
}

export const batchDownload = async (fileList: unknown[]) => {
  const res = await downloadFileList(fileList)
  const blob = new Blob([res as any], { type: 'application/zip' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `file${new Date()}.zip`
  a.click()
  URL.revokeObjectURL(url)
}

// 跳转到 需求处理 页面的通用方法
export const jumpToDemand = (id: string | number, processTypeId: string | number, targer = false, router: Router) => {
  const options = { name: 'ProcessDetail', params: { id } }

  if (!targer) router.push(options)
  else window.open(router.resolve(options).href, '_blank')
}

export const trimObjectStrings = obj => {
  const result = {}
  for (const key in obj) {
    const val = obj[key]
    result[key] = typeof val === 'string' ? val.trim() : val
  }
  return result
}

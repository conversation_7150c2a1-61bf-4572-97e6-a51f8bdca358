<template>
  <div class="base-info-container">
    <FormRender
      v-if="formKey || BaseMilepost?.formKey"
      :id="formKey || BaseMilepost.formKey"
      type="view"
      :data="TFFormData(BaseMilepost)"
    />
    <!-- 附件 -->
    <MilepostAppendix :milepost="BaseMilepost" />
  </div>
</template>

<script setup lang="ts">
import { markRaw, computed } from 'vue'
import { IProcess } from '@/types/handle'
import FormRender from '@/components/FormRender/index.vue'
import MilepostAppendix from '@/views/process-detail/components/HandleInfo/components/MilepostAppendix/index.vue'

interface IProps {
  processInfo: IProcess[]
  formKey?: string | undefined
}

const props = defineProps<IProps>()

const BaseMilepost = computed(() => props.processInfo[0] || {}) // 需求信息

// const TFFormType = (milepost: IProcess) => (formKeymilepost.status === 2 && milepost.milepostRole == 1 ? 'edit' : 'view')
const TFFormData = (milepost: IProcess) => {
  // 剔除一些基本用不到又可能会更新的字段，减少表单重新渲染的次数，表单重新渲染会造成页面抖动
  // eslint-disable-next-line prettier/prettier, @typescript-eslint/no-unused-vars
  const {
    completeTime,
    contentData,
    createdTime,
    creator,
    updatedTime,
    nodeStartTime,
    planTime,
    children,
    processInstanceCode,
    instanceTopicName,
    processType,
    instanceId,
    formData,
    ...data
  } = milepost
  return markRaw({
    envData: {
      processInstanceCode,
      instanceTopicName,
      processType,
      instanceId,
    },
    envDefaultFormData: formData,
  })
}
</script>

<style lang="scss" scoped>
.base-info-container {
  margin: 12px;
}
</style>

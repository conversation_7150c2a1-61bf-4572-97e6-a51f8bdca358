<template>
  <div class="process-query-form">
    <FForm layout="inline" :model="formData">
      <FFormItem v-if="!props.single">
        <FSelect
          v-model:value="formData.processType"
          :press-line="i18n.t('流程类型')"
          show-search
          allow-clear
          style="width: 120px"
          :placeholder="i18n.t('请选择')"
          :options="processTypeData"
          :filter-option="filterOption"
          @change="handleProcessTypeChange"
        />
      </FFormItem>
      <FFormItem>
        <FSelect
          v-model:value="formData.tags"
          mode="multiple"
          :press-line="i18n.t('需求标签')"
          style="width: 120px"
          show-search
          allow-clear
          :placeholder="i18n.t('请选择')"
          max-tag-count="responsive"
          :options="tagData"
          @change="onSearch"
        />
      </FFormItem>
      <FFormItem>
        <FSelect
          v-model:value="formData.currNodes"
          :press-line="i18n.t('当前节点')"
          show-search
          allow-clear
          style="width: 120px"
          :options="milepostData"
          :placeholder="i18n.t('请选择')"
          @change="onSearch"
        />
      </FFormItem>
      <FFormItem>
        <FSelect
          v-model:value="formData.isUrgent"
          :press-line="i18n.t('优先级')"
          allow-clear
          style="width: 120px"
          :options="[
            { value: '0', label: i18n.t('一般') },
            { value: '1', label: i18n.t('加急') },
          ]"
          :placeholder="i18n.t('请选择')"
          @change="onSearch"
        />
      </FFormItem>
      <FFormItem>
        <FCascader
          v-model:value="formData.member"
          class="project-cust-cascader"
          dropdown-class-name="project-cust-cascader-dropdown"
          multiple
          show-arrow
          :placeholder="i18n.t('请输入')"
          :press-line="i18n.t('项目成员')"
          max-tag-count="responsive"
          show-checked-strategy="SHOW_CHILD"
          :field-names="{ label: 'name', value: 'uuid', children: 'departmentChildrens' }"
          :show-search="{ filterOption }"
          :options="memberData"
          :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
          @change="onChangePerson"
        />
      </FFormItem>
      <FFormItem>
        <FRangePicker
          v-model:value="formData.timeRange"
          style="width: 230px; height: 32px"
          allow-clear
          format="YYYY-MM-DD"
          @change="onSearch"
        />
      </FFormItem>
    </FForm>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { messageInstance as message } from '@fs/smart-design'
import { GetTagAndNode, getDepartment } from '@/api'
import { IProcessClassType, IProcessClassTypeMilepost, IProcessClassTypeTag } from '@/types/handle'
import { useI18n } from '@/utils'

const i18n = useI18n()

interface IProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  modelValue: IFormData
  single?: boolean
}

interface IOptions<T = null> {
  label: string
  value: unknown
  data: T
}

interface IFormData {
  processType?: number
  tags?: string[]
  currNodes?: string[]
  isUrgent?: string[]
  member?: string[]
  timeRange?: string[]
  other?: string
}

const props = defineProps<IProps>()

const formData = reactive<IFormData>({})
const processTypeData = ref<IOptions<IProcessClassType>[]>([])
const tagData = ref<IOptions<IProcessClassTypeTag>[]>([])
const milepostData = ref<IOptions<IProcessClassTypeMilepost>[]>([])
const memberData = ref<IOptions<any>[]>([])

onMounted(() => {
  initFormData(props.modelValue)

  getProcessTypes()
  getDepartmentUser()
})

const initFormData = (initData?: IFormData) => {
  if (!initData) return
  formData.processType = props.modelValue.processType
  formData.tags = props.modelValue.tags
  formData.currNodes = props.modelValue.currNodes
  formData.isUrgent = props.modelValue.isUrgent
  formData.member = props.modelValue.member
  formData.timeRange = props.modelValue.timeRange
  formData.other = props.modelValue.other
}

const handleProcessTypeChange = () => {
  if (!formData.processType) return
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const currProcessType = processTypeData.value.find(item => item.value == formData.processType)!.data

  formData.tags = undefined
  formData.currNodes = undefined

  tagData.value =
    currProcessType?.dictionarys?.map(item => ({
      label: item.name,
      value: `$.${item.field}:${item.value}`,
      data: item,
    })) ?? []

  milepostData.value =
    currProcessType?.nodes?.map(item => ({
      label: item.milepostName,
      value: item.nodeId,
      data: item,
    })) ?? []
}

const onChangePerson = () => {
  console.log(11)
}
const onSearch = () => {
  console.log(11)
}

const getDepartmentUser = async () => {
  try {
    const res = await getDepartment()
    memberData.value = TFDepartmentUser(res.data)
  } catch (error: any) {
    throw new Error(i18n.t('部门成员：') + error)
  }
}

const getProcessTypes = async () => {
  try {
    const res = await GetTagAndNode()
    processTypeData.value = (res.data ?? []).map(item => ({ label: item.processName, value: item.id, data: item }))
  } catch (error: any) {
    throw new Error(i18n.t('流程类型：') + error)
  }
}

// 数据转换
const TFDepartmentUser = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      let data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && TFDepartmentUser(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

// 默认过滤
const filterOption = (input: string, options: IOptions) =>
  options.label.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
</script>

<style scoped lang="scss">
.process-query-form {
  display: flex;
  align-items: center;
  padding: 8px 20px 0 20px;
  height: 72px;
  border-radius: 4px;
  background: #ffffff;
  box-shadow: 0px 5px 15px 0px #dfe2e6cc;
  box-sizing: border-box;

  :deep(.project-cust-cascader) {
    width: 120px !important;
    .fs-select-selector {
      height: 32px;
    }
  }
  :deep(.project-cust-cascader-dropdown) {
    padding-right: 0 !important;
    .fs-cascader-menu {
      border-right-color: #eee;
      &:last-child {
        border-right: none;
      }
      .fs-cascader-menu-item {
        margin-top: 2px;
        &[aria-checked='true'] {
          background: #ebf3fd;
          border-radius: 3px;
        }
      }
      .fs-cascader-menu-item-active[aria-checked='true'] {
        background: #ebf3fd;
        border-radius: 3px;
      }
    }
  }
}
</style>

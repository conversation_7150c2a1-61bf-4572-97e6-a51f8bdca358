{"name": "bpm-project-manage", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:m": "vue-cli-service build --mode m", "build:pre": "vue-cli-service build --mode pre", "build:test": "vue-cli-service build --mode test", "build:staging": "vue-cli-service build --mode staging", "build:sit": "vue-cli-service build --mode sit", "build:compliance": "vue-cli-service build --mode compliance", "build:localtest": "vue-cli-service build --mode localtest", "eslint": "eslint \"./src/**/*.{js,jsx,ts,tsx,vue}\"", "prettier": "prettier --write \"./src/**/*.{js,jsx,ts,tsx,vue}\""}, "lint-staged": {"src/**/*.{ts,vue}": ["pnpm eslint", "pnpm prettier", "git add ."]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@fs/hooks": "^1.3.1", "@fs/i18n": "^0.2.2", "@fs/request": "0.0.25", "@fs/smart-design": "1.0.9-beta.0", "@fs/translator": "^0.0.5", "@logicflow/core": "^1.2.27", "@logicflow/extension": "^1.2.27", "@vueup/vue-quill": "1.0.0-alpha.40", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@wangeditor/plugin-mention": "^1.0.0", "@fs/trigger-actions": "^1.0.36", "@fs/trigger-core": "^1.0.36", "@fs/trigger-engine": "^1.0.36", "@fs/trigger-materials": "^1.0.36", "@fs/trigger-plugin-panel-dock": "^1.0.36", "@fs/trigger-setters": "^1.0.36", "@fs/trigger-utils": "^1.0.36", "axios": "^0.27.2", "clipboard": "^2.0.11", "cron-parser": "^5.2.0", "dayjs": "^1.11.7", "echarts": "^5.4.1", "fs-menu": "^1.0.10", "fs-speed": "1.1.10", "lodash": "^4.17.21", "lodash.throttle": "^4.1.1", "mitt": "^3.0.0", "normalize.css": "^8.0.1", "quill-blot-formatter": "^1.0.5", "quill-image-uploader": "^1.2.3", "quill-magic-url": "^4.2.0", "quill-mention": "^3.1.0", "sortablejs": "^1.15.2", "v-viewer": "^3.0.10", "vue": "^3.2.45", "vue-router": "^4.0.3", "vue-scrollto": "^2.20.0", "vuedraggable": "^4.1.0", "vuex": "^4.0.0"}, "devDependencies": {"@sentry/webpack-plugin": "^2.16.0", "@types/node": "^18.11.0", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "commitlint": "^17.0.3", "commitlint-config-cz": "^0.13.3", "cz-customizable": "^6.9.0", "eslint": "^8.19.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.2.0", "husky": "4", "lint-staged": "10", "prettier": "^2.7.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "typescript": "^4.5.4", "vue-eslint-parser": "^9.0.3", "vue-tsc": "^0.34.7", "webpack-bundle-analyzer": "^4.10.2"}}
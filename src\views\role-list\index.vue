<template>
  <FSpin :spinning="loading" :tip="i18n.t('加载中...')">
    <div class="fei-su-main">
      <div class="fei-su-main-box">
        <div class="fei-su-main-left">
          <div class="fei-su-left-title flex-space-between">
            <span>{{ i18n.t('角色列表') }} </span>
            <span class="color-bule cursor" @click="addARole(1)">{{ i18n.t('新增角色') }} </span>
          </div>
          <div class="input-box">
            <FInput
              v-model:value="search"
              @blur="fetchData(1)"
              @press-enter="fetchData(1)"
              allow-clear
              class="w240"
              :placeholder="i18n.t('请输入名称搜索')"
            >
              <template #suffix>
                <i v-if="!search" class="iconfont iconsousuo form-search-icon fontSize12" />
              </template>
            </FInput>
          </div>
          <div class="process-type-box">
            <FSpin :spinning="listLoading" :tip="i18n.t('加载中...')">
              <div v-if="roleList.length > 0">
                <div
                  v-for="(item, index) in roleList"
                  :key="index"
                  :class="['item', { selectd: item.id === modelValue }]"
                  @click="onSelectHandel(item)"
                >
                  {{ item.roleName }}
                </div>
              </div>
              <FEmpty v-else :image="require(`@/assets/images/empty.png`)" />
            </FSpin>
          </div>
        </div>
        <div class="fei-su-main-right">
          <div class="select-options flex-space-between">
            <div>
              <span class="marginR10">{{ i18n.t('角色详情') }} </span>
              <span class="role-label">{{
                roleType && i18n.t('角色类型：') + IRoleType[roleType as keyof typeof IRoleType]
              }}</span>
            </div>
            <div class="color-bule">
              <!-- <span class="marginR10 cursor" @click="roleConfirm(1)"> 启用角色 </span>
            <span class="marginR10 cursor" @click="roleConfirm(2)"> 停用角色 </span> -->
              <span class="marginR10 cursor" @click="roleConfirm(0)"> {{ i18n.t('删除角色') }} </span>
              <span class="marginR10 cursor" @click="addARole(2)"> {{ i18n.t('编辑角色') }} </span>
            </div>
          </div>
          <div class="fei-su-card">
            <div class="card-btn">
              <div class="marginB16 flex-space-between">
                <span>{{ i18n.t('人员清单') }} </span>
                <div class="color-bule">
                  <span class="marginR10 cursor" @click="addUser()">{{ i18n.t('添加人员') }} </span>
                  <span class="marginR10 cursor" @click="addElsx()">{{ i18n.t('批量导入') }} </span>
                  <input
                    type="file"
                    style="display: none"
                    ref="inputFileRef"
                    accept=".xlsx, .xls, .csv"
                    @change="onUpload"
                  />
                </div>
              </div>
              <FTable :data-source="dataList" :columns="columns" :pagination="false" :loading="loading">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'roleCode'">
                    <span>{{ presentRole?.roleCode }}</span>
                  </template>
                  <template v-if="column.key === 'conditionExpression'">
                    <span v-if="!record.conditionExpression || record.conditionExpression.length < 30">{{
                      record.conditionExpression
                    }}</span>
                    <template v-else>
                      <FTooltip>
                        <template #title>{{ record.conditionExpression }}</template>
                        {{ record.conditionExpression.substring(0, 30) }}...
                      </FTooltip>
                    </template>
                  </template>
                  <template v-if="column.key === 'action'">
                    <span class="color-bule cursor" @click="roleConfirm(3, record)">{{ i18n.t('移除') }} </span>
                    <span
                      v-if="roleType && roleType === 3"
                      class="marginL4 color-bule cursor"
                      @click="addUser(true, record)"
                      >{{ i18n.t('编辑') }}
                    </span>
                  </template>
                </template>
                <template #emptyText>
                  <img :src="noImgUrl" />
                  <p class="colorBBB fontSize12 fei-su-tip">{{ i18n.t('暂无流程类型~') }}</p>
                </template>
              </FTable>
              <div class="fei-su-pagination">
                <FPagination
                  v-model:current="paging.page"
                  v-model:page-size="paging.pageSize"
                  :total="paging.total"
                  show-size-changer
                  @change="onChange"
                  :show-total="() => `${i18n.t('共')} ${paging.total} ${i18n.t('页')}`"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <AddRole
        :title="title"
        @popup-close="close"
        :edit-tepy="editTepy"
        :present-role="presentRole"
        :show="show"
        @submit="submitAddRole"
      />
      <AddUser
        :show="addUserShow"
        :role-type="roleType"
        :title="roleTitle"
        :edit="roleEdit"
        :default-role="defaultRoles"
        :default-text="defaultRecord?.conditionExpression || undefined"
        @popup-close="closeAdduser"
        @submit="submitAddUser"
      ></AddUser>
    </div>
  </FSpin>
</template>

<script setup lang="ts">
import { ref, onMounted, createVNode, unref, computed } from 'vue'
import { IPage } from '@/types/common'
import noImgUrl from '@/assets/images/no-data.png'
import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { RoleListItem, ConfigListParams, ConfigListItemList, IAddRole, IRoleType } from '@/types/role'
import {
  getAllRoleList,
  getRoleDetails,
  addRloe,
  editRloe,
  deleteRloe,
  deleteRloeUser,
  addUserRloe,
  addUserXlse,
  updateRoleStuff,
} from '@/api'
import { messageInstance as message, FModal } from '@fs/smart-design'
import AddRole from './components/addRole.vue'
import AddUser from './components/addUser.vue'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import { useI18n } from '@/utils'

const i18n = useI18n()

onMounted(() => {
  fetchData()
})

const columns = computed<TableColumnsType>(() => [
  { title: i18n.t('人员id'), dataIndex: 'id', key: 'id' },
  { title: i18n.t('中文名称'), dataIndex: 'nameCh', key: 'nameCh' },
  { title: i18n.t('英文名称'), dataIndex: 'nameEn', key: 'nameEn' },
  { title: i18n.t('表达式'), dataIndex: 'conditionExpression', key: 'conditionExpression' },
  // { title: '是否有效', dataIndex: 'effective', key: 'effective' },
  { title: i18n.t('角色编码'), dataIndex: 'roleCode', key: 'roleCode' },
  { title: i18n.t('操作'), dataIndex: 'action', key: 'action' },
])
//获取角色list
const roleList = ref<Array<RoleListItem>>([])
const roleType = ref<number>(0)
const presentRole = ref<IAddRole>()
const search = ref<string>('')
const fetchData = async (type = 0) => {
  listLoading.value = true
  const res = await getAllRoleList(search.value)
  if (res.code === 200) {
    roleList.value = res.data
    if (type) {
      modelValue.value = res.data[0]?.id
    } else {
      modelValue.value = presentRole.value?.id ?? res.data[0]?.id
    }
    const parmas = {
      roleConfigId: modelValue.value,
      pageNum: paging.value.page,
      pageSize: paging.value.pageSize,
    }
    fetchList(parmas)
  }
  listLoading.value = false
}
const modelValue = ref<any>()
const loading = ref<boolean>(false)
const listLoading = ref(false)
const dataList = ref<ConfigListItemList[]>([])
const paging = ref<IPage>({ page: 1, pageSize: 10, total: 0 })
const onSelectHandel = (item: any) => {
  loading.value = true
  modelValue.value = item.id
  presentRole.value = item
  const parmas = {
    roleConfigId: modelValue.value,
    pageNum: 1,
    pageSize: paging.value.pageSize,
  }
  fetchList(parmas)
}

const fetchList = async (params: ConfigListParams) => {
  loading.value = true
  const res = await getRoleDetails(params)
  if (res.code === 200) {
    dataList.value = res.data.page.list
    presentRole.value = res.data.roleConfigRsp
    roleType.value = presentRole.value?.type || 0
    paging.value.total = res.data?.page.totalPage ?? 0
    paging.value.pageSize = res.data?.page.pageSize ?? 10
    paging.value.total = res.data?.page.totalCount ?? 0
    loading.value = false
  } else {
    message.warning(res.msg)
  }
}

//新增 编辑 角色
const title = ref<string>('')
const show = ref<boolean>(false)
const roleTypt: Record<number, string> = {
  1: i18n.t('新增角色'),
  2: i18n.t('编辑角色'),
}
const editTepy = ref(1)
const addARole = (type: number) => {
  title.value = roleTypt[type]
  show.value = true
  editTepy.value = type
}

const submitAddRole = async (data: IAddRole) => {
  if (editTepy.value === 1) {
    const res = await addRloe(unref(data))
    if (res.code === 200) {
      message.success(i18n.t('新增角色成功'))
    }
  } else {
    const res = await editRloe({
      ...unref(data),
      id: presentRole.value?.id,
    })
    if (res.code === 200) {
      message.success(i18n.t('编辑角色成功'))
    }
  }
  fetchData()
  show.value = false
}

const close = () => {
  editTepy.value = 1
  show.value = false
}

// 禁用/启用角色
const roleConfirm = (type: number, data?: any) => {
  const content: Record<string, string> = {
    0: i18n.t('删除后不可恢复,请谨慎操作'),
    1: i18n.t('确定要停用该角色吗？'),
    2: i18n.t('确定要禁用该角色吗？'),
    3: i18n.t('确定要在该角色内删除该人员吗？'),
  }
  const title: Record<string, string> = {
    0: i18n.t('确定将删除角色吗?'),
    1: i18n.t('提示'),
    2: i18n.t('提示'),
    3: i18n.t('提示'),
  }
  FModal.confirm({
    title: title[type],
    icon: createVNode(ExclamationCircleFilled),
    content: createVNode('div', { style: 'color:#666666;' }, `${content[type]}`),
    okText: i18n.t('确定'),
    cancelText: i18n.t('取消'),
    centered: true,
    onOk() {
      const id = presentRole.value?.id as number
      if (type === 0) {
        deleteRloe(id).then(() => {
          message.success(i18n.t('删除角色成功'))
          fetchData()
        })
      } else if (type === 3) {
        deleteRloeUser(data.id).then(() => {
          message.success(i18n.t('移除人员成功'))
          fetchData()
        })
      }
    },
    class: 'del-model',
  })
}

//添加人员
const addUserShow = ref<boolean>(false)
const roleTitle = ref<string>('')
const roleEdit = ref<boolean>(false)
const defaultRoles = ref<any[]>([])
const defaultRecord = ref<any>(null)
const addUser = (edit = false, record: any = undefined) => {
  addUserShow.value = true
  roleTitle.value = (edit && i18n.t('编辑人员')) || i18n.t('新增人员')
  roleEdit.value = edit
  defaultRoles.value = (record && [record.uuid]) || []
  defaultRecord.value = record || {}
}
const closeAdduser = () => {
  addUserShow.value = false
}
const submitAddUser = (data: any) => {
  if (roleEdit.value) {
    const params = {
      id: defaultRecord.value.id,
      conditionExpression: data.conditionExpression,
    }
    updateRoleStuff(params).then(res => {
      if (res.code === 200) {
        message.success(i18n.t('编辑人员成功'))
        fetchData()
      }
    })
  } else {
    const params = Object.assign(data, {
      roleConfigId: presentRole.value?.id as number,
    })
    addUserRloe(params).then(res => {
      if (res.code === 200) {
        message.success(i18n.t('添加人员成功'))
        fetchData()
      }
    })
  }
}

//批量添加人员
const inputFileRef = ref()
// const tempFileItem = ref()
const addElsx = () => {
  inputFileRef.value.dispatchEvent(new MouseEvent('click'))
}
const onUpload = async (e: any) => {
  const files = e.target.files[0]
  const file = new FormData()
  file.append('file', files)
  addUserXlse(file, modelValue.value).then(res => {
    if (res.code === 200) {
      message.success(i18n.t('添加人员成功'))
      fetchData()
      e.target.value = ''
    }
  })
}
//分页
const onChange = (current: number, pageSize: number) => {
  paging.value.pageSize = pageSize
  fetchData()
}
</script>

<style scoped lang="scss">
:deep(.fs-table-tbody) {
  > tr:hover:not(.fs-table-expanded-row) > td,
  .fs-table-row-hover,
  .fs-table-row-hover > td {
    background: #f1f4f8 !important;
  }
  .fs-table-cell:empty::after {
    content: '--';
  }
}
.fs-table-fixed {
  .fs-table-row-hover,
  .fs-table-row-hover > td {
    background: #f1f4f8 !important;
  }
}
.flex-space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex {
  display: flex;
  flex-wrap: wrap;
}
.fei-su-main-box {
  display: flex;
  width: 100%;
  .fei-su-main-left {
    width: 260px;
    margin-right: 15px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
    .fei-su-left-title {
      padding: 16px 24px;
      font-size: 14px;
      border-bottom: 1px solid #eeeeee;
      font-weight: 500;
    }
    .process-type-box {
      margin: 16px 0;
      overflow-y: auto;
      height: calc(100vh - 180px);
      .item {
        height: 32px;
        line-height: 32px;
        cursor: pointer;
        font-size: 12px;
        padding: 0 24px 3px;
        box-sizing: border-box;
        margin-bottom: 4px;
      }
      .selectd {
        color: #378eef;
        background: rgba(55, 142, 239, 0.08);
      }
    }
  }
  .fei-su-main-right {
    flex: 1;
    .select-options {
      background-color: #fff;
      padding: 24px;
    }
    .w240 {
      width: 240px;
      margin-right: 24px;
    }
    .text-area {
      width: 200px !important;
      height: 300px !important;
    }
    .role-label {
      color: #3dcca6;
    }
  }
  .marginB16 {
    margin-bottom: 16px;
  }
  .color-bule {
    color: rgb(55, 142, 239);
  }
  .input-box {
    margin: 24px 12px 0px 12px;
  }
}
</style>

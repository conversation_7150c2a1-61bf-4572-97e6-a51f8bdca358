<template>
  <div :class="['demand-handle-main-message', messageFlag ? 'isCollapse' : '']">
    <Message
      :process-no="props.processNo"
      :instance-id="props.processId"
      :process-name="props.processName"
      :params-wrapper="props.paramsWrapper"
    >
      <template #header>
        <div class="message-box-head">
          <span>{{ i18n.t('消息协同') }}</span>
          <Icon class="pointer" icon="icontubiao_zhankai1" @click="handleMessageCollapse" />
        </div>
        <div class="message-header-box">
          <p class="title">
            <i class="icon" />
            <span>
              <FTooltip placement="top">
                <template #title>
                  <span class="color666">{{ `${props.processName}：` }}</span>
                  <span class="color333">{{ props.processNo }}</span>
                </template>
                <span class="color666">{{ `${props.processName}：` }}</span>
                <span class="color333">{{ props.processNo }}</span>
              </FTooltip>
            </span>
          </p>
        </div>
      </template>
    </Message>
  </div>
  <!-- 消息协同浮动按钮 -->
  <div class="message-box" v-show="messageFlag">
    <div class="pointer" :title="i18n.t('展开消息协同')">
      <div class="message-box-hideen" @click="handleMessageCollapse"><span class="iconfont">&#xe68f;</span></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Icon from '@/components/Icon/index.vue'
import Message from '@/components/Message/index.vue'
import { useI18n } from '@/utils'

interface IProps {
  processId: number
  processNo: string
  processName: string
  paramsWrapper: any
}
const props = defineProps<IProps>()
const messageFlag = ref(false)
const i18n = useI18n()
// 消息框折叠
const handleMessageCollapse = () => {
  messageFlag.value = !messageFlag.value
}
</script>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}
.demand-handle-main-message {
  flex: 0 0 330px;
  margin-left: 24px;
  transition: all 0.3s linear;

  &.isCollapse {
    flex: 0 0 0;
    width: 0;
    opacity: 0;
    margin-right: -20px;
  }

  .message-box-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 48px;
    border-bottom: 1px solid #eee;
  }

  .message-header-box {
    padding: 16px 4px;
    margin: 0px 20px 0;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    align-items: center;
    .title {
      display: flex;
      align-items: center;
      width: 100%;
      margin: 0;
      i {
        display: inline-block;
        width: 14px;
        height: 12px;
        background: url('~@/assets/images/forwarder_detial_title01.png') repeat no-repeat;
        background-size: 100% 100%;
        margin-right: 5px;
        flex-shrink: 0;
      }
      span {
        flex: 1;
        font-size: 12px;
        color: #999999;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.message-box {
  position: fixed;
  right: 20px;
  bottom: 100px;
  .message-box-hideen {
    margin-left: 24px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    background-color: #4699ff;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 6px 24px 0px rgba(55, 120, 206, 0.34);

    .iconfont {
      font-size: 27px;
      color: #fff;
    }
  }
}
</style>

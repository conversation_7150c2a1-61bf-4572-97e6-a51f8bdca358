<template>
  <div class="tab-list-container">
    <FSpin class="cust-spin" size="large" fix :spinning="loading">
      <FCard
        v-processTabSticky="{
          container: '#container',
          offset: 80,
        }"
        style="box-shadow: 0 2px 8px 0 rgba(88, 98, 110, 0.08)"
        :tab-list="tabList"
        :active-tab-key="tagActive"
        :body-style="CRAD_BODY_STYLE"
        :head-style="COMPONENT_CRAD_BODY_STYLE"
        @tab-change="tabChangeFn"
      >
        <template #customTab="item">
          <FBadge v-if="item.key === TagKeyNew.relevance" :count="relevanceNumber" :offset="[5, -3]" size="small">
            <span class="cust-tab-text">{{ item.tab }}</span>
          </FBadge>
          <FBadge v-if="item.key === TagKeyNew.todoList" :count="toDoList.length" :offset="[5, -3]" size="small">
            <span class="cust-tab-text">{{ item.tab }}</span>
          </FBadge>
          <FBadge
            v-if="item.key === TagKeyNew.RelevanceIRSRARDetail"
            :count="newIpdRelevanceProcessInfo?.typeOneCount ?? 0"
            :offset="[5, -3]"
            size="small"
          >
            <span class="cust-tab-text">{{ item.tab }}</span>
          </FBadge>
          <FBadge
            v-if="item.key === TagKeyNew.RelevanceTestDetail"
            :count="newIpdRelevanceProcessInfo?.typeTwoCount ?? 0"
            :offset="[5, -3]"
            size="small"
          >
            <span class="cust-tab-text">{{ item.tab }}</span>
          </FBadge>
        </template>
      </FCard>
      <!-- Tab 组件 -->
      <FSpin :spinning="spinning">
        <component
          :is="TAB_COMPONENT_NEW[tabComponent as keyof typeof TAB_COMPONENT_NEW]"
          :process-info="processInfo"
          :other="{ processRoleInfo }"
          @operate="handleOperate"
          :form-key="formKey"
          :key="updateKey"
          id="precessTab"
        />
      </FSpin>
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import {
  COMPONENT_CRAD_BODY_STYLE,
  CRAD_BODY_STYLE,
  EmitType,
  TAB_COMPONENT_NEW,
  TAB_LIST_NEW,
  TagKeyNew,
} from '@/views/process-detail/config'
import { deepClone } from '@/utils'
import { inject, onMounted, provide, Ref, ref } from 'vue'
import { IProcess, ITabOption, ITask } from '@/types/handle'
import { IProcessRoleAndUser } from '@/types/request'
import { getProcessRelateList, getTabConfig } from '@/api'
import { useI18n } from '@/utils'

const i18n = useI18n()
const processId = inject<number>('processId') // 流程 id
// const processNo = inject<string>('processNo') // 流程编号
// const processName = inject<string>('processName') // 流程名称
// const processType = inject<string>('processType') // 流程类型
const processInfo = inject('processInfo') as Ref<IProcess[]> // 流程信息
const processRoleInfo = inject<IProcessRoleAndUser[]>('processRoleInfo') // 流程角色信息
// const currtMilepost = inject<Ref<IProcess>>('currtMilepost') // 当前里程碑信息
// const operate = inject('operate') as (key: EmitType, data: IProcess) => void
const tagActive = ref<TagKeyNew | string>(TagKeyNew.handle)
const tabComponent = ref<TagKeyNew | string>(TagKeyNew.handle)
const formKey = ref<string | undefined>(undefined)
const relevanceNumber = ref(0)
const toDoList = inject<ITask[]>('toDoList') as ITask[]
const newIpdRelevanceProcessInfo = inject<any>('newIpdRelevanceProcessInfo')
const spinning = ref(false)
const loading = ref<boolean>(false)
const updateKey = ref<number>(Date.now() + Math.random())
const tabList = ref<ITabOption[]>(deepClone(TAB_LIST_NEW))
const operateFunctions = {
  // role
  [EmitType.updateRole]: (data: any) => {
    // getProcessRoleInfo()
    // getProcessInfo()
  },
}
const handleOperate = (value: { type: keyof typeof operateFunctions; data: unknown }) => {
  const { type, data } = value
  operateFunctions[type](data as any)
}
const initRelevanceNumber = async () => {
  const { data = [] } = await getProcessRelateList(processId as number)
  relevanceNumber.value = data.length
}

const scrollEl = () => {
  const el = document.getElementById('precessTab')
  if (el) {
    el.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const tabChangeFn = (key: string) => {
  const currentTabInfo = (tabList.value.find((item: any) => {
    return item.key === key
  }) || {}) as ITabOption
  tagActive.value = currentTabInfo?.key
  tabComponent.value = currentTabInfo?.tabComponent || currentTabInfo.key
  formKey.value = (currentTabInfo?.tabComponent && currentTabInfo?.key) || undefined
  updateKey.value = Date.now() + Math.random()
  scrollEl()
}
const getTabConfigFn = async () => {
  try {
    loading.value = true
    if (
      (new Function(`return ${process.env.VUE_APP_PROCESS_DETAIL_TAB_WHITE}`)() || []).includes(
        processInfo?.value[0]?.processConfigId
      )
    ) {
      tabList.value.push({ key: TagKeyNew.dataManage, tab: i18n.t('资料管理') })
    }
    if (
      (new Function(`return ${process.env.VUE_APP_PROCESS_NEW_IPD_CONFIG_ID}`)() || '') ===
      processInfo?.value[0]?.processConfigId
    ) {
      tabList.value.push(
        { key: TagKeyNew.RelevanceIRSRARDetail, tab: i18n.t('IR/SR/AR') },
        { key: TagKeyNew.RelevanceTestDetail, tab: i18n.t('SDV/SIT/SVT及实验局测试') }
      )
    }
    const res = await getTabConfig(processId as number)
    if (res.code === 200) {
      tabList.value.push(
        ...res.data.map((item: any) => {
          return {
            key: item.formKey,
            tab: item.tabName,
            tabComponent: TagKeyNew.handle,
          }
        })
      )
    }
  } catch (error) {
    throw new Error(i18n.t('tab表单获取失败'))
  } finally {
    loading.value = false
  }
}

provide('setRlevanceNumber', (value: number) => (relevanceNumber.value = value))
provide('handleOperate', handleOperate)
onMounted(() => {
  getTabConfigFn()
  requestIdleCallback(initRelevanceNumber)
})
</script>

<style scoped lang="scss">
.tab-list-container {
  margin-top: 16px;
  background-color: #fff;
  :deep(.fs-card-bordered) {
    border: none;
    box-shadow: none;
    .fs-card-head {
      border: none;
      .fs-tabs-tab-btn {
        height: 22px;
        font-size: 14px;
        color: #333;
        .cust-tab-text {
          font-size: 14px;
        }
      }
      .fs-tabs-tab-active {
        .fs-tabs-tab-btn {
          color: #378eef;
        }
        .cust-tab-text {
          color: #378eef;
        }
      }
      .fs-tabs-ink-bar {
        background: #378eef;
      }
    }
  }
}
</style>

<template>
  <div>
    <template v-for="item of search.options" :key="item.componentValueKey">
      <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, nextTick, ref, inject, Ref } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { Search } from '@/views/message-template/components/SearchContent/search'
import { cache } from '@/utils'
import { GetTagAndNode } from '@/api'
import usePublish from '@/hooks/usePublish'

const { hasEnvPublish } = usePublish()

interface IProps {
  queryData: any
}

const { currentRoute } = useRouter()
const props = defineProps<IProps>()
const emits = defineEmits(['update:queryData'])
const store = useStore()
const allUserList = computed(() => store.state.user.allUser || [])
const search = new Search()
const queryData = computed({
  get: () => props.queryData,
  set: val => emits('update:queryData', val),
})
const routerName = computed<any>(() => currentRoute.value?.name)
const statusOptions = inject<Ref<any>>('statusOptions') as any
const roleTypeList = inject<Ref<any>>('roleTypeList') as any
const processList = inject<Ref<any>>('processList') as any
const publishStatus = ref([
  { value: 1, label: '已发布' },
  { value: 0, label: '未发布' },
])
const onChange = () =>
  (queryData.value = {
    ...search.getParams(),
    ...{ cacheValue: search.getCacheSearch() },
  })

const configList = [
  {
    componentName: 'FSelect',
    componentValueKey: 'processConfigId',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '所属流程'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: computed(() => processList.value || []),
      fieldNames: { label: 'processName', value: 'id' },
      optionFilterProp: 'processName',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('processConfigId.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'status',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '启用状态'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: computed(() => statusOptions.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('status.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'publishStatus',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '发布状态'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: computed(() => publishStatus.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('publishStatus.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'type',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '角色类型'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: computed(() => roleTypeList.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('type.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'createBy',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '创建人'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: allUserList,
      fieldNames: { value: 'feiShuName', label: 'feiShuName' },
      optionFilterProp: 'feiShuName',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('createBy.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FRangePicker',
    componentValueKey: 'time',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '创建时间'),
      valueFormat: 'YYYY-MM-DD',
      onChange,
    },
    getComponentValueFormat: (value: any) => {
      if (!value || value.length !== 2) return undefined
      return {
        createTimeStart: value[0] + ' 00:00:00',
        createTimeEnd: value[1] + ' 23:59:59',
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions(
        'time.componentValue',
        ((data || {})?.componentValue && [data.componentValue[0], data.componentValue[1]]) || undefined
      )
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'search',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '快速检索'),
      placeholder: computed(() => '角色编码/角色名称/角色说明'),
      allowClear: true,
      type: 'search-clear',
      onSearch: onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('search.componentValue', (data || {})?.componentValue || undefined)
    },
  },
]

const setSearchConfigFn = () => {
  search.initOptions(configList)
  !hasEnvPublish?.value && search.deteleOptions(['publishStatus'])
  search.clear()
  const cachData = (cache.get(routerName?.value) && JSON.parse(cache.get(routerName?.value) as string)) || {}
  search.setDefaultSearch(cachData)
  nextTick(() => {
    queryData.value = {
      ...search.getParams(),
      ...{ cacheValue: cachData },
    }
  })
}

onMounted(() => {
  setSearchConfigFn()
})
</script>

<style lang="scss" scoped>
:deep(.fs-input-affix-wrapper) {
  padding: 6px 8px !important;
}
</style>

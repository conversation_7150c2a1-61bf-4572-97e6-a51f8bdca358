<template>
  <div class="dict-tree-container">
    <draggable
      v-model="treeData"
      group="dict-tree-root"
      item-key="id"
      handle=".drag-handle"
      :animation="200"
      ghost-class="ghost-item"
      chosen-class="chosen-item"
    >
      <template #item="{ element, index }">
        <DictTreeNode :parent-node-children="treeData" :node="element" :level="0" :index="index">
          <!-- 传递操作按钮插槽 -->
          <template #actions="slotProps">
            <slot name="actions" v-bind="slotProps" />
          </template>
        </DictTreeNode>
      </template>
    </draggable>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, withDefaults } from 'vue'
import draggable from 'vuedraggable'
import DictTreeNode from './DictTreeNode.vue'

// 定义组件属性
const props = withDefaults(
  defineProps<{
    data: any[]
  }>(),
  {
    data: () => [],
  }
)

// 定义事件
const emit = defineEmits(['node-toggle', 'lazy-load', 'node-select', 'update:data'])

// 响应式数据
const treeData = computed<any[]>({
  get: () => {
    return props.data || []
  },
  set: value => emit('update:data', value),
})
</script>

<style scoped lang="scss">
.dict-tree {
  width: 100%;

  .dict-tree-container {
    background-color: #fff;
  }
}

.ghost-item {
  opacity: 0.5;
  background-color: #f0f0f0;
}

.chosen-item {
  background-color: #e6f7ff;
}
</style>

<template>
  <div class="process-list-new-content-table-container">
    <div class="flex space-between mb16">
      <div class="fw-500 f14">
        {{
          (currProcessTypeData?.processName && currProcessTypeData.processName + '列表') ||
          (processConfigIdList &&
            processTypeData?.[0]?.bussinessType &&
            processTypeData?.[0]?.bussinessType + '分组列表') ||
          '流程列表'
        }}
      </div>
      <FSpace :size="[12]" wrap class="handle-row-box">
        <FDropdown
          v-if="queryData?.processConfigId && hasExportTemplate"
          trigger="click"
          destroy-popup-on-hide
          :overlay-style="{ minWidth: 'auto' }"
        >
          <FButton class="download-btn marginR12" :open="true">
            <i class="iconfont icontubiao_xiazai marginR4"></i>
            <span class="marginR4">下载数据</span>
            <span class="btn-line"></span>
            <i class="iconfont iconjiantouxia marginL16"></i>
          </FButton>
          <template #overlay>
            <FMenu @click="({ key }) => $emit('on-export-process', key)">
              <FMenuItem key="common">统一格式下载</FMenuItem>
              <FMenuItem key="exportTemplate">模板格式下载</FMenuItem>
            </FMenu>
          </template>
        </FDropdown>
        <FButton v-else-if="queryData?.processConfigId && !hasExportTemplate" @click="$emit('on-export-process')">
          <template #icon><i class="iconfont icontubiao_xiazai marginR4" /></template>
          下载数据
        </FButton>
        <FButton type="primary" class="mr6" @click="isNewdemandModal = true">
          <template #icon><i class="iconfont icontubiao_tianjia1 marginR4" /></template>
          创建流程</FButton
        >
      </FSpace>
    </div>
    <FTable
      class="table-warp"
      :columns="columnsConfig.tableColumns"
      :loading="loading"
      :data-source="list"
      :row-key="(data:any) => data.id"
      :row-selection="rowSelection"
      :sticky="{ offsetHeader: 0 }"
      :scroll="{ x: 'min-content' }"
      :pagination="{
          total: pageData.total,
          current: pageData.pageNum,
          pageSize: pageData.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
          onChange: onPaginationChangeFn
        }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'creatorInfo'">
          <div class="label-box">
            <span class="label">[录入时间]</span>
            <span>{{
              (record?.createdTime && transformDate(record?.createdTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--'
            }}</span>
          </div>
          <div class="label-box">
            <span class="label">[录入人]</span>
            <span>{{ record?.creator ?? '--' }}</span>
          </div>
          <span class="code-link mt2" @click="logModalRef?.onOpenFn(record?.id)">查看日志</span>
        </template>

        <template v-if="column.dataIndex === 'processInfo'">
          <div class="label-box">
            <span class="label">[流程编号]</span>
            <span :class="[record?.id ? 'code-link' : '']" @click="onJumpDemandDetial(record)">{{
              record?.processInstanceCode ?? '--'
            }}</span>
          </div>
          <div class="label-box">
            <span class="label">[流程标题]</span>
            <MoreTextTips :line-clamp="1">
              <span>{{ record?.topicName ?? '--' }}</span>
            </MoreTextTips>
          </div>
          <div class="label-box">
            <span class="label">[流程类型]</span>
            <MoreTextTips :line-clamp="1">
              <span>{{ record?.processType ?? '--' }}</span>
            </MoreTextTips>
          </div>
        </template>

        <template v-if="column.dataIndex === 'createTagList'">
          <MoreTextTips
            :line-clamp="3"
            v-if="Array.isArray(record.createTagList) && record.createTagList.length"
            :get-popup-container-element="getPopupContainerElement"
          >
            <div
              v-for="(item, index) in record.createTagList"
              :class="['info-type', `info-type${index % 4}`]"
              :key="item"
              @click="onHandleTagClick(item, record)"
            >
              {{ item }}
            </div>
          </MoreTextTips>
        </template>

        <template v-if="column.dataIndex === 'describe'">
          <MoreTextTips :line-clamp="4">
            <MyViewer :html="record?.formData?.describe ?? '--'" />
          </MoreTextTips>
        </template>

        <template v-if="column.dataIndex === 'currentMilepost'">
          <ProcessDetail
            v-model:value="record.currentMilepost"
            :expandedIds="[record?.status === 0 && record?.currentMilepost?.id].filter(Boolean)"
          />
          <div class="label-box">
            <span class="label">[责任人]</span>
            <span>{{ record?.currentMilepost?.superviser ?? '--' }}</span>
          </div>
          <div class="label-box">
            <span class="label">[DDL]</span>
            <span>{{
              (record?.currentMilepost?.forcastTime &&
                transformDate(record?.currentMilepost?.forcastTime, 'YYYY-MM-DD HH:mm:ss')) ??
              '--'
            }}</span>
          </div>
        </template>

        <template v-if="column.dataIndex === 'processStaus'">
          <div class="flex">
            <FTag
              size="small"
              :border="false"
              :color="
                (record?.status === 0 && 'warning') ||
                (record?.status === 1 && 'success') ||
                (record?.status === 2 && 'error') ||
                'default'
              "
            >
              {{
                (record?.status === 0 && '进行中') ||
                (record?.status === 1 && '已完成') ||
                (record?.status === 2 && '已终止') ||
                '--'
              }}
            </FTag>
            <FTooltip v-if="record?.status === 2" color="#fff" :getPopupContainer="getPopupContainerElement">
              <template #title>
                <div class="content-box">
                  <div class="label-box">
                    <span class="label">[终止原因]</span>
                    <span>{{ record?.terminateReason ?? '--' }}</span>
                  </div>
                  <div class="label-box">
                    <span class="label">[终止说明]</span>
                    <span>{{ record?.terminateDescription ?? '--' }}</span>
                  </div>
                </div>
              </template>
              <i class="iconfont icontubiao_tishi colorBBB ml8 fontSize14" />
            </FTooltip>
          </div>
        </template>

        <template v-if="column.dataIndex === 'propertyInfo'">
          <div class="label-box">
            <span class="label">[项目所属组织]</span>
            <span>{{ record?.organization ?? '--' }}</span>
          </div>
        </template>

        <template v-if="column.dataIndex === 'relevanceList'">
          <div v-if="record?.relevanceList?.length" class="relevance-list">
            <MoreTextBtn :line-clamp="3">
              <span class="label">[关联流程]</span>
              <span
                class="warp-content"
                v-for="(item, index) in record?.relevanceList ?? []"
                :key="index"
                @click="onJumpDemandDetial(item, 'relevanceInstanceId')"
                >{{ item?.relevanceNumber ?? '--' }}</span
              >
            </MoreTextBtn>
          </div>
        </template>
      </template>
    </FTable>
    <!-- 新增弹框 -->
    <CreateProcessModal v-if="currProcessTypeId" v-model="isNewdemandModal" :is-detail="false" />
    <LogModal ref="logModalRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, inject } from 'vue'
import { useRouter } from 'vue-router'
import CreateProcessModal from '@/components/CreateProcessModal/index.vue'
import { useTableColumn } from '@/components/TableOperation/index'
import MoreTextTips from '@/components/MoreTextTips/index'
import LogModal from '@/views/process-ipd-list/components/LogModal/index.vue'
import ProcessDetail from '@/views/process-staffmeeting-list/components/ProcessDetail/index.vue'
import MyViewer from '@/components/Editor/MyViewer.vue'
import MoreTextBtn from '../MoreTextBtn/index.vue'
import { transformDate } from '@/utils'

interface IProps {
  queryData: any
  pageData: any
  currProcessTypeData: any
  currProcessTypeId: any
  list: any
  loading: boolean
  hasExportTemplate: boolean
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:pageData', 'on-export-process', 'get-processes-list-fn'])
const router = useRouter()
const pageData = computed({
  get: () => props.pageData,
  set: val => emits('update:pageData', val),
})

const isNewdemandModal = ref<boolean>(false)
const selectedKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[]) => (selectedKeys.value = selectedRowKeys),
}))
const logModalRef = ref()
const columnsConfig = reactive(
  useTableColumn(
    [
      { title: '录入时间/操作日志', dataIndex: 'creatorInfo', key: 'creatorInfo', width: 260 },
      { title: '流程信息', dataIndex: 'processInfo', key: 'processInfo', width: 260 },
      { title: '流程标签', dataIndex: 'createTagList', key: 'createTagList', width: 220 },
      { title: '当前阶段', dataIndex: 'currentMilepost', key: 'currentMilepost', width: 210 },
      { title: '流程状态', dataIndex: 'processStaus', key: 'processStaus', width: 130 },
      { title: '关键信息', dataIndex: 'propertyInfo', key: 'propertyInfo', width: 180 },
      { title: '关联流程', dataIndex: 'relevanceList', key: 'relevanceList', width: 200 },
    ],
    `processListNewTable_` + props.currProcessTypeId
  )
)
const getPopupContainerElement = ref(
  () => document.querySelector('.process-list-new-content-table-container') as HTMLElement
)
const processTypeData = inject('processTypeData') as any
const processConfigIdList = inject('processConfigIdList') as any

const jumpToDemand = (id: string | number, processTypeId: string | number, targer = false) => {
  const options = { name: 'ProcessDetail', params: { id } }

  if (!targer) router.push(options)
  else window.open(router.resolve(options).href, '_blank')
}

// 需求详情跳转
const onJumpDemandDetial = (record: any, idKey = 'id') => {
  record?.[idKey] && jumpToDemand(record[idKey], undefined, true)
}

const onHandleTagClick = (label: string, record: any) => {
  if (record?.tagUrl && label.startsWith('IPD')) {
    window.open(record.tagUrl)
  }
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  pageData.value.pageNum = current
  pageData.value.pageSize = pageSize
  emits('get-processes-list-fn')
}
</script>

<style lang="scss" scoped>
.process-list-new-content-table-container {
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .code-link {
    display: inline-block;
    cursor: pointer;
    color: #378eef;
  }
  .mt2 {
    margin-top: 2px;
  }
  .label-box {
    display: flex;
    color: #333;
    margin-bottom: 2px;
    .label {
      margin-right: 4px;
      color: #999;
      white-space: nowrap;
    }
  }
  .info-type {
    display: inline-block;
    height: 18px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 18px;
    padding: 0px 4px;
    margin-right: 4px;
    margin-bottom: 4px;
    cursor: pointer;
  }
  .info-type0 {
    color: #2fcc83;
    background: #eafaf2;
  }
  .info-type1 {
    color: #378eef;
    background: #ebf3fd;
  }
  .info-type2 {
    color: #ffab00;
    background: #fef7e7;
  }
  .info-type3 {
    color: #a697fe;
    background: rgba(166, 151, 254, 0.2);
  }
  .content-box {
    padding: 8px;
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
  .relevance-list {
    .label {
      margin-right: 4px;
      color: #999;
      white-space: nowrap;
    }
    .warp-content {
      word-break: break-all;
      white-space: normal;
      cursor: pointer;
      color: #378eef;
      &::after {
        content: ' / ';
      }
      &:last-child::after {
        content: '';
      }
    }
  }
  .download-btn {
    padding-right: 8px !important;
    .btn-line {
      display: inline-block;
      position: absolute;
      right: 30px;
      height: 100%;
      border-left-width: 1px;
      border-left-style: solid;
      border-left-color: inherit;
    }
  }
}
</style>

<template>
  <div
    tabindex="-1"
    ref="rangeNumberRef"
    class="cust-range-number width210 height32 marginL16 marginT23"
    :class="[focusStatus ? 'cust-input-number-focused' : '']"
  >
    <FInputNumber
      :bordered="false"
      style="width: 80px"
      press-line="Min"
      v-model:value="value[0]"
      @blur="inputNumberChange($event, 'min')"
      @focus="
        () => {
          focusStatus = true
        }
      "
    />
    <i>-</i>
    <FInputNumber
      :bordered="false"
      style="width: 80px"
      press-line="Max"
      v-model:value="value[1]"
      @blur="inputNumberChange($event, 'max')"
      @focus="
        () => {
          focusStatus = true
        }
      "
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from '@fs/smart-design'

interface IProps {
  value: any
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:value'])
const value = computed({
  get: () => props.value,
  set: val => emits('update:value', val),
})
const focusStatus = ref<boolean>(false)

const inputNumberChange = (value: any, label: any) => {
  focusStatus.value = false
  if (value && value.length === 2 && value[0] > value[1]) {
    message.warning(`${label}输入不正确，请重新输入！`)
  }
}
</script>

<style scoped lang="scss">
.cust-range-number {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 12px;
  color: #333;
  padding: 0 8px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  &.cust-input-number-focused {
    border-color: #378eef;
    box-shadow: 0 0 0 2px #afd1f8;
    border-right-width: 1px !important;
    outline: 0;
  }
  &:hover {
    border: 1px solid #5fa4f2;
  }
  &:focus {
    border-color: #378eef;
    box-shadow: 0 0 0 2px #afd1f8;
    border-right-width: 1px !important;
    outline: 0;
  }
  :deep(.fs-input-number) {
    height: 30px;
    .fs-input-number-handler-wrap {
      display: none;
    }
  }
}
</style>

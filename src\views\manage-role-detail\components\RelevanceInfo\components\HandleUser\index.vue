<template>
  <FModal
    width="400px"
    wrapClassName="bom-modal-wrap-container"
    v-model:visible="visible"
    :title="handleData?.[handleType]?.title ?? '新增人员'"
    centered
    :confirm-loading="loading"
    @cancel="onCancelFn"
    @ok="onSubmitFn"
  >
    <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <FRow :gutter="[24, 0]">
        <FCol :span="24">
          <FFormItem label="人员" name="uuids">
            <FSelect
              v-model:value="formState.uuids"
              placeholder="请选择"
              :options="allUserList"
              :fieldNames="{ value: 'uuid', label: 'feiShuName' }"
              optionFilterProp="feiShuName"
              show-search
              allow-clear
              mode="multiple"
              maxTagCount="responsive"
              @change="onChangeFiledFn"
            />
          </FFormItem>
        </FCol>
        <FCol :span="24" v-if="roleConfig.type === 3">
          <FFormItem name="conditionExpression" class="condition-item">
            <template #label>
              <div class="condition-label-box">
                <span class="nowrap color999 color333">条件表达式</span>
                <ConditionExpression @change="onConditionChangeFn" />
              </div>
            </template>
            <FTextarea
              v-model:value="formState.conditionExpression"
              style="min-height: 88px"
              placeholder="请输入条件表达式"
            />
          </FFormItem>
        </FCol>
      </FRow>
    </FForm>
    <div></div>
  </FModal>
</template>

<script setup lang="ts">
import { message } from '@fs/smart-design'
import { ref, reactive, computed, inject, Ref } from 'vue'
import { useStore } from 'vuex'
import { addManageRoleUser, updateManageRoleUser } from '@/api'
import ConditionExpression from '../ConditionExpression/index.vue'
import { trimObjectStrings } from '@/utils'

const emits = defineEmits(['updateChange'])
const visible = ref<boolean>(false)
const roleConfig = inject<Ref<any>>('roleConfig')
const store = useStore()
const allUserList = computed(() => store.state.user.allUser || [])
const loading = ref<boolean>(false)
const formRef = ref()
const formState = reactive<any>({
  uuids: undefined,
  conditionExpression: undefined,
})
const currentRowRecord = ref<any>()
const handleType = ref('')
const handleData = computed(() => ({
  addRoleUser: {
    title: '新增人员',
    msg: '新增人员成功！',
    apiUrl: addManageRoleUser,
    baseParams: {
      roleConfigId: roleConfig?.value?.id,
    },
  },
  updateRoleUser: {
    title: '编辑人员',
    msg: '编辑人员成功！',
    apiUrl: updateManageRoleUser,
    baseParams: {
      id: currentRowRecord?.value?.id,
    },
  },
}))

const rules: Record<string, any[]> = {
  uuids: [{ required: true, message: '请选择人员' }],
  conditionExpression: [{ required: true, message: '请输入条件表达式' }],
}

const onChangeFiledFn = () => {
  formState.conditionExpression = undefined
}

const onConditionChangeFn = item => {
  formState.conditionExpression =
    ((formState?.conditionExpression && formState.conditionExpression + ' ') || '') + item.value
}

const onCancelFn = () => {
  visible.value = false
}

const onSubmitFn = async () => {
  try {
    loading.value = true
    if (!formRef.value) return
    await formRef.value.validate()
    const params: any = Object.assign({}, formState, handleData?.value?.[handleType?.value]?.baseParams)
    await handleData?.value?.[handleType?.value]?.apiUrl(trimObjectStrings(params))
    message.success(handleData?.value?.[handleType?.value]?.msg ?? '操作成功')
    onCancelFn()
    emits('updateChange')
  } finally {
    loading.value = false
  }
}

const onInitData = async () => {
  Object.entries(formState).forEach(([key, value]) => {
    formState[key] =
      handleData?.value?.[handleType?.value]?.defaultForm?.[key] || (currentRowRecord.value || {})[key] || undefined
  })
}

const onOpenFn = async (typeValue: string, data: any = {}) => {
  visible.value = true
  currentRowRecord.value = data
  handleType.value = typeValue
  await onInitData()
}

defineExpose({ onOpenFn })
</script>

<style scoped lang="scss">
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
:deep(.condition-item) {
  label {
    width: 100%;
    .condition-label-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }
  }
}
</style>

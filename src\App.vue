<template>
  <FConfigProvider :locale="locale">
    <RouterView :key="key" />
    <iframe :src="url" style="display: none" id="dcs"></iframe>
  </FConfigProvider>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import { getLanguage, getToken, getUserInfo, isDev } from '@/utils'
import { checkUserAiPermission } from '@/api'
import zhCN from '@fs/smart-design/dist/ant-design-vue_es/locale-provider/zh_CN'
import enUS from '@fs/smart-design/dist/ant-design-vue_es/locale-provider/en_US'
import 'dayjs/locale/zh-cn'
import 'dayjs/locale/en'

const url = computed(() => window.location.href)
const DifyUrl = process.env.VUE_APP_DIFY_URL
const DifyToken = process.env.VUE_APP_DIFY_TOKEN

const store = useStore()
const key = ref('bpmApp')
const language = ref(getLanguage() || 'zh-CN')
const locale = ref(language.value === 'zh-CN' ? zhCN : enUS)

// 初始化用户数据, 等待改造
const token = getToken()
const userInfo = getUserInfo()

// token 不存在的时候会去 SSO 获取 token
// userInfo 不存在的时候会去请求接口获取 userInfo 并且回请求 power 数据
if (token && userInfo) {
  store?.dispatch('user/getUsers')
  initDifyChatbot()
}

// 微前端环境下，国际化语言变化监听
if (window.__MICRO_APP_ENVIRONMENT__) {
  // 监听基座下发的数据变化
  window.microApp.addDataListener((data: { path: string; language: string }) => {
    if (data.language) {
      language.value = data.language
      locale.value = data.language === 'zh-CN' ? zhCN : enUS
    }
  })
}

async function initDifyChatbot() {
  // 添加聊天机器人配置
  window.difyChatbotConfig = {
    // token: DifyToken,
    baseUrl: DifyUrl,
    systemVariables: {},
    userVariables: {},
    isDev: isDev,
    draggable: true,
    containerProps: {
      className: 'bpm-chat-button',
      style: {
        '--dify-chatbot-bubble-button-bg-color': '#155EEF',
      },
    },
    dynamicScript: true, // 设置为动态脚本加载模式
  }

  const chatbotStyle = document.createElement('style')
  chatbotStyle.textContent = `
    #dify-chatbot-bubble-button {
      background-color: #1C64F2 !important;
    }
    #dify-chatbot-bubble-window {
      width: 44rem !important;
      height: 40rem !important;
      right: calc(1rem + 60px) !important; /* 向左移动60px，避免与按钮重叠 */
      bottom: calc(1rem + 20px) !important; /* 向上移动20px */
    }
  `
  document.head.appendChild(chatbotStyle)

  const { data } = await checkUserAiPermission(userInfo.uuid)
  if (!data?.isShow) return console.warn('用户无权使用AI聊天机器人！')
  if (window.difyChatbotConfig) {
    window.difyChatbotConfig.systemVariables = { user_id: data.uuid }
    window.difyChatbotConfig.userVariables = { avatar_url: data.avatarUrl, name: data.name }
    window.difyChatbotConfig.token = data.appId
    window.difyChatbotConfig.inputs = { uuid: data.uuid }
  }

  if (document.getElementById(DifyToken)) return console.warn('聊天机器人脚本已加载')

  const chatbotScript = document.createElement('script')
  chatbotScript.id = DifyToken
  chatbotScript.src = `${DifyUrl}/embed.min.js`
  chatbotScript.async = true
  chatbotScript.onload = () => console.log('聊天机器人脚本加载成功')
  chatbotScript.onerror = () => console.error('聊天机器人脚本加载失败')
  document.body.appendChild(chatbotScript)
}
</script>

<template>
  <div class="approval-workbench-container">
    <!-- <Breadcrumb class="approve-box-shadow" :data="[i18n.t('审批工作流'), i18n.t('工作台')]" /> -->
    <div class="background-container">
      <div class="content-box">
        <div class="left-box marginR16">
          <FSpin :spinning="countCardLoading">
            <CountCard :count-card-data="countCardData" :approval-tab-data-count="approvalTabDataCount" />
          </FSpin>
          <FSpin :spinning="approvalTabLoading">
            <ApprovalTab
              v-model:page="page"
              v-model:search="approvalTabSearch"
              :approval-tab-data-count="approvalTabDataCount"
              :approval-tab-data="approvalTabData"
              @getDataFn="getApprovalTabFn"
              :key="updateKey"
            />
          </FSpin>
        </div>
        <div class="right-box">
          <FSpin :spinning="addApprovalTabLoading">
            <AddApproval :add-approval-data="addApprovalData" />
          </FSpin>
          <ApprovalData />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, provide } from 'vue'
import CountCard from './components/CountCard/index.vue'
import ApprovalTab from './components/ApprovalTab/index.vue'
import ApprovalData from './components/ApprovalData/index.vue'
import AddApproval from './components/AddApproval/index.vue'
import Breadcrumb from '@/views/pgb-data-board/components/Breadcrumb/index.vue'
import { useI18n } from '@/utils'
import {
  getFlowChartIndicators,
  FlowChartIndicatorsRes,
  getFlowChartProcessAll,
  FlowChartProcessAllRes,
  getApprovalProcess,
  PageApprovalProcessRes,
  PageApprovalProcessParams,
  BasicPageParams,
  getApprovalProcessCount,
  PageApprovalProcessCountRes,
} from '@/api'

const i18n = useI18n()
const countCardLoading = ref<boolean>(false)
const countCardData = ref<FlowChartIndicatorsRes>()

const approvalTabLoading = ref<boolean>(false)
const approvalTabData = ref<PageApprovalProcessRes[]>()
const approvalTabDataCount = ref<PageApprovalProcessCountRes>()
const page = ref<BasicPageParams>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const approvalTabSearch = ref<PageApprovalProcessParams>({
  type: 1,
})
const updateKey = ref<number>(Date.now() + Math.random())

const addApprovalTabLoading = ref<boolean>(false)
const addApprovalData = ref<FlowChartProcessAllRes[]>()

const getApprovalTabCountFn = async (data = null) => {
  const res = await getApprovalProcessCount()
  if (res.code !== 200) throw new Error(res.msg)
  approvalTabDataCount.value = res?.data ?? {}
}

const getApprovalTabFn = async (data = null) => {
  try {
    approvalTabLoading.value = true
    getApprovalTabCountFn()
    const params = Object.assign({}, approvalTabSearch.value, page.value, data)
    delete params.total
    const res = await getApprovalProcess(params)
    if (res.code !== 200) throw new Error(res.msg)
    approvalTabData.value = res?.data?.list ?? []
    page.value.total = res?.data?.totalCount ?? 0
  } finally {
    approvalTabLoading.value = false
    updateKey.value = Date.now() + Math.random()
  }
}

const getCountCardFn = async () => {
  try {
    countCardLoading.value = true
    const res = await getFlowChartIndicators()
    if (res.code !== 200) throw new Error(res.msg)
    countCardData.value = res?.data
  } finally {
    countCardLoading.value = false
  }
}

const getFlowChartProcessAllFn = async () => {
  try {
    addApprovalTabLoading.value = true
    const res = await getFlowChartProcessAll()
    if (res.code !== 200) throw new Error(res.msg)
    addApprovalData.value = res?.data
  } finally {
    addApprovalTabLoading.value = false
  }
}

const init = () => {
  getCountCardFn()
  getFlowChartProcessAllFn()
  getApprovalTabFn()
}

onMounted(() => {
  init()
})

provide('getApprovalInfoFn', init)
</script>

<style scoped lang="scss">
.approval-workbench-container {
  .approve-box-shadow {
    margin: -20px -20px 0 -20px;
  }
  .background-container {
    overflow-x: auto;
    .content-box {
      display: flex;
      flex-wrap: nowrap;
      // margin-top: 24px;
      .left-box {
        flex: 1;
        min-width: 600px;
      }
      .right-box {
        flex: 1 1 340px;
        max-width: 380px;
        min-width: 340px;
      }
    }
  }
}
</style>

<template>
  <div :class="wrapperClasses">
    <div :class="spinnerClasses">
      <FSpin :indicator="indicator"> </FSpin>
      <div v-if="text" :class="textClasses">{{ text }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, h } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
const props = defineProps({
  active: {
    type: Boolean,
    default: false,
  },
  text: {
    type: String,
    default: '',
  },
})
const indicator = h(LoadingOutlined, {
  style: {
    fontSize: '24px',
  },
  spin: true,
})
const prefixCls = 'ivu-scroll'
const wrapperClasses = computed(() => {
  return [
    `${prefixCls}-loader-wrapper`,
    {
      [`${prefixCls}-loader-wrapper-active`]: props.active,
    },
  ]
})
const spinnerClasses = computed(() => {
  return `${prefixCls}-spinner`
})
const textClasses = computed(() => {
  return `${prefixCls}-loader-text`
})
</script>
<style lang="scss" scoped>
.ivu-scroll-loader-wrapper {
  padding: 5px 0;
  height: 0;
  background-color: inherit;
  transform: scale(0);
  transition: opacity 0.3s, transform 0.5s, height 0.5s;
  &.ivu-scroll-loader-wrapper-active {
    height: 70px;
    transform: scale(1);
  }
  .ivu-scroll-loader-text {
    color: #378eef;
  }
}
</style>

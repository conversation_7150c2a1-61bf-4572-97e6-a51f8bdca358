<template>
  <div class="map-nav-list" ref="listRef">
    <div
      class="map-nav-list-item"
      v-for="item in props.data"
      :key="item.id"
      :class="{ active: hoverIndex === item }"
      @click="handleMapNavClick(item)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { IMicroMenu } from '@fs/hooks'

interface IProps {
  data: IMicroMenu[]
}
const props = defineProps<IProps>()
const emits = defineEmits(['on-menu-change'])
const hoverIndex = ref<IMicroMenu>()

watch(
  () => props.data,
  () => {
    if (!props.data.length || hoverIndex.value) return
    hoverIndex.value = props.data?.[0]
    emits('on-menu-change', hoverIndex.value?.children || [])
  },
  { immediate: true }
)

const handleMapNavClick = (item: IMicroMenu) => {
  hoverIndex.value = item
  emits('on-menu-change', item.children || [])
}
</script>

<style lang="scss" scoped>
.map-nav-list {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  height: 36px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .slider-bg {
    position: absolute;
    top: 0;
    height: 36px;
    border-radius: 6px;
    // transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
    background: #d6e6ff;
    pointer-events: none;

    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: 0;
      width: 24px;
      height: 4px;
      border-radius: 3px;
      background: #196afb;
      transform: translateX(-50%);
      z-index: 0;
    }
  }

  .map-nav-list-item {
    position: relative;
    margin-right: 16px;
    height: 36px;
    line-height: 36px;
    padding: 0 8px;
    color: #333;
    font-size: 14px;
    border-radius: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    z-index: 1;

    &:last-child {
      margin-right: 0;
    }

    &:hover {
      // font-weight: 500;
      background-color: #d6e6ff;
    }

    &.active,
    &:active {
      color: #196afb;
      font-weight: 500;
      background-color: #d6e6ff;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 0;
        width: 24px;
        height: 4px;
        border-radius: 3px;
        background: #196afb;
        transform: translateX(-50%);
        z-index: 0;
      }
    }
  }
}
</style>

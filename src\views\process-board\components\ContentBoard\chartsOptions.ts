import type { EChartsOption } from '@/components/BaseEchart/config'
import { i18n } from '@/init'

export const barOptions: EChartsOption = {
  color: ['#378EEF', '#2FCC83'],
  legend: {
    itemWidth: 8,
    itemHeight: 8,
    bottom: 0,
    itemGap: 16,
    textStyle: {
      color: '#666666',
    },
    data: [
      {
        name: i18n.t('任务数量'),
        icon: 'path://M0 0h1024v1024H0z',
      },
      {
        name: i18n.t('占比'),
        icon: 'path://M2730.666667 170.666667v682.666666H0v-682.666666z',
      },
    ],
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#666',
        type: 'solid',
      },
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: '#000000B3',
    borderColor: 'transparent',
  },
  grid: {
    left: '45px',
    right: '43px',
    top: '28px',
    bottom: '58px',
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#cccccc',
      },
    },
    axisLabel: {
      color: '#999999',
    },
    axisPointer: {
      show: true,
      type: 'line',
      lineStyle: {
        color: '#666666',
      },
    },
    data: [],
  },
  yAxis: [
    {
      type: 'value',
      name: i18n.t('数量（个）'),
      nameTextStyle: {
        padding: [0, 24, 0, 0],
      },
      splitLine: {
        lineStyle: {
          color: '#EEEEEE',
        },
      },
      axisLabel: {
        color: '#999999',
        formatter: function (value: any) {
          return value + ''
        },
      },
    },
    {
      type: 'value',
      name: i18n.t('占比（%）'),
      nameTextStyle: {
        padding: [0, -34, 0, 0],
      },
      splitLine: {
        lineStyle: {
          color: '#EEEEEE',
        },
      },
      axisLabel: {
        color: '#999999',
        formatter: function (value: any) {
          return value + '%'
        },
      },
    },
  ],
  series: [],
}

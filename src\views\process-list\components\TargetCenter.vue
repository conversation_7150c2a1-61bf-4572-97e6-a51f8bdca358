<template>
  <Card :title="i18n.t('指标中心')" icon="iconzhibiaozhongxin">
    <div class="target positionR">
      <div class="target-item">
        <img class="marginR10" src="@/assets/images/bpm/target-1.png" />
        <div>
          <p style="margin-bottom: 6px">{{ i18n.t('项目数量') }}</p>
          <p class="number">{{ data.total }}</p>
          <p class="ring-than">
            {{ i18n.t('环比') }} {{ data.totalRingRatio }} <img :src="getUpAndDownImg(data.totalRingRatio)" />
          </p>
        </div>
      </div>
      <div class="target-item">
        <img class="marginR10" src="@/assets/images/bpm/target-2.png" />
        <div>
          <p style="margin-bottom: 6px">{{ i18n.t('已完成') }}</p>
          <p class="number">{{ data.completed }}</p>
          <p class="ring-than">
            {{ i18n.t('环比') }} {{ data.completedRingRatio }} <img :src="getUpAndDownImg(data.completedRingRatio)" />
          </p>
        </div>
      </div>
      <div class="target-item">
        <img class="marginR10" src="@/assets/images/bpm/target-3.png" />
        <div>
          <p style="margin-bottom: 6px">{{ i18n.t('按时完成') }}</p>
          <p class="number">{{ data.onTimeCompleted }}</p>
          <p class="ring-than">{{ i18n.t('按时完成率') }} {{ data.onTimeCompletedRate }}</p>
        </div>
      </div>
      <div class="target-item">
        <img class="marginR10" src="@/assets/images/bpm/target-4.png" />
        <div>
          <p style="margin-bottom: 6px">{{ i18n.t('已延期') }}</p>
          <p class="number">{{ data.postponed }}</p>
          <p class="ring-than">{{ i18n.t('延期率') }} {{ data.defermentRate }}</p>
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { IIndicatorCenterInfo } from '@/types/request'

import Card from '@/components/Card/index.vue'

import upPng from '@/assets/images/bpm/up.png'
import downPng from '@/assets/images/bpm/down.png'
import { useI18n } from '@/utils'

interface IProps {
  data: IIndicatorCenterInfo
}

const i18n = useI18n()
const props = defineProps<IProps>()
const data = computed(() => props.data)
const getUpAndDownImg = (data: string) => {
  return data?.startsWith('-') ? downPng : upPng
}
</script>

<style lang="scss" scoped>
.target {
  display: flex;
  align-items: center;
  margin: 24px 0;
  .target-item {
    color: #666;
    width: 25%;
    font-size: 12px;
    display: flex;
    align-items: center;
    padding-left: 10px;
    > img {
      width: 60px;
      height: 60px;
    }
    > div img {
      vertical-align: baseline;
    }
    .number {
      color: #333;
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 6px;
      line-height: 20px;
    }
    .ring-than {
      margin-bottom: 0;
      color: #999;
    }
  }
}
</style>

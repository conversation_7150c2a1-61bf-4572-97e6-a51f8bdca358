<template>
  <div class="data-board-header-container">
    <div class="data-board-header-container__box">
      <div class="data-board-header-container__box__left">
        <FSpin size="large" :spinning="heaserInfo.loading">
          <div class="data-board-header-container__box__left__header">
            <TitleCard
              :first="true"
              bg-color="#F5FAFE"
              :num="heaserInfo.registerNum"
              :label-num="heaserInfo.registerRatio"
            />
            <TitleCard
              bgColor="#FFF7EE"
              :title="i18n.t('UV数据')"
              :num="heaserInfo.uvNum"
              :labelNum="heaserInfo.uvRatio"
            />
          </div>
        </FSpin>
        <div class="data-board-header-container__box__left__content">
          <p class="data-board-header-container__box__left__content__title">{{ i18n.t('业务数据概览') }}</p>
          <FSpin size="large" :spinning="localOrderNumInfo.loading">
            <DataCard
              page-color="#FBFDFF"
              :charts-colors="['#CBDCFFFF', '#4D7CFEFF']"
              :charts-title="i18n.t('订单总额')"
              charts-unit="(US$)"
              :info="firstInfo"
            />
            <DataCard
              page-color="#F9FEFCFF"
              :charts-colors="['#CCF4E3FF', '#2FCC83FF']"
              :info="twoInfo"
              :charts-title="i18n.t('订单总数')"
              :charts-unit="i18n.t('个')"
              style="margin-top: 12px; margin-bottom: 15px"
            />
          </FSpin>
          <FSpin size="large" :spinning="retentionData.loading">
            <DataCard
              page-color="#FFFCF8"
              :charts-colors="['#FEE5CCFF', '#FA8F23FF']"
              :info="threeInfo"
              :charts-title="i18n.t('Case总数')"
              :charts-unit="i18n.t('个')"
              :has-bottom="true"
            />
          </FSpin>
        </div>
      </div>
      <div class="data-board-header-container__box__right">
        <div class="data-board-header-container__box__right__tasks-num">
          <p class="title">{{ i18n.t('流程任务完成总数') }}</p>
          <div class="data-board-header-container__box__right__tasks-num__task-box">
            <FSpin size="large" wrapper-class-name="cust-task-num" :spinning="processTaskNumInfos.loading">
              <TaskCard :process-task-num-info="props.processTaskNumInfos.processTaskNumInfo" />
            </FSpin>
          </div>
        </div>
        <div class="data-board-header-container__box__right__node-num">
          <div class="data-board-header-container__box__right__node-num__title">
            <span class="title">{{ i18n.t('流程节点任务完成数') }}</span>
            <div>
              <FSelect
                v-model:value="selectTask"
                class="cust-select"
                style="width: 80px"
                placement="bottomRight"
                :dropdownMatchSelectWidth="false"
                :options="selectOptions"
                :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
                :field-names="{ label: 'label', value: 'value' }"
                @change="onChange"
              >
              </FSelect>
            </div>
          </div>
          <div class="data-board-header-container__box__right__node-num__node-box">
            <FSpin size="large" wrapper-class-name="cust-task-num" :spinning="processTaskNumInfos.loading">
              <NodeCard :node-lst="nodeLst" />
            </FSpin>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import TitleCard from './TitleCard.vue'
import DataCard from './DataCard.vue'
import TaskCard from './TaskCard.vue'
import NodeCard from './NodeCard.vue'
import { LocalOrderNumIndo, RegisterAndUVInfo, Datum, RetentiondataInfoRes } from '@/types/localizationSystem/dataBoard'
import { useI18n } from '@/utils'
const i18n = useI18n()

type TOption = {
  label: string
  value: string
}

type TNode = {
  name: string
  value: number
}

type propsType = {
  localOrderNumInfo: LocalOrderNumIndo
  heaserInfo: RegisterAndUVInfo
  processTaskNumInfos: { loading: boolean; processTaskNumInfo: Datum[] }
  retentionData: RetentiondataInfoRes
}

const props = withDefaults(defineProps<propsType>(), {
  localOrderNumInfo: () => ({
    OMSPrice: undefined,
    TMSPrice: undefined,
    totalPrice: undefined,
    OMSNum: undefined,
    TMSNum: undefined,
    totalNum: undefined,
    OMSNumRate: undefined,
    TMSNumRate: undefined,
    totalNumRate: undefined,
    OMSPriceRate: undefined,
    TMSPriceRate: undefined,
    totalPriceRate: undefined,
    graph: {},
  }),
  heaserInfo: () => ({
    registerNum: 0,
    registerRatio: '',
    uvNum: 0,
    uvRatio: '',
  }),
  processTaskNumInfos: () => ({
    loading: false,
    processTaskNumInfo: [],
  }),
  retentionData: () => ({
    totalCount: '',
    totalCountRate: 0,
    phoneCount: 0,
    phoneCountRate: 0,
    salesCount: '',
    salesCountRate: 0,
    liveChatCount: 0,
    liveChatCountRate: 0,
    graphData: {},
  }),
})
const selectTask = ref<string>('')
const selectOptions = ref<TOption[]>([])
const nodeLst = ref<TNode[]>([])

const onChange = (val: any) => {
  let list = props.processTaskNumInfos.processTaskNumInfo.find(item => item && item.processName === val)
  nodeLst.value = (list?.processTaskChilds || []).map(item => ({
    name: item.processTaskName,
    value: item.processTaskCompleteNum,
  })) as TNode[]
}

const firstInfo = computed(() => {
  let dataName = []
  let dataValue = []
  for (const key in props.localOrderNumInfo.graph) {
    dataName.push(props.localOrderNumInfo.graph[key].date)
    dataValue.push(props.localOrderNumInfo.graph[key].orderPrice)
  }
  let param = {
    firstName: i18n.t('订单总额(US$)'),
    firstPrice: props.localOrderNumInfo.totalPrice,
    firstRate: props.localOrderNumInfo.totalPriceRate,
    twoName: i18n.t('OMS订单总额(US$)'),
    twoPrice: props.localOrderNumInfo.OMSPrice,
    twoRate: props.localOrderNumInfo.OMSPriceRate,
    threeName: i18n.t('TMS订单总额(US$)'),
    threePrice: props.localOrderNumInfo.TMSPrice,
    threeRate: props.localOrderNumInfo.TMSPriceRate,
    chartsData: {
      dataName,
      dataValue,
    },
  }
  return param
})

const twoInfo = computed(() => {
  let dataName = []
  let dataValue = []
  for (const key in props.localOrderNumInfo.graph) {
    dataName.push(props.localOrderNumInfo.graph[key].date)
    dataValue.push(props.localOrderNumInfo.graph[key].orderNum)
  }
  let param = {
    firstName: i18n.t('订单总数(个)'),
    firstPrice: props.localOrderNumInfo.totalNum,
    firstRate: props.localOrderNumInfo.totalNumRate,
    twoName: i18n.t('OMS订单数(个)'),
    twoPrice: props.localOrderNumInfo.OMSNum,
    twoRate: props.localOrderNumInfo.OMSNumRate,
    threeName: i18n.t('TMS订单数(个)'),
    threePrice: props.localOrderNumInfo.TMSNum,
    threeRate: props.localOrderNumInfo.TMSNumRate,
    chartsData: {
      dataName,
      dataValue,
    },
  }
  return param
})

const threeInfo = computed(() => {
  let param = {
    firstName: i18n.t('Case总数(个)'),
    firstPrice: props.retentionData.totalCount,
    firstRate: parseFloat(props.retentionData.totalCountRate.toFixed(2)),
    twoName: i18n.t('电话 Case数(个)'),
    twoPrice: props.retentionData.phoneCount,
    twoRate: parseFloat(props.retentionData.phoneCountRate.toFixed(2)),
    threeName: i18n.t('Sales邮箱 Case数(个)'),
    threePrice: props.retentionData.salesCount,
    threeRate: parseFloat(props.retentionData.salesCountRate.toFixed(2)),
    fourName: i18n.t('Livechat Case数(个)'),
    fourPrice: props.retentionData.liveChatCount,
    fourRate: parseFloat(props.retentionData.liveChatCountRate.toFixed(2)),
    chartsData: {
      dataName: Object.keys(props.retentionData.graphData),
      dataValue: Object.values(props.retentionData.graphData),
    },
  }
  return param
})

watch(
  () => props.processTaskNumInfos.processTaskNumInfo,
  newVal => {
    if (newVal.length) {
      let list = newVal.filter(item => item)
      selectOptions.value = list.map(item => ({ label: item.processName || '', value: item.processName || '' }))
      selectTask.value = list[0].processName
      nodeLst.value = (list[0].processTaskChilds || []).map(item => ({
        name: item.processTaskName,
        value: item.processTaskCompleteNum,
      }))
    } else {
      selectOptions.value = []
      selectTask.value = ''
      nodeLst.value = []
    }
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.data-board-header-container {
  width: 100%;
  &__box {
    display: flex;
    width: 100%;
    &__left {
      flex: 1;
      width: 66%;
      &__header {
        display: flex;
        justify-content: space-between;
      }
      &__content {
        width: 100%;
        margin-top: 16px;
        padding: 24px;
        padding-top: 20px;
        background: #ffffff;
        border-radius: 4px;
        &__title {
          height: 24px;
          margin-bottom: 20px;
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          line-height: 24px;
        }
      }
    }
    &__right {
      display: flex;
      flex: 1;
      width: 33%;
      padding: 24px;
      padding-top: 20px;
      background: #ffffff;
      border-radius: 4px;
      .title {
        height: 20px;
        margin-bottom: 40px;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        line-height: 20px;
      }
      &__tasks-num,
      &__node-num {
        display: flex;
        flex-direction: column;
      }
      &__tasks-num {
        &__task-box {
          .cust-task-num {
            height: 100%;
            :deep(.fs-spin-container) {
              height: 100%;
            }
          }
          flex: 1;
        }
      }
      &__node-num {
        &__title {
          display: flex;
          justify-content: space-between;
          :deep(.cust-select) {
            .fs-select-selector {
              padding: 0;
              box-shadow: none !important;
              border: none;
            }
          }
        }
        &__node-box {
          flex: 1;
          .cust-task-num {
            height: 100%;
            :deep(.fs-spin-container) {
              height: 100%;
            }
          }
        }
      }
    }
  }
  @media screen and (max-width: 1456px) {
    &__box {
      flex-wrap: wrap;
      flex-direction: column;
      &__left,
      &__right {
        width: 100%;
      }
      &__right {
        margin-top: 20px;
        margin-bottom: 24px;
        &__tasks-num,
        &__node-num {
          flex: 1 1 50%;
          min-height: 360px;
        }
        &__tasks-num {
          &__task-box {
            margin-bottom: 0px;
          }
        }
        &__node-num {
          &__title {
            padding-left: 24px;
          }
        }
      }
    }
  }
  @media screen and (min-width: 1456px) {
    &__box {
      flex-wrap: nowrap;
      padding-bottom: 24px;
      &__left {
        flex: 1 1 942px;
        padding-right: 16px;
      }
      &__right {
        flex-direction: column;
        flex: 1 1 458px;
        &__tasks-num,
        &__node-num {
          width: 100%;
          height: 50%;
        }
        &__tasks-num {
          &__task-box {
            margin-bottom: 40px;
          }
        }
      }
    }
  }
}
</style>

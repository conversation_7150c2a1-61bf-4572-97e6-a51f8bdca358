export interface Res<T = null> {
  code: number // 状态码
  status: string // 消息
  data: T // 数据
}

export interface TRes<T = null> {
  code: number // 状态码
  msg: string // 消息
  traceId: string // 消息
  data: T // 数据
}

export interface LocalOrderNumParams {
  country?: any[]
  startTime?: string
  endTime?: string
  [key: string]: any
}

interface Graph {
  [key: string]: {
    date: string
    orderPrice: string
    orderNum: number
  }
}

export interface LocalOrderNumIndo {
  OMSPrice: string | undefined
  TMSPrice: string | undefined
  totalPrice: string | undefined
  OMSNum: number | undefined
  TMSNum: number | undefined
  totalNum: number | undefined
  OMSNumRate: number | undefined
  TMSNumRate: number | undefined
  totalNumRate: number | undefined
  OMSPriceRate: number | undefined
  TMSPriceRate: number | undefined
  totalPriceRate: number | undefined
  graph: Graph | undefined
  [key: string]: any
}

interface ChartsData {
  dataName: any[]
  dataValue: any[]
}

export interface DataCard {
  firstName: string
  firstPrice: any
  firstRate: number | undefined
  twoName: string
  twoPrice: any
  twoRate: number | undefined
  threeName: string
  threePrice: any
  threeRate: number | undefined
  fourName?: string
  fourPrice?: any
  fourRate?: number | undefined
  chartsData: ChartsData | undefined
  [key: string]: any
}

export interface RegisterAndUVInfo {
  registerNum: number
  registerRatio: string
  uvNum: number
  uvRatio: string
  [key: string]: any
}

export interface Datum {
  processName: string
  sumProcessTaskCompleteNum: number
  processTaskChilds: ProcessTaskChild[]
}

interface ProcessTaskChild {
  processTaskName: string
  processTaskCompleteNum: number
}

export interface LocalizationConfig {
  ext: string
  id: number
  isUsing: number
  name: string
}

export interface RetentiondataParams {
  lang: string
  localizationDataQry: LocalOrderNumParams
}

interface RetentiondataInfoResGraph {
  [key: string]: string
}

export interface RetentiondataInfoRes {
  totalCount: string
  totalCountRate: number
  phoneCount: number
  phoneCountRate: number
  salesCount: string
  salesCountRate: number
  liveChatCount: number
  liveChatCountRate: number
  graphData: RetentiondataInfoResGraph
  [key: string]: any
}

export interface RetentiondataInfo {
  res: RetentiondataInfoRes
}

export interface LocalizationRegion {
  regionId: number
  regionName: string
  parentRegionId: number
  localizationRegions: LocalizationRegion[]
  ext: unknown
}

export interface IAllLocalizationCountry {
  type: number
}

export interface IAllLocalizationCountryData {
  countryId: number
  countryName: string
  countryNameEn: string
  localizationCountrys: IAllLocalizationCountryData[]
}

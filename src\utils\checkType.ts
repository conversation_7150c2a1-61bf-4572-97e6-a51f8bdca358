export const isBoolean = (value: unknown): value is boolean => typeof value === 'boolean'
export const isString = (value: unknown): value is string => typeof value === 'string'
export const isNumber = (value: unknown): value is number => typeof value === 'number'
export const isUndefined = (value: unknown): value is undefined => typeof value === 'undefined'
export const isNull = (value: unknown): value is null => value === null
export const isObject = (value: unknown): value is object => typeof value === 'object'
export const isArray = (value: unknown): value is unknown[] => Array.isArray(value)
// eslint-disable-next-line @typescript-eslint/ban-types
export const isFunction = (value: unknown): value is Function => typeof value === 'function'
export const isSymbol = (value: unknown): value is symbol => typeof value === 'symbol'
export const isRegExp = (value: unknown): value is RegExp => value instanceof RegExp
export const isDate = (value: unknown): value is Date => value instanceof Date

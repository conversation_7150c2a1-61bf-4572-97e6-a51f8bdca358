<template>
  <div class="chat-logs" ref="chatLogsRef">
    <FSpin :spinning="loading" />
    <div class="chat-log-wrapper" v-for="item in props.data" :key="item.id">
      <div class="chat-log-avatar"><img :src="defaultfImg" /></div>
      <div class="chat-log-content">
        <div class="chat-log-userinfo">
          <span class="chat-log-username" :title="item.sendBy">{{ item.sendBy }}</span>
          <div class="chat-log-op">
            <i class="iconfont icontubiao_xiaoxi" title="回复" @click="handleClick('reply', item)" />
            <FPopconfirm :ok-text="i18n.t('确定')" :cancel-text="i18n.t('取消')" @confirm="handleClick('urgent', item)">
              <template #title>
                <p class="color333 fontSize14 marginB5">{{ i18n.t('确定加急给被@人吗？') }}</p>
                <div class="color999 fontSize12">{{ i18n.t('点击确定后将触发飞书提醒') }}</div>
              </template>
              <i class="iconfont iconcaidanbeifen" title="加急" />
            </FPopconfirm>
            <FPopconfirm
              v-if="!isOutTime(item.sendDate)"
              :ok-text="i18n.t('确定')"
              :cancel-text="i18n.t('取消')"
              @confirm="handleClick('revoke', item)"
            >
              <template #title>
                <p class="color333 fontSize14 marginB5">{{ i18n.t('确定撤回消息吗？') }}</p>
                <div class="color999 fontSize12">{{ i18n.t('撤回消息后将不可恢复') }}</div>
              </template>
              <i class="iconfont icontubiao_chehui" title="撤回" />
            </FPopconfirm>
          </div>
        </div>
        <div class="chat-log-text">
          <MyViewer :html="item.content" />
        </div>
        <div class="chat-log-tips">
          <span class="chat-log-time">{{ transformDate(item.sendDate, 'YYYY-MM-DD HH:mm') }}</span>
          <div class="chat-log-read" v-if="item.messageReadRsps">
            <div v-if="item.messageReadRsps.length === 1">
              <span style="color: #378eef">@ {{ item.messageReadRsps[0].receiver }}</span>
              <span class="marginL4 green" v-if="item.messageReadRsps[0].isRead">{{ i18n.t('未读') }}</span>
              <span class="marginL4" v-else>{{ i18n.t('已读') }}</span>
            </div>
            <div v-else>
              <ReadingPop :reading-list="item.messageReadRsps.filter(item => item.isRead === 0)" :reading="true" />
              <ReadingPop :reading-list="item.messageReadRsps.filter(item => item.isRead === 1)" :reading="false" />
            </div>
          </div>
        </div>
        <div class="chat-log-file" v-if="item.file && item.file.length">
          <div
            class="chat-log-file-item"
            v-for="file in item.file"
            :key="file.resourseKey"
            @click="download(file.resourseKey, file.name)"
          >
            <i class="chat-log-file-icon iconfont">&#xe655;</i>
            <span class="chat-log-file-name" :title="file.name">{{ file.name }}</span>
            <span class="chat-log-file-size"> ({{ (file.size / 1024).toFixed(2) }} KB)</span>
            <i
              v-if="getPreviewShow(file.name)"
              class="iconfont icontubiao_yanjing cursor color4677C7"
              @click="handlePreview(file.resourseKey)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, Ref, onUnmounted } from 'vue'
import { transformDate, download, useI18n } from '@/utils'
import { IMessage } from '@/types/request'

import ReadingPop from '../ReadingPop/index.vue'
import MyViewer from '@/components/Editor/MyViewer.vue'

import defaultfImg from '@/assets/images/head.png'

interface IProps {
  data: IMessage[]
}

const i18n = useI18n()
const emits = defineEmits(['operate'])
const props = defineProps<IProps>()
const loading = inject<Ref<boolean>>('charLogsloading')
const chatLogsRef = ref()
const currTime = ref(Date.now())
const timer = ref()
const observer = ref()

onMounted(() => {
  if (!chatLogsRef.value) return
  const chatLogs = chatLogsRef.value
  observer.value = new MutationObserver(() => (chatLogs.scrollTop = chatLogs.scrollHeight))
  observer.value.observe(chatLogsRef.value, { childList: true, subtree: true, attributes: true })

  // 5分钟刷新一次当前时间
  timer.value = setInterval(() => (currTime.value = Date.now()), 5 * 60 * 1000)
})

onUnmounted(() => {
  clearInterval(timer.value)
  observer.value?.disconnect()
})

const isOutTime = (date: string) => {
  const time = new Date(date).getTime()
  const diff = (currTime.value - time) / 1000 / 60
  return diff > 10
}

const handleClick = (type: string, item: IMessage) => {
  emits('operate', { type, item })
}

const getPreviewShow = (name: string) => {
  const key = (name.split('.').pop() as string).toLowerCase()
  return ['jpg', 'png', 'tif', 'gif', 'svg', 'pdf', 'txt', 'mp4'].includes(key)
}

const handlePreview = (url: string) => window.open(url)
</script>

<style scoped lang="scss">
.green {
  color: #3dcca6;
}

.chat-logs {
  flex: 1;
  padding: 0 18px;
  overflow-y: auto;

  :deep(.fs-spin) {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .chat-log-wrapper {
    position: relative;
    padding-left: 52px;

    > .chat-log-avatar {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;

      img {
        width: 36px;
        height: 36px;
        border-radius: 50%;
      }

      &::before {
        position: absolute;
        content: '';
        bottom: -4px;
        left: 18px;
        width: 1px;
        height: calc(100% - 36px);
        background-color: #eeeeee;
      }
    }

    > .chat-log-content {
      padding-bottom: 32px;

      > .chat-log-userinfo {
        color: #333333;
        font-size: 12px;
        font-family: PingFangSC, PingFang SC;

        p {
          margin: 0;
          line-height: 18px;
        }

        > .chat-log-op {
          float: right;

          > i {
            display: inline-block;
            width: 20px;
            height: 20px;
            color: #4677c7;
            font-size: 16px;
            text-align: center;
            line-height: 20px;
            border-radius: 2px;
            cursor: pointer;

            &:hover {
              background-color: #e1e8f0;
            }
          }
        }
      }

      > .chat-log-tips {
        display: flex;
        color: #666;

        > .chat-log-read {
          margin-left: 6px;
        }
      }

      > .chat-log-file {
        > .chat-log-file-item {
          display: flex;
          align-items: center;
          cursor: pointer;

          > * {
            margin-right: 6px;
          }

          > .chat-log-file-icon {
            color: #fba54f;
            font-size: 12px;
          }

          > .chat-log-file-size {
            color: #999;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div
    v-sticky="{
      container: '#container',
      offset: 12,
      type: 'sticky',
      top: -20,
    }"
    class="process-nav-container"
  >
    <div class="top-content">
      <div class="title-info">
        <span class="title" :title="approvalInfo?.topicName ?? '--'">{{ approvalInfo?.topicName ?? '--' }}</span>
        <FTag v-if="statusInfo?.status" style="margin-left: 8px" :border="false" :color="statusInfo.status">
          {{ statusInfo.label }}
        </FTag>
        <TipBtn :tip-title="i18n.t('复制该申请链接')">
          <i
            class="iconfont icontubiao_fuzhi copy-text"
            style="margin-left: 8px"
            :data-clipboard-text="copyTest"
            @click="copy"
          />
        </TipBtn>
      </div>
      <div class="operation">
        <FButton @click="push({ name: 'approvalWorkbench' })">
          <i class="iconfont icontubiao_chehui" />
          {{ i18n.t('返回') }}
        </FButton>
        <template v-if="handleNode">
          <FButton type="primary" @click="onSubmitNodeFn" v-if="nav === 'ApprovalInfo'">
            <i class="iconfont icontubiao_chenggong marginL4" />

            {{ (handleNode?.properties?.type === 1 && i18n.t('同意')) || i18n.t('提交') }}
          </FButton>
          <FButton
            danger
            @click="refuseNodeModalRef?.open"
            v-if="nav === 'ApprovalInfo' && handleNode?.properties?.type === 1"
          >
            <i class="iconfont icontubiao_shanchu2 marginL4" />
            {{ i18n.t('拒绝') }}
          </FButton>
          <FDropdown
            v-if="handleNode?.properties?.operation"
            :dropdownMatchSelectWidth="true"
            :getPopupContainer="target"
            :overlay-style="{ minWidth: 'auto', zIndex: 999 }"
          >
            <template #overlay>
              <FMenu>
                <FMenuItem
                  key="1"
                  v-if="(handleNode?.properties?.operation || []).includes(1)"
                  @click="transferNodeModalRef?.open"
                >
                  <i class="iconfont icontubiao_xietongzhuanpai marginL4" />
                  {{ i18n.t('转办') }}
                </FMenuItem>
                <!-- <FMenuItem key="2">
                  <i class="iconfont icontubiao_tuisong marginL4" />
                  {{ i18n.t('抄送') }}
                </FMenuItem> -->
                <FMenuItem
                  key="3"
                  v-if="
                    (handleNode?.properties?.operation || []).some(item => [3, 4].includes(item)) &&
                    beforeNodeList?.length
                  "
                  @click="rejectNodeModalRef?.open(beforeNodeList)"
                >
                  <i class="iconfont icontubiao_chehui marginL4" />
                  {{ i18n.t('驳回') }}
                </FMenuItem>
                <FMenuItem
                  key="4"
                  v-if="(handleNode?.properties?.operation || []).includes(2) && handleNode?.userList?.length > 1"
                  @click="subtractionNodeModalRef?.open"
                >
                  <i class="iconfont icona-bianzu3 marginL4" />
                  {{ i18n.t('减签') }}
                </FMenuItem>
                <FMenuItem
                  key="5"
                  v-if="(handleNode?.properties?.operation || []).includes(2)"
                  @click="countersignNodeModalRef?.open"
                >
                  <i class="iconfont iconicon_tianjiafenlei marginL4" />
                  {{ i18n.t('加签') }}
                </FMenuItem>
              </FMenu>
            </template>
            <FButton>
              {{ i18n.t('更多') }}
              <i class="iconfont iconjiantouxia marginL4" />
            </FButton>
          </FDropdown>
        </template>
      </div>
    </div>
    <div class="text-content">
      <span class="text">{{ approvalInfo?.processInstanceCode }}</span>
      <span class="line" />
      <span class="text">
        {{ i18n.t('申请人：') }}
      </span>
      <span class="text">
        {{ approvalInfo?.creatorName }}
      </span>
      <span class="line" />
      <span class="text">
        {{ i18n.t('发起时间：') }}
      </span>
      <span class="text">
        {{ approvalInfo?.createdTime ? dayjs(approvalInfo?.createdTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}
      </span>
    </div>
    <SubmitNodeModal ref="submitNodeModalRef" />
    <RefuseNodeModal ref="refuseNodeModalRef" />
    <TransferNodeModal ref="transferNodeModalRef" />
    <CountersignNodeModal ref="countersignNodeModalRef" />
    <SubtractionNodeModal ref="subtractionNodeModalRef" />
    <RejectNodeModal ref="rejectNodeModalRef" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, inject, Ref } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { useI18n, BAESURL } from '@/utils'
import { ApprovalInfoRes, HandleNodeList } from '@/api'
import Clipboard from 'clipboard'
import { message } from '@fs/smart-design'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import SubmitNodeModal from './SubmitNodeModal.vue'
import RefuseNodeModal from './RefuseNodeModal.vue'
import TransferNodeModal from './TransferNodeModal.vue'
import CountersignNodeModal from './CountersignNodeModal.vue'
import SubtractionNodeModal from './SubtractionNodeModal.vue'
import RejectNodeModal from './RejectNodeModal.vue'

interface IProps {
  approvalInfo?: ApprovalInfoRes
  nav?: string
}
const i18n = useI18n()
const props = defineProps<IProps>()
const target = ref(() => document.querySelector('.bpm-approval-container'))
const statusInfo = computed(() => {
  switch (props.approvalInfo?.status) {
    case 0:
      return {
        status: 'warning',
        label: i18n.t('进行中'),
      }
    case 1:
      return {
        status: 'success',
        label: i18n.t('已完成'),
      }
    case 2:
      return {
        status: 'success',
        label: i18n.t('已拒绝'),
      }
    default:
      return {}
  }
})
// { text: '转交', value: 1 },
// { text: '加签', value: 2 },
// { text: '驳回上一级', value: 3 },
// { text: '驳回任一级', value: 4 },
const handleNode = inject<Ref<HandleNodeList>>('handleNode') as Ref<HandleNodeList> // 当前里程碑信息
const nodeList = inject<Ref<HandleNodeList[]>>('nodeList') as Ref<HandleNodeList[]> // 当前里程碑信息
const beforeNodeList = computed(() => {
  const index = nodeList?.value?.findIndex(item => item.id === handleNode?.value.id)
  return nodeList?.value?.slice(1, index).filter(item => [3, 4, 5].includes(item.status))
})
const submitNodeModalRef = ref()
const refuseNodeModalRef = ref()
const transferNodeModalRef = ref()
const countersignNodeModalRef = ref()
const subtractionNodeModalRef = ref()
const rejectNodeModalRef = ref()

const copyTest = ref('')
const copy = () => {
  copyTest.value = `${BAESURL}/bpm-manage/approval/detail/${props.approvalInfo.id}`
  const clipboard = new Clipboard('.copy-text')
  clipboard.on('success', e => {
    message.success(i18n.t('复制成功'))
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    message.warning(i18n.t('复制失败'))
    clipboard.destroy()
  })
}

const onSubmitNodeFn = async () => {
  submitNodeModalRef?.value?.open()
}

const { push } = useRouter()
</script>

<style scoped lang="scss">
.process-nav-container {
  margin-top: 1px;
  padding: 24px;
  background-color: #fff;
  padding-bottom: 0px;
  box-sizing: border-box;

  .top-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-info {
      display: flex;
      align-items: center;
      flex: 1;
      width: 0;

      .title {
        height: 30px;
        font-size: 18px;
        font-weight: 500;
        color: #333333;
        line-height: 30px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .copy-text {
    color: #999999;
    cursor: pointer;
  }
  .operation {
    flex-shrink: 0;
    margin-left: 24px;
    z-index: 10;

    :deep(.fs-btn) {
      .iconfont {
        margin-right: 4px;
      }

      &.btn-primary-view {
        border-color: #378eef;
        color: #378eef;

        &:hover {
          border-color: #5fa4f2;
          color: #5fa4f2;
        }

        &:active {
          border-color: #2c71bf;
          color: #2c71bf;
        }
      }
    }

    :deep(.fs-btn + .fs-btn) {
      margin-left: 12px;
    }
  }

  .text-content {
    padding: 16px 0 24px;
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 18px;
    height: 18px;
    box-sizing: content-box;
    display: flex;
    align-items: center;

    .line {
      display: inline-block;
      width: 1px;
      height: 18px;
      background: #eeeeee;
      margin: 0px 8px;
      vertical-align: middle;
    }
  }
}
</style>

<template>
  <div class="bpm-approval-container">
    <FSpin :spinning="pageLoading">
      <!-- <Breadcrumb class="approve-box-shadow" :data="[i18n.t('审批工作流'), i18n.t('工作台'), i18n.t('流程处理')]" /> -->
      <NavHeader :approval-info="approvalInfo" :nav="nav" />
      <NavTabs :list="navs" v-model:value="nav" />
      <div class="approval-main-container marginT24">
        <FSpin :spinning="componentsLoading" style="width: 100%">
          <component
            v-if="id && components[nav]?.component && !componentsLoading"
            :is="components[nav].component"
            v-model:page="pageData"
            v-model:search="nodeTableSearch"
            @get-data-fn="components[nav]?.getDataFn"
            v-bind="components[nav]?.componentAttr ?? {}"
          />
        </FSpin>
      </div>
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, reactive, provide, defineAsyncComponent, watch, markRaw } from 'vue'
import { useRoute } from 'vue-router'
import NavHeader from './components/NavHeader.vue'
import NavTabs from './components/NavTabs.vue'
import Breadcrumb from '@/views/pgb-data-board/components/Breadcrumb/index.vue'
import { useI18n } from '@/utils'
import { BasicPageParams } from '@/types/processBoard'
import {
  selectApprovalByInstanceId,
  ApprovalInfoRes,
  getApplyFormByInstanceId,
  getFlowChartByInstanceId,
  pageApprovalNodeList,
  readApplyByInstanceId,
  PageApprovalNodeListParams,
} from '@/api'

const i18n = useI18n()
const { id } = useRoute().params as { id: string }
const formRenderRef = ref<any>()
const pageLoading = ref<boolean>(false)
const componentsLoading = ref<boolean>(false)
const navs = computed(() =>
  [
    {
      value: 'ApprovalInfo',
      label: '流程表单',
    },
    {
      value: 'ApprovalStatusTable',
      label: '审批状态',
    },
    {
      value: 'WorkFlow',
      label: '流程图',
    },
  ].filter(Boolean)
)
const nodeTableSearch = ref<PageApprovalNodeListParams>({
  type: 1,
})
const nav = ref('ApprovalInfo')
const approvalInfo = ref<ApprovalInfoRes>()
const handleNode = computed(
  () => (approvalInfo?.value?.handleNodeList && approvalInfo?.value?.handleNodeList[0]) || null
)
const nodeList = computed(() => approvalInfo?.value?.nodeList ?? [])

const components = reactive({
  ApprovalInfo: {
    component: markRaw(defineAsyncComponent(() => import('./components/ApprovalInfo.vue'))),
    componentAttr: {},
    getDataFn: () => getApplyFormFn(),
  },
  ApprovalStatusTable: {
    component: markRaw(defineAsyncComponent(() => import('./components/NodeTable.vue'))),
    componentAttr: {},
    getDataFn: () => getNodeTableFn(),
  },
  WorkFlow: {
    component: markRaw(defineAsyncComponent(() => import('./components/ProcessDesigner/index.vue'))),
    componentAttr: {},
    getDataFn: () => getFlowChartFn(),
  },
})
const pageData = reactive<BasicPageParams>({
  currPage: 1,
  pageSize: 10,
  total: 0,
})

const getApprovalInfoFn = async () => {
  try {
    pageLoading.value = true
    const res = await selectApprovalByInstanceId(id)
    if (res.code !== 200) throw new Error(res.msg)
    approvalInfo.value = res?.data
    components[nav.value]?.getDataFn && components[nav.value].getDataFn()
    readApplyByInstanceIdFn()
  } finally {
    pageLoading.value = false
  }
}

const readApplyByInstanceIdFn = async () => {
  const res = await readApplyByInstanceId(id)
  if (res.code !== 200) throw new Error(res.msg)
}

const getApplyFormFn = async () => {
  try {
    componentsLoading.value = true
    const res = await getApplyFormByInstanceId(id)
    if (res.code !== 200) throw new Error(res.msg)
    components.ApprovalInfo.componentAttr = { applyFormInfo: res?.data ?? {} }
  } finally {
    componentsLoading.value = false
  }
}

const getNodeTableFn = async () => {
  try {
    componentsLoading.value = true
    const params = {
      instanceId: id,
      pageNum: pageData.currPage,
      pageSize: pageData.pageSize,
      type: nodeTableSearch.value.type,
    }
    const res = await pageApprovalNodeList(params)
    if (res.code !== 200) throw new Error(res.msg)
    pageData.total = res?.data?.totalCount || 0
    components.ApprovalStatusTable.componentAttr = {
      list: res?.data?.list ?? [],
    }
  } finally {
    componentsLoading.value = false
  }
}

const getFlowChartFn = async () => {
  try {
    componentsLoading.value = true
    const res = await getFlowChartByInstanceId(id)
    if (res.code !== 200) throw new Error(res.msg)
    components.WorkFlow.componentAttr = res?.data ?? {}
  } finally {
    componentsLoading.value = false
  }
}

watch(
  () => nav.value,
  val => {
    components[val]?.getDataFn && components[val].getDataFn()
  }
)

onMounted(() => {
  if (id) {
    getApprovalInfoFn()
  }
})

provide('handleNode', handleNode)
provide('nodeList', nodeList)
provide('approvalInfo', approvalInfo)
provide('formRenderRef', formRenderRef)
provide('setNavFn', (value: any) => (nav.value = value))
provide('setFormRenderRef', (ref: any) => (formRenderRef.value = ref))
provide('getApprovalInfoFn', getApprovalInfoFn)
</script>

<style scoped lang="scss">
.bpm-approval-container {
  display: flex;
  flex-direction: column;
  position: relative;
  .approve-box-shadow {
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    margin: -20px -20px 0px 0px;
    padding: 0;
    padding-left: 24px;
  }
  :deep(.fs-dropdown-menu) {
    min-width: 86px !important;
    .fs-dropdown-menu-item {
      width: max-content;
    }
  }
}
</style>

<template>
  <div class="data-board-container">
    <FSpin size="large" :spinning="loading">
      <SearchCard @on-search="search" :countryList="countryList" :cbuCountryList="cbuCountryList" />
      <BoardContent
        :localOrderNumInfo="localOrderNumInfo"
        :heaserInfo="heaserInfo"
        :processTaskNumInfos="processTaskNumInfos"
        :retentionData="retentionData"
      />
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import SearchCard from './components/SearchCard.vue'
import BoardContent from './components/BoardContent.vue'
import {
  getStatisticsToken,
  getLocalOrderNum,
  getRegisterAndUV,
  getProcessTaskNum,
  getRetentiondata,
  selectAllLocalizationCountry,
} from '@/api/localizationSystem/dataBoard'
import {
  LocalOrderNumIndo,
  RegisterAndUVInfo,
  Datum,
  RetentiondataInfoRes,
  RetentiondataInfo,
  IAllLocalizationCountryData,
} from '@/types/localizationSystem/dataBoard'
import dayjs from 'dayjs'
import { getDefaultValue } from '@/views/localizationSystem/lib/utils'

type IpForm = {
  country: number[] | undefined
  state: string[] | undefined
  startTime: string | undefined
  endTime: string | undefined
}

type TCountry = {
  name: string
  id: number
}

const store = useStore()
const searchQuery = reactive<IpForm>({
  country: undefined,
  state: undefined,
  startTime: dayjs().startOf('month').format('YYYY-MM-DD'),
  endTime: dayjs().format('YYYY-MM-DD'),
})

const search = async (searchConditions: any) => {
  searchQuery.country = searchConditions.country
  searchQuery.state = searchConditions.state
  if (searchConditions.time && searchConditions.time.length) {
    searchQuery.startTime = dayjs(searchConditions.time[0]).format('YYYY-MM-DD')
    searchQuery.endTime = dayjs(searchConditions.time[1]).format('YYYY-MM-DD')
  } else {
    searchQuery.startTime = dayjs().startOf('month').format('YYYY-MM-DD')
    searchQuery.endTime = dayjs().format('YYYY-MM-DD')
  }
  getData()
}

const loading = ref<boolean>(false)
const localOrderNumInfo = reactive<LocalOrderNumIndo>({
  OMSPrice: undefined,
  TMSPrice: undefined,
  totalPrice: undefined,
  OMSNum: undefined,
  TMSNum: undefined,
  totalNum: undefined,
  OMSNumRate: undefined,
  TMSNumRate: undefined,
  totalNumRate: undefined,
  OMSPriceRate: undefined,
  TMSPriceRate: undefined,
  totalPriceRate: undefined,
  graph: {},
  loading: false,
})

const heaserInfo = reactive<RegisterAndUVInfo>({
  registerNum: 0,
  registerRatio: '',
  uvNum: 0,
  uvRatio: '',
  loading: false,
})

const retentionData = reactive<RetentiondataInfoRes>({
  totalCount: '',
  totalCountRate: 0,
  phoneCount: 0,
  phoneCountRate: 0,
  salesCount: '',
  salesCountRate: 0,
  liveChatCount: 0,
  liveChatCountRate: 0,
  graphData: {},
  loading: false,
})

const processTaskNumInfos = reactive<{ loading: boolean; processTaskNumInfo: Datum[] }>({
  processTaskNumInfo: [],
  loading: false,
})
const countryList = ref<IAllLocalizationCountryData[]>([])
const cbuCountryList = ref<IAllLocalizationCountryData[]>([])

const getStatisticsTokens = async () => {
  const res = await getStatisticsToken()
  if (res.code === 200) {
    store.dispatch('user/setStateToken', res.data.token || '')
    await getData()
  }
}

const getAllLocalizationConfig = async () => {
  const cbuRes = await selectAllLocalizationCountry({ type: 0 })
  const res = await selectAllLocalizationCountry({ type: 1 })
  if (res.code === 200) {
    countryList.value = res.data
    cbuCountryList.value = cbuRes.data
    const valueData = getDefaultValue(cbuRes.data, {}, 'localizationCountrys', '', '新加坡', 'countryId', 'countryName')
    valueData['新加坡'] && (searchQuery.country = [valueData['新加坡'].at(-1)])
  }
}

const getLocalOrderNumFn = async () => {
  try {
    localOrderNumInfo.loading = true
    const res = await getLocalOrderNum(searchQuery)
    if (res.code === 200) {
      Object.assign(localOrderNumInfo, res.data)
    } else {
      Object.assign(localOrderNumInfo, {
        OMSPrice: undefined,
        TMSPrice: undefined,
        totalPrice: undefined,
        OMSNum: undefined,
        TMSNum: undefined,
        totalNum: undefined,
        OMSNumRate: undefined,
        TMSNumRate: undefined,
        totalNumRate: undefined,
        OMSPriceRate: undefined,
        TMSPriceRate: undefined,
        totalPriceRate: undefined,
        graph: {},
      })
    }
  } finally {
    localOrderNumInfo.loading = false
  }
}

const getRegisterAndUVFn = async () => {
  try {
    heaserInfo.loading = true
    const res = await getRegisterAndUV(searchQuery)
    if (res.code === 200) {
      Object.assign(heaserInfo, res.data)
    } else {
      Object.assign(heaserInfo, {
        registerNum: 0,
        registerRatio: '',
        uvNum: 0,
        uvRatio: '',
      })
    }
  } finally {
    heaserInfo.loading = false
  }
}

const getProcessTaskNumFn = async () => {
  try {
    processTaskNumInfos.loading = true
    const res = await getProcessTaskNum(searchQuery)
    if (res.code === 200) {
      processTaskNumInfos.processTaskNumInfo = res.data as Datum[]
    } else {
      processTaskNumInfos.processTaskNumInfo = []
    }
  } finally {
    processTaskNumInfos.loading = false
  }
}

const getRetentiondataFn = async () => {
  try {
    retentionData.loading = true
    const res = await getRetentiondata({ lang: 'zh', localizationDataQry: searchQuery })
    if (res.code === 200) {
      Object.assign(
        retentionData,
        (res.data as RetentiondataInfo)?.res ?? {
          totalCount: '',
          totalCountRate: 0,
          phoneCount: 0,
          phoneCountRate: 0,
          salesCount: '',
          salesCountRate: 0,
          liveChatCount: 0,
          liveChatCountRate: 0,
          graphData: {},
        }
      )
    } else {
      Object.assign(retentionData, {
        totalCount: '',
        totalCountRate: 0,
        phoneCount: 0,
        phoneCountRate: 0,
        salesCount: '',
        salesCountRate: 0,
        liveChatCount: 0,
        liveChatCountRate: 0,
        graphData: {},
      })
    }
  } finally {
    retentionData.loading = false
  }
}

const getData = async () => {
  try {
    loading.value = true
    if (!store.state.user.stateToken) {
      await getStatisticsTokens()
    } else {
      getLocalOrderNumFn()
      getRegisterAndUVFn()
      getProcessTaskNumFn()
      getRetentiondataFn()
    }
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  try {
    loading.value = true
    await getAllLocalizationConfig()
    await getStatisticsTokens()
  } finally {
    loading.value = false
  }
})
</script>

<style lang="scss" scoped>
.data-board-container {
  width: 100%;
  height: 100%;
}
</style>

<template>
  <div class="select-box shadow-radius">
    <div class="select-box-left">
      <div class="search-box">
        <div class="marginR12">
          <FSelect
            v-if="props.processTypeList && props.processTypeList.length > 0"
            v-model:value="searchData.processConfigId"
            :press-line="i18n.t('流程类型')"
            show-search
            allow-clear
            style="width: 120px"
            :placeholder="i18n.t('请选择')"
            option-filter-prop="label"
            :options="props.processTypeList.map(item => ({ value: item.id, label: item.processName }))"
            @change="onProcessType"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe799;</i>
            </template>
          </FSelect>
        </div>

        <div class="marginR12">
          <FSelect
            v-model:value="searchData.status"
            :press-line="i18n.t('状态')"
            style="width: 120px"
            allow-clear
            :placeholder="i18n.t('请选择')"
            :options="statusList"
            @change="onSearch"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe799;</i>
            </template>
          </FSelect>
        </div>
        <div class="marginR12" v-if="hasEnvPublish">
          <FSelect
            v-model:value="searchData.publishStatus"
            :press-line="i18n.t('发布状态')"
            style="width: 120px"
            allow-clear
            :placeholder="i18n.t('请选择')"
            :options="publishStatus"
            @change="onSearch"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe799;</i>
            </template>
          </FSelect>
        </div>
        <div class="marginR12">
          <FSelect
            v-model:value="searchData.isSyn"
            :press-line="i18n.t('执行方式')"
            style="width: 120px"
            allow-clear
            :placeholder="i18n.t('请选择')"
            :options="isSynList"
            @change="onSearch"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe799;</i>
            </template>
          </FSelect>
        </div>

        <div class="marginR12">
          <FSelect
            v-model:value="searchData.triggerType"
            :press-line="i18n.t('触发器类型')"
            allow-clear
            style="width: 120px"
            :options="triggerList"
            :placeholder="i18n.t('请选择')"
            @change="onSearch"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe799;</i>
            </template>
          </FSelect>
        </div>

        <div class="marginR12">
          <FSelect
            v-if="props.createUserList && props.createUserList.length > 0"
            v-model:value="searchData.createdUserId"
            :press-line="i18n.t('创建人')"
            allow-clear
            show-search
            style="width: 120px"
            :options="props.createUserList.map(item => ({ value: item.uuid, label: item.nameCh }))"
            option-filter-prop="label"
            @change="onSearch"
            :placeholder="i18n.t('请选择')"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe799;</i>
            </template>
          </FSelect>
        </div>
        <div class="btn_div_relative marginR12">
          <span class="icon_span_absolute icon_span_absolute_left">{{ i18n.t('创建日期') }}</span>
          <FRangePicker
            v-model:value="range"
            @change="onSearch"
            format="YYYY-MM-DD"
            style="width: 240px; height: 32px"
            allow-clear
          />
        </div>
        <div class="btn_div_relative marginR12">
          <div class="serach-item">
            <span class="icon_span_absolute icon_span_absolute_left">{{ i18n.t('快速搜索') }}</span>
            <FInput
              :placeholder="i18n.t('触发器名称/备注说明/触发器编码')"
              style="width: 240px"
              @press-enter="onSearch"
              v-model:value="searchData.input"
            >
              <template #suffix>
                <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe70e;</i>
              </template>
            </FInput>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { SelectProps } from '@fs/smart-design/dist/ant-design-vue_es'
import dayjs from 'dayjs'
import { transformDate } from '@/utils'
import { processTye, triggerListSearch, triggerTypeRadioList } from '@/types/excutionListModel'
import { IUser } from '@/types/handle'
import { useI18n } from '@/utils'
import { useStore } from 'vuex'
import usePublish from '@/hooks/usePublish'

const { hasEnvPublish } = usePublish()
interface IProps {
  processTypeList: processTye[]
  createUserList: IUser[]
}
const i18n = useI18n()
const props = defineProps<IProps>()
const store = useStore()
const searchData = reactive<triggerListSearch>({
  processConfigId: undefined,
  status: undefined,
  triggerType: undefined,
  createdUserId: undefined,
  startTime: undefined,
  endTime: undefined,
  input: undefined,
  isSyn: undefined,
  publishStatus: undefined,
})
const range = ref<[dayjs.Dayjs, dayjs.Dayjs]>()
const statusList = ref<SelectProps['options']>([
  { value: 0, label: i18n.t('停用') },
  { value: 1, label: i18n.t('启用') },
])
const isSynList = ref<SelectProps['options']>([
  { value: 0, label: i18n.t('异步执行') },
  { value: 1, label: i18n.t('同步执行') },
])
const publishStatus = ref<SelectProps['options']>([
  { value: 1, label: i18n.t('已发布') },
  { value: 0, label: i18n.t('未发布') },
])
const triggerList = triggerTypeRadioList

const emit = defineEmits(['search'])
const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
onMounted(async () => {
  setDefaultSearch()
})

// 选择标签联动
const onProcessType = (val: any) => {
  searchData.processConfigId = val
  onSearch()
}

const setDefaultSearch = () => {
  const cache = store.getters['local/getLocalAutomationData']
  if (cache !== undefined && cache !== null) {
    const localSearchData = cache?.localAutomationSearchData ?? {}
    localSearchData.startTime &&
      localSearchData.endTime &&
      (range.value = [dayjs(localSearchData.startTime), dayjs(localSearchData.endTime)])
    for (const key in searchData) {
      searchData[key] = localSearchData[key]
    }
  }
  onSearch()
}

const onSearch = () => {
  const params = { ...searchData }
  params.startTime = range.value?.[0] && transformDate(range.value[0], 'YYYY-MM-DD')
  params.endTime = range.value?.[1] && transformDate(range.value[1], 'YYYY-MM-DD')
  emit('search', params)
}
</script>

<style lang="scss" scoped>
.select-box {
  padding-top: 24px;
  padding-bottom: 24px;
  padding-left: 24px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  margin-bottom: 16px;
  .select-box-left {
    flex: 1;
    display: flex;
    .search-box {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      .btn_div_relative {
        display: inline-block;
        position: relative;
        height: 34px;
        line-height: 1;
        :deep(.fs-picker) {
          .fs-picker-input-active {
            background-color: #fff;
          }
        }
        :deep(.fs-picker-focused) {
          .fs-picker-input-active {
            background-color: #f1f4f8 !important;
          }
        }
        :deep(.icon_span_absolute) {
          display: inline-block;
          position: absolute;
          top: -7px;
          left: 4px;
          z-index: 10;
          padding: 0 3px;
          color: #999;
          background: #fff;
          font-size: 11px;
          -webkit-transform: scale(0.9);
          transform: scale(0.9);
        }
        .icon_span_absolute_left {
          left: 7px;
        }
      }
      .marginR12 {
        margin-right: 12px;
      }
    }
  }
}
.shadow-radius {
  background: #ffffff;
  box-shadow: 0 2px 8px 0 rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
</style>

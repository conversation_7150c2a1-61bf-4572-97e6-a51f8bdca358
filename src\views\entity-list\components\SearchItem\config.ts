import RangeNumber from './components/RangeNumber/index.vue'
import { markRaw } from 'vue'
import { transformDate } from '@/utils'
import { i18n } from '@/init'

export const components = {
  inputCode: {
    componentName: 'FInput',
    componentAttrs: {
      style: {
        width: '162px',
      },
      pressLine: i18n.t('目标值'),
      placeholder: i18n.t('请输入目标值'),
      allowClear: true,
      getPopupContainer(triggerNode: Element) {
        return triggerNode?.parentNode || null
      },
    },
    getComponentValueFormat: (value: any) => {
      return (value && [value]) || undefined
    },
    getComponentValueLabelFormat: (value: any) => {
      return value || undefined
    },
    values: undefined,
  },
  inputNumberCode: {
    componentName: 'FInputNumber',
    componentAttrs: {
      style: {
        width: '162px',
      },
      pressLine: i18n.t('目标值'),
      placeholder: i18n.t('请输入目标值'),
      allowClear: true,
      getPopupContainer(triggerNode: Element) {
        return triggerNode?.parentNode || null
      },
    },
    getComponentValueFormat: (value: any) => {
      return (value && [value]) || undefined
    },
    getComponentValueLabelFormat: (value: any) => {
      return value || undefined
    },
    values: undefined,
  },
  selectCode: {
    componentName: 'FSelect',
    componentAttrs: {
      style: {
        width: '162px',
      },
      pressLine: i18n.t('目标值'),
      placeholder: i18n.t('请选择目标值'),
      allowClear: true,
      getPopupContainer(triggerNode: Element) {
        return triggerNode?.parentNode || null
      },
    },
    getComponentValueFormat: (value: any) => {
      return (value && [value]) || undefined
    },
    getComponentValueLabelFormat: (value: any) => {
      return value || undefined
    },
    values: undefined,
  },
  selectMultipleCode: {
    componentName: 'FSelect',
    componentAttrs: {
      style: {
        width: '162px',
      },
      pressLine: i18n.t('目标值'),
      placeholder: i18n.t('请选择目标值'),
      allowClear: true,
      mode: 'multiple',
      getPopupContainer(triggerNode: Element) {
        return triggerNode?.parentNode || null
      },
    },
    getComponentValueFormat: (value: any) => {
      return value || undefined
    },
    getComponentValueLabelFormat: (value: any) => {
      return (value && value.join()) || undefined
    },
    values: undefined,
  },
  // datePickerCode: {
  //   componentName: 'FDatePicker',
  //   componentAttrs: {
  //     style: {
  //       width: '162px',
  //     },
  //     pressLine: i18n.t('目标值'),
  //     placeholder: i18n.t('请选择目标值'),
  //     allowClear: true,
  //     getPopupContainer (triggerNode: Element) { return triggerNode?.parentNode || null},
  //   },
  //   getComponentValueFormat: (value: any) => {
  //     return value && [transformDate(value, 'YYYY-MM-DD HH:mm:ss')] || undefined
  //   },
  //   getComponentValueLabelFormat: (value: any) => {
  //     return value && transformDate(value, 'YYYY-MM-DD') || undefined
  //   },
  //   values: undefined,
  // },
  rangePickerCode: {
    componentName: 'FRangePicker',
    componentAttrs: {
      style: {
        width: '162px',
      },
      pressLine: i18n.t('目标值'),
      allowClear: true,
      getPopupContainer(triggerNode: Element) {
        return triggerNode?.parentNode || null
      },
    },
    getComponentValueFormat: (value: any) => {
      return (
        (value &&
          value.length === 2 && [
            transformDate(value[0], 'YYYY-MM-DD HH:mm:ss'),
            transformDate(value[1], 'YYYY-MM-DD HH:mm:ss'),
          ]) ||
        undefined
      )
    },
    getComponentValueLabelFormat: (value: any) => {
      return (
        (value &&
          value.length === 2 &&
          [transformDate(value[0], 'YYYY-MM-DD'), transformDate(value[1], 'YYYY-MM-DD')].join()) ||
        undefined
      )
    },
    values: undefined,
  },
  rangeNumberCode: {
    componentName: markRaw(RangeNumber),
    componentAttrs: {
      style: {
        width: '162px',
      },
    },
    getComponentValueFormat: (value: any) => {
      return (value && value.length === 2 && [value[0], value[1]]) || undefined
    },
    getComponentValueLabelFormat: (value: any) => {
      return (value && value.length === 2 && [value[0], value[1]].join()) || undefined
    },
    values: [undefined, undefined],
  },
}

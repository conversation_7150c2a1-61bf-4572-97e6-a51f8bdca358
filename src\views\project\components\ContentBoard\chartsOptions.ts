import type { EChartsOption } from '@/components/BaseEchart/config'
import imgEmpty from './images/empty.svg'

/**
 * 金额格式化，添加千位分隔符
 * @param {number | string} value 要格式化的数字
 * @returns {string} 格式化后的字符串
 */
export function formatAmountWithCommas(value: number | string): string {
  if (value === null || value === undefined || value === '') {
    return '--'
  }
  const parts = String(value).split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return parts.join('.')
}

// 公共配置
const commonTooltip: EChartsOption['tooltip'] = {
  appendToBody: true,
  textStyle: { color: '#fff' },
  backgroundColor: '#000000B3',
  borderColor: 'transparent',
  borderRadius: 4, // 圆角
  padding: [8, 12, 8, 12], // 内边距
}

export const commonLegend: EChartsOption['legend'] = {
  orient: 'horizontal', // 横向排列
  bottom: 0, // 放在底部
  // left: 'center',
  // icon: 'pie',
  icon: 'path://M0,0A4,4,0,1,1,0,8A4,4,0,1,1,0,0Z',
  itemWidth: 8,
  itemHeight: 8,
  // formatter 可自定义显示内容
  // formatter: (name: string) => name,
  padding: [0, 0, 0, 0], // 图例内边距
  itemGap: 12, // 图例项之间的间距
  textStyle: {
    fontSize: 12,
    color: '#666666',
  },
}

const commonColors = ['#37BBEF', '#2FCC83', '#FA8F23', '#F5CC38', '#6762FC', '#04BEC4', '#378EEF', '#F04141']

export const commonTitle: EChartsOption['title'] = {
  show: true,
  text: '',
  subtext: '',
  left: '49%',
  top: '26%',
  textAlign: 'center',
  itemGap: 6,
  textStyle: {
    fontSize: '12px',
    color: '#666666',
    fontWeight: 400,
  },
  subtextStyle: {
    fontSize: '16px',
    color: '#333333',
    fontWeight: 500,
  },
}

// 折线图公共配置
const commonLineGrid = {
  left: 0,
  right: 4,
  top: 22,
  bottom: 5,
  containLabel: true,
}
const commonLineXAxis = {
  type: 'category' as const,
  boundaryGap: false,
  data: [],
  axisLine: {
    show: false,
    lineStyle: {
      color: '#ccc',
      width: 1,
    },
  },
  axisTick: { show: false },
  axisLabel: {
    color: '#999',
    fontSize: 12,
  },

  splitLine: {
    show: false,
    lineStyle: {
      color: '#EEEEEE',
      width: 1,
    },
  },
}
const commonLineYAxis = {
  type: 'value' as const,
  axisLine: { show: false },
  axisTick: { show: false },
  axisLabel: {
    color: '#999',
    fontSize: 12,
  },
  splitLine: {
    show: true,
    lineStyle: {
      color: '#F1F2F4',
      width: 1,
    },
  },
}
const commonLineTooltip = {
  trigger: 'axis' as const,
  backgroundColor: '#000000B3',
  borderColor: 'transparent',
  textStyle: { color: '#fff' },
  borderRadius: 4, // 圆角
  padding: [8, 12, 8, 12], // 内边距
  axisPointer: {
    type: 'line' as const,
    lineStyle: {
      color: '#666666',
      type: 'solid' as const,
      width: 1,
    },
  },
}

const commonGraphic = {
  type: 'image',
  left: 'center',
  top: '28%',
  style: {
    image: imgEmpty,
    width: 80,
    height: 101,
  },
}

export const pieOptions: EChartsOption = {
  tooltip: {
    ...commonTooltip,
    trigger: 'item',
    valueFormatter: (value: any) => value + '个',
    formatter: (params: any) => {
      // params.marker 是自带的颜色方块图标
      // params.name 是扇区名称 (如 '园区交换机')
      // params.value 是扇区数值 (如 17)
      // params.percent 是扇区百分比 (如 14)
      // 获取当前数据项的颜色
      const itemColor = params.color || '#fff' // 确保有颜色，默认为白色

      // 自定义 marker 的 HTML 结构和样式
      const customMarker = `
        <span style="
          display: inline-block;
          margin-right: 8px;
          width: 8px;
          height: 8px;
          border-radius: 2px;
          background-color: ${itemColor};
          vertical-align: middle;
        "></span>
      `
      return `
        ${customMarker} <span>${params.name}</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left;">项目数</span> <span>${params.value}</span>
      `
    },
  },
  legend: {
    ...commonLegend,
  },
  color: commonColors,
  series: [
    {
      name: 'outerPie',
      type: 'pie',
      silent: true, // 关闭 hover 和 tooltip
      label: { show: false },
      labelLine: { show: false },
      radius: ['60%', '65%'], // 外环
      center: ['50%', '35%'], // 中上位置
      data: [{ value: 1, itemStyle: { color: '#F1F2F4' } }],
    },
    {
      type: 'pie',
      radius: ['40%', '60%'], // 主环
      center: ['50%', '35%'], // 中上位置
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'inside',
        formatter: '{c}',
        color: '#fff',
        fontSize: 12,
        fontWeight: 400,
      },
      // emphasis: { scaleSize: 15 },
      labelLine: { show: false },
      data: [],
    },
    {
      name: 'innerPie',
      type: 'pie',
      silent: true, // 关闭 hover 和 tooltip
      label: { show: false },
      labelLine: { show: false },
      radius: ['35%', '40%'], // 内环
      center: ['50%', '35%'], // 中上位置
      data: [{ value: 1, itemStyle: { color: '#F1F2F4' } }],
    },
    {
      type: 'pie',
      radius: ['0%', '65%'], // 主环
      center: ['50%', '45%'], // 中上位置
      label: {
        show: true,
        color: '#999999',
      },
      labelLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC', // 线颜色
          width: 1,
        },
      },
      avoidLabelOverlap: false,
      data: [],
    },
  ],
}
const getSplitInterval = (data: number[]) => {
  let max = Math.ceil(Math.max(...data) / 100) * 100
  if (max === 0) {
    max = 100
  }
  return max
}
/**
 * 生成折线图 option
 * @param {Object} params
 * @param {string} params.title 图表标题
 * @param {string} params.yName Y轴名称
 * @param {string[]} params.xData X轴类目
 * @param {number[]} params.seriesData 折线数据
 * @param {string} params.lineColor 折线颜色
 * @param {string} params.unit 单位
 * @returns {EChartsOption}
 */
export function getLineOptions({
  title,
  yName,
  xData,
  seriesData,
  lineColor = '#378EEF',
  unit,
  formatter,
  rotate,
  areaColor,
  ellipsisLimit,
  fontSize,
}: {
  title: string
  yName: string
  xData: string[]
  seriesData: number[]
  lineColor?: string
  unit?: string
  formatter?: any
  rotate?: number
  areaColor?: string | string[] // 修改类型定义
  ellipsisLimit?: number
  fontSize?: number
}): EChartsOption {
  const nummax = Math.max(...seriesData)
  const isEmpty = !seriesData || seriesData.length === 0
  // 处理区域颜色
  let areaStyle = {}
  if (areaColor) {
    if (Array.isArray(areaColor)) {
      // 如果是数组，使用渐变
      areaStyle = {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: areaColor[0] },
            { offset: 1, color: areaColor[1] },
          ],
        },
      }
    } else {
      // 如果是单个颜色，直接使用
      areaStyle = {
        color: areaColor,
      }
    }
  } else {
    // 默认使用透明区域
    areaStyle = {
      color: 'transparent',
    }
  }
  return {
    dataZoom: {
      show: xData?.length > 10, // 为true 滚动条出现
      realtime: true,
      type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示距离顶端36px，一般就是在图上面。
      height: 6, // 表示滚动条的高度，也就是粗细
      start: 0, // 表示默认展示百分比范围。
      end: (xData?.length > 10 && (10 / xData?.length) * 100) || 100,
      showDetail: false,
      zoomLock: true,
      brushSelect: false,
      bottom: 0,
      handleStyle: {
        borderWidth: 0,
        borderCap: 'round',
        color: 'rgba(221, 225, 229, 1)',
        // borderRadius: '50%',
      },
      textStyle: {
        color: 'transparent',
      },
      borderColor: 'transparent',
      dataBackground: {
        areaStyle: {
          color: 'transparent',
        },
        lineStyle: {
          color: 'transparent',
        },
      },
      selectedDataBackground: {
        areaStyle: {
          color: 'transparent',
        },
        lineStyle: {
          color: 'transparent',
        },
      },
      fillerColor: '#DDD',
      borderRadius: 4, // 整个滚动条圆角
      moveHandleStyle: {
        color: '#DDD',
        shadowColor: '#DDD',
        borderCap: 'round',
      },
    },
    title: {
      show: true,
      text: '',
      subtext: unit || yName,
      left: -4,
      top: -14,
      subtextStyle: {
        color: '#BBBBBB',
        fontSize: 11,
        fontWeight: 400,
      },
      textStyle: {
        fontSize: 0,
      },
    },
    grid: { ...commonLineGrid },
    graphic: isEmpty ? commonGraphic : [],
    xAxis: isEmpty
      ? undefined
      : {
          ...commonLineXAxis,
          data: xData,
          axisLabel: {
            ...commonLineXAxis.axisLabel,
            fontSize: fontSize,
            rotate: rotate,
            ...(ellipsisLimit && {
              formatter: function (value: string) {
                if (value.length > ellipsisLimit) {
                  return value.substring(0, ellipsisLimit) + '...'
                }
                return value
              },
            }),
          },
        },
    yAxis: isEmpty
      ? undefined
      : {
          min: 0,
          max: nummax,
          splitNumber: 5,
          minInterval: 1,
          interval: Number((nummax / 5).toFixed(2)),
          ...commonLineYAxis,
          name: yName,
          nameTextStyle: {
            color: '#A6B1C2',
            fontSize: 12,
            fontWeight: 400,
            align: 'left',
            padding: [0, 0, 8, 0],
          },
        },
    tooltip: {
      ...commonLineTooltip,
      formatter: formatter,
    },
    series: isEmpty
      ? []
      : [
          {
            name: title,
            type: 'line',
            data: seriesData,
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            showSymbol: true,
            lineStyle: {
              color: lineColor,
              width: 2,
            },
            itemStyle: {
              color: lineColor,
              borderColor: '#fff',
              borderWidth: 2,
            },
            areaStyle: areaStyle,
            emphasis: {
              focus: 'series',
            },
          },
        ],
  }
}

/**
 * 生成柱状图 option
 * @param {Object} params
 * @param {string} params.title 图表标题
 * @param {string} params.unit 单位
 * @param {string[]} params.xData x轴类目
 * @param {number[]} params.seriesData 柱状数据
 * @param {string[]} params.color 柱状颜色
 * @returns {EChartsOption}
 */
export function getBarOptions({
  title = '',
  unit = '',
  xData = [],
  seriesData = [],
  color = ['#378EEF', '#22C993', '#F5CC38', '#FA8F23'],
  formatter,
  barWidth = 26,
  ellipsisLimit,
}: {
  title?: string
  unit?: string
  xData: string[]
  seriesData: number[]
  color?: string[]
  formatter?: any
  barWidth?: any
  ellipsisLimit?: any
}): EChartsOption {
  // 颜色轮询处理
  const coloredSeriesData = seriesData.map((value, idx) => ({
    value,
    itemStyle: { color: color[idx % color.length] },
  }))
  const isEmpty = !seriesData || seriesData.length === 0
  const nummax = Math.max(...seriesData)
  return {
    dataZoom: {
      show: xData?.length > 8, // 为true 滚动条出现
      realtime: true,
      type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示距离顶端36px，一般就是在图上面。
      height: 6, // 表示滚动条的高度，也就是粗细
      start: 0, // 表示默认展示百分比范围。
      end: (xData?.length > 8 && (8 / xData?.length) * 100) || 100,
      showDetail: false,
      zoomLock: true,
      brushSelect: false,
      bottom: 0,
      handleStyle: {
        borderWidth: 0,
        borderCap: 'round',
        color: 'rgba(221, 225, 229, 1)',
      },
      textStyle: {
        color: 'transparent',
      },
      borderColor: 'transparent',
      dataBackground: {
        areaStyle: {
          color: 'transparent',
        },
        lineStyle: {
          color: 'transparent',
        },
      },
      selectedDataBackground: {
        areaStyle: {
          color: 'transparent',
        },
        lineStyle: {
          color: 'transparent',
        },
      },
      fillerColor: '#DDD',
      moveHandleStyle: {
        color: '#DDD',
        shadowColor: '#DDD',
        borderCap: 'round',
      },
    },
    title: {
      show: true,
      text: '',
      subtext: unit,
      left: -4,
      top: -14,
      subtextStyle: {
        color: '#BBBBBB',
        fontSize: 11,
        fontWeight: 400,
      },
      textStyle: {
        fontSize: 0,
      },
    },
    color,
    grid: {
      left: 0,
      right: 0,
      top: 22,
      bottom: 5,
      containLabel: true,
    },
    legend: {
      show: false,
    },
    graphic: isEmpty ? commonGraphic : [],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#666666',
          type: 'solid',
          width: 1,
        },
      },
      backgroundColor: '#000000B3',
      borderColor: 'transparent',
      textStyle: { color: '#fff' },
      borderRadius: 4, // 圆角
      padding: [8, 12, 8, 12], // 内边距
      formatter: formatter,
    },
    xAxis: isEmpty
      ? undefined
      : {
          type: 'category',
          data: xData,
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: '#999',
            fontSize: 12,
            interval: 0, // 强制显示所有标签
            ...(ellipsisLimit && {
              formatter: function (value: string) {
                if (value.length > ellipsisLimit) {
                  return value.substring(0, ellipsisLimit) + '...'
                }
                return value
              },
            }),
          },
          splitLine: { show: false },
        },
    yAxis: isEmpty
      ? undefined
      : {
          type: 'value',
          name: unit,
          min: 0,
          max: nummax,
          splitNumber: 5,
          minInterval: 1,
          interval: Number((nummax / 5).toFixed(2)),
          nameTextStyle: {
            color: '#BBBBBB',
            fontSize: 11,
            fontWeight: 400,
            align: 'left',
            padding: [0, 0, 8, 0],
          },
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#EEEEEE',
              width: 1,
            },
          },
        },
    series: isEmpty
      ? []
      : [
          {
            name: title,
            type: 'bar',
            data: coloredSeriesData,
            barWidth: barWidth,
            barMinHeight: 2,
            itemStyle: {
              borderRadius: [4, 4, 0, 0],
            },
            emphasis: {
              itemStyle: {
                opacity: 1,
              },
            },
          },
        ],
  }
}

export const barOptions: EChartsOption = {
  color: ['#378EEF'],
  tooltip: {
    trigger: 'axis',
    appendToBody: true,
    axisPointer: {
      type: 'none',
    },
    textStyle: {
      color: '#fff',
    },
    valueFormatter: (value: any) => {
      return value + '个'
    },
    backgroundColor: '#000000B3',
    borderColor: 'transparent',
  },
  grid: {
    left: '0',
    right: '0',
    top: '12%',
    bottom: '0',
  },
  xAxis: {
    show: false,
    data: [],
  },
  yAxis: {
    type: 'value',
    show: false,
  },
  series: [],
}

export function getBarOneOptions({
  title = '',
  unit = '',
  xData = [],
  seriesData = [],
  color = ['#378EEF', '#22C993', '#F5CC38', '#FA8F23'],
  formatter,
  barWidth = 26,
  ellipsisLimit,
}: {
  title?: string
  unit?: string
  xData: string[]
  seriesData: number[]
  color?: string[]
  formatter?: any
  barWidth?: any
  ellipsisLimit?: any
}): EChartsOption {
  // 颜色轮询处理
  const coloredSeriesData = seriesData.map((value, idx) => ({
    value,
    itemStyle: { color: color[idx % color.length] },
  }))
  const isEmpty = !seriesData || seriesData.length === 0
  return {
    dataZoom: {
      // orient: vertical,
      orient: 'vertical', // 垂直方向
      yAxisIndex: [0], // 控制第一个y轴
      show: xData?.length > 8, // 为true 滚动条出现
      realtime: true,
      type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示距离顶端36px，一般就是在图上面。
      // height: 6, // 表示滚动条的高度，也就是粗细
      start: 0, // 表示默认展示百分比范围。
      end: (xData?.length > 8 && (8 / xData?.length) * 100) || 100,
      showDetail: false,
      zoomLock: true,
      brushSelect: false,
      width: 6,
      left: 0,
      // right: 10, // 距离右侧的距离
      // top: 50, // 距离顶部的距离
      // bottom: 50, // 距离底部的距离
      handleStyle: {
        borderWidth: 0,
        borderCap: 'round',
        color: 'rgba(221, 225, 229, 1)',
      },
      textStyle: {
        color: 'transparent',
      },
      borderColor: 'transparent',
      dataBackground: {
        areaStyle: {
          color: 'transparent',
        },
        lineStyle: {
          color: 'transparent',
        },
      },
      selectedDataBackground: {
        areaStyle: {
          color: 'transparent',
        },
        lineStyle: {
          color: 'transparent',
        },
      },
      fillerColor: '#DDD',
      moveHandleStyle: {
        color: '#DDD',
        shadowColor: '#DDD',
        borderCap: 'round',
      },
    },
    title: {
      show: true,
      text: '',
      subtext: unit,
      left: -4,
      top: -14,
      subtextStyle: {
        color: '#BBBBBB',
        fontSize: 11,
        fontWeight: 400,
      },
      textStyle: {
        fontSize: 0,
      },
    },
    color,
    grid: {
      left: 10,
      right: 0,
      top: 22,
      bottom: 5,
      containLabel: true,
    },
    legend: {
      show: false,
    },
    graphic: isEmpty ? commonGraphic : [],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#666666',
          type: 'solid',
          width: 1,
        },
      },
      backgroundColor: '#000000B3',
      borderColor: 'transparent',
      textStyle: { color: '#fff' },
      borderRadius: 4,
      padding: [8, 12, 8, 12],
      formatter: formatter,
    },
    // 交换x轴和y轴的配置
    xAxis: isEmpty
      ? undefined
      : {
          type: 'value', // 改为数值轴
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            show: false, // 不显示x轴的数字
          },
          splitLine: {
            show: false, // 不显示网格线
          },
        },
    yAxis: isEmpty
      ? undefined
      : {
          type: 'category', // 改为类目轴
          data: xData, // 将xData放在y轴
          axisLine: {
            show: true,
            lineStyle: {
              color: '#CCCCCC',
              width: 1,
            },
          },
          axisTick: { show: false },
          axisLabel: {
            color: '#999',
            fontSize: 12,
            interval: 0,
            ...(ellipsisLimit && {
              formatter: function (value: string) {
                if (value.length > ellipsisLimit) {
                  return value.substring(0, ellipsisLimit) + '...'
                }
                return value
              },
            }),
          },
          splitLine: { show: false }, // 不显示网格线
        },
    series: isEmpty
      ? []
      : [
          {
            name: title,
            type: 'bar',
            data: coloredSeriesData,
            barWidth: barWidth, // 这里实际上控制条形的高度
            barMinHeight: 2,
            itemStyle: {
              borderRadius: [0, 4, 4, 0], // 调整圆角位置（右侧圆角）
            },
            emphasis: {
              itemStyle: {
                opacity: 1,
              },
            },
            // 添加标签显示数值
            label: {
              show: true,
              position: 'insideLeft',
              formatter: '{c}', // 显示数值
              color: '#fff',
              fontSize: 12,
            },
          },
        ],
  }
}

//
// 请求
// --------------------------------

import { IProcess } from './handle'
import { BasicFetchResponse } from '@/types/common'

export interface IRes<T = null> {
  code: number // 状态码
  message: string // 消息
  msg?: string // 消息
  data: T // 数据
}

export interface IResDataList<T = null> {
  pageNum: number //
  pageSize: number //
  startRow: number //
  endRow: number //
  pages: number //
  prePage: number //
  nextPage: number //
  size: number //
  total: number // 总页数
  totalCount: number // 总页数
  list: T[] // 数据列表
}

export interface ITask {
  taskId: string // 任务ID
  taskName: string // 任务名称
  taskAssignee: string // 指派人
  taskCreateTime: string // 创建时间
  version: number // 版本号
  businessKey: string // 业务ID
  executionId: string // 执行ID
  processKey: string // 流程key
  processDefinitionId: string // 流程定义ID
  processInstanceId: string // 流程实例ID
  processStatus: boolean // 流程状态
}

export interface ITaskHandleLog {
  taskId: string // 任务ID
  taskName: string // 任务名称
  assignee: string // 指派人
  message: string // 消息
  processInstanceId: string // 流程实例ID
  startTime: string // 开始时间
  endTime: string // 结束时间
  status: boolean // 状态 true 已处理  false 未处理
  taskStatus: string // 任务状态 0 - 待处理 1 - 已处理 2 - 驳回 - 3 - 终止
}

export interface ITaskFormInfo {
  formPage: string // 表单页面
  formData: string // 表单数据
}

export interface IIndicatorCenterInfo {
  completed: number // 已完成
  completedRingRatio: string // 已完成环比
  onTimeCompleted: number // 准时完成
  onTimeCompletedRate: string // 准时完成率
  postponed: number // 已延期
  defermentRate: string // 延期率
  total: number // 总数
  totalRingRatio: string // 总数环比
}

export interface ISeeMessage {
  instanceId: number // 实例id
  content: string // 内容
  processType: string // 流程类型
  projectNum: string // 项目编号
  sendBy: string // 发送人
  sendDate: string // 发送时间
}

export interface IMessage {
  content: string // 内容
  file: IFile[] // 文件
  id: number // id
  messageReadRsps: any[] // 已读人员
  processType: string // 流程类型
  processName: string // 流程名称
  projectNum: string // 项目编号
  receiver: IReceiver[] // 接收人
  sendBy: string // 发送人
  sendByUuid: string // 发送人uuid
  sendDate: string // 发送时间
  messageOpType: number // 0 可撤回 1 已撤回可编辑
  opExpire: string // messageOpType0 可撤回过期时间 1已撤回可编辑过期时间
}

export interface IFile {
  name: string // 文件名
  size: number // 文件大小
  resourseKey: string // 文件key
  percent?: number // 进度
  status?: string // 状态
  key?: string | number // 唯一值
}

export interface IReceiver {
  receiver: string // 接收人
  receiverUuid: string // 接收人uuid
}

export interface IProcessType {
  processConfigId: number
  instanceCount: number
  processName: string
  instanceList: IProcess[]
}

export interface IResourceType {
  uuid: string // 类型uuid
  name: string // 类型名称
  number: string // 类型编号
  status: number // 状态
  created_at: string // 创建时间
  updated_at: string // 更新时间
  locale: unknown[] // 本地化
  count: number // 数量
}

export interface IResource {
  uuid: string // 资源uuid
  name: string // 资源名称
  number: string // 资源编号
  status: number // 状态
  created_at: string // 创建时间
  updated_at: string // 更新时间
  locale: unknown[] // 本地化
  count: number // 数量
  file_name: string // 文件名
  file_uuid: string // 文件uuid
  file_size: number // 文件大小
  file_created_at: string // 文件创建时间
  mime_type: string // 文件类型
  last_version: string // 最新版本
  internal_downloads: number // 内部下载次数
  external_downloads: number // 外部下载次数
  allow_external: number // 是否允许外部下载
  visible: number // 是否可见
  labels: Array<Record<string, string>> // 标签
  lang: string // 语言
  module: number // 模块
  description: string // 描述
  creator_uuid: string // 创建人uuid
  creator_name: string // 创建人名称
  bpm_uuid: string // bpm uuid
  resource_category: unknown[] // 资源分类
  parent_uuid: string // 父级uuid
  parent_name: string // 父级名称
}

export interface IResourceLabel {
  uuid: string // 标签uuid
  name: string // 标签名称
  status: number // 状态
  created_at: string // 创建时间
  creator_name: string // 创建人名称
  creator_uuid: string // 创建人uuid
  updated_at: string // 更新时间
  locale: unknown[] // 本地化
  click_count: number // 点击次数
  resources_count: number // 资源数量
  sort: number // 排序
  status_remarks: string // 状态备注
  resource_category_uuid: string // 资源分类uuid
  parent_uuid: string // 父级uuid
  category: unknown[] // 分类
  children: IResourceLabel[] // 子级
  allow_external: number // 是否允许外部下载
}

export interface IProcessRoleAndUser {
  ext?: unknown
  instanceId: number
  id: number
  uuid: string
  name: string
  roleCode: string
  roleName: string
  isCustom: 0 | 1
  users: { uuid: string; name: string }[]
}

// 关联流程数据
export interface IRelationProcess {
  id: number
  instanceId: number // 流程 id
  projectNum: string // 流程编号
  projectName: string // 流程名称
  nodeOwner: string // 当前节点负责人
  sponsor: string // 发起人
  processType: string // 流程类型
  processConfigId: number // 流程配置 id
  currentStage: string // 当前阶段
  processStatus: 0 | 1 | 2 // 流程状态
  targetDate: string // 预计完成日期
  actualDate: string // 实际完成日期
  [key: string]: unknown // 其他
}

// 搜索配置
export interface ISearchConfig {
  field: string
  id: number
  moduleType: string
  name: string
  processConfigId: number
  remarks: string
  searchType: string
  status: number
  url: string
  valueList: ValueList[]
  valueType: number
  propsConfig: string
  [key: string]: any
}

interface ValueList {
  children: ValueList[]
  field: string
  name: string
  value: string
}

export interface ITabConfig {
  tabName: string
  formKey: string
}

export interface BasicPageParams {
  pageNum?: number
  pageSize?: number
  total?: number
}

export interface IPageMessage extends BasicPageParams {
  sendBy?: string[]
  receiver?: string[]
  endDate?: string
  isNew?: number
  nodeId?: string
  processConfigId?: number
  search?: string
  startDate?: string
}

export interface IMessageListItem {
  processType: string
  topicName: string
  processInstanceCode: string
  nodeName: string
  nodeId: string
  instanceId: string
  content: string
  sendBy: string
  receiver: string
  isRead: string
  isNew: string
  sendDate: string
}

export interface MessageListResponse extends BasicFetchResponse {
  data: {
    list: IMessageListItem[]
    total: number
    [key: string]: any
  }
}

// MES 用户信息
export interface IMESUserInfo {
  adminId: number // 用户id
  adminLevel: string // 用户等级
  adminName: string // 用户名
  assistant: string // 若assistantTag = 1,则assistant为当前登录用户的销售助手id,若assistantTag = 2 ,则assistant为当前登录用户下的销售id
  assistantTag: string // 1:销售助手 2:销售
  department: string // 部门
  duties: string // 职务 当 duties=60 时为销售总监
  power: number // 权限 0 销售总监,1 销售主管,2 销售,3 销售助手
  token: string // token
}

export interface IBPMUserInfo {
  token: string // token
  userInfo: {
    uuid: string // 用户id
    name: string // 用户名
    email: string // 邮箱
    originId: string // 用户id
    permissionUsers: IBPMUserInfo['userInfo'][]
    avatar: string // 头像
    createdAt: string // 创建时间
    updatedAt: string // 更新时间
  }
}

// 子应用
export interface IApplication {
  id: number // 应用 id
  projectId: number // 项目 id
  name: string // 应用名称
  director: string // 应用负责人
  entry: string // 应用入口
  keyword: string // 应用关键字
  create: string // 创建人
  createTime: string // 创建时间
  update: string // 更新人
  updateTime: string // 更新时间
}

// 菜单
export interface IMenu {
  id: number // 菜单 id
  projectId: number // 项目 id
  parentId: number | null // 父级菜单 id
  subAppId: number // 子应用 id
  name: string // 菜单名称
  type: string // 菜单类型
  icon: string // 菜单图标
  path: string // 菜单路径
  create: string // 创建人
  createTime: string // 创建时间
  update: string // 更新人
  updateTime: string // 更新时间
}

export interface IMicroAppConfig {
  id: number // 应用 id
  name: string // 应用名称
  director: string // 应用负责人
  access: string // 应用访问地址
  appid: string // 应用 appid
  application: IApplication[] // 子应用
  menu: IMenu[] // 菜单
  create: string // 创建人
  createTime: string // 创建时间
  update: string // 更新人
  updateTime: string // 更新时间
}

export interface IMapNav {
  sys: IMapNavItem[] // 系统
  custom: IMapNavItem[] // 自定义
}

export interface IMapNavItem {
  uuid: string // id
  type: number // 类型
  sort: number // 排序
  icon: string // 图标
  route: string // 路径
  title: string // 名称
  admin_uuid: string //
  created_at: string // 创建时间
  updated_at: string // 更新时间
}

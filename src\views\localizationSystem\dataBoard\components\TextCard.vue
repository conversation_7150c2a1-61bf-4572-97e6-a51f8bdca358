<template>
  <div class="board-text-container">
    <p class="board-text-container__text-title">{{ title || '--' }}</p>
    <p class="board-text-container__text-num">{{ (num && format(num)) || '--' }}</p>
    <p class="board-text-container__text-label">
      <span v-if="labelTitle" style="display: inline-block; margin-right: 4px">{{ labelTitle }}</span>
      <span class="cust-text" :style="{ color: labelNum < 0 ? '#F04141' : '#2FCC83' }">
        {{ labelNum >= 0 ? '+' : '' }}{{ labelNum || 0.0 }}%
        <i :class="['iconfont', labelNum < 0 ? 'icontubiao_xiajiang' : 'icontubiao_shangsheng']"></i>
      </span>
    </p>
  </div>
</template>

<script setup lang="ts">
import { toRefs } from 'vue'

type propsType = {
  title: string
  num: any
  labelTitle?: string
  labelNum?: number | string
}

const props = withDefaults(defineProps<propsType>(), {
  title: '',
  num: 0,
  labelTitle: '',
  labelNum: '',
})

const { title, num = 0, labelTitle, labelNum = 0 } = toRefs(props)
const format = (num: number): string => {
  return parseInt(num.toString())
    .toString()
    .replace(/^[+-]?\d+/, int => {
      return int.replace(/(\d)(?=(\d{3})+$)/g, '$1,')
    })
}
</script>

<style scoped lang="scss">
.board-text-container {
  min-width: 129px;
  p {
    margin-bottom: 0;
    color: #666666;
    line-height: 1.5;
  }
  &__text-title {
    font-size: 12px;
    font-weight: 400;
    white-space: nowrap;
  }
  &__text-num {
    margin-top: 2px;
    margin-top: 4px;
    font-size: 20px;
    font-weight: 500;
    color: #333333 !important;
  }
  &__text-label {
    font-size: 12px;
    white-space: nowrap;
    .cust-text {
      display: flex;
      align-items: center;
      color: #2fcc83;
    }
    i {
      font-size: 14px;
    }
  }
}
</style>

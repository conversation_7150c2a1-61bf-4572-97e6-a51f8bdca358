<template>
  <div class="relevance-info-container">
    <NavTabs :list="navs" v-model:value="nav" @change="tabChangeFn" :no-line="true" />
    <div class="tab-content" v-if="nav">
      <FSpin :spinning="componentsLoading" style="width: 100%">
        <keep-alive>
          <component v-show="!componentsLoading" :is="components[nav].component" :ref="setComponentsRefFn" />
        </keep-alive>
      </FSpin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, markRaw, defineAsyncComponent, nextTick } from 'vue'
import NavTabs from '@/views/process-class-detail/components/NavTabs/index.vue'

interface IProps {
  matlId?: string
  instanceId?: string
  processType?: string
  viewTableData?: any[]
  fromData?: any
  isChange?: boolean
}

const props = defineProps<IProps>()
const componentsLoading = ref<boolean>(false)
const navs = ref([
  {
    value: 'UserList',
    label: '人员清单',
  },
])
const nav = ref('UserList')
const components = reactive({
  UserList: {
    componentTitle: '人员清单',
    componentViewKey: 'UserList',
    componentRef: undefined,
    component: markRaw(
      defineAsyncComponent(
        () => import('@/views/manage-role-detail/components/RelevanceInfo/components/UserList/index.vue')
      )
    ),
    componentAttr: {},
    // getDataFn: async () => {},
  },
})

const tabChangeFn = val => {
  // !components[val]?.info?.flag && components[val]?.getDataFn()
}

const setComponentsRefFn = el => {
  nextTick(() => {
    components[nav.value].componentRef = el
  })
}

defineExpose({
  getRelevanceInfoDataFn: components,
})
</script>

<style scoped lang="scss">
.relevance-info-container {
  background-color: #fff;
  border-radius: 4px;
  .tab-content {
    min-height: min-content;
    padding: 24px;
    transition: all 1s;
  }
}
</style>

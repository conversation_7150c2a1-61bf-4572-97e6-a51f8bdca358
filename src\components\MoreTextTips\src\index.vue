<script setup lang="ts">
import { ref } from 'vue'

type PlacementType =
  | 'top'
  | 'left'
  | 'right'
  | 'bottom'
  | 'topLeft'
  | 'topRight'
  | 'bottomLeft'
  | 'bottomRight'
  | 'leftTop'
  | 'leftBottom'
  | 'rightTop'
  | 'rightBottom'
interface IProps {
  lineClamp?: number
  placement?: PlacementType
  bgColor?: string
  textColor?: string
  getPopupContainerElement?: any
}
withDefaults(defineProps<IProps>(), {
  lineClamp: 2,
  placement: 'bottomLeft',
  bgColor: '#fff',
  textColor: '#333',
  getPopupContainerElement: null,
})
defineOptions({
  name: 'MoreTextTips',
})
const contentRef = ref()
const showTooltip = ref(false)

const handleTooltipIn = () => {
  const $content = contentRef.value
  let range = document.createRange()
  range.setStart($content, 0)
  range.setEnd($content, $content.childNodes.length)
  const rangeHeight = range.getBoundingClientRect().height
  showTooltip.value = rangeHeight > $content.offsetHeight
  range = null as any
}
</script>

<template>
  <div
    v-if="!showTooltip"
    ref="contentRef"
    class="more-text-tips-box"
    :style="[{ '-webkit-line-clamp': lineClamp }]"
    @mouseenter="handleTooltipIn"
  >
    <slot />
  </div>
  <FTooltip
    v-else
    :get-popup-container="getPopupContainerElement"
    :placement="placement"
    :color="bgColor"
    :align="{ offset: [0, 0] }"
    :destroy-tooltip-on-hide="true"
  >
    <div class="more-text-tips-box" :style="[{ '-webkit-line-clamp': lineClamp }]">
      <slot />
    </div>
    <template #title>
      <div :style="`color: ${textColor}`">
        <slot />
      </div>
    </template>
  </FTooltip>
</template>

<style scoped lang="scss">
.more-text-tips-box {
  display: -webkit-box;
  overflow: hidden;
  width: 100%;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
</style>

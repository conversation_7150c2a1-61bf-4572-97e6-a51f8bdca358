<template>
  <div v-bind="$attrs" class="card-wrapper">
    <div class="card-title">
      <div class="left">
        <i v-if="icon" :class="`iconfont ${icon}`" />
        {{ props.title }}
      </div>
      <div class="right">
        <slot name="titleRight"></slot>
      </div>
    </div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  icon: {
    type: String,
    default: '',
  },
})
</script>

<style scoped lang="scss">
.card-wrapper {
  padding: 16px 20px 20px 20px;
  border-radius: 4px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.04);
  background-color: #fff;

  > .card-title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    padding-bottom: 15px;
    border-bottom: 1px solid #ececec;
    > i {
      font-size: 14px;
    }
  }
}
</style>

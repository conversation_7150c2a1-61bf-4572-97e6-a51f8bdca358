<template>
  <div v-if="visible" class="prompt-text">
    <div v-if="isTips" class="prompt">
      <slot name="tips">
        <i class="prompt-icon icon iconicon_tishi" />
      </slot>
    </div>

    <div class="text-content">
      <span v-if="text">{{ text }}</span>
      <slot v-else />
    </div>

    <div v-if="isClose" class="close">
      <slot name="close">
        <i class="icon icontubiao_shanchu11" @click="visible = false" style="color: #666" />
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
interface Iprops {
  text?: string
  isClose?: boolean
  isTips?: boolean
}
const visible = ref(true)
withDefaults(defineProps<Iprops>(), {
  text: '',
  isClose: true,
  isTips: true,
})
</script>

<style lang="scss" scoped>
.prompt-text {
  border: 1px solid #fdd2a7;
  background-color: #fef4e9;
  padding: 12px 16px;
  font-size: 12px;
  color: #333333;
  line-height: 18px;
  box-sizing: border-box;
  border-radius: 5px;
  display: flex;
  justify-content: space-around;

  .prompt-icon {
    color: #fa8f23;
    margin-right: 8px;
  }

  .text-content {
    flex: 1;
  }

  .close-icon {
    color: #fa8f23;
    cursor: pointer;
    margin-left: 8px;
  }
}
</style>

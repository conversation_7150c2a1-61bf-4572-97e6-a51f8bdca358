<template>
  <div class="user-warpper">
    <FAvatar class="user-avatar" :src="userInfo.avatar" :size="28" />
    <FDropdown trigger="click">
      <div :class="['user-info', arrowFlag ? 'active' : '']" @click.prevent="handleUserClick">
        <div class="user-info-wrapper">
          <p>{{ userInfo.adminName || userInfo.name }}</p>
          <p>{{ userInfo.department }}</p>
        </div>
        <i class="iconfont iconjiantouxia1" />
      </div>
      <template #overlay>
        <FMenu>
          <!-- 信息 -->
          <FMenuItem style="background-color: transparent !important; cursor: auto" key="0" disabled>
            <p class="w248 mt7 mb4 f14 lh22 fw-500 c333">
              {{ userInfo.adminName || userInfo.name }}
            </p>
            <p class="mb0 mt2 f12 lh18 c999">
              企业邮箱：{{ userInfo.email }}
              <FIcon class="ml8" type="icon-fuzhi1" @click="handleCopy(userInfo.email)" />
            </p>
          </FMenuItem>

          <!-- 中心 -->
          <FMenuDivider class="w mt8 mb8 ml12 mr12" />
          <FMenuItem key="1" @click="handleUserConfigClick">
            <template #icon><FIcon type="icon-renyuan1" /></template>{{ i18n.t('个人中心') }}
          </FMenuItem>
          <!-- <FMenuItem style="margin-top: 2px" key="2" @click.prevent="">
            <template #icon><FIcon type="icon-tishi1" /></template>{{ i18n.t('帮助中心') }}
          </FMenuItem> -->

          <!-- 登出 -->
          <FMenuDivider class="w mt8 mb8 ml12 mr12" />
          <FMenuItem class="mb4" key="9" @click="handleLogoutClick">
            <template #icon><FIcon type="icon-tuichu2" /></template>{{ i18n.t('退出登录') }}
          </FMenuItem>
        </FMenu>
      </template>
    </FDropdown>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import { message } from '@fs/smart-design'
import { i18n } from '@/init'
import { getUserInfo } from '@/utils'

const store = useStore()
const arrowFlag = ref(false)
const userInfo = computed(() => getUserInfo() ?? {})

const handleUserClick = () => {
  arrowFlag.value = !arrowFlag.value
}

const handleCopy = (text: string) => {
  const input = document.createElement('input')
  input.setAttribute('readonly', 'readonly')
  input.setAttribute('value', text)
  input.style.position = 'absolute'
  input.style.zIndex = '-9999'
  input.style.opacity = '0'
  document.body.appendChild(input)
  input.select()
  document.execCommand('copy')
  document.body.removeChild(input)
  message.success('复制成功')

  handleUserClick()
}

// 跳转到 uums 的个人中心
const handleUserConfigClick = () => {
  window.open('https://uums.fs.com/userManage/userInfo/settings/base')

  handleUserClick()
}

// 退出登录
const handleLogoutClick = () => {
  store.dispatch('user/logout')

  handleUserClick()
}
</script>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}

.w {
  width: auto !important;
}

.mt7 {
  margin-top: 7px;
}

.w248 {
  width: 248px;
}

.user-warpper {
  display: flex;
  align-items: center;
  width: 157px;
  height: 36px;
  background: #ecf7ff;
  border-radius: 18px;
  border: 1px solid #ffffff;

  .user-avatar {
    margin-left: 4px;
    margin-right: 8px;
  }
  .user-info {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

    &.active .fs-icon {
      transform: rotate(180deg);
    }

    .user-info-wrapper {
      margin-right: 4px;
      text-align: left;
      p {
        margin: 0;
        font-size: 14px;
        color: #333;
        line-height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      p:nth-child(2) {
        color: #999;
        font-size: 12px;
        line-height: 18px;
      }
    }
    .iconfont {
      margin-right: 8px;
      transition: all 0.3s;
    }
  }
}
</style>

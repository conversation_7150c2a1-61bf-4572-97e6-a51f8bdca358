import { TFText } from './translatro'

interface TranslationResponse {
  data: Record<string, string>
}

export abstract class BaseDevice {
  abstract translate(texts: string[], toLang: string): Promise<Partial<TFText>[]>
}

export class GoogleDevice extends BaseDevice {
  private requestCache: Map<string, Partial<TFText>[]>
  private maxCacheSize: number
  private debugCounters = {
    requests: 0,
    cacheHits: 0,
    errors: 0
  }
  private apiUrl: string
  private token?: string

  constructor(apiUrl: string, token?: string) {
    super()
    this.maxCacheSize = 1000
    this.requestCache = new Map()
    this.apiUrl = apiUrl
    this.token = token
  }

  private getToken(): string | undefined {
    if (this.token) return this.token

    // 从cookie中查找带有_SSO_TOKEN的token
    const cookies = document.cookie.split(';')
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=')
      if (name.includes('_SSO_TOKEN')) {
        return value
      }
    }
    return undefined
  }

  async translate(texts: string[], toLang: string): Promise<Partial<TFText>[]> {
    try {
      this.debugCounters.requests++

      // 检查缓存
      const cacheKey = this.getCacheKey(texts, toLang)
      const cachedResult = this.requestCache.get(cacheKey)
      if (cachedResult) {
        this.debugCounters.cacheHits++
        return cachedResult
      }

      // 获取翻译数据
      const data = await this.fetch(texts, toLang)

      // 解析翻译结果
      const result = this.parse(data)

      // 更新缓存
      this.updateCache(cacheKey, result)

      return result
    } catch (error) {
      this.debugCounters.errors++
      throw error
    }
  }

  private async fetch(texts: string[], toLang: string): Promise<TranslationResponse> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      // 添加token到header
      const token = this.getToken()
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          textList: texts,
          targetLang: toLang,
          sourceLang: 'zh' // 默认源语言为中文
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // 检查响应格式
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format from translation API')
      }

      // 确保响应数据存在且是对象
      const translations = data.data || data
      if (!translations || typeof translations !== 'object') {
        throw new Error('Response data should be an object')
      }

      return { data: translations }
    } catch (error) {
      console.error('[GoogleDevice] Fetch error:', error)
      throw error
    }
  }

  private parse(data: TranslationResponse): Partial<TFText>[] {
    return Object.entries(data.data).map(([key, value]) => ({
      translation: value,
      source: key
    }))
  }

  private getCacheKey(texts: string[], toLang: string): string {
    return `${toLang}:${texts.filter(Boolean).join('|')}`
  }

  private updateCache(key: string, result: Partial<TFText>[]) {
    // 如果缓存已满，删除最早的条目
    if (this.requestCache.size >= this.maxCacheSize) {
      const firstKey = this.requestCache.keys().next().value
      if (firstKey) {
        this.requestCache.delete(firstKey)
      }
    }

    this.requestCache.set(key, result)
  }

  public getDebugInfo() {
    return {
      ...this.debugCounters,
      cacheSize: this.requestCache.size,
      maxCacheSize: this.maxCacheSize
    }
  }

  public clearCache() {
    this.requestCache.clear()
  }
}

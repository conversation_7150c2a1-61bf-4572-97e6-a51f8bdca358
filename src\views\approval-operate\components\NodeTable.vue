<template>
  <div class="node-table-container">
    <FTabSet class="cust-node-table-tab" :tab-list="tabList" v-model:activeKey="search.type" @change="handleTypeFn">
    </FTabSet>
    <FTable
      :data-source="list"
      :columns="columns"
      table-layout="fixed"
      :pagination="{
        total: page.total,
        current: page.currPage,
        pageSize: page.pageSize,
        showTotal: (total: number) => `${i18n.t('共')}${total}${i18n.t('条')}`,
        showQuickJumper: true,
        showSizeChanger: true,
        onChange: onPaginationChangeFn
      }"
      :scroll="{ x: '100%', y: 'calc(100vh - 458px)' }"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'type' && text">
          <span>{{
            (text === 1 && record?.nodeType === 1 && i18n.t('审批')) ||
            (record?.nodeType === 2 && i18n.t('处理')) ||
            (text === 2 && i18n.t('抄送'))
          }}</span>
        </template>
        <template v-if="column.dataIndex === 'nodeMode' && text">
          <span>{{
            (text === 1 && i18n.t('单人审批')) || (text === 2 && i18n.t('会签')) || (text === 3 && i18n.t('或签'))
          }}</span>
        </template>
        <template v-if="column.dataIndex === 'status' && text">
          <span
            :class="[
              'status-box',
              'color333',
              (record.status === 1 && 'no-view') ||
                (record.status === 2 && record.type === 1 && 'wait-check') ||
                (record.status === 2 && record.type === 2 && 'success') ||
                (record.status === 3 && 'success'),
            ]"
          >
            <i
              class="iconfont marginR4"
              :class="[
                (record.status === 1 && 'icontubiao_cuowu') ||
                  (record.status === 2 && 'icontubiao_cuowu') ||
                  (record.status === 3 && 'icontubiao_chenggong') ||
                  '',
              ]"
            ></i>
            {{
              (record.status === 1 &&
                ((record.type === 1 && i18n.t('未查看')) || (record.type === 2 && i18n.t('未读')))) ||
              (record.status === 2 &&
                ((record.type === 1 && i18n.t('待处理')) || (record.type === 2 && i18n.t('已读')))) ||
              (record.status === 3 && i18n.t('已完成')) ||
              '--'
            }}
          </span>
        </template>
        <template v-if="['files'].includes(column.dataIndex) && text && text?.length">
          <div class="file-box" v-for="item in text || []" :key="item.url">
            <img class="icon-pic marginR4" :src="getPicFn(item)" />
            <span class="text-name" :title="item.fileName" @click="onPreviewFn(item)">{{ item.fileName }}</span>
            <i class="iconfont icontubiao_xiazai marginL4" @click="downLoadFile(item)"></i>
          </div>
        </template>
        <template v-if="column.dataIndex === 'msg'">
          <MoreTextTips v-if="text">
            {{ text }}
          </MoreTextTips>
        </template>
        <template v-if="['completeTime', 'createdTime', 'readTime', 'endTime'].includes(column.dataIndex) && text">
          <span>
            {{ (text && transformDate(text, 'YYYY-MM-DD HH:mm:ss')) || '--' }}
          </span>
        </template>
        <template v-if="column.dataIndex === 'handle'">
          <FRow :gutter="[12, 0]">
            <FCol :span="6">
              <TipBtn
                v-if="record.status === 1 || (record.status === 2 && record.type === 1)"
                has-pop
                :tip-title="i18n.t('催办')"
                :pop-title="i18n.t('确认操作催办吗？')"
                :pop-content="i18n.t('操作后会推送消息提醒！')"
                @onConfirmFn="onSendMsgFn(record)"
              >
                <i class="iconfont icontubiao_shangchuan_mian1 hover-btn"></i>
              </TipBtn>
              <span style="white-space: nowrap" v-else>--</span>
            </FCol>
          </FRow>
        </template>
      </template>
    </FTable>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useI18n, transformDate, download, S3URL } from '@/utils'
import { BasicPageParams } from '@/types/processBoard'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import MoreTextTips from '@/components/MoreTextTips/index'
import { message } from '@fs/smart-design'
import { PageApprovalNodeListRes, PageApprovalNodeListParams, approvalNodeHurryMessage } from '@/api'
import pdf from '../image/pdf.png'
import chm from '../image/chm.png'
import divPic from '../image/div.png'
import other from '../image/other.png'
import png from '../image/png.png'
import ppt from '../image/ppt.png'
import txt from '../image/txt.png'

interface IProps {
  page: BasicPageParams
  list: PageApprovalNodeListRes[]
  search: PageApprovalNodeListParams
}
const props = defineProps<IProps>()
const i18n = useI18n()
const emits = defineEmits(['getDataFn', 'update:page', 'update:search'])
const page = computed<BasicPageParams>({
  get: () => props.page,
  set: val => emits('update:page', val),
})
const search = computed<PageApprovalNodeListParams>({
  get: () => props.search,
  set: val => emits('update:search', val),
})
const tabList = ref<any>([
  {
    title: i18n.t('处理人'),
    status: 1,
  },
  {
    title: i18n.t('抄送人'),
    status: 2,
  },
])
const columns = computed(() =>
  [
    {
      title: i18n.t('节点名称'),
      dataIndex: 'topicName',
      width: 148,
    },
    {
      title: i18n.t('节点编号'),
      dataIndex: 'milepostId',
      width: 107,
    },
    search?.value?.type === 1 && {
      title: i18n.t('处理类型'),
      dataIndex: 'type',
      width: 107,
    },
    search?.value?.type === 1 && {
      title: i18n.t('审批类型'),
      dataIndex: 'nodeMode',
      width: 107,
    },
    {
      title: i18n.t('状态'),
      dataIndex: 'status',
      width: 107,
    },
    {
      title: i18n.t('审批说明'),
      dataIndex: 'msg',
      width: 217,
    },
    {
      title: i18n.t('附件'),
      dataIndex: 'files',
      width: 170,
    },
    {
      title: i18n.t('负责人'),
      dataIndex: 'feishuName',
      width: 146,
    },
    {
      title: i18n.t('到达时间'),
      dataIndex: 'createdTime',
      width: 170,
    },
    {
      title: i18n.t('查看时间'),
      dataIndex: 'readTime',
      width: 170,
    },
    {
      title: i18n.t('截止时间'),
      dataIndex: 'endTime',
      width: 170,
    },
    {
      title: i18n.t('处理时间'),
      dataIndex: 'completeTime',
      width: 170,
    },
    {
      title: i18n.t('操作'),
      dataIndex: 'handle',
      fixed: 'right',
      width: 98,
    },
  ].filter(Boolean)
)

const handleTypeFn = value => {
  emits('getDataFn')
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  page.value.currPage = current
  page.value.pageSize = pageSize
  emits('getDataFn')
}

const getPicFn = (item: any) => {
  const type = item.fileName.substring(item.fileName.lastIndexOf('.') + 1).toLowerCase()
  if (['pdf'].includes(type)) {
    return pdf
  } else if (['chm'].includes(type)) {
    return chm
  } else if (['png', 'jpg'].includes(type)) {
    return png
  } else if (['ppt'].includes(type)) {
    return ppt
  } else if (['txt'].includes(type)) {
    return txt
  } else if (['div'].includes(type)) {
    return divPic
  }
  return other
}

const onPreviewFn = (item: any) => {
  const httpUrl = `${S3URL}/api/s3/getFileByUrl?url=${encodeURIComponent(item.url)}&filename=${encodeURIComponent(
    item.fileName
  )}&type=1`
  window.open(httpUrl, '_blank')
}

const downLoadFile = (item: any) => {
  download(item.url, item.fileName)
}

const onSendMsgFn = async (record: PageApprovalNodeListRes) => {
  const res = await approvalNodeHurryMessage({ recordId: record.id })
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('已发送催办消息！'))
}
</script>

<style scoped lang="scss">
.node-table-container {
  padding: 16px 24px 24px;
  background: #ffffff;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  :deep(.cust-node-table-tab) {
    .fs-tabs-tab {
      height: 32px;
      padding-top: 0;
      align-items: flex-start;
    }
  }
  .file-box {
    display: flex;
    height: 32px;
    align-items: center;
    padding: 0 4px;
    &:hover {
      background-color: #f1f4f8;
      border-radius: 2px;
      .icontubiao_xiazai {
        display: inline-block;
      }
    }
    .icon-pic {
      width: 16px;
    }
    .text-name {
      max-width: 78%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }
    .icontubiao_xiazai {
      display: none;
      flex: 1;
      text-align: right;
      font-size: 16px;
      color: #999;
      cursor: pointer;
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .status-box {
    display: flex;
    align-items: center;
    &.no-view {
      .iconfont {
        color: #bbbbbb;
      }
    }
    &.wait-check {
      .iconfont {
        color: #f04141;
      }
    }
    &.success {
      .iconfont {
        color: #2fcc83;
      }
    }
  }
  :deep(.fs-table-tbody) {
    .fs-table-cell {
      &:empty {
        &::after {
          content: '--';
        }
      }
      &.fs-table-cell-fix-right-first {
        &:empty {
          &::after {
            content: '';
          }
        }
      }
    }
  }
  :deep(.fs-pagination) {
    margin-bottom: 0;
  }
}
</style>

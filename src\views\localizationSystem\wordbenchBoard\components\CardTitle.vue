<template>
  <div class="card-title-container">
    <div class="title">
      <FTooltip
        color="#fff"
        overlay-class-name="cust-desc-tip"
        :title="titleInfo.name || '--'"
        placement="top"
        :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
      >
        <div class="left">
          <span class="text">{{ titleInfo.name }}</span>
          <span>·{{ titleInfo.total }}</span>
        </div>
      </FTooltip>
      <HandleSort :sortList="titleInfo.sortList" />
    </div>
    <div class="cust-progress">
      <Progress :percent="titleInfo.percent" :show-info="false"> </Progress>
      <span class="num">{{ titleInfo.total - titleInfo.undoneNum }}/{{ titleInfo.total }}</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Progress } from '@fs/smart-design/dist/ant-design-vue_es'
import HandleSort from './HandleSort.vue'
const props = defineProps({
  titleInfo: { type: Object, default: () => ({}) },
})
</script>
<style lang="scss" scoped>
.card-title-container {
  padding: 16px 24px 12px 24px;
  background-color: #fff;
  border-radius: 12px 12px 0px 0px;
  :deep(.cust-desc-tip) {
    .fs-tooltip-inner {
      color: #333333;
    }
  }
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    height: 22px;
    line-height: 22px;
    .left {
      display: flex;
      max-width: 100%;
      height: 22px;
      line-height: 22px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      overflow: hidden;
      .text {
        max-width: 95%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -webkit-text-overflow: ellipsis;
      }
    }
  }
  :deep(.cust-progress) {
    width: 211;
    display: flex;
    justify-content: space-between;
    .fs-progress {
      display: flex;
      align-items: center;
      .fs-progress-outer {
        display: flex;
        align-items: center;
        padding-right: 0;
        margin-right: 0;
        .fs-progress-inner {
          height: 6px;
          background-color: #ebf3fd;
          .fs-progress-bg {
            background-color: #378eef;
          }
        }
      }
    }
    .num {
      display: inline-block;
      height: 18px;
      font-size: 12px;
      color: #999;
      line-height: 18px;
      margin-left: 8px;
    }
  }
}
</style>

<template>
  <div class="tip-layout-container" :style="{ backgroundColor: bgColor }">
    <div class="left">
      <div class="title">{{ title }}</div>
      <div class="num">{{ num }}</div>
      <div class="rate" v-if="rate || rate === 0">
        <span class="rate-label">{{ i18n.t('环比') }}</span>
        <span :class="(rate >= 0 && 'up') || 'down'">
          <img v-if="rate >= 0" src="../images/up.png" />
          <img v-else src="../images/down.png" />
          <span>{{ rate }}%</span>
        </span>
      </div>
    </div>
    <div class="right">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from '@/utils'
const i18n = useI18n()
interface IProps {
  title: string
  num: number
  rate?: number
  bgColor?: string
}
const props = defineProps<IProps>()
</script>

<style scoped lang="scss">
.tip-layout-container {
  display: flex;
  padding: 24px;
  border-radius: 4px;
  .left {
    margin-right: 24px;
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      line-height: 22px;
      white-space: nowrap;
    }
    .num {
      margin-top: 24px;
      margin-bottom: 12px;
      font-size: 34px;
      font-weight: 500;
      color: #333333;
      line-height: 34px;
      white-space: nowrap;
    }
    .rate-label {
      display: inline-block;
      margin-right: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #666666;
      line-height: 18px;
    }
    .up {
      color: #2fcc83;
    }
    .down {
      color: #f04141;
    }
    img {
      width: 14px;
      margin-right: 4px;
    }
  }
}
</style>

<template>
  <div
    tabindex="-1"
    ref="rangeNumberRef"
    class="cust-range-number height32 marginR12 marginB10"
    :class="[focusStatus ? 'cust-input-number-focused' : '']"
  >
    <FInputNumber
      :bordered="false"
      :press-line="pressLine"
      v-model:value="value[0]"
      @blur="inputNumberChange"
      @focus="
        () => {
          focusStatus = true
        }
      "
    />
    <i style="color: #ccc">-</i>
    <FInputNumber
      :bordered="false"
      :press-line="pressLine"
      v-model:value="value[1]"
      @blur="inputNumberChange"
      @focus="
        () => {
          focusStatus = true
        }
      "
    />
    <i class="cust-number-clear cursor iconfont colord8d8d8 fontSize12" @click="onAsyncSearch">&#xe70e;</i>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from '@fs/smart-design'

interface IProps {
  value: any
  pressLine: any
  config: any
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:value', 'change'])
const value = computed({
  get: () => props?.value ?? [],
  set: val => emits('update:value', val),
})
const rangeNumberRef = ref<HTMLElement[] | null>(null)
const focusStatus = ref<boolean>(false)

const inputNumberChange = () => {
  focusStatus.value = false
  if (value?.value?.length === 2 && value?.value[0] > value?.value[1]) {
    message.warning(`${props?.pressLine}输入不正确，请重新输入！`)
  }
}

const onAsyncSearch = () => {
  value.value = [...value.value]
  emits('change', value.value, props?.config)
}
</script>

<style lang="scss" scoped>
.cust-range-number {
  display: inline-flex;
  align-items: center;
  width: 240px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 12px;
  color: #333;
  padding: 0 8px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  :deep(.btn_div_relative) {
    flex: 1;
  }
  &.cust-input-number-focused {
    border-color: #378eef;
    box-shadow: 0 0 0 2px #afd1f8;
    border-right-width: 1px !important;
    outline: 0;
  }
  &:hover {
    border: 1px solid #5fa4f2;
  }
  &:focus {
    border-color: #378eef;
    box-shadow: 0 0 0 2px #afd1f8;
    border-right-width: 1px !important;
    outline: 0;
  }
  :deep(.fs-input-number) {
    height: 30px;
    .fs-input-number-handler-wrap {
      display: none;
    }
  }
}
.height32 {
  height: 32px;
}
</style>

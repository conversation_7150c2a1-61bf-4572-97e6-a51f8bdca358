import { NodeConfig, GraphModel } from '@logicflow/core'
import { BpmnBaseModel, BpmnBaseNode } from './bpmnBase'

class UserTaskModel extends BpmnBaseModel {
  constructor(data: NodeConfig, graphModel: GraphModel) {
    super(data, graphModel)
    this.themeColor = '#2FCC83'
  }
}

class UserTaskView extends BpmnBaseNode {}

export default {
  type: 'bpmn:userTask',
  view: UserTaskView,
  model: UserTaskModel,
}

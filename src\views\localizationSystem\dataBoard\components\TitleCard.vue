<template>
  <div class="board-title-container">
    <div class="board-title-container__left">
      <div class="board-title-container__left__img-box" :style="{ background: bgColor }">
        <img v-if="props.first" src="./images/title_left.png" alt="" />
        <img v-else src="./images/title_right.png" alt="" />
      </div>
      <TextCard
        class="cust-text-box"
        :title="title || i18n.t('注册用户数')"
        :num="props.num"
        :labelTitle="i18n.t('月环比')"
        :labelNum="props.labelNum"
      />
    </div>
    <div class="board-title-container__right">
      <img v-if="props.first" src="./images/left.png" alt="" />
      <img v-else src="./images/right.png" alt="" />
    </div>
  </div>
</template>

<script setup lang="ts">
import TextCard from './TextCard.vue'
import { useI18n } from '@/utils'
const i18n = useI18n()

type propsType = {
  first?: boolean
  title?: string
  bgColor: string
  num: number
  labelNum: string
}

const props = withDefaults(defineProps<propsType>(), {
  first: false,
  bgColor: '',
  num: 0,
  labelNum: '',
})
</script>

<style scoped lang="scss">
.board-title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(50% - 8px);
  padding: 24px;
  height: 136px;
  background: #ffffff;
  border-radius: 4px;
  &__left {
    display: flex;
    align-items: center;
    &__img-box {
      width: 88px;
      height: 88px;
      margin-right: 24px;
      line-height: 88px;
      border-radius: 12px;
      text-align: center;
      img {
        width: 36px;
        height: 36px;
      }
    }
    :deep(.cust-text-box) {
      .board-text-container__text-title {
        height: 20px;
        font-size: 14px;
        line-height: 20px;
      }
      .board-text-container__text-num {
        height: 40px;
        font-size: 28px;
        line-height: 40px;
      }
      .board-text-container__text-label {
        font-size: 14px;
        i {
          font-size: 16px;
        }
      }
    }
  }
  &__right {
    img {
      max-width: 116px;
    }
  }
}
</style>

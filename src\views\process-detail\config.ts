import HandleInfo from './components/HandleInfo/index.vue'
import ProjectPlan from './components/ProjectPlan/index.vue'
import ProjectPlanNew from './components/ProjectPlanNew/index.vue'
import OperationLog from './components/OperationLog/index.vue'
import RelevanceProcess from './components/RelevanceProcess/index.vue'
import RequirementInfo from './components/RequirementInfo/index.vue'
import TodoList from './components/TodoList/index.vue'
import OperationLogNew from './components/OperationLogNew/index.vue'
import DataManage from './components/DataManage/index.vue'
import RelevanceIRSRARDetail from '@/views/process-operate/components/CustomComponents/NewIpd/RelevanceIRSRARDetail/index.vue'
import RelevanceTestDetail from '@/views/process-operate/components/CustomComponents/NewIpd/RelevanceTestDetail/index.vue'
import { i18n } from '@/init'
import { reactive } from 'vue'

export const COMPONENT_CRAD_BODY_STYLE = {
  padding: '0 24px 0 24px !important',
}

export const TaskModalTilte = reactive({
  add: i18n.t('添加子节点'),
  addChild: i18n.t('添加子任务'),
  edit: i18n.t('编辑子节点'),
  batchEdit: i18n.t('批量编辑子节点'),
})

export enum TagKey {
  handle = 'HandleInfo',
  plan = 'ProjectPlan',
  log = 'OperationLog',
  relevance = 'relevance',
}

export enum TagKeyNew {
  handle = 'RequirementInfo',
  plan = 'ProjectPlan',
  relevance = 'relevance',
  todoList = 'TodoList',
  // allInfo = 'AllInfo',
  log = 'OperationLogNew',
  // projectFiles = 'ProjectFiles',
  dataManage = 'DataManage',
  RelevanceIRSRARDetail = 'RelevanceIRSRARDetail',
  RelevanceTestDetail = 'RelevanceTestDetail',
}

export const TAB_COMPONENT = {
  [TagKey.handle]: HandleInfo,
  [TagKey.plan]: ProjectPlan,
  [TagKey.log]: OperationLog,
  [TagKey.relevance]: RelevanceProcess,
}
export const TAB_COMPONENT_NEW = {
  [TagKeyNew.handle]: RequirementInfo,
  [TagKeyNew.plan]: ProjectPlanNew,
  [TagKeyNew.relevance]: RelevanceProcess,
  [TagKeyNew.todoList]: TodoList,
  // [TagKeyNew.allInfo]: AllInfo,
  [TagKeyNew.log]: OperationLogNew,
  // [TagKeyNew.projectFiles]: ProjectFiles,
  [TagKeyNew.dataManage]: DataManage,
  [TagKeyNew.RelevanceIRSRARDetail]: RelevanceIRSRARDetail,
  [TagKeyNew.RelevanceTestDetail]: RelevanceTestDetail,
}
export const TAB_LIST = reactive([
  { key: TagKey.handle, tab: i18n.t('处理信息') },
  { key: TagKey.plan, tab: i18n.t('项目计划') },
  { key: TagKey.relevance, tab: i18n.t('关联流程') },
  { key: TagKey.log, tab: i18n.t('操作记录') },
])

export const TAB_LIST_NEW = reactive([
  { key: TagKeyNew.handle, tab: i18n.t('需求信息') },
  { key: TagKeyNew.plan, tab: i18n.t('项目计划') },
  { key: TagKeyNew.relevance, tab: i18n.t('关联流程') },
  { key: TagKeyNew.todoList, tab: i18n.t('待办事项') },
  // { key: TagKeyNew.allInfo, tab: '所有信息' },
  { key: TagKeyNew.log, tab: i18n.t('操作记录') },
  // { key: TagKeyNew.projectFiles, tab: '项目文件' },
  // { key: TagKeyNew.dataManage, tab: i18n.t('资料管理') },
])

export const CRAD_BODY_STYLE = {
  margin: 0,
  padding: 0,
  backgroundColor: 'transparent',
}

export const RELATE_PROCESS_STATUS_NAME = reactive({
  0: i18n.t('进行中'),
  1: i18n.t('已完成'),
  2: i18n.t('办结'),
})

export const RELATE_PROCESS_STATUS_COLOR = {
  0: '#3dcca6',
  1: '#3dcca6',
  2: '#3dcca6',
}

export const MILEPOST_STATUS_NAME = reactive({
  0: i18n.t('未开始'),
  1: i18n.t('待领取'),
  2: i18n.t('处理中'),
  3: i18n.t('已完成'),
  4: i18n.t('已办结'),
  5: i18n.t('逾期完成'),
})

export const TASK_STATUS_NAME = reactive({
  0: i18n.t('未开始'),
  1: i18n.t('进行中'),
  2: i18n.t('按时完成'),
  3: i18n.t('延期完成'),
  4: i18n.t('待指派'),
  5: i18n.t('流程终止'),
  6: i18n.t('待审批'),
})

export const TASK_STATUS_COLOR = {
  0: '#BBBBBB',
  1: '#FA8F23',
  2: '#2FCC83',
  3: '#F04141',
  4: '#F04141',
  5: '#378EEF',
  6: '#FA8F23',
}

export const TASK_STATUS_ICON = {
  0: 'iconweikaishiicon',
  1: 'icontubiao_jinhangzhong',
  2: 'iconyiwanchengicon',
  3: 'iconyiwanchengicon',
  4: 'iconweikaishiicon',
  5: 'iconyiwanchengicon',
  6: 'iconyiwanchengicon',
}

export enum EmitType {
  // task
  createTask = 'createTask',
  createChildTask = 'createChildTask',
  editTask = 'editTask',
  batchEditTask = 'batchEditTask',
  revokeTask = 'revokeTask',
  handleTask = 'handleTask',
  saveTask = 'saveTask',
  judgeTask = 'judgeTask',
  delTask = 'delTask',
  batchDelTask = 'batchDelTask',
  viewTask = 'viewTask',

  // process
  lostSingle = 'lostSingle', // ltc失单
  finish = 'finish', // 办结
  dispatch = 'dispatch', // 转派
  reject = 'reject', // 驳回
  submit = 'submit', // 提交
  revoke = 'revoke', // 撤回
  createProcess = 'createProcess', // 创建流程

  // role
  updateRole = 'updateRole', // 角色信息变更提示更新
  // poolTask
  createPoolTask = 'createPoolTask',
  deleteProcess = 'deleteProcess',
}

export const ProcessModalTilte = reactive({
  submit: i18n.t('提交'),
  finish: i18n.t('流程终止'),
  lostSingle: i18n.t('失单'),
})

export const TASK_STATUS_BACKGROUND_COLOR = {
  0: 'rgba(187,187,187,0.1)',
  1: 'rgba(250,143,35,0.1)',
  2: 'rgba(61,204,166,0.1)',
  3: 'rgba(240,65,65,0.1)',
  4: 'rgba(240,65,65,0.1)',
  5: 'rgba(55,142,239,0.1)',
  6: 'rgba(250,143,35,0.1)',
}
export enum ProcessViewType {
  table,
  tiling,
}

export enum InteractCustomInput {
  init,
  edit,
  save,
}

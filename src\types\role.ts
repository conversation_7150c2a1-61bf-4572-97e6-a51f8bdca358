export interface Common {
  code: number
  msg: string
}
export interface RoleListResult extends Common {
  data: RoleListItem[]
}

export interface RoleListItem {
  ext: string
  id: number
  roleCode: string
  roleDesc: string
  roleName: string
  url: string
}

export interface ConfigListParams {
  pageNum: number
  pageSize: number
  roleConfigId: number
}

export interface ConfigListParamsResult extends Common {
  data: ConfigListItem
}

export interface ConfigListItem {
  roleConfigRsp: RoleListItem
  page: {
    currPage: number
    list: ConfigListItemList[]
    pageSize: number
    totalCount: number
    totalPage: number
  }
}

export interface ConfigListItemList {
  ext: string
  feiShuName: string
  id: number
  nameCh: string
  nameEn: string
  roleConfigId: number
  uuid: string
}
//新增角色
export interface IAddRole {
  roleCode: string
  roleDesc?: string
  roleName: string
  url?: string
  id?: number
  type?: number
  isSyn?: any
}

export interface AddRoleResult extends Common {
  data: string
}

export interface userAllResult extends Common {
  data: userList[]
}
export interface userList {
  feiShuName: string
  nameCh: string
  nameEn: string
  nameLocal: string
  organizationsName: string
  uuid: string
}

export interface AddUser {
  roleConfigId: number
  uuid: string[]
  conditionExpression?: string
}

export interface AddUserExl {
  roleConfigId: number
  file: any
}

export const IRoleType = {
  1: '铁六角',
  2: '轮询',
  3: '表达式',
  4: '抄送',
  5: 'mom同步角色',
}

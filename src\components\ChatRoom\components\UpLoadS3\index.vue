<template>
  <div class="talk-file">
    <template v-if="newFiles && newFiles.length > 0">
      <div class="file-item" v-for="(item, index) in newFiles" :key="'newFiles' + index">
        <div class="file-item-box" :class="['error'].includes(item.status as string) && 'file-error'">
          <div class="file-left">
            <span class="iconfont fontSize12" style="color: #fdb926">&#xe655;</span>
            <p class="file-txt" :title="item.name">{{ item.name }}</p>
            <span class="file-size">({{ (item.size / 1024).toFixed(0) }} KB)</span>
          </div>
          <div class="process-box">
            <i class="iconfont icon-del" @click="delNewFile(index)">&#xe715;</i>
          </div>
        </div>
        <FProgress
          v-if="['uploading'].includes(item.status as string)"
          class="cust-progress"
          :percent="item.percent"
          stroke-width="2"
          :show-info="false"
        />
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { upload } from '@/api/handle'
import { IFile } from '@/types/request'
import { messageInstance as message } from '@fs/smart-design'
import { ref, watch, computed } from 'vue'
import { useI18n } from '@/utils'
const i18n = useI18n()

interface IProps {
  value: IFile[]
  fileTypes: string[]
}

interface upLoadingData {
  name: string
  status: string
  size: number
  percent: number
}
const FILE_TYPES = [
  'PDF',
  'JPG',
  'PNG',
  'DOC',
  'DOCX',
  'PPT',
  'PPTX',
  'XLS',
  'XLSX',
  'TXT',
  'CSV',
  'ZIP',
  'RAR',
  'MP4',
  'BIN',
  'TEST',
  'VSD',
  'VSDX',
  'CNF',
]

const props = defineProps<IProps>()
const emit = defineEmits(['input'])

const limitFileTypes = computed(() => props.fileTypes || FILE_TYPES)
const newFiles = ref<IFile[]>([])
// const upLoadingPending = ref<upLoadingData[]>([])
const handleUpload = (file: File) => {
  if (!fileLimit(file)) {
    return false
  }
  uploadItem(file)
  return false
}

// 检测上传进度
const onHandleUploadProgress = (progressEvent: any, key: string) => {
  const index = newFiles.value.findIndex((item: any) => item.key === key)
  index !== -1 &&
    (newFiles.value[index].percent = Math.round((progressEvent.loaded / progressEvent.total) * 10000) / 100.0)
}

const uploadItem = (file: File) => {
  let formData = new FormData()
  const key = Date.now() + '' + Math.random()
  formData.append('file', file)
  formData.append('isOpen', 'false')
  formData.append('expire', '0')
  newFiles.value.push({
    name: file.name,
    size: file.size,
    resourseKey: '',
    percent: 0,
    key,
    status: 'uploading',
  })
  upload(formData, onHandleUploadProgress, key).then(res => {
    const index = newFiles.value.findIndex((item: any) => item.key === key)
    if (res && index !== -1) {
      newFiles.value[index].resourseKey = res
      newFiles.value[index].status = 'success'
    } else if (index !== -1) {
      newFiles.value[index].status = 'error'
      message.error(i18n.t('上传失败，请移除！'))
    }
  })
}
const delNewFile = (index: number) => {
  newFiles.value.splice(index, 1)
}
// const delupLoadingPending = (index: number, item: any) => {
//   item.cancel()
//   upLoadingPending.value.splice(index, 1)
// }
const fileLimit = (file: File) => {
  const fileType = file.name.replace(/.+\./, '')
  const allowTypes = limitFileTypes.value
  const allowSize = 2 * 1024
  if (allowTypes.indexOf(fileType.toUpperCase()) < 0) {
    message.error(i18n.t('文件类型错误'))
    return false
  }
  if (file.size / (1024 * 1024) > allowSize) {
    message.error('最大文件2G')
    return false
  }
  return true
}
watch(
  () => props.value,
  () => {
    newFiles.value = props.value
    emit('input', props.value)
  },
  { deep: true, immediate: true }
)
defineExpose({
  handleUpload,
})
</script>
<style lang="scss" scoped>
.talk-file {
  max-height: 45%;
  overflow-y: auto;
  overflow-x: hidden;
  flex-shrink: 0;
  .file-item {
    background: #f7f8f9;
    border-radius: 2px;
    margin: 4px 20px;
    padding: 4px 10px;
    .file-item-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      &.file-error {
        color: red;
        .file-size,
        .icon-del {
          color: red !important;
        }
      }
      .file-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex: 1;
        overflow: hidden;
        .icon-file {
          padding-right: 3px;
          width: 14px;
          height: 14px;
          flex-shrink: 0;
        }
        .file-txt {
          overflow: hidden;
          padding: 0 3px;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin: 0;
          max-width: 70%;
        }
        .file-size {
          color: #bbbbbb;
        }
      }
      .icon-del {
        color: #bbbbbb;
        cursor: pointer;
        font-size: 12px;
        flex-shrink: 0;
      }
    }
    :deep(.cust-progress) {
      display: block;
      font-size: 0;
    }
  }
}
.spin-box {
  color: #378eef;
}
.success-tips {
  color: #3dcca6;
}
.error-tips {
  color: #ff4a4a;
}
.waiting-tips {
  color: #999999;
}
.process-box {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  & > span {
    margin: 0px 4px;
    font-size: 12px;
  }
}
.upload-error {
  color: #ff4a4a;
}
</style>

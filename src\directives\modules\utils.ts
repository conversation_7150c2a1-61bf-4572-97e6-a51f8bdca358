// 吸顶指令
export const sticky = {
  mounted(el, binding) {
    const { container, zIndex = 1, type = 'fixed', stickyClass = 'fs-sticky-directive-el' } = binding.value
    const scrollContainer = document.querySelector(container) || window
    let isSticky = false

    const handleScroll = () => {
      const scrollY = scrollContainer === window ? window.scrollY : scrollContainer.scrollTop
      if (scrollY >= binding.value.offset && !isSticky) {
        el.style.position = type
        el.style.top = (binding.value.top ?? binding.value.offset) + 'px'
        el.style.zIndex = zIndex
        isSticky = true
        el.classList.add(stickyClass)
      } else if (scrollY < binding.value.offset && isSticky) {
        el.style.position = 'static'
        isSticky = false
        el.classList.remove(stickyClass)
      }
    }

    scrollContainer.addEventListener('scroll', handleScroll)

    // 在指令元素销毁时移除滚动事件监听器
    el.__handleScroll = handleScroll
  },
  beforeUnmount(el, binding) {
    const { container } = binding.value
    const scrollContainer = document.querySelector(container) || window

    // 移除滚动事件监听器
    scrollContainer.removeEventListener('scroll', el.__handleScroll)
  },
}

const rafThrottle = (fn: (...args: any[]) => void) => {
  let locked = false
  return (...args: any[]) => {
    if (locked) return
    locked = true
    requestAnimationFrame(() => {
      fn(...args)
      locked = false
    })
  }
}

const getElementViewStatus = (el: HTMLElement, container: HTMLElement | Window): 'above' | 'below' | 'inside' => {
  const elRect = el.parentElement ? el.parentElement.getBoundingClientRect() : el.getBoundingClientRect()
  const containerRect =
    container === window ? { top: 0, bottom: window.innerHeight } : (container as HTMLElement).getBoundingClientRect()

  if (elRect.top <= el.offsetHeight + 20) return 'above'
  if (elRect.top - containerRect.bottom >= -el.offsetHeight) return 'below'
  return 'inside'
}

export const processTabSticky = {
  mounted(el, binding) {
    const { container, stickyClass = 'fs-sticky-directive-el', top = 0, zIndex = 10 } = binding.value

    const scrollContainer = document.querySelector(container) || window
    let lastHeight = scrollContainer.scrollHeight
    let currentStatus: 'top' | 'bottom' | 'none' = 'none'
    const originalWidth = el.style.width

    const applySticky = () => {
      el.style.position = 'sticky'
      el.style.top = `${top}px`
      el.style.bottom = 'auto'
      el.style.zIndex = zIndex
      el.style.width = originalWidth
      el.classList.add(stickyClass)
      currentStatus = 'top'
    }

    const applyFixedBottom = () => {
      el.style.position = 'fixed'
      el.style.top = 'auto'
      el.style.bottom = '0px'
      el.style.zIndex = zIndex
      el.style.width = el.parentElement?.offsetWidth + 'px'
      el.classList.add(stickyClass)
      currentStatus = 'bottom'
    }

    const resetStyle = () => {
      el.style.position = 'static'
      el.style.top = 'auto'
      el.style.bottom = 'auto'
      el.style.zIndex = 'auto'
      el.style.width = originalWidth
      el.classList.remove(stickyClass)
      currentStatus = 'none'
    }

    const handleScroll = rafThrottle(() => {
      const status = getElementViewStatus(el, scrollContainer)
      if (status === 'above' && currentStatus !== 'top') {
        applySticky()
      } else if (status === 'below' && currentStatus !== 'bottom') {
        applyFixedBottom()
      } else if (status === 'inside' && currentStatus !== 'none') {
        resetStyle()
      }
    })

    const resizeObserver = new ResizeObserver(() => {
      const newHeight = scrollContainer.scrollHeight
      if (newHeight !== lastHeight) {
        lastHeight = newHeight
        handleScroll()
      }
    })

    resizeObserver.observe(scrollContainer?.firstElementChild)

    scrollContainer.addEventListener('scroll', handleScroll)
    el.__handleScroll = handleScroll
    el.__resizeObserver = resizeObserver
  },

  beforeUnmount(el, binding) {
    const { container } = binding.value
    const scrollContainer = document.querySelector(container) || window
    scrollContainer.removeEventListener('scroll', el.__handleScroll)
    el.__resizeObserver.disconnect()
  },
}

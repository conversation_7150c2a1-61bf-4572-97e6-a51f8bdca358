import { h, createApp, VNode } from 'vue'
import { BaseNodeModel, GraphModel, HtmlNode, HtmlNodeModel, NodeConfig } from '@logicflow/core'
import { genBpmnId } from '@logicflow/extension/es/bpmn-elements/utils'
import BpmnBaseElement from '@/views/approval-operate/components/ProcessDesigner/components/BpmnBaseElement/index.vue'

export class BpmnBaseModel extends HtmlNodeModel {
  constructor(data: NodeConfig, graphModel: GraphModel) {
    if (!data.id) data.id = `Task_${genBpmnId()}`
    if (!data.text) data.text = ''
    if (typeof data.text === 'string') data.text = { value: data.text, x: data.x, y: data.y }

    super(data, graphModel)
    this.themeColor = '#378eef'
  }

  initNodeData(data: any) {
    super.initNodeData(data)
    this.width = 140
    this.height = 62
    this.text.x = this.x - this.width / 2 + 46
    this.text.y = this.y + 2
  }

  getAnchorStyle(anchorInfo: any) {
    const style = super.getAnchorStyle(anchorInfo)
    style.stroke = this.themeColor
    style.hover.fill = this.themeColor
    style.hover.stroke = this.themeColor
    return style
  }

  getOutlineStyle() {
    const style = super.getOutlineStyle()
    style.r = 5
    style.stroke = this.themeColor
    // style.strokeDasharray = ''
    style.hover && (style.hover.stroke = this.themeColor)
    return style
  }

  getTextStyle() {
    const style = super.getTextStyle()
    style.textAnchor = 'start'
    if (this?.properties?.custActualNodeStatus === undefined) {
      style.color = '#999999'
    }
    return style
  }

  getNodeStyle() {
    return super.getNodeStyle()
  }
}

export class BpmnBaseNode extends HtmlNode {
  $container?: HTMLElement

  app: ReturnType<typeof createApp>
  vueRender: VNode

  constructor(props: { model: BaseNodeModel; graphModel: GraphModel }) {
    super(props)
    this.vueRender = h(BpmnBaseElement, {
      // text: props.model.inputData,
      model: props.model,
      properties: props.model.getProperties(),
      themeColor: props.model.themeColor,
    })

    this.app = createApp({ render: () => this.vueRender })
  }

  setHtml($root: HTMLElement) {
    if (this.$container && this.vueRender.component) {
      this.vueRender.component.props.properties = this.props.model.getProperties()
    } else {
      this.$container = document.createElement('div')
      $root.appendChild(this.$container)
      this.app.mount(this.$container)
    }
  }
}

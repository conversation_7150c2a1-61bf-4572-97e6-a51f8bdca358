import { unref } from 'vue'
/**
 * 从选项列表中查找匹配值的选项
 * @param list 选项列表
 * @param value 要查找的值
 * @param valueKey 值的键名
 * @returns 找到的选项对象
 */
export const getValueFn = (list: any[], value: string | number, valueKey = 'value') => {
  const options = unref(list)
  if (!Array.isArray(options)) return undefined

  for (let index = 0; index < options.length; index++) {
    const item = options[index]
    if (item[valueKey] == value) {
      return item
    }

    if (item?.children?.length) {
      const found = getValueFn(item.children, value, valueKey)
      if (found) return found
    }
  }

  return undefined
}

// 判断值是否为空（空字符串、null、undefined）
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) {
    return true
  }
  if (typeof value === 'string' && value.trim() === '') {
    return true
  }
  if (Array.isArray(value) && value.length === 0) {
    return true
  }
  if (typeof value === 'object' && Object.keys(value).length === 0 && !(value instanceof Date)) {
    return true
  }
  return false
}

// 判断值是否不为空
export const isNotEmpty = (value: any): boolean => {
  return !isEmpty(value)
}

// 清理空的children数组，将其设置为undefined
export const cleanEmptyChildren = (data: any[]): any[] => {
  if (isEmpty(data) || !Array.isArray(data)) return []

  return data.map(item => {
    const newItem = { ...item }
    if (newItem.children && Array.isArray(newItem.children)) {
      if (newItem.children.length === 0) {
        newItem.children = undefined
      } else {
        newItem.children = cleanEmptyChildren(newItem.children)
      }
    }
    return newItem
  })
}

// 清理空的children数组，将其设置为[]
export const handleEmptyChildren = (data: any[]): any[] => {
  if (isEmpty(data) || !Array.isArray(data)) return []

  return data.map(item => {
    const newItem = { ...item }
    if (isEmpty(newItem.children)) {
      newItem.children = []
    } else if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children = cleanEmptyChildren(newItem.children)
    }
    return newItem
  })
}

// 初始化数据，添加层级和自定义属性
export const initializeData = (
  data: any[],
  parentId: string | null = null,
  level = 0,
  customProps: Record<string, any> = {}
): any[] => {
  if (isEmpty(data) || !Array.isArray(data)) return []

  return data.map(item => {
    const newItem = { ...item }
    newItem.level = level
    if (isNotEmpty(parentId)) {
      newItem._parentId = parentId
    }
    Object.keys(customProps).forEach(key => {
      if (newItem[key] === undefined) {
        newItem[key] = customProps[key]
      }
    })
    if (isNotEmpty(newItem.children) && Array.isArray(newItem.children) && newItem.children.length > 0) {
      newItem.children = initializeData(newItem.children, newItem.id, level + 1, customProps)
    }
    return newItem
  })
}

export const findNodeWithPath = (items: any[], nodeId: string): any => {
  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    if (item.id === nodeId) {
      return item
    }
    if (item.children && item.children.length > 0) {
      const found = findNodeWithPath(item.children, nodeId)
      if (found) return found
    }
  }
  return null
}

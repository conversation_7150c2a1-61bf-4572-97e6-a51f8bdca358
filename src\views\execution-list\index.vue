<template>
  <div className="execution-list-container">
    <!-- <div class="bread-container">
      <Breadcrumb :data="title" />
    </div> -->
    <search-excution @search="onSearch" :process-type-list="processTypeList"></search-excution>
    <excution-list :search-params="searchParams"></excution-list>
  </div>
</template>

<script setup lang="ts">
import SearchExcution from '@/views/execution-list/components/search-excution.vue'
import ExcutionList from '@/views/execution-list/components/excution-list.vue'
import { onMounted, ref, computed } from 'vue'
import { ExcutionListSearch, processTye } from '@/types/excutionListModel'
import { getProcessTypeListNew } from '@/api/automotion'
import { messageInstance as message } from '@fs/smart-design'
import Breadcrumb from '@/views/process-list-one/components/Breadcrumb/index.vue'
import { useI18n } from '@/utils'
const searchParams = ref<ExcutionListSearch>({})
const processTypeList = ref<processTye[]>([])
const i18n = useI18n()
const title = computed(() => [i18n.t('首页'), i18n.t('执行记录')])
const onSearch = (params: ExcutionListSearch) => {
  searchParams.value = params
}
const getProcessList = async () => {
  const res = await getProcessTypeListNew()
  if (res.code === 200) {
    processTypeList.value = res.data
  } else {
    message.warn(res.msg)
  }
}
onMounted(async () => {
  await getProcessList()
})
</script>

<style lang="scss" scoped>
.execution-list-container {
  .bread-container {
    margin-bottom: 24px;
  }
}
</style>

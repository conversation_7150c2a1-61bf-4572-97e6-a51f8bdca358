<template>
  <div class="handle-process-workflow-container">
    <slot name="default" :handleViewWorkflow="handleViewWorkflow">
      <span class="color4677C7 cursor" @click="handleViewWorkflow">{{
        (props?.processWorkflowId && processWorkflowInfo.label) || ''
      }}</span>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { WORKFLOW_URL } from '@/utils'

interface IProps {
  processWorkflowId: any
  processWorkflowType?: any
}

const props = defineProps<IProps>()

const processWorkflowUrl = {
  process: {
    url: '/process/designer/',
    label: '查看流程图',
  },
  form: {
    url: '/form-manage/form/designer/',
    label: '查看表单',
  },
}
const processWorkflowInfo = computed(() => processWorkflowUrl[props?.processWorkflowType ?? 'process'])

const handleViewWorkflow = () => {
  WORKFLOW_URL &&
    props.processWorkflowId &&
    window.open(`${WORKFLOW_URL}${processWorkflowInfo.value.url}${props.processWorkflowId}`)
}
</script>

<style lang="scss" scoped>
.handle-process-workflow-container {
  display: inline-block;
}
</style>

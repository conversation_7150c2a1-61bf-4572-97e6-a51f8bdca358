<template>
  <div class="fs-card-body">
    <FTable
      :columns="columns"
      :data-source="tableData"
      :row-key="(data:any) => data.id"
      :scroll="{ x: 'max-content' }"
      :loading="loading"
    >
      <template #bodyCell="{ column, record }">
        <!-- 动态渲染表单组件 -->
        <template v-if="column.dataIndex && column.component">
          <div style="display: inline-block">
            <component
              :is="column.component"
              v-model:value="record[column.dataIndex]"
              v-bind="column.componentAttrs || {}"
              :componentConfig="{
                dataType: column.componentType || 'input',
                isDetail: true,
                valueFormatFn: column?.valueFormatFn ?? undefined,
                componentAttrs: column.componentAttrs || {},
                currtRecord: record,
              }"
            />
          </div>
        </template>
      </template>
    </FTable>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, onMounted, reactive } from 'vue'
import { message } from '@fs/smart-design'
import dayjs from 'dayjs'
import { useStore } from 'vuex'
import CustomInput from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInput/index.vue'
import CustomSelect from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomSelect/index.vue'
import CustomDatePicker from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomDatePicker/index.vue'
import CustomUpload from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomUpload/index.vue'
import ProcessDetail from '@/views/process-operate/components/CustomComponents/BusinessComponent/ProcessDetail/index.vue'
import CustomInputTextarea from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInputTextarea/index.vue'
import { getIpdSubProcessTable } from '@/api'
import {
  initializeData,
  cleanEmptyChildren,
  isEmpty,
} from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'

interface IProps {
  isReadOnly?: any
}

const props = defineProps<IProps>()
const store = useStore()

// 注入的数据
const processId = inject<number>('processId') // 流程 id

// 本地状态
const loading = ref(false)
const tableData = ref<any[]>([])

// 选项列表
const optionsList = reactive<Record<string, any>>({
  typeOptions: [
    { label: '研发类型', value: '研发类型' },
    { label: '包装类型', value: '包装类型' },
    { label: '结构类型', value: '结构类型' },
    { label: '外观类型', value: '外观类型' },
    { label: '认证类型', value: '认证类型' },
    { label: '知识产权类型', value: '知识产权类型' },
    { label: '质量类型', value: '质量类型' },
    { label: '工艺类型', value: '工艺类型' },
    { label: '计划类型', value: '计划类型' },
    { label: '运营类型', value: '运营类型' },
    { label: '营销类型', value: '营销类型' },
    { label: '财务类型', value: '财务类型' },
    { label: '产品培训', value: '产品培训' },
    { label: '资料归档', value: '资料归档' },
    { label: '产品BOM类型', value: '产品BOM类型' },
    { label: '上线资料输出', value: '上线资料输出' },
    { label: '小批量试产', value: '小批量试产' },
    { label: '量产与备库执行', value: '量产与备库执行' },
    { label: '产品发布', value: '产品发布' },
    { label: '供应链类型', value: '供应链类型' },
    { label: 'SDV', value: 'SDV' },
    { label: 'SIT', value: 'SIT' },
    { label: 'SVT', value: 'SVT' },
    { label: '实验局测试', value: '实验局测试' },
  ],
  userOptions: store.state.user.allUser || [],
  urgentOptions: [
    {
      label: '一般',
      value: 0,
    },
    {
      label: '紧急',
      value: 2,
    },
    {
      label: '非常紧急',
      value: 1,
    },
  ],
})

// 表格列定义
const columns = computed(() => [
  {
    title: '事项名称',
    dataIndex: 'title',
    width: 230,
    component: CustomInput,
    componentType: 'input',
    componentAttrs: {},
    fixed: 'left',
  },
  {
    title: '流程编号',
    dataIndex: 'milepost',
    width: 180,
    component: ProcessDetail,
    componentType: 'customProcessDetail',
    componentAttrs: {
      showValueKey: 'instanceCode',
    },
  },
  {
    title: '需求类型',
    dataIndex: 'type',
    width: 180,
    component: CustomSelect,
    componentType: 'select',
    componentAttrs: {
      options: optionsList?.typeOptions || [],
    },
  },
  {
    title: '负责人',
    dataIndex: 'ownerUuid',
    width: 180,
    component: CustomSelect,
    componentType: 'select',
    componentAttrs: {
      options: optionsList.userOptions,
      fieldNames: { label: 'feiShuName', value: 'uuid' },
    },
  },
  {
    title: '期望完成时间',
    dataIndex: 'endDate',
    width: 180,
    component: CustomDatePicker,
    componentType: 'datePicker',
    valueFormatFn: (value: any) => {
      return value ? dayjs(value).format('YYYY-MM-DD') : '--'
    },
    componentAttrs: {},
  },
  {
    title: '紧急程度',
    dataIndex: 'isUrgent',
    width: 180,
    component: CustomSelect,
    componentType: 'select',
    componentAttrs: {
      options: optionsList.urgentOptions,
    },
  },
  {
    title: '需求描述',
    dataIndex: 'describemsg',
    width: 230,
    component: CustomInputTextarea,
    componentType: 'textarea',
    componentAttrs: {},
  },
  {
    title: '历程',
    dataIndex: 'milepost',
    width: 180,
    component: ProcessDetail,
    componentType: 'customProcessDetail',
    componentAttrs: {
      hasTooltip: true,
    },
  },
  {
    title: '产品型号',
    dataIndex: 'irProdModel',
    width: 230,
    component: CustomInput,
    componentType: 'input',
    componentAttrs: {
      placeholder: '请输入产品型号',
    },
  },
  {
    title: '版本号',
    dataIndex: 'irVers',
    width: 230,
    component: CustomInput,
    componentType: 'input',
    componentAttrs: {
      placeholder: '请输入版本号',
    },
  },
  {
    title: '附件',
    dataIndex: 'file',
    width: 230,
    component: CustomUpload,
    componentType: 'upload',
    componentAttrs: {},
  },
])

// 获取表格数据
const fetchTableData = async () => {
  tableData.value = []
  if (isEmpty(processId)) return
  loading.value = true
  try {
    const res = await getIpdSubProcessTable(processId, 1)
    if (isEmpty(res) || isEmpty(res.data)) {
      console.log('API返回的数据为空')
      return
    }
    let initializedData = initializeData(res.data)
    initializedData = cleanEmptyChildren(initializedData)
    tableData.value = initializedData
  } catch (error) {
    console.error('获取数据失败:', error)
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchTableData()
})
</script>

<style scoped lang="scss"></style>

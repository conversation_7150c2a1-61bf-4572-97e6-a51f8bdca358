import { request, fileRequest } from '@/utils'
// import { IRes, IResDataList } from '@/types/request'

export const getBoardData = (data: any) => {
  return request.post('/api/hrAdminBoard/getBoardData', data)
}

//流程分布
export const getProcessDistributionData = (data: any) => {
  return request.post('/api/hrAdminBoard/getProcessDistributionData', data)
}

//获取项目数量趋势数据
export const getProjectTrendData = (data: any) => {
  return request.post('/api/hrAdminBoard/getProjectTrendData', data)
}

//项目分布情况2
export const getProjectDistributionData = (data: any) => {
  return request.post('/api/hrAdminBoard/getProjectDistributionData', data)
}

//阶段完成情况
export const getStageCompletionData = (data: any) => {
  return request.post('/api/hrAdminBoard/getStageCompletionData', data)
}

//项目所属阶段
export const getProjectStageData = (data: any) => {
  return request.post('/api/hrAdminBoard/getProjectStageData', data)
}

//项目数量分布
export const getProjectFieldDistributionData = (data: any) => {
  return request.post('/api/hrAdminBoard/getProjectFieldDistributionData', data)
}

//获取项目平均交付周期统计
export const getProjectDeliveryCycleData = (data: any) => {
  return request.post('/api/hrAdminBoard/getProjectDeliveryCycleData', data)
}

//获取人员项目统计列表（支持发起人和处理人统计）
export const getPersonProjectStatisticsList = (data: any) => {
  return request.post('/api/hrAdminBoard/getPersonProjectStatisticsList', data)
}

export function exportPersonalProjectStatistics(data: any): Promise<any> {
  return fileRequest.post('/api/hrAdminBoard/exportPersonalProjectStatistics', data, { responseType: 'blob' })
}

/* eslint-disable */
import { upload } from '../../../api'

let uuid = 0

/**
 *@description 观察者模式 全局监听富文本编辑器
 */
export const QuillWatch = {
  watcher: {}, // 登记编辑器信息
  active: null, // 当前触发的编辑器
  on: function (imageExtendId, ImageExtend) {
    // 登记注册使用了ImageEXtend的编辑器
    if (!this.watcher[imageExtendId]) {
      this.watcher[imageExtendId] = ImageExtend
    }
  },
  emit: function (activeId, type = 1) {
    // 事件发射触发
    this.active = this.watcher[activeId]
    if (type === 1) {
      imgHandler()
    }
  },
}

/**
 * @description 图片功能拓展： 增加上传 拖动 复制
 */
export class ImageExtend {
  /**
   * @param quill {Quill}富文本实例
   * @param config {Object} options
   * config  keys: action, headers, editForm start end error  size response
   */
  constructor(quill, config = {}) {
    this.id = Math.random()
    this.quill = quill
    this.quill.id = this.id
    this.config = config
    this.file = '' // 要上传的图片
    this.imgURL = '' // 图片地址
    quill.root.addEventListener('paste', this.pasteHandle.bind(this), false)
    quill.root.addEventListener(
      'drop',
      function (e) {
        e.preventDefault()
      },
      false
    )
    quill.root.addEventListener(
      'dropover',
      function (e) {
        e.preventDefault()
      },
      false
    )
    this.cursorIndex = 0
    QuillWatch.on(this.id, this)
  }

  /**
   * @description 粘贴
   * @param e
   */
  async pasteHandle(e) {
    QuillWatch.emit(this.quill.id, 0)
    let clipboardData = e.clipboardData
    let i = 0
    let items, item, types

    if (clipboardData) {
      items = clipboardData.items
      if (!items) {
        return
      }
      item = items[0]
      types = clipboardData.types || []

      for (; i < types.length; i++) {
        if (types[i] === 'Files') {
          item = items[i]
          break
        }
      }
      if (item && item.kind === 'file' && item.type.match(/^image\//i)) {
        e.preventDefault()
        this.file = item.getAsFile()
        let self = this
        // 如果图片限制大小
        if (self.config.size && self.file.size >= self.config.size * 1024 * 1024) {
          self.config.sizeError && self.config.sizeError()
          return
        }
        if (self.config.isImageUpload && self.config.isImageUpload()) {
          return
        }
        await this.uploadImg()
      }
    }
  }

  /**
   * @description 上传图片到服务器
   */
  uploadImg() {
    const self = this
    let config = self.config
    // 构造表单
    let formData = new FormData()
    formData.append('isOpen', 'false')
    formData.append('expire', '0')
    formData.append('file', self.file)
    self.config.start && self.config.start()
    upload(formData)
      .then(data => {
        self.imgURL = config.response(data)
        self.uuid = uuid++
        self.insertImg()
        self.config.success && self.config.success()
      })
      .catch(error => {
        self.config.error && self.config.error()
      })
      .finally(() => {
        self.config.end && self.config.end()
      })
  }

  /**
   * @description 往富文本编辑器插入图片
   */
  insertImg() {
    const self = QuillWatch.active
    const selectionIndex = self.quill.selection.savedRange.index
    self.quill.insertEmbed(selectionIndex, 'image1', {
      imgURL: self.imgURL,
      uuid: self.uuid,
    })
    self.quill.insertText(selectionIndex + 1, '\n')
    self.quill.update()
    self.quill.setSelection(selectionIndex + 2)
  }
}

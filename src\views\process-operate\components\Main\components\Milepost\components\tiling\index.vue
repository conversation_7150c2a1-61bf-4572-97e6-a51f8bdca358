<template>
  <div class="tiling-list-container">
    <!-- 我来组成头部 -->
    <div class="tiling-header">
      <div class="tiling-header-left cursor" @click="arrowClick">
        <DownOutlined v-show="arrow == 'expand'" />
        <RightOutlined v-show="arrow != 'expand'" />
        <span style="margin-left: 8px">{{ i18n.t('子节点列表') }}</span>
      </div>
      <div class="tiling-header-middle"></div>
      <div class="tiling-header-right">
        <FCheckbox
          class="fontSize12 color333"
          v-model:checked="formQueryData.incompleteChecked"
          @change="checkedBoxChenge"
          >{{ i18n.t('我负责的') }}</FCheckbox
        >
        <FCheckbox
          style="margin-left: 4px"
          class="fontSize12 color333"
          v-model:checked="formQueryData.responsibleChecked"
          @change="checkedBoxChenge"
          >{{ i18n.t('未完成的') }}</FCheckbox
        >
        <FPopover
          trigger="click"
          placement="bottomRight"
          v-model:visible="visible"
          :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
        >
          <template #content>
            <div class="more-search-box" style="width: 288px">
              <FRow :gutter="[12, 24]" class="marginB24">
                <FCol :span="12">
                  <FSelect
                    style="width: 100%"
                    :press-line="i18n.t('状态')"
                    v-model:value="formQueryData.status"
                    :options="taskStatusList"
                    show-search
                    allow-clear
                    :placeholder="i18n.t('请选择')"
                  />
                </FCol>
                <FCol :span="12">
                  <FSelect
                    style="width: 100%"
                    v-model:value="formQueryData.superviser"
                    :press-line="i18n.t('负责人')"
                    :options="superviserUserList"
                    :filter-option="filterOption"
                    :field-names="{ label: 'name', value: 'uuid' }"
                    show-search
                    allow-clear
                    :placeholder="i18n.t('请选择')"
                  />
                </FCol>
                <FCol :span="12">
                  <FSelect
                    style="width: 100%"
                    :press-line="i18n.t('是否当前节点完成')"
                    v-model:value="formQueryData.isSys"
                    :options="isCurrentNodeList"
                    show-search
                    allow-clear
                    :placeholder="i18n.t('请选择')"
                  />
                </FCol>
                <FCol :span="12">
                  <FSelect
                    style="width: 100%"
                    :press-line="i18n.t('审核人')"
                    v-model:value="formQueryData.approver"
                    :options="approverUserList"
                    :filter-option="filterOption"
                    :field-names="{ label: 'name', value: 'uuid' }"
                    show-search
                    allow-clear
                    :placeholder="i18n.t('请选择')"
                  />
                </FCol>
                <FCol :span="24">
                  <FSelect
                    style="width: 100%"
                    :press-line="i18n.t('任务分组')"
                    v-model:value="formQueryData.group"
                    :options="groupList"
                    allow-clear
                    :placeholder="i18n.t('请选择')"
                  />
                </FCol>
                <FCol :span="24">
                  <div class="btn_div_relative marginR12" style="width: 98%">
                    <span class="icon_span_absolute icon_span_absolute_left">{{ i18n.t('实际完成时间') }}</span>
                    <FRangePicker
                      style="width: 100%; height: 32px"
                      :press-line="i18n.t('实际完成时间')"
                      v-model:value="range"
                      format="YYYY-MM-DD"
                      allow-clear
                    />
                  </div>
                </FCol>
              </FRow>
              <div style="display: flex; justify-content: flex-end; margin-top: 16px">
                <FButton type="text" style="margin-right: 12px" @click="queryTilingCancel">{{
                  i18n.t('取消')
                }}</FButton>
                <FButton type="primary" style="margin-right: 6px" @click="queryTilingList(true)">{{
                  i18n.t('确定')
                }}</FButton>
              </div>
            </div>
          </template>
          <FTooltip>
            <template #title>{{ i18n.t('更多筛选') }}</template>
            <span class="icon iconfont icontubiao_lvqi cursor marginR12 marginL4 color333 fontSize16"></span>
          </FTooltip>
        </FPopover>
        <div class="change-list-view">
          <span class="sp" @click="changeView">
            <span class="icon iconfont icontubiao_yunweiguanli1 color333 fontSize16 marginR4 cursor"></span>
            <span class="cursor color333 fontSize12" style="vertical-align: text-bottom">{{ i18n.t('列表') }}</span>
          </span>
        </div>
      </div>
    </div>

    <!-- 我是列表本体 -->
    <TilingList
      :currt-milepost-children="(currtMilepost!.children as ITask[])"
      :arrow="arrow"
      :role="props.role"
      :form-query-data="formQueryData"
    />

    <!-- 添加子节点 -->
    <div class="task-add-view" v-if="isAddTask">
      <!--添加子节点-->
      <CustomInput @cancel="cancelTask" @save="saveTask">
        <template #left>
          <div class="task-type-select">
            <FCascader
              style="width: 96px"
              v-model:value="formData.taskType"
              :disabled="true"
              :options="taskTypeList"
              :placeholder="i18n.t('选择任务类别')"
              option-filter-prop="label"
              :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
            >
            </FCascader>
          </div>
        </template>
        <template #right>
          <FDatePicker
            :class="['add-time-picker', addTaskUserStatus == InteractCustomInput.save ? 'add-time-picker-padding' : '']"
            v-model:value="formData.forcastTime"
            format="YYYY-MM-DD"
            @change="addTimePickerChange"
            :disabled-date="disabledDate"
            :allow-clear="false"
            :bordered="false"
          >
            <template #suffixIcon>
              <div
                style="width: 102px; display: flex; align-items: center"
                class="color666 fontSize12 tubiao"
                v-show="addTaskTimeStatus == InteractCustomInput.init"
              >
                <span class="icon iconfont icontubiao_rili1 fontSize18 marginR4 colord8d8d8"></span>
                <span class="fontSize12 time-yuji">{{ i18n.t('预计完成时间') }}</span>
              </div>
              <div class="fontsize12 save-time" @click="editTaskTime">
                <span
                  :class="[isToday ? 'today-span' : 'time-span']"
                  v-show="addTaskTimeStatus == InteractCustomInput.save"
                >
                  <span v-if="!isToday">
                    {{ transformDate(formData.forcastTime, 'YYYY-MM-DD') }}{{ i18n.t('截止') }}
                  </span>
                  <span v-else>{{ i18n.t('今天截止') }}</span>
                </span>
              </div>
            </template>
          </FDatePicker>
          <div class="user-container">
            <FDropdown
              :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
              placement="bottom"
            >
              <div class="outer-container">
                <div
                  class="color666 fontSize12 cursor init-user"
                  v-show="addTaskUserStatus == InteractCustomInput.init"
                >
                  <span class="icon iconfont icontubiao_renyuan2 marginR4 colord8d8d8"></span>
                  <span>{{ i18n.t('负责人') }}</span>
                </div>
                <div class="save-user" v-show="addTaskUserStatus == InteractCustomInput.save">
                  <img
                    src="@/assets/images/process-detail/user.png"
                    style="width: 26px; border-radius: 16px; border: 1px solid #eeeeee; margin-right: 4px"
                  />
                  <span class="fontSize12 color333 uesr-name">{{
                    transformProcessText(formData.superviser as string)
                  }}</span>
                </div>
              </div>
              <template #overlay>
                <FMenu>
                  <FMenuItem key="1">
                    <FSelect
                      @click.stop="FselectStop"
                      v-model:value="formData.superviser"
                      :options="superviserUserList"
                      :filter-option="filterOption"
                      :field-names="{ label: 'name', value: 'uuid' }"
                      :dropdown-match-select-width="true"
                      :bordered="false"
                      :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
                      :placeholder="i18n.t('请选择')"
                      @select="handleAddUserSelect"
                      show-search
                    >
                    </FSelect>
                  </FMenuItem>
                </FMenu>
              </template>
            </FDropdown>
          </div>
          <FCheckbox class="f12 fontSize12 node-check" v-model:checked="formData.isSys">
            <span style="margin-left: 4px" class="fininsh">{{ i18n.t('当前节点完成') }}</span>
          </FCheckbox>
          <div class="tiling-more-edit-parent">
            <FPopover
              trigger="click"
              placement="bottom"
              :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
            >
              <template #content>
                <div class="cursor" @click="handleTaskEdit" style="width: 360px">
                  <FRow>
                    <FCol :span="24">
                      <FSelect
                        style="width: 100%; margin-bottom: 20px"
                        v-model:value="formData.superviserRoleCode"
                        :field-names="{ label: 'roleName', value: 'roleCode' }"
                        :options="props.role"
                        :press-line="i18n.t('负责角色')"
                        allow-clear
                        :placeholder="i18n.t('请选择')"
                        @change="handleSuperviserRoleChange"
                      >
                        <template #suffixIcon>
                          <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
                        </template>
                      </FSelect>
                    </FCol>
                  </FRow>
                  <FRow>
                    <FCol :span="24">
                      <FSelect
                        style="width: 100%; margin-bottom: 20px"
                        v-model:value="formData.approver"
                        :options="approverUserList"
                        :filter-option="filterOption"
                        :field-names="{ label: 'name', value: 'uuid' }"
                        :press-line="i18n.t('审核人')"
                        show-search
                        allow-clear
                        :placeholder="i18n.t('请选择')"
                      />
                    </FCol>
                  </FRow>
                  <FRow>
                    <FCol :span="24">
                      <FSelect
                        v-model:value="formData.approverRoleCode"
                        style="width: 100%; margin-bottom: 20px"
                        :options="props.role"
                        :field-names="{ label: 'roleName', value: 'roleCode' }"
                        :press-line="i18n.t('审核角色')"
                        allow-clear
                        :placeholder="i18n.t('请选择')"
                        @change="handleapproverRoleChange"
                      />
                    </FCol>
                  </FRow>
                  <FRow>
                    <FCol :span="24">
                      <FSelect
                        style="width: 100%; margin-bottom: 20px"
                        v-model:value="formData.preTask"
                        :options="[
                          { label: i18n.t('无'), value: 0 },
                          ...(props.data.children ?? []).map(item => ({ label: item.taskName, value: item.id })),
                        ]"
                        allow-clear
                        :press-line="i18n.t('前置任务')"
                        :placeholder="i18n.t('请选择')"
                      />
                    </FCol>
                  </FRow>
                  <FRow>
                    <FCol :span="24">
                      <FTextarea
                        v-model:value="(formData.contentData as Record<string, unknown>).taskDesc"
                        :placeholder="i18n.t('请输入任务说明')"
                      />
                    </FCol>
                  </FRow>
                  <FRow class="row-upload">
                    <FCol :span="24">
                      <UploadFile class="row-todo-upload" v-model:value="formData.attachmentData" :hidden-size="false">
                        <template #downloadIcon>
                          <span
                            class="icon iconfont icontubiao_lianjie3"
                            style="height: 13px; verticalalign: -2px; margin-right: 4px"
                          ></span>
                        </template>
                        <template #uploadIcon>
                          <FButton>
                            <UploadOutlined></UploadOutlined>
                            {{ i18n.t('文件上传') }}
                          </FButton>
                        </template>
                      </UploadFile>
                    </FCol>
                  </FRow>
                </div>
              </template>
              <FTooltip :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}">
                <template #title>{{ i18n.t('其他信息') }}</template>
                <span class="icon iconfont icongengduo21 marginR16"></span>
              </FTooltip>
            </FPopover>
          </div>
        </template>
      </CustomInput>
    </div>
    <div :class="['blue-link', 'inline-block', 'cursor', isAddTask ? '' : 'marginT8']">
      <div v-if="(currtMilepost as IProcess).isItConfigured && !isAddTask && !ltcData.flag">
        <FDropdown
          :trigger="['click']"
          :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
        >
          <a class="fs-dropdown-link" @click.prevent.stop>
            <span class="icon iconfont icontubiao_tianjia1"></span>
            <span class="add-task">{{ i18n.t('添加子节点') }}</span>
          </a>
          <template #overlay>
            <FMenu>
              <FMenuItem key="0">
                <span @click.prevent="ltcTask(currtMilepost as IProcess)">{{ i18n.t('任务池任务') }}</span>
              </FMenuItem>
              <FMenuItem key="1">
                <span @click.prevent.stop="addTask(currtMilepost as IProcess)">{{ i18n.t('自定义任务') }}</span>
              </FMenuItem>
            </FMenu>
          </template>
        </FDropdown>
      </div>
      <a v-else-if="!isAddTask" class="ant-dropdown-link" @click.prevent.stop="addTask(currtMilepost as IProcess)">
        <span class="icon iconfont icontubiao_tianjia1"></span>
        <span class="add-task">{{ i18n.t('添加子节点') }}</span>
      </a>
    </div>
    <LtcModal
      :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
      v-model="ltcData.flag"
      :form-key="ltcData.formKey"
      :title="i18n.t('添加任务池任务')"
      @submit="handleTaskSubmit"
    />
  </div>
</template>

<script setup lang="ts">
interface IProps {
  role?: IProcessRoleAndUser[]
  data: ITask | IProcess
}
import { getLtcForm, submitTaskPool } from '@/api/handle'
import UploadFile from '@/components/UploadFile/index.vue'
import { Ref, computed, inject, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { SelectProps } from '@fs/smart-design/dist/ant-design-vue_es'
import { DownOutlined, RightOutlined, UploadOutlined } from '@ant-design/icons-vue'
import dayjs, { Dayjs } from 'dayjs'
import { transformDate, useI18n } from '@/utils'
import { transformFieldIdToLabel } from '@/utils/filter'
import { EmitType, InteractCustomInput } from '@/views/process-detail/config'
import { IProcess, ITask, IUser } from '@/types/handle'
import { IProcessRoleAndUser } from '@/types/request'
import { IFormData, IFormQueryData } from './interface'
import TilingList from './components/TilingList.vue'
import CustomInput from '@/components/Input/CustomInput.vue'
import LtcModal from '@/views/process-operate/components/TaskModal/LtcModal.vue'
import { message } from '@fs/smart-design'

type UserType = IUser & { name: string }
const ltcData = reactive<any>({ flag: false, formKey: 0 })

const i18n = useI18n()
const store = useStore()
const props = defineProps<IProps>()
const emits = defineEmits(['changeView', 'submit'])
const currtMilepost = inject<Ref<IProcess>>('currtMilepost')
const range = ref<[dayjs.Dayjs, dayjs.Dayjs]>()
const approverUserList = ref<UserType[]>([])
const superviserUserList = ref<UserType[]>([])
const taskTypeList = ref<SelectProps['options']>([])
const arrow = ref<string>('expand')
const addTaskTimeStatus = ref<InteractCustomInput>(InteractCustomInput.init)
const addTaskUserStatus = ref<InteractCustomInput>(InteractCustomInput.init)
const isAddTask = ref<boolean>(false)
const userData = computed<UserType[]>(() => store.state.user.allUser || [])
const milestoneOperate = inject('milestoneOperate') as (key: keyof typeof EmitType, data: unknown) => void
const groupList = ref<SelectProps['options']>([])
const visible = ref<boolean>(false)
const isToday = computed<boolean>(() => {
  const myDate = new Date(new Date().getTime() + 8 * 60 * 60 * 1000)
  const time = myDate.toJSON().split('T').join(' ').substr(0, 10)
  return time == transformDate(formData.forcastTime, 'YYYY-MM-DD')
})
const taskStatusList = computed<SelectProps['options']>(() => [
  { value: 0, label: i18n.t('未开始') },
  { value: 1, label: i18n.t('进行中') },
  { value: 2, label: i18n.t('按时完成') },
  { value: 3, label: i18n.t('延期完成') },
  { value: 4, label: i18n.t('待指派') },
  { value: 5, label: i18n.t('已办结') },
  { value: 6, label: i18n.t('待审批') },
])
const isCurrentNodeList = computed<SelectProps['options']>(() => [
  { value: 0, label: '否' },
  { value: 1, label: '是' },
])
const roleMap = computed<Map<string, IProcessRoleAndUser>>(() => {
  const map = new Map()
  if (Array.isArray(props.role) && props.role.length > 0) {
    props.role.forEach(item => map.set(item.roleCode, item))
  }
  return map
})
const formData = reactive<IFormData>({
  taskName: '',
  superviser: null,
  superviserRoleCode: null,
  approver: null,
  approverRoleCode: null,
  forcastTime: null,
  isSys: true,
  contentData: { taskDesc: '' },
  preTask: null,
  taskType: undefined,
  attachmentData: [],
})
const formQueryData = reactive<IFormQueryData>({
  superviser: null,
  approver: null,
  startTime: '',
  endTime: '',
  status: undefined,
  isSys: undefined,
  group: '',
  incompleteChecked: false, // 我负责的
  responsibleChecked: false, // 未完成的
  isSure: false,
})
// 初始化 select 的列表数据
watch(
  () => userData.value,
  () => {
    superviserUserList.value = userData.value
    approverUserList.value = userData.value
  },
  { immediate: true }
)
watch(
  () => range.value,
  () => {
    if (range.value && Array.isArray(range.value) && range.value.length > 0) {
      formQueryData.startTime = transformDate(range.value[0], 'YYYY-MM-DD')
      formQueryData.endTime = transformDate(range.value[1], 'YYYY-MM-DD')
    } else {
      formQueryData.startTime = ''
      formQueryData.endTime = ''
    }
  }
)

const disabledDate = (current: Dayjs) => current && current < dayjs().subtract(1, 'days').endOf('day')
const filterOption = (input: string, option: UserType) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0

const FselectStop = ($event: any) => {
  $event.stopPropagation()
  $event.preventDefault()
}
const changeView = () => {
  emits('changeView')
}
const arrowClick = () => {
  arrow.value == 'expand' ? (arrow.value = 'shrink') : (arrow.value = 'expand')
}
const checkedBoxChenge = () => {
  queryTilingList(false)
}
const queryTilingList = (isSure: boolean) => {
  isSure ? (formQueryData.isSure = true) : (formQueryData.isSure = false)
  visible.value = false
}
const queryTilingCancel = () => {
  visible.value = false
}
const addTask = (currtMilepost: IProcess) => {
  isAddTask.value = true
  clearState()
  milestoneOperate(EmitType.createTask, currtMilepost)
}
const ltcTask = (currtMilepost: IProcess) => {
  // 强制触发表单更新
  ltcData.formKey = 0
  ltcData.flag = false
  getLtcForm(currtMilepost.id)
    .then(res => {
      ltcData.formKey = res.data.id
      ltcData.flag = true
    })
    .catch(err => {
      console.log('err:::', err)
    })
}
const handleTaskEdit = (event: MouseEvent) => {
  event.stopPropagation()
}
const cancelTask = () => {
  isAddTask.value = false
  clearState()
}
const clearState = () => {
  addTaskTimeStatus.value = InteractCustomInput.init
  addTaskUserStatus.value = InteractCustomInput.init
  superviserUserList.value = userData.value
  approverUserList.value = userData.value
  formData.taskName = ''
  formData.superviser = null
  formData.superviserRoleCode = null
  formData.approver = null
  formData.approverRoleCode = null
  formData.forcastTime = null
  formData.isSys = true
  formData.contentData = { taskDesc: '' }
  formData.preTask = null
  formData.taskType = undefined
  formData.attachmentData = []
}
const saveTask = (val: string) => {
  formData.taskName = val
  formData.isSys = formData.isSys ? 1 : 0
  let isPass: boolean = ruleParams(formData)
  if (!isPass) {
    return
  }
  const data = { ...formData }
  data.forcastTime = data.forcastTime ? dayjs(data.forcastTime).format('YYYY-MM-DD HH:mm:ss') : null
  emits('submit', data, () => {
    isAddTask.value = false
  })
}
const ruleParams = (formData: IFormData) => {
  let isPass = true
  if (!formData.taskName) {
    isPass = false
    message.info(i18n.t('请填写任务名字'))
    return isPass
  }
  if (!formData.superviser) {
    isPass = false
    message.info(i18n.t('请填写负责人'))
    return isPass
  }
  return isPass
}
const editTaskTime = () => {
  addTaskTimeStatus.value = InteractCustomInput.init
}
const addTimePickerChange = () => {
  addTaskTimeStatus.value = InteractCustomInput.save
}
const handleAddUserSelect = () => {
  addTaskUserStatus.value = InteractCustomInput.save
}
const handleSuperviserRoleChange = (roleCode: string | null, flag = true) => {
  superviserUserList.value = userData.value
  if (roleCode && roleMap.value.has(roleCode)) {
    const role = roleMap.value.get(roleCode)
    flag && (formData.superviser = role?.users?.[0].uuid ?? null)
  }
}
const handleapproverRoleChange = (roleCode: string | null, flag = true) => {
  approverUserList.value = userData.value
  if (roleCode && roleMap.value.has(roleCode)) {
    const role = roleMap.value.get(roleCode)
    flag && (formData.approver = role?.users?.[0].uuid ?? null)
  }
}
const transformProcessText = (text: number | string) => {
  return transformFieldIdToLabel(superviserUserList.value as Array<any>, 'uuid', 'name', text)
}

const handleTaskSubmit = async (data: any) => {
  if (currtMilepost?.value.id) {
    const poolData = {
      formData: { ...data },
      milepostId: currtMilepost!.value.id,
    }
    milestoneOperate(EmitType.createPoolTask, poolData)
  } else {
    message.error(i18n.t('当前里程碑不存在，无法提交任务池表单'))
  }
}
</script>

<style scoped lang="scss">
.tiling-list-container {
  background-color: #fff;
  .tiling-header {
    height: 48px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    margin-bottom: 6px;
    .tiling-header-left {
      display: flex;
      align-items: center;
      width: 200px;
      margin-right: 2px;
      font-weight: 500;
      color: #333333;
      span {
        :deep(svg) {
          width: 12px;
          height: 12px;
        }
      }
    }
    .tiling-header-middle {
      flex-grow: 1;
    }
    .tiling-header-right {
      display: flex;
      margin-top: 18px;
      width: auto;
      .change-list-view {
        display: inline-block;
        .sp:hover {
          span {
            color: #5fa4f2 !important;
          }
        }
      }
      .change-list-view::before {
        content: '';
        height: 24px;
        display: inline-block;
        line-height: 24px;
        border-left: 1px solid #eeeeee;
        vertical-align: middle;
        margin-right: 12px;
      }
      .more-search-box {
        .btn_div_relative {
          display: block;
        }
      }
    }
  }
}
.task-add-view {
  height: 48px;
  line-height: 48px;
  .user-container {
    display: inline-block;
    // width: 140px;
    line-height: 1;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
    margin-right: 8px;
    .init-user {
      display: inline-block;
      width: 68px;
      // height: 28px;
      // padding-top: 5px;
      // margin-right: 28px;
    }
    .init-user:hover {
      span {
        color: #333;
      }
    }
    .save-user {
      width: 100%;
      margin-right: 0;
    }
    :deep(.fs-dropdown) {
      min-width: 315px !important;
      border-radius: 3px;
      box-shadow: 0 4px 12px 1px rgba(88, 98, 110, 0.14);
      background-color: #fff;
      .fs-dropdown-content {
        background-color: #fff;
      }
      .fs-dropdown-menu {
        padding: 0;
        margin: 16px 16px 7px 16px;
        .fs-dropdown-menu-item {
          padding: 8px;
          height: 32px;
          box-sizing: border-box;
          background-color: #fff;
          .fs-dropdown-menu-title-content {
            .fs-select-selector {
              padding: 0;
              .fs-select-selection-item {
                padding-left: 0;
              }
              .fs-select-selection-search {
                left: -2px !important;
              }
            }
            .fs-select-dropdown {
              min-width: 315px !important;
              left: -24px !important;
              padding: 0 !important;
              .fs-select-item-option-active {
                background-color: #f1f4f8;
              }
              .fs-select-item-option-selected {
                background-color: #ebf3fd;
                color: #378eef !important;
                text-align: left;
              }
            }
          }
        }
        .fs-dropdown-menu-item:hover {
          background-color: #ffffff;
          border-radius: 3px;
          border: 1px solid #378eef;
          box-shadow: 0 0 0 2px #afd1f8;
        }
      }
      .fs-select {
        width: 100%;
      }
    }
  }
  .node-check {
    padding-top: 3px;
  }
  :deep(.custom-input-container) {
    position: relative;
  }
  .task-type-select {
    margin-left: 12px;
    margin-right: 16px;
    :deep(.fs-select-dropdown) {
      .fs-cascader-menu {
        .fs-cascader-menu-item-active {
          color: #378eef;
        }
      }
    }
    :deep(.fs-select) {
      cursor: pointer;
      .fs-select-selector {
        padding: 0 4px;
        cursor: pointer;
        border: none !important;
        box-shadow: none !important;
        height: 30px;
        &:hover {
          cursor: pointer;
          background-color: #f1f4f8;
        }
        .fs-select-selection-placeholder {
          padding-right: 0;
        }
      }
      .fs-select-arrow {
        right: 4px;
      }
      .fs-select-selection-item {
        background-color: #f1f4f8;
      }
    }
    :deep(.fs-select-focused) {
      border: none !important;
      box-shadow: none !important;
    }
  }
  :deep(.add-time-picker) {
    // position: absolute;
    // padding: 4px 0;
    // right: 240px;
    // z-index: 2;
    .fs-picker-input {
      cursor: pointer;
      input {
        display: none;
      }
    }
  }
  .add-time-picker-padding {
    right: 306px;
  }
  .save-time {
    .time-span {
      width: 116px;
      display: inline-flex;
      justify-content: center;
      padding: 3px 8px;
      font-size: 12px;
      color: #333333;
      background-color: #f8f8f8;
      border-radius: 3px;
    }
    .today-span {
      width: 66px;
      display: inline-block;
      padding: 3px 8px;
      font-size: 12px;
      color: #f04141;
      background-color: #fdecec;
      border-radius: 3px;
    }
  }
  .fs-picker:hover {
    .tubiao {
      .icontubiao_rili1 {
        color: #333;
      }
      .time-yuji {
        color: #333;
      }
    }
  }
  .save-user {
    width: 145px;
    margin-right: 48px;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
  }
  .fs-checkbox-wrapper {
    :deep(span) {
      padding: 0;
      display: inline-block;
    }
    :deep(span.fininsh) {
      padding: 0;
      display: inline-block;
      width: 74px;
      margin-right: 16px;
    }
    :deep(.fs-checkbox) {
      width: 16px;
      height: 16px;
    }
    :deep(.fs-checkbox-inner) {
      width: 16px;
    }
  }
}
.marginR2 {
  margin-right: 2px;
}
.blue-link {
  color: #378eef;
}
.inline-block {
  display: inline-block;
}
.marginL20 {
  margin-left: 20px;
}
.fontSize18 {
  font-size: 18px !important;
}
.btn_div_relative {
  display: inline-block;
  position: relative;
  // height: 34px;
  line-height: 1;
  :deep(.icon_span_absolute) {
    display: inline-block;
    position: absolute;
    top: -7px;
    left: 4px;
    z-index: 10;
    padding: 0 3px;
    color: #999;
    background: #fff;
    font-size: 11px;
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }
  .icon_span_absolute_left {
    left: 7px;
  }
}
.icontubiao_rili1:before {
  border: 1px solid #eee;
  border-radius: 50%;
  padding: 3px;
}
.icontubiao_renyuan2 {
  vertical-align: text-bottom;
}
.icontubiao_renyuan2:before {
  border: 1px solid #eee;
  border-radius: 50%;
  padding: 3px;
}
.tiling-more-edit-parent {
  position: relative;
  padding-top: 3px;
  .btn_div_relative {
    width: 100%;
  }
  .row-upload {
    padding: 24px 0 0 0;
    .row-todo-upload {
      :deep(.upload-btn) {
        float: none;
        margin-bottom: 8px;
        display: inline-block;
      }
      :deep(.upload-list) {
        margin-top: 16px;
        .upload-item {
          background-color: #fff;
          padding: 0 4px 0 4px;
          width: 100%;
          box-sizing: border-box;
          margin-bottom: 0;
          height: 26px;
        }
        .upload-item:hover {
          background-color: #f1f4f8;
        }
      }
    }
  }
}
</style>

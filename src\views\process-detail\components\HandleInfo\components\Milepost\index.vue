<template>
  <MilepostCard :id="cardId" :milepost="props.milepost">
    <!-- 基本数据 -->
    <MilepostBaseInfo :milepost="props.milepost" />

    <!-- 表单渲染 -->
    <FormRender :id="milepost.formKey" :type="TFFormType(milepost)" :data="TFFormData(milepost)" />

    <!-- 附件 -->
    <MilepostAppendix :milepost="props.milepost" />

    <!-- 工作协同 -->
    <MilepostTask v-if="props.milepost.status === 2 || props.milepost.children" :milepost="props.milepost" />

    <!-- 操作按钮 -->
    <MilepostFooter :milepost="props.milepost" />
  </MilepostCard>
</template>
<script setup lang="ts">
import { computed, markRaw } from 'vue'
import FormRender from '@/components/FormRender/index.vue'
import MilepostCard from '../MilepostCard/index.vue'
import MilepostBaseInfo from '../MilepostBaseInfo/index.vue'
import MilepostAppendix from '../MilepostAppendix/index.vue'
import MilepostTask from '../MilepostTask/index.vue'
import MilepostFooter from '../MilepostFooter/index.vue'
import type { IProcess } from '@/types/handle'

interface IProps {
  milepost: IProcess
}

const props = defineProps<IProps>()
const cardId = computed(() => `${props.milepost.id}-${props.milepost.topicName}`)

const TFFormType = (milepost: IProcess) => (milepost.status === 2 && milepost.milepostRole == 1 ? 'edit' : 'view')
const TFFormData = (milepost: IProcess) => {
  // 剔除一些基本用不到又可能会更新的字段，减少表单重新渲染的次数，表单重新渲染会造成页面抖动
  // eslint-disable-next-line prettier/prettier, @typescript-eslint/no-unused-vars
  const {
    completeTime,
    contentData,
    createdTime,
    creator,
    updatedTime,
    nodeStartTime,
    planTime,
    children,
    formData,
    ...data
  } = milepost
  return markRaw({ envData: data, envDefaultFormData: formData })
}
</script>

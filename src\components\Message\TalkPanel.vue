<template>
  <div class="talk-panel-box">
    <div class="talk-content">
      <DialogEditor :placeholder="i18n.t('请输入内容')" ref="refDialogEditor" />
      <UpLoadS3 ref="refUpLoadS3" v-model:value="fileList" :file-types="FILT_TYPE"> </UpLoadS3>
    </div>
    <div class="talk-footer">
      <div class="icon-box">
        <FUpload :file-list="[]" :before-upload="(file: any) => handleUpload(file)">
          <span class="iconfont icon">&#xe625;</span>
        </FUpload>
      </div>
      <div class="send">
        <FButton :loading="sendLoading" @click="sendHandle()"> {{ i18n.t('发送') }} </FButton>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { messageInstance } from '@fs/smart-design'

import DialogEditor from './Dialogbox/DialogEditor.vue'
import UpLoadS3 from './UpLoadS3/index.vue'
import { IFile } from '@/types/request'
import { BPM_MESSAGE_EDITOR, useI18n } from '@/utils'

const i18n = useI18n()
const FILT_TYPE = [
  'PDF',
  'JPG',
  'JPEG',
  'PNG',
  'DOC',
  'DOCX',
  'XLS',
  'XLSX',
  'TXT',
  'PPT',
  'PPTX',
  'ZIP',
  'PCAP',
  'RAR',
  'MP4',
]

const emits = defineEmits(['send'])
const refDialogEditor = ref()
const fileList = ref<IFile[]>([])
const sendLoading = ref(false)
const refUpLoadS3 = ref()
const handleUpload = (file: any) => {
  refUpLoadS3.value.handleUpload(file)
  return false
}

const clearContent = () => {
  refDialogEditor.value.clearContent()
  fileList.value = []
  localStorage.removeItem(BPM_MESSAGE_EDITOR)
}

const sendHandle = () => {
  const { content, reminders: receiverReqs = [] } = refDialogEditor.value.getContent() || {}
  if (!content || content === '<p><br></p>' || /^<p>\s*<\/p>$/.test(content))
    return messageInstance.warn(i18n.t('请输入要发送的信息'))
  if (fileList.value.some((item: any) => item.status !== 'success'))
    return messageInstance.warn(i18n.t('请检查文件是否上传完成或上传失败'))
  emits('send', { file: fileList.value, receiverReqs, content, callback: clearContent })
}

const onSelectRoleFn = (data: any) => {
  refDialogEditor?.value?.onSelectRoleFn(data)
}

const onSetContentFn = (data: any) => {
  refDialogEditor.value.onSetContentFn(data?.content ?? '')
  fileList.value = data?.file ?? []
}

defineExpose({
  onSelectRoleFn,
  onSetContentFn,
})
</script>
<style lang="scss" scoped>
.talk-panel-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  position: relative;
  .show-placeholder::before {
    content: attr(placeholder);
    pointer-events: none;
    color: #bbbbbb;
  }
  .show-placeholder:focus:before {
    content: none;
  }
  .talk-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .talk-file {
    max-height: 45%;
    overflow-y: auto;
    overflow-x: hidden;
    flex-shrink: 0;
    .file-item {
      width: 300px;
      height: 32px;
      line-height: 32px;
      background: #f7f8f9;
      border-radius: 2px;
      margin: 10px 20px 10px 20px;
      display: flex;
      font-size: 12px;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      .file-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .icon-file {
          padding-right: 3px;
          width: 14px;
          height: 14px;
          flex-shrink: 0;
        }
        .file-txt {
          max-width: 200px;
          overflow: hidden;
          padding: 0 3px;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin: 0;
        }
        .file-size {
          color: #bbbbbb;
        }
      }
      .icon-del {
        color: #bbbbbb;
        cursor: pointer;
        font-size: 12px;
      }
    }
    .file-item:last-child {
      margin-bottom: 0px;
    }
  }
  .talk-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 12px;
    height: 14px;
    line-height: 14px;
    .send {
      height: 100%;
    }
    .icon-box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      height: 100%;
      .icon {
        color: #666666;
        margin-right: 14px;
        &:hover {
          color: #378eef;
          cursor: pointer;
        }
      }
    }
  }
}
</style>

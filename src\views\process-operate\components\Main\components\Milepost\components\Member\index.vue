<template>
  <div class="meber-wrapper">
    <span class="meber" @click="flag && (selectFlag = !selectFlag)">
      <img class="meber-img" :src="props.src" />
      {{ props.name }}
    </span>
    <div class="meber-select" v-if="selectFlag && flag">
      <div class="meber-select-input-wrapper">
        <input
          class="meber-select-input"
          type="text"
          :value="currUserName"
          @input="handleChange"
          :placeholder="props.name"
        />
      </div>
      <div class="meber-select-item-wrapper">
        <div
          :class="['meber-select-item', { active: item.value === props.id }]"
          :key="item.value"
          v-for="item in userData"
          @click="handleClick(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <FModal
      v-model:visible="visible"
      :title="i18n.t('转派节点')"
      :width="480"
      :confirm-loading="loading"
      :ok-text="i18n.t('确认')"
      :cancel-text="i18n.t('取消')"
      @cancel="onCancel"
      @ok="onOk"
    >
      <div class="tip">
        <i class="iconfont icontishi"></i>
        <div>
          <p>{{ i18n.t('是否确认更换节点负责人？') }}</p>
          <p>{{ i18n.t('该节点负责人将会变更为 ${name} ，请谨慎操作！', { name: selectData?.label }) }}</p>
        </div>
      </div>
      <FForm ref="formRef" :model="formState" layout="vertical">
        <FFormItem label="是否后续节点统一调整" name="isUpdateRole">
          <FRadioGroup
            v-model:value="formState.isUpdateRole"
            :options="[
              { label: '否', value: 0 },
              { label: '是', value: 1 },
            ]"
          />
        </FFormItem>
      </FForm>
      <i />
    </FModal>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref, inject, h, reactive } from 'vue'
import { useStore } from 'vuex'
import { FModal } from '@fs/smart-design'
import { useI18n } from '@/utils'
import { IUser } from '@/types/handle'
import { EmitType } from '@/views/process-detail/config'

type UserType = {
  label: string
  value: string
}

interface IProps {
  id?: string
  name?: string
  src?: string
  flag?: boolean
}

const i18n = useI18n()
const store = useStore()
const props = defineProps<IProps>()
const visible = ref<boolean>(false)
const loading = ref(false)
const selectData = ref<UserType>()
const formState = reactive({
  isUpdateRole: 0,
})
const operate = inject('operate') as (key: EmitType, data: any) => void

const selectFlag = ref<boolean>(false)
const currUserId = ref<string>()
const currUserName = ref<string>()
const userData = computed<UserType[]>(() =>
  (store.state.user.allUser || [])
    .map((item: IUser) => ({ label: item.name, value: item.uuid }))
    .filter(
      (item: UserType) => !currUserName.value || item.label.toLowerCase().includes(currUserName.value.toLowerCase())
    )
)

watch(
  () => props.id,
  () => {
    currUserId.value = props.id
  },
  { immediate: true }
)

const onCancel = () => {
  selectData.value = undefined
  formState.isUpdateRole = 0
  visible.value = false
}

const onOk = async () => {
  currUserId.value = selectData?.value?.value
  currUserName.value = selectData?.value?.label
  await operate(EmitType.dispatch, {
    type: 'switch',
    data: { id: selectData?.value?.value, name: selectData?.value?.label, isUpdateRole: formState.isUpdateRole },
  })
  onCancel()
}

const handleClick = (item: UserType) => {
  selectData.value = item
  visible.value = true
}

const handleChange = (e: any) => {
  currUserName.value = e.target.value
}
</script>

<style scoped lang="scss">
.meber-wrapper {
  position: relative;
  z-index: 9;
}
.meber {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 12px;

  > .meber-img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
}

.meber-select {
  position: absolute;
  top: 32px;
  left: 0;
  width: 315px;
  height: 228px;
  background: #ffffff;
  box-shadow: 0px 4px 12px 1px rgba(88, 98, 110, 0.14);
  border-radius: 3px;

  .meber-select-input-wrapper {
    position: relative;
    margin: 16px 16px 0 16px;
    .meber-select-input {
      width: 283px;
      height: 32px;
      line-height: 32px;
      padding: 0 8px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      background: #ffffff;
      border-radius: 3px;
      border: 1px solid #378eef;
      box-sizing: border-box;
      outline: none;

      &:focus {
        border: 1px solid #378eef;
        box-shadow: 0px 0px 0px 2px #afd1f8;
      }
    }

    &::before {
      position: absolute;
      top: calc(16px - 8px);
      right: 8px;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      font-size: 16px;
      font-family: 'bpm-iconfont' !important;
      color: #bbb;
      content: '\e79a';
    }

    &::placeholder {
      color: #bbb;
      font-size: 12px;
    }
  }

  .meber-select-item-wrapper {
    margin: 8px 4px 4px 4px;
    height: 168px;
    overflow: hidden;
    overflow-y: auto;

    .meber-select-item {
      padding: 0 12px;
      margin-bottom: 2px;
      height: 32px;
      line-height: 32px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      background: #ffffff;
      border-radius: 3px;
      box-sizing: border-box;
      cursor: pointer;

      &:hover {
        background-color: #f1f4f8;
      }

      &.active {
        color: #378eef;
        background-color: #ebf3fd;
      }
    }
  }
}
.tip {
  display: flex;
  padding: 8px;
  margin-bottom: 24px;
  border: 1px solid #fdd2a7;
  background: #fef4e9;
  border-radius: 3px;
  i {
    height: 18px;
    line-height: 18px;
    margin-top: -1px;
    margin-right: 8px;
    color: #fa9a39;
  }
  p {
    margin-bottom: 4px;
  }
  p:first-child {
    height: 22px;
    margin-bottom: 4px;
    font-weight: 400;
    color: #333333;
    line-height: 1.2;
    font-size: 14px;
  }
  p:last-child {
    margin-bottom: 0;
    color: #999;
    font-size: 12px;
    line-height: 18px;
  }
}
</style>

import { ProductListParams, ProductListResponse } from '@/types/productRemoveList'
import { IRes } from '@/types/request'
import { request } from '@/utils'

export function getIpdProductLaunchList(data: ProductListParams): Promise<ProductListResponse> {
  return request.post('/api/ipdProductLaunch/getPage', data)
}

// 导出
export const exportProcess = async (params: ProductListParams) => {
  const res = request.post<IRes>('/api/ipdProductLaunch/download', params)
  return res as unknown as IRes
}

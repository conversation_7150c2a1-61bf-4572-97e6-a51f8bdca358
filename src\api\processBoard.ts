import { IResData, SuccessIRes } from '@/types/handle'
import { ProcessListParams, ProcessListResponse } from '@/types/processBoard'
import { request, fileRequest } from '@/utils'

export function getTaskSituationList(data: ProcessListParams): Promise<ProcessListResponse> {
  return request.post('/api/baseReportForms/taskSituation', data)
}

export function getTaskSituationExport(data: ProcessListParams): Promise<any> {
  return request.post('/api/baseReportForms/download', data, { responseType: 'blob' })
}

export function getProcessSituation(data: ProcessListParams): Promise<SuccessIRes> {
  return request.post('/api/baseReportForms/processSituation', data)
}

export function getTaskSituationChart(data: ProcessListParams): Promise<SuccessIRes> {
  return request.post('/api/baseReportForms/taskSituationChart', data)
}

export function getTaskDetailChart(data: ProcessListParams): Promise<SuccessIRes> {
  return request.post('/api/baseReportForms/taskDetailIndex', data)
}

export function getTaskDetailExport(data: ProcessListParams): Promise<any> {
  return request.post('/api/baseReportForms/downloadTaskDetailIndex', data, { responseType: 'blob' })
}

export function getProcessDetailChart(data: ProcessListParams): Promise<SuccessIRes> {
  return request.post('/api/baseReportForms/projectDetailIndex', data)
}

export function getProcessDetailExport(data: ProcessListParams): Promise<any> {
  return request.post('/api/baseReportForms/downloadProjectDetailIndex', data, { responseType: 'blob' })
}

export function getModifyProcessCount(data: any): Promise<SuccessIRes> {
  return request.post('/api/bodifyReportForms/projecCount', data)
}

export function getModifyProcessUserTop(data: any): Promise<SuccessIRes> {
  return request.post('/api/bodifyReportForms/userTop', data)
}

export function getModifyProcessDictionary(): Promise<SuccessIRes> {
  return request.get('/api/bodifyReportForms/getDictionary')
}

export function getModifyProcessExport(data: ProcessListParams): Promise<any> {
  return fileRequest.post('/api/bodifyReportForms/download', data, { responseType: 'blob' })
}

export function getModifyProcessInstanceHoursCount(data: any): Promise<SuccessIRes> {
  return request.post('/api/bodifyReportForms/instanceHours', data)
}

export function downModifyProcessCount(data: any): Promise<any> {
  return fileRequest.post('/api/bodifyReportForms/export', data, { responseType: 'blob' })
}

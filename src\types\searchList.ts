export interface IRes<T = null> {
  code: number
  msg: string
  traceId: string
  data: T
}

export interface ISearchDataList<T = null> {
  endRow: number
  nextPage: number
  pageNum: number
  pageSize: number
  pages: number
  prePage: number
  size: number
  startRow: number
  total: number
  totalCount: number
  list: T[]
}

export interface ISearchData {
  [key: string]: unknown
  field: string
  id: number
  moduleType: string
  name: string
  processConfigId: number
  remarks: string
  searchType: string
  status: number
  url: string
}

interface IPageParmas {
  pageNum: number // 当前页
  pageSize: number // 每页条数
}

export interface IQueryParams extends IPageParmas {
  remarks?: null | string
  field?: string
  id?: number
  moduleType?: string
  name?: string
  processConfigId?: number
  searchType?: string
  status?: number
  url?: string
}

export enum ITitle {
  add = '新增筛选项',
  edit = '编辑筛选项',
}

export enum IsearchType {
  eq = '等值',
  like = '模糊',
  gt = '大于',
  lt = '小于',
  range = '范围',
}

export enum ImoduleType {
  ONE_LINE_INPUT = '单行文本框',
  RANGE_NUMBER = '区间数字',
  MULTIPLE_SELECT = '下拉多选框',
  SELECT = '下拉框',
  TREE_SELECT = '树形下拉框',
  DATE_PICKER = '时间日期',
  RANGE_PICKER = '区间日期',
}

export const IvalueType = {
  1: '字符串类型',
  2: '数字类型',
}

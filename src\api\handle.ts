import { request, S3URL } from '@/utils'
import type {
  IProcessRes,
  IQueryParams,
  ICreateParams,
  IResData,
  IUser,
  IPreParams,
  IDeleteParams,
  ITransferParams,
  ITurnParams,
  IRejectParams,
  ISubmitParams,
  ISaveParams,
  IRes,
  IOperationRes,
  IOperation,
  IProcessModelRes,
  ProcessModel,
  IPreTask,
  IMakeParams,
} from '@/types/handle'
import { PoolParams, SortITask, SuccessIRes } from '@/types/handle'

export function getInstanceInfo(data: IQueryParams): Promise<IProcessRes> {
  return request.post('/api/demandProcessing/selectByInstanceId', data)
}

export function addTask(data: ICreateParams): Promise<IResData> {
  return request.post('/api/demandProcessing/addTask', data)
}

export function editTask(data: ICreateParams): Promise<IResData> {
  return request.post('/api/demandProcessing/updateTask', data)
}

interface IBacthEditTaskParams {
  instanceId: number
  instanceTopicName: string
  processInstanceCode: string
  processType: string
  taskIdList: number[]
  forcastTime: string
}

export const batchEditTask = async (params: IBacthEditTaskParams) => {
  const res = await request.post<IRes>('/api/demandProcessing/batchUpdateTask', params)
  return res as unknown as IRes
}

export function addChildTask(data: ICreateParams): Promise<IResData> {
  return request.post('/api/demandProcessing/addChildTask', data)
}

export async function getAllUsers() {
  const res = await request.get<IRes<IUser[]>>('/api/user/select')
  return res as unknown as IRes<IUser[]>
}

export async function getPreTask(data: IPreParams) {
  const res = await request.post<IRes<IPreTask[]>>('/api/demandProcessing/showPreTask', data)
  return res as unknown as IRes<IPreTask[]>
}

export function removeTask(data: IDeleteParams): Promise<IResData> {
  return request.get('/api/demandProcessing/deleteTask', {
    params: data,
  })
}

interface IBacthDelTaskParams {
  instanceId: number
  instanceTopicName: string
  processInstanceCode: string
  processType: string
  topicName: string
  taskIdList: number[]
}

export const batchDelTask = (params: IBacthDelTaskParams) => {
  return request.post('/api/demandProcessing/batchDeleteTask', params)
}

export function transferrTask(data: ITransferParams): Promise<IResData> {
  return request.post('/api/demandProcessing/conclude', data)
}

export function turnTask(data: ITurnParams): Promise<IResData> {
  return request.post('/api/demandProcessing/reassignmentByMilepost', data)
}

export function rejectTask(data: IRejectParams): Promise<IResData> {
  return request.post('/api/demandProcessing/reject', data)
}

export function commitTask(data: ISubmitParams): Promise<IResData> {
  return request.post('/api/demandProcessing/submitMilepost', data)
}

export function saveTask(data: ISaveParams): Promise<IResData> {
  return request.post('/api/demandProcessing/save', data)
}

export async function upload(data: any, callback: any = undefined, key: any = undefined): Promise<string> {
  const res = await request({
    method: 'post',
    url: S3URL + '/api/s3/upload',
    data,
    onUploadProgress(progress) {
      callback && callback(progress, key)
    },
  })
  return res as unknown as string
}

export function submitMission(data: any): Promise<IResData> {
  return request.post('/api/demandProcessing/submitTask', data)
}

export function saveMission(data: any): Promise<IResData> {
  return request.post('/api/demandProcessing/saveTask', data)
}

export function dispatchMission(data: any): Promise<IResData> {
  return request.post('/api/demandProcessing/reassignmentByTask', data)
}

export function checkMission(data: any): Promise<IResData> {
  return request.post('/api/demandProcessing/verifyTask', data)
}

export function rejectMission(data: any): Promise<IResData> {
  return request.post('/api/demandProcessing/rejectTask', data)
}

export function operationRecord(data: any): Promise<IRes<IOperationRes<IOperation>>> {
  return request.post('/api/demandProcessing/getHistory', data)
}

export function getProcess(data: any): Promise<IRes<IProcessModelRes<ProcessModel>>> {
  return request.post('/api/bpmDefine/getProcessModel', data)
}

// 表单保存
export const saveMilepostFormData = (data: { id: number; formData: Record<string, unknown> }) => {
  return request.post('api/bpmInstance/saveMilepostFormData', data)
}

export function getDepartment(): Promise<IResData> {
  return request.get('/api/DepartmentPerson/getDepartment')
}

// 任务重排序
export function resortTask(data: SortITask): Promise<SuccessIRes> {
  return request.post('/api/demandProcessing/resortTask', data)
}

// 已完成任务回滚
export function rollingBack(data: { id: number }): Promise<SuccessIRes> {
  return request.post('/api/demandProcessing/rollingBack', data)
}

// 通过里程碑id获取任务池表单
export function getLtcForm(milepostId: number): Promise<SuccessIRes> {
  return request.get(`/api/demandProcessing/taskPool/getForm/${milepostId}`)
}

// 提交任务池
export function submitTaskPool(data: PoolParams): Promise<SuccessIRes> {
  return request.post(`/api/demandProcessing/submitTaskPool`, data)
}

// 查询抄送人
export function getMakePerson(data: IMakeParams): Promise<SuccessIRes> {
  return request({
    url: `/api/demandProcessing/getMakePerson`,
    method: 'post',
    params: data,
  })
}

// 通过id删除流程
export function deleteProcessFn(processId: number): Promise<SuccessIRes> {
  return request.get(`/api/demandProcessing/deleteProcess/${processId}`)
}

// 获取通知模板
export function getAllCode(): Promise<SuccessIRes> {
  return request.get('/api/notifyTemplate/getAllCode')
}

// 获取角色code
export function getRoleAllCode(): Promise<SuccessIRes> {
  return request.get('/api/roleConcern/getAllCode')
}

// 通过里程碑id获取任务池表单
export function getConcludeMsg(instanceId: number): Promise<SuccessIRes> {
  return request.get(`/api/demandProcessing/getConcludeMsg/${instanceId}`)
}

interface ITaskTimeParams {
  forcastTime: string
  taskStartTime: string
  contentData: any
  taskId: number
}
export function editTaskTime(data: ITaskTimeParams): Promise<IResData> {
  return request.post('/api/demandProcessing/updateTaskTime', data)
}

export const getRelatedModifyInstances = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/bpmInstance/getRelatedModifyInstances', data)
  return res as unknown as IRes<any>
}

export const templateExportProcess = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/file/templateExport', data)
  return res as unknown as IRes<any>
}

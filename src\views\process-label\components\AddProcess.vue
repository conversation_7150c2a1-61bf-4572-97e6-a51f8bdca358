<template>
  <FModal v-model:visible="visible" :title="title" wrap-class-name="cust-add-process-modal" :width="500">
    <div class="process-lable-modal-wrapper">
      <FForm ref="formRef" :model="formDate" :rules="rules" layout="vertical">
        <FFormItem :label="i18n.t('流程类型')" name="processClassId">
          <FSelect
            v-model:value="processClassId"
            :placeholder="i18n.t('请选择')"
            :options="classify"
            disabled
            @change="classigyChange"
          >
          </FSelect>
        </FFormItem>
        <FFormItem :label="i18n.t('里程碑')" name="milepostNodeId">
          <FSelect
            v-model:value="formDate.milepostNodeId"
            :placeholder="i18n.t('请选择')"
            :options="milepostNodelist"
          />
        </FFormItem>
        <FFormItem :label="i18n.t('自动完成')" name="isComplete">
          <FSelect
            v-model:value="formDate.isComplete"
            :placeholder="i18n.t('是否自动完成里程碑')"
            :options="[
              { label: i18n.t('是'), value: 1 },
              { label: i18n.t('否'), value: 0 },
            ]"
          />
        </FFormItem>
        <FFormItem :label="i18n.t('是否发送消息通知')" name="isSend">
          <FSelect
            v-model:value="formDate.isSend"
            :placeholder="i18n.t('请选择是否发送消息通知')"
            :options="[
              { label: i18n.t('发送'), value: 1 },
              { label: i18n.t('不发送'), value: 0 },
            ]"
          />
        </FFormItem>
        <FFormItem :label="i18n.t('默认任务表单')" name="taskDefaultForm">
          <FSelect
            v-model:value="formDate.taskDefaultForm"
            :placeholder="i18n.t('请选择任务表单')"
            :options="formAlls"
            :field-names="{ label: 'name', value: 'id' }"
            show-search
            option-filter-prop="name"
            allow-clear
            @change="taskChange"
          ></FSelect>
        </FFormItem>
        <FFormItem :label="i18n.t('移动任务表单')" name="taskMobileFormKey">
          <FSelect
            v-model:value="formDate.taskMobileFormKey"
            :placeholder="i18n.t('请选择任务表单')"
            :options="formAlls"
            :field-names="{ label: 'name', value: 'id' }"
            show-search
            option-filter-prop="name"
            allow-clear
            @change="taskMobileChange"
          ></FSelect>
        </FFormItem>
        <FFormItem :label="i18n.t('人员表达式')" name="userCondition">
          <FTextarea v-model:value="formDate.userCondition" :rows="4" :placeholder="i18n.t('请输入')" />
        </FFormItem>
        <FFormItem :label="i18n.t('角色表达式')" name="roleCondition">
          <FTextarea v-model:value="formDate.roleCondition" :rows="4" :placeholder="i18n.t('请输入')" />
        </FFormItem>
        <FFormItem :label="i18n.t('抄送人角色表达式')" name="makeCondition">
          <FTextarea v-model:value="formDate.makeCondition" :rows="4" :placeholder="i18n.t('请输入')" />
        </FFormItem>
        <FFormItem :label="i18n.t('工时表达式')" name="timeCondition">
          <FTextarea v-model:value="formDate.timeCondition" :rows="4" :placeholder="i18n.t('请输入')" />
        </FFormItem>
        <FFormItem :label="i18n.t('节点定制化组件')" name="formComponent">
          <FTextarea v-model:value="formDate.formComponent" :rows="4" :placeholder="i18n.t('请输入')" />
        </FFormItem>
        <FFormItem class="handle-process-workflow-box" name="taskPoolForm">
          <template #label>
            <span>任务池任务表单</span>
            <HandleProcess :processWorkflowId="formDate.taskPoolForm" processWorkflowType="form" />
          </template>
          <FSelect
            v-model:value="formDate.taskPoolForm"
            :placeholder="i18n.t('请选择任务池任务表单')"
            :options="formAlls"
            :field-names="{ label: 'name', value: 'id' }"
            show-search
            option-filter-prop="name"
            allow-clear
            @change="taskPoolChange"
          ></FSelect>
        </FFormItem>
        <FFormItem class="handle-process-workflow-box" name="taskPoolProcessKey">
          <template #label>
            <span>任务池流程图</span>
            <HandleProcess :processWorkflowId="formDate.taskPoolProcessKey" />
          </template>
          <FSelect
            :placeholder="i18n.t('请选择')"
            v-model:value="formDate.taskPoolProcessKey"
            :options="processList"
            :field-names="{ label: 'processName', value: 'id' }"
            show-search
            option-filter-prop="processName"
            allow-clear
            @change="taskPoolProcessChange"
          />
        </FFormItem>
      </FForm>
    </div>
    <template #footer>
      <FConfigProvider :auto-insert-space-in-button="false">
        <FButton key="back" @click="visible = false">{{ i18n.t('取消') }}</FButton>
        <FButton key="submit" type="primary" @click="handleOk(formRef)">{{ i18n.t('确定') }}</FButton>
      </FConfigProvider>
    </template>
  </FModal>
</template>
<script setup lang="ts">
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { GetProcessNode, getFormAll } from '@/api/label'
import { ConfigListItem, ProcessNodeItem, IFormData } from '@/types/processLabelModel'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { ProcessModel } from '@/types/handle'
import { getProcess } from '@/api/handle'
import { useI18n } from '@/utils'
import HandleProcess from '@/views/process-class/components/HandleProcess.vue'

const i18n = useI18n()

type OptionsType = { label: string; value: number; tag: string }
const formRef = ref<FormInstance>()
const processClassId = ref<number | null>(null)
const emit = defineEmits(['submit', 'update:visible'])
const processList = ref<ProcessModel[]>([])
const props = defineProps({
  title: { type: String, default: '' },
  type: { type: String, default: '' },
  visible: { type: Boolean, default: false },
  formState: { type: Object, default: null },
  classifyList: { type: Object, default: null },
  classifyId: { type: Number, default: null },
})
// const layout = { labelCol: { span: 8 }, wrapperCol: { span: 16 } }
const visible = computed({
  get() {
    return props.visible
  },
  set(val: boolean) {
    emit('update:visible', val)
  },
})

const milepostNodelist = ref<OptionsType[]>([])
const formDate = reactive<ConfigListItem>({
  id: null,
  processConfigId: null,
  milepostNodeId: null,
  userCondition: null,
  roleCondition: null,
  timeCondition: null,
  makeCondition: null,
  taskDefaultForm: null,
  taskDefaultFormName: null,
  taskMobileFormKey: null,
  taskMobileFormName: null,
  taskPoolForm: null,
  taskPoolFormName: null,
  taskPoolProcessKey: null,
  taskPoolProcessName: null,
  isComplete: 0,
  isSend: 1,
  formComponent: null,
})
const rules: Record<string, Rule[]> = {
  processConfigId: [{ required: true, message: i18n.t('请选择') }],
  milepostNodeId: [{ required: true, message: i18n.t('请选择') }],
}
const formAlls = ref<IFormData[]>([])
watch([() => props.visible, () => props.type, () => props.classifyId], newVal => {
  processClassId.value = props.classifyId
  if (processClassId.value) {
    fetchData()
  }
  formRef.value?.resetFields()
  if (newVal[1] === 'edit') {
    const formState = props.formState
    formDate.id = formState.id
    formDate.processConfigId = formState.processConfigId
    formDate.milepostNodeId = formState.milepostNodeId
    formDate.userCondition = formState.userCondition
    formDate.roleCondition = formState.roleCondition
    formDate.timeCondition = formState.timeCondition
    formDate.makeCondition = formState.makeCondition
    formDate.taskDefaultForm = (formState.taskDefaultForm && Number(formState.taskDefaultForm)) || null
    formDate.taskDefaultFormName = formState.taskDefaultFormName || null
    formDate.taskMobileFormKey = (formState.taskMobileFormKey && Number(formState.taskMobileFormKey)) || null
    formDate.taskMobileFormName = formState.taskMobileFormName || null
    formDate.taskPoolForm = (formState.taskPoolForm && Number(formState.taskPoolForm)) || null
    formDate.taskPoolFormName = formState.taskPoolFormName || null
    formDate.taskPoolProcessKey = (formState.taskPoolProcessKey && Number(formState.taskPoolProcessKey)) || null
    formDate.taskPoolProcessName = formState.taskPoolProcessName || null
    formDate.isComplete = formState.isComplete ?? 0
    formDate.isSend = formState.isSend ?? 1
    formDate.formComponent = formState.formComponent || null
  } else {
    Object.keys(formDate).forEach(key => (formDate[key as keyof typeof formDate] = null))
    formDate.processConfigId = props.classifyId
    formDate.isComplete = 0
    formDate.isSend = 1
  }
})
const classify = computed(() => {
  return props.classifyList.map((item: any) => {
    return {
      label: item.processName,
      value: item.id,
    }
  })
})
const classigyChange = (value: number, option: any) => {
  processClassId.value = value
  formDate.milepostNodeId = null
  milepostNodelist.value = []
  fetchData()
}

const fetchData = async () => {
  const params = { processConfigId: processClassId.value }
  const res = await GetProcessNode(params)
  if (res.code === 200) {
    milepostNodelist.value = res.data.map((item: ProcessNodeItem) => {
      return {
        label: item.milepostName,
        value: item.id,
        tag: item.tag,
      }
    })
  }
}

const handleOk = async (formRef: any) => {
  if (!formRef) return
  await formRef.validate().then(() => {
    formDate.processConfigId = processClassId.value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { ...data } = formDate
    emit('submit', data)
  })
}

const InitFormAlls = async () => {
  const res = await getFormAll()
  if (res.code === 200) {
    formAlls.value = res.data
  }
}

const taskChange = (value: number, option: any) => {
  formDate.taskDefaultFormName = option?.name ?? null
}
const taskMobileChange = (value: number, option: any) => {
  formDate.taskMobileFormName = option?.name ?? null
}
const taskPoolChange = (value: number, option: any) => {
  formDate.taskPoolFormName = option?.name ?? null
}
const taskPoolProcessChange = (value: number, option: any) => {
  formDate.taskPoolProcessName = option?.processName ?? null
}
onMounted(() => {
  InitFormAlls()
  getProcess({ currPage: 1, pageSize: 9999, processName: '' }).then(res => {
    processList.value = res.data.list
  })
})
</script>
<style lang="scss" deep>
.cust-add-process-modal {
  .fs-modal-body {
    height: 62vh;
    overflow: scroll;
  }
}
.process-lable-modal-wrapper {
  .fs-form-item {
    margin-bottom: 22px !important;
  }
  .fs-modal-content .fs-modal-footer .fs-btn {
    padding: 0 24px !important;
    width: auto !important;
  }
  div.fs-form-item-control-input-content,
  .fs-form-item-control-input-content textarea.fs-input {
    height: auto !important;
  }

  .fs-modal-footer .fs-btn {
    display: inline-block;
  }
}
</style>

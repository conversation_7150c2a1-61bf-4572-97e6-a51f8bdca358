import type { BasicFetchResponse, BasicPageParams } from './common'

export interface ProcessClassifyItem {
  supplierNum: string
  createdTime: string
  id: number | null
  invalid: number | null
  processDefineKey: string
  processName: string
  remark: string
  tag: string
  updateTime: string
}
export interface ProcessClassifyResponse extends BasicFetchResponse {
  data: {
    list: ProcessClassifyItem[]
    [key: string]: unknown
  }
}
export interface ProcessNodeItem {
  // [x: string]: any
  createdTime: string
  id: number
  milepostName: string
  nodeMode: number
  parentId: number
  processDefineNodeKey: string
  superviser: string
  tag: string
  updatedTime: string
  updateTime: string
}
export interface GetProcessParams extends BasicPageParams {
  processName?: string
  source?: number
}
export interface ProcessNodeResponse extends BasicFetchResponse {
  data: ProcessNodeItem[]
  traceId: string
}
export interface ProcessNodeParams {
  processConfigId: number | null
}
export interface ManagerConfigParams {
  createUser?: string
  manager?: string
  milepostNodeId?: number
  processConfigId?: number
  sort?: number
  tag?: string
  timeConsuming?: number | null
}
export interface UpdataManagerConfigParams extends ManagerConfigParams {
  id?: number | void
}
export interface ManagerConfigResponse extends BasicFetchResponse {
  traceId: string
}
export interface ConfigListItem {
  id?: number | null
  processConfigId?: number | null
  milepostNodeId?: number | null
  milepostNodeName?: string | null
  userCondition?: string | null
  roleCondition?: string | null
  timeCondition?: string | null
  makeCondition?: string | null
  sort?: number | null
  manager?: string | null
  createUser?: string | null
  status?: number | null
  taskDefaultForm?: number | null
  taskDefaultFormName?: string | null
  taskMobileFormKey?: number | null
  taskMobileFormName?: string | null
  isComplete?: number | null
  taskPoolForm?: number | null
  taskPoolFormName?: string | null
  taskPoolProcessKey?: number | null
  taskPoolProcessName?: string | null
  isSend?: number | null
  [key: string]: any
}
export interface ConfigListResponse extends BasicFetchResponse {
  data: {
    list: ConfigListItem[]
    total?: number | null
    totalCount?: number | null
    [key: string]: unknown
  }
}
export interface ConfigListParams extends BasicPageParams {
  processConfigId?: number
  queryInput?: string
}
export interface UpdateStatusResponse extends BasicFetchResponse {
  traceId: string
}
export interface UpdateStatusParams {
  id: number
}
export interface DelManagerResponse extends BasicFetchResponse {
  traceId: string
}
export interface DelManagerParams {
  id: number
}

export interface IFormData {
  id: number
  name: string
}
export interface IFormAll extends BasicFetchResponse {
  data: IFormData[]
}

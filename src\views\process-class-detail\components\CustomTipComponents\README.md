# 优雅的 tipText 功能使用指南

## 概述

全新的 `tipText` 功能采用统一的 `TipRenderer` 组件和类型安全的配置系统，提供了更优雅、简洁的使用方式：

1. **字符串方式**：传统的简单文本提示（完全向后兼容）
2. **函数方式**：动态生成提示文本，智能响应字段值变化
3. **组件方式**：使用自定义 Vue 组件，实现复杂的提示逻辑
4. **预设配置**：提供常用的提示模板，开箱即用
5. **工厂函数**：标准化的配置创建器，确保类型安全

## 核心优势

### 🎯 **类型安全**
- 完整的 TypeScript 类型定义
- 编译时类型检查，避免运行时错误
- 智能代码提示和自动补全

### 🛠️ **统一渲染**
- 单一的 `TipRenderer` 组件处理所有类型
- 标准化的配置格式
- 一致的渲染逻辑和性能优化

### 📦 **开箱即用**
- 丰富的预设配置 (`tipPresets`)
- 常用场景的模板化解决方案
- 工厂函数简化配置创建

## 使用方式

### 1. 字符串方式（最简洁）

```typescript
{
  fieldKey: 'processName',
  label: '流程名称',
  tipText: '请输入流程的完整名称，建议使用中文描述'
}
```

### 2. 预设配置（推荐）

```typescript
import { tipPresets } from '@/views/process-class-detail/components/BaseForm/tipTypes'

// 必填提示
{
  tipText: tipPresets.required('流程名称')
}

// 长度限制提示
{
  tipText: tipPresets.length(10, 100)
}

// 带值显示的动态提示
{
  tipText: tipPresets.withValue('流程描述', '当前长度：', '字符')
}

// 条件提示
{
  tipText: tipPresets.conditional(
    (fieldValue) => !!fieldValue,
    '已填写内容',
    '请填写相关信息'
  )
}
```

### 3. 工厂函数（标准化）

```typescript
import { createTipConfig } from '@/views/process-class-detail/components/BaseForm/tipTypes'

// 文本提示
{
  tipText: createTipConfig.text('提示文本', {
    bgColor: '#000',
    textColor: '#fff'
  })
}

// 动态提示
{
  tipText: createTipConfig.dynamic((fieldValue, formData) => {
    return fieldValue ? `当前值：${fieldValue}` : '请填写内容'
  })
}

// 自定义组件
{
  tipText: createTipConfig.component(ProcessTip, {
    baseText: '帮助信息',
    showValue: true
  })
}
```

### 4. 函数方式（高级用法）

```typescript
{
  tipText: (fieldValue, formData, fieldConfig) => {
    // 复杂的动态逻辑
    if (!fieldValue) return '请填写内容'

    const relatedField = formData.relatedField
    if (relatedField) {
      return `当前值：${fieldValue}，关联：${relatedField}`
    }

    return `当前值：${fieldValue}`
  }
}
```

## 内置组件

### ProcessTip 组件

简单的提示组件，支持显示字段值。

**Props:**
- `baseText`: 基础提示文本
- `showValue`: 是否显示字段值
- `valuePrefix`: 值的前缀文本
- `valueSuffix`: 值的后缀文本

### DynamicTip 组件

复杂的动态提示组件，支持多种内容展示。

**Props:**
- `title`: 弹窗标题
- `text`: 触发文本
- `type`: 提示类型 ('info' | 'warning' | 'error' | 'success')
- `showRelatedProcesses`: 是否显示关联流程
- `showValidation`: 是否显示验证状态
- `customSuggestions`: 自定义建议列表

## 实际使用示例

### 示例1：根据字段值动态提示

```typescript
processLevel: {
  component: CustomSelect,
  fieldKey: 'processLevel',
  label: '流程级别',
  tipText: (fieldValue, formData) => {
    const levelMap = {
      1: '一级流程：需要最高级别审批',
      2: '二级流程：需要部门级别审批',
      3: '三级流程：普通审批流程'
    }
    
    if (!fieldValue) {
      return '请选择流程级别，不同级别有不同的审批权限要求'
    }
    
    const description = levelMap[fieldValue] || '未知级别'
    const relatedProcess = formData.processDefineKey 
      ? `，关联流程：${formData.processDefineKey}` 
      : ''
    
    return `${description}${relatedProcess}`
  }
}
```

### 示例2：使用自定义组件

```typescript
processType: {
  component: CustomSelect,
  fieldKey: 'processType',
  label: '流程类型',
  tipText: {
    component: DynamicTip,
    props: {
      title: '流程类型说明',
      text: '类型详情',
      type: 'info',
      showRelatedProcesses: true,
      showValidation: true,
      customSuggestions: [
        '不同类型的流程有不同的审批规则',
        '选择后将自动配置相关参数'
      ]
    }
  }
}
```

## 创建自定义提示组件

### 基本结构

```vue
<template>
  <div style="width: max-content;">
    <!-- 你的提示UI -->
    <FTooltip :title="tooltipContent">
      <span style="margin-left: 4px; cursor: pointer;">
        <i class="iconfont icontubiao_tishi_mian" />
        <span>{{ displayText }}</span>
      </span>
    </FTooltip>
  </div>
</template>

<script setup lang="ts">
interface Props {
  fieldKey?: string      // 字段键名
  fieldValue?: any       // 字段当前值
  fieldConfig?: any      // 字段配置
  formData?: any         // 完整表单数据
  // 你的自定义属性...
}

const props = defineProps<Props>()

// 你的组件逻辑...
</script>
```

### 注意事项

1. **响应式更新**：组件会在字段值变化时自动重新渲染
2. **性能考虑**：避免在组件中进行重复的异步操作
3. **样式一致性**：保持与现有UI风格的一致性
4. **错误处理**：确保组件在异常情况下不会崩溃

## 辅助工具

### 预设配置

```typescript
import { commonTipConfigs } from './TipUsageExample'

// 使用预设配置
{
  tipText: commonTipConfigs.required('流程名称')
}

{
  tipText: commonTipConfigs.customTip('这是自定义提示', 'warning')
}

{
  tipText: commonTipConfigs.dynamicTip('详细说明', ['建议1', '建议2'])
}
```

### 配置创建器

```typescript
import { createTipConfig } from './TipUsageExample'

{
  tipText: createTipConfig(YourCustomComponent, {
    prop1: 'value1',
    prop2: 'value2'
  })
}
```

## 最佳实践

1. **保持简洁**：提示信息应该简洁明了
2. **动态内容**：利用字段值和表单数据提供相关的提示
3. **用户体验**：确保提示不会干扰正常的表单操作
4. **性能优化**：避免在提示组件中进行重复的计算或API调用
5. **可访问性**：确保提示内容对屏幕阅读器友好

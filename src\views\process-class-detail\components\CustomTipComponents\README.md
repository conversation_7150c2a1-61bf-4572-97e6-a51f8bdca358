# 增强的 tipText 功能使用指南

## 概述

增强的 `tipText` 功能支持三种使用方式：
1. **字符串方式**：传统的简单文本提示（保持向后兼容）
2. **函数方式**：动态生成提示文本，可以访问当前字段值和表单数据
3. **组件方式**：使用自定义 Vue 组件，实现复杂的提示逻辑

## 使用方式

### 1. 字符串方式（传统方式）

```typescript
{
  fieldKey: 'processName',
  label: '流程名称',
  tipText: '请输入流程的完整名称，建议使用中文描述',
  // ... 其他配置
}
```

### 2. 函数方式（动态文本）

```typescript
{
  fieldKey: 'processDefineKey',
  label: '关联流程图',
  tipText: (fieldValue, formData, fieldConfig) => {
    if (!fieldValue) {
      return '请选择关联的流程图，这将决定流程的执行路径'
    }
    return `已选择流程图ID: ${fieldValue}，当前流程将按此图执行`
  },
  // ... 其他配置
}
```

**函数参数说明：**
- `fieldValue`: 当前字段的值
- `formData`: 整个表单的数据对象
- `fieldConfig`: 当前字段的配置对象

### 3. 组件方式（自定义组件）

```typescript
import ProcessTip from './CustomTipComponents/ProcessTip.vue'

{
  fieldKey: 'processDescription',
  label: '流程描述',
  tipText: {
    component: ProcessTip,
    props: {
      baseText: '流程描述帮助',
      showValue: true,
      valuePrefix: '当前字符数：',
      valueSuffix: '字'
    }
  },
  // ... 其他配置
}
```

**组件配置说明：**
- `component`: Vue 组件
- `props`: 传递给组件的属性

**组件会自动接收以下 props：**
- `fieldKey`: 字段键名
- `fieldValue`: 字段当前值
- `fieldConfig`: 字段配置对象
- `formData`: 完整表单数据

## 内置组件

### ProcessTip 组件

简单的提示组件，支持显示字段值。

**Props:**
- `baseText`: 基础提示文本
- `showValue`: 是否显示字段值
- `valuePrefix`: 值的前缀文本
- `valueSuffix`: 值的后缀文本

### DynamicTip 组件

复杂的动态提示组件，支持多种内容展示。

**Props:**
- `title`: 弹窗标题
- `text`: 触发文本
- `type`: 提示类型 ('info' | 'warning' | 'error' | 'success')
- `showRelatedProcesses`: 是否显示关联流程
- `showValidation`: 是否显示验证状态
- `customSuggestions`: 自定义建议列表

## 实际使用示例

### 示例1：根据字段值动态提示

```typescript
processLevel: {
  component: CustomSelect,
  fieldKey: 'processLevel',
  label: '流程级别',
  tipText: (fieldValue, formData) => {
    const levelMap = {
      1: '一级流程：需要最高级别审批',
      2: '二级流程：需要部门级别审批',
      3: '三级流程：普通审批流程'
    }
    
    if (!fieldValue) {
      return '请选择流程级别，不同级别有不同的审批权限要求'
    }
    
    const description = levelMap[fieldValue] || '未知级别'
    const relatedProcess = formData.processDefineKey 
      ? `，关联流程：${formData.processDefineKey}` 
      : ''
    
    return `${description}${relatedProcess}`
  }
}
```

### 示例2：使用自定义组件

```typescript
processType: {
  component: CustomSelect,
  fieldKey: 'processType',
  label: '流程类型',
  tipText: {
    component: DynamicTip,
    props: {
      title: '流程类型说明',
      text: '类型详情',
      type: 'info',
      showRelatedProcesses: true,
      showValidation: true,
      customSuggestions: [
        '不同类型的流程有不同的审批规则',
        '选择后将自动配置相关参数'
      ]
    }
  }
}
```

## 创建自定义提示组件

### 基本结构

```vue
<template>
  <div style="width: max-content;">
    <!-- 你的提示UI -->
    <FTooltip :title="tooltipContent">
      <span style="margin-left: 4px; cursor: pointer;">
        <i class="iconfont icontubiao_tishi_mian" />
        <span>{{ displayText }}</span>
      </span>
    </FTooltip>
  </div>
</template>

<script setup lang="ts">
interface Props {
  fieldKey?: string      // 字段键名
  fieldValue?: any       // 字段当前值
  fieldConfig?: any      // 字段配置
  formData?: any         // 完整表单数据
  // 你的自定义属性...
}

const props = defineProps<Props>()

// 你的组件逻辑...
</script>
```

### 注意事项

1. **响应式更新**：组件会在字段值变化时自动重新渲染
2. **性能考虑**：避免在组件中进行重复的异步操作
3. **样式一致性**：保持与现有UI风格的一致性
4. **错误处理**：确保组件在异常情况下不会崩溃

## 辅助工具

### 预设配置

```typescript
import { commonTipConfigs } from './TipUsageExample'

// 使用预设配置
{
  tipText: commonTipConfigs.required('流程名称')
}

{
  tipText: commonTipConfigs.customTip('这是自定义提示', 'warning')
}

{
  tipText: commonTipConfigs.dynamicTip('详细说明', ['建议1', '建议2'])
}
```

### 配置创建器

```typescript
import { createTipConfig } from './TipUsageExample'

{
  tipText: createTipConfig(YourCustomComponent, {
    prop1: 'value1',
    prop2: 'value2'
  })
}
```

## 最佳实践

1. **保持简洁**：提示信息应该简洁明了
2. **动态内容**：利用字段值和表单数据提供相关的提示
3. **用户体验**：确保提示不会干扰正常的表单操作
4. **性能优化**：避免在提示组件中进行重复的计算或API调用
5. **可访问性**：确保提示内容对屏幕阅读器友好

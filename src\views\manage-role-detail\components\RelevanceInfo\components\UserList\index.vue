<template>
  <div class="user-list-container">
    <div class="flex space-between align-items marginB16">
      <FInput
        v-model:value="search"
        type="search-clear"
        pressLine="快速搜索"
        @search="searchChangeFn"
        @clear="searchChangeFn"
        allow-clear
        class="width240"
        placeholder="中文名/英文名"
      />
      <div class="extra-box">
        <FSpace :size="[8]">
          <FButton size="small" @click="handleImport">
            <i class="icontubiao_daoru iconfont marginR4"></i>角色导入
          </FButton>
          <FButton v-if="roleId" size="small" @click="userModalRef?.onOpenFn?.('addRoleUser')">
            <template #icon>
              <i class="iconfont icontubiao_tianjia1 marginR4"></i>
            </template>
            新增人员
          </FButton>
        </FSpace>
      </div>
    </div>

    <FForm ref="formRef" :model="dataList" layout="vertical" validateTrigger="none" name="tableForm">
      <FTable
        class="cust-table"
        :loading="loading"
        :row-key="(record:any) => record.id"
        :columns="columns"
        :data-source="dataList"
        :sticky="{ offsetHeader: 50 }"
        :scroll="{ x: 'min-content' }"
        :pagination="{
          total: paging.total,
          current: paging.pageNum,
          pageSize: paging.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
          onChange: onPaginationChangeFn
        }"
      >
        <template #bodyCell="{ column, index, record }">
          <template v-if="column.dataIndex && column.component">
            <FFormItem
              :name="[index, column.dataIndex]"
              :rules="creatRules(column, record, index) || []"
              class="form-item-no-margin"
            >
              <component
                :is="column.component"
                v-model:value="record[column.dataIndex]"
                v-bind="column.componentAttrs || {}"
                :componentConfig="{
                  dataType: column.componentType || 'input',
                  isDetail: !record.isEditing || column.isDetail,
                  valueFormatFn: column?.valueFormatFn ?? undefined,
                  componentAttrs: column.componentAttrs || {},
                  currtRecord: record,
                }"
              />
            </FFormItem>
          </template>
          <template v-if="column.key === 'handle'">
            <FSpace :size="[8]">
              <template v-if="record.isEditing">
                <TipBtn key="save" tip-title="保存">
                  <i class="iconfont icontubiao_baocun1 hover-btn" @click="saveRow(record, index)"></i>
                </TipBtn>
                <TipBtn key="cancel" tip-title="取消">
                  <i class="iconfont icontubiao_quxiao hover-btn" @click="cancelEdit(record, index)"></i>
                </TipBtn>
              </template>
              <TipBtn v-else tip-title="编辑">
                <i class="iconfont icontubiao_xietongbianji hover-btn" @click="editRow(record)" />
              </TipBtn>
              <TipBtn
                has-pop
                tip-title="删除"
                pop-title="确定删除选中的数据吗？"
                @onConfirmFn="onDeleteConfirmFn(record)"
              >
                <i class="iconfont icontubiao_xietongshanchu hover-btn"></i>
              </TipBtn>
            </FSpace>
          </template>
        </template>
      </FTable>
    </FForm>

    <ImportModal
      v-model:visible="importData.visible"
      :loading="importData.loading"
      :title="importData.title"
      tip
      template
      @download="onDownloadFn"
      @submit="onSubmitFn"
    />
    <HandleUser ref="userModalRef" @update-change="onGetByIdConfig" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, Ref, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { message } from '@fs/smart-design'
import ImportModal from '@/views/manage-role/components/ImportModal/index.vue'
import CustomInput from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInput/index.vue'
import CustomSelect from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomSelect/index.vue'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import HandleUser from '../HandleUser/index.vue'
import { isEmpty } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'
import { getManageRoleUserList, importManageRoleUser, deleteManageRoleUser, updateManageRoleUser } from '@/api'
import { download } from '@/utils'

const roleId = inject<Ref<string>>('roleId')
const roleConfig = inject<Ref<any>>('roleConfig')
const importData = reactive<any>({
  visible: false,
  loading: false,
  title: '人员导入',
})
const search = ref()
const formRef = ref()
const loading = ref<boolean>(false)
const dataList = ref([])
const store = useStore()
const allUserList = computed(() => store.state.user.allUser || [])
const columns = ref<any[]>([
  {
    title: '序号',
    dataIndex: 'serialNo',
    key: 'serialNo',
    width: 80,
  },
  {
    title: '人员名称',
    dataIndex: 'uuid',
    key: 'uuid',
    component: CustomSelect,
    componentType: 'select',
    componentAttrs: {
      options: allUserList,
      allowClear: true,
      showSearch: true,
      fieldNames: { value: 'uuid', label: 'feiShuName' },
      optionFilterProp: 'feiShuName',
      placeholder: '请选择人员',
    },
    rules: [{ required: true, message: '请选择人员' }],
    valueFormatFn: (value: any, componentConfig: any) => {
      return componentConfig?.currtRecord?.feiShuName
    },
  },
  {
    title: '条件表达式',
    dataIndex: 'conditionExpression',
    key: 'conditionExpression',
    component: computed(() => (roleConfig.value?.type === 3 ? CustomInput : undefined)),
    componentType: 'input',
    componentAttrs: {
      placeholder: '请输入条件表达式',
    },
    rules: [{ required: computed(() => (roleConfig.value?.type === 3 ? true : false)), message: '请输入条件表达式' }],
  },
  {
    title: '操作',
    dataIndex: 'handle',
    key: 'handle',
    width: 110,
    fixed: 'right',
  },
])
const userModalRef = ref()
const paging = reactive<any>({ pageNum: 1, pageSize: 10, total: 0 })
const downloadUrl = computed(() =>
  roleConfig.value?.type === 3
    ? 'https://pvt-doc.whgxwl.com/default/72a14abf0d1149899547f4e23f723804?AWSAccessKeyId=AKIAZ36WLN7CVAY2ALUW&Expires=1840261735&Signature=y6zgZhd3IMz1dBBHt7nI2dhRbgE%3D'
    : 'https://pvt-doc.whgxwl.com/default/db58a145afb44d49bd05475e54ba5486?AWSAccessKeyId=AKIAZ36WLN7CVAY2ALUW&Expires=1840261823&Signature=5elJSjX8efd6KZabZFvk0Fedd2E%3D'
)

const handleImport = () => {
  importData.visible = true
}

const onDownloadFn = () => {
  download(downloadUrl.value, '角色人员导入模板.xls')
}

const onSubmitFn = async (data: any) => {
  importData.loading = true
  try {
    const formData = new FormData()
    formData.set('file', data.files[0].originFileObj)
    formData.set('roleConfigId', roleId.value)
    const res = await importManageRoleUser(formData)
    if (res.code !== 200) throw new Error(res.msg)
    message.success(res?.msg || '导入成功')
    onGetByIdConfig()
    importData.visible = false
  } finally {
    importData.loading = false
  }
}

const clearValidate = (record: any, index: number) => {
  if (formRef.value) {
    try {
      const keys = columns.value.map(item => [index, item.dataIndex])
      formRef.value.clearValidate(keys)
    } catch (error) {
      console.error('清空校验信息失败:', error)
    }
  }
}

const validateFieldsFn = async (record: any, index: number) => {
  if (formRef.value) {
    const keys = columns.value.map(item => [index, item.dataIndex])
    return await formRef.value.validateFields(keys)
  }
}

const validatorKeys = {}

const creatRules = (column: any, record: any, index: number) => {
  return (column?.rules ?? [])?.map((columnRule: any) => {
    return Object.assign({
      validator: (rule: any, value: any, callback: any) => {
        if (validatorKeys?.[column.dataIndex]) {
          validatorKeys[column.dataIndex](rule, value, callback, column, record, index)
        } else {
          if (columnRule?.required && record.isEditing && isEmpty(record?.[column.dataIndex])) {
            callback(new Error(columnRule?.message || '请先编辑'))
          } else {
            callback()
          }
        }
      },
    })
  })
}

// 取消编辑
const cancelEdit = (record: any, index: number) => {
  // 恢复原始数据
  if (record._originalData) {
    Object.keys(record._originalData).forEach(key => {
      if (key !== '_originalData' && key !== 'isEditing') {
        record[key] = record._originalData[key]
      }
    })
    delete record._originalData
  }

  // 退出编辑状态
  record.isEditing = false
  clearValidate(record, index)
}

const submitKeys = ['uuid', 'conditionExpression']
// 保存行
const saveRow = async (record: any, index: number) => {
  try {
    await validateFieldsFn(record, index)

    const submitData = submitKeys.reduce((acc, key) => {
      const column = columns.value.find(item => item.dataIndex === key)
      if (column?.submitValueFormatFn) {
        console.log('column?.submitValueFormatFn(column, record) :>> ', column?.submitValueFormatFn(column, record))
        acc = { ...acc, ...column?.submitValueFormatFn(column, record) }
      } else {
        acc[key] = record[key]
      }
      return acc
    }, {})
    const params = {
      ...submitData,
    }
    await updateManageRoleUser({ ...params, id: record.id, roleConfigId: roleId.value })
    onGetByIdConfig()
    message.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    error?.errorFields?.[0]?.errors?.[0] && message.warning(error.errorFields[0].errors[0] ?? '请先编辑')
  }
}

// 编辑行
const editRow = (record: any) => {
  record._originalData = JSON.parse(JSON.stringify(record))
  record.isEditing = true
}

const onDeleteConfirmFn = async (record: any) => {
  const res = await deleteManageRoleUser({ id: record.id })
  if (res.code !== 200) throw new Error(res.msg)
  paging.pageNum = 1
  message.success('删除成功')
  onGetByIdConfig()
}

const onGetByIdConfig = async () => {
  if (!roleId.value) {
    throw new Error('角色ID不能为空')
  }
  try {
    loading.value = true
    const params = {
      roleConfigId: Number(roleId.value),
      pageNum: paging.pageNum,
      pageSize: paging.pageSize,
      input: search.value?.trim() || undefined,
    }
    const res = await getManageRoleUserList(params)
    if (res.code !== 200) throw new Error(res.msg)
    dataList.value = res?.data?.page?.list ?? []
    paging.total = res?.data?.page?.totalCount || 0
  } finally {
    loading.value = false
  }
}

const searchChangeFn = () => {
  paging.pageNum = 1
  onGetByIdConfig()
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  paging.pageNum = current
  paging.pageSize = pageSize
  onGetByIdConfig()
}

watch(
  () => roleId.value,
  newVal => {
    newVal && onGetByIdConfig()
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.align-items {
  align-items: center;
}
.padding24 {
  padding: 24px;
}
:deep(.fs-form-item-control-input-content) {
  line-height: 32px;
  height: auto !important;
  min-height: 32px;
}
.form-item-no-margin {
  margin-bottom: 0 !important;
  :deep(.fs-form-item-explain-connected) {
    position: absolute;
    bottom: -26px;
  }
}
.hover-btn {
  color: #378eef;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  &:hover {
    background-color: #d8d8d8;
  }
}
:deep(.fs-table-body) {
  .fs-table-cell {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
}
:deep(.fs-table-pagination.fs-pagination) {
  margin-bottom: 0;
}
</style>

import { Dayjs } from 'dayjs'

export interface IFormData {
  taskName: string
  superviser: string | null
  superviserRoleCode: string | null
  approver: string | null
  approverRoleCode: string | null
  forcastTime: string | Dayjs | null
  isSys: 0 | 1 | boolean
  contentData: { taskDesc: string }
  preTask: number | null
  taskType?: string
  attachmentData?: []
}
export interface IFormQueryData {
  superviser: string | null
  approver: string | null
  startTime: string
  endTime: string
  status?: number
  isSys?: 0 | 1
  group: ''
  incompleteChecked: boolean
  responsibleChecked: boolean
  isSure: boolean
}

<template>
  <div class="dashboard-container">
    <iframe v-if="dashboardUrl" width="100%" height="100%" frameborder="0" :src="dashboardUrl"></iframe>
  </div>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue'
import { useRoute } from 'vue-router'
import { SUPERSET_URL } from '@/utils'

const route = useRoute()
const dashboardUrl = ref()
watch(
  () => route.params,
  val => {
    dashboardUrl.value = `${SUPERSET_URL}/login/?next=${SUPERSET_URL}/superset/dashboard/${val.id}/?standalone=2`
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
.dashboard-container {
  height: 100%;
}
</style>

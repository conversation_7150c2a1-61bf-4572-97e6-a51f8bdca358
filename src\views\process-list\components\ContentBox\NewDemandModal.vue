<template>
  <FModal
    :title="i18n.t('选择项目类型')"
    class="fs-modal new-demand"
    :visible="visible"
    :width="720"
    :footer="null"
    @cancel="handleCancel"
    :body-style="{
      paddingBottom: '0px',
      maxHeight: '676px',
      overflowY: 'auto',
    }"
  >
    <div class="modal-box">
      <template v-if="entrances && entrances.length > 0">
        <div class="card" v-for="(item, index) in entrances" :key="index" @click="onOpenUrl(item)">
          <img :src="require(`@/assets/images/bpm/add_demand_link.png`)" alt="" />
          <div class="info">
            <p class="info-title">{{ item.processName }}</p>
            <p class="info-text" :title="item.remark">{{ item.remark }}</p>
          </div>
        </div>
      </template>
      <div v-else class="empty">{{ i18n.t('暂无数据') }}</div>
    </div>
  </FModal>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { messageInstance as message } from '@fs/smart-design'
import { getClassList } from '@/api/class'
import { IClassData } from '@/types/class'
import { useI18n } from '@/utils'

const i18n = useI18n()

const emit = defineEmits(['cancel'])
const props = defineProps({
  isNewdemandModal: { type: Boolean, default: false },
  params: { type: Object, default: () => ({}) },
})
const entrances = ref<any>([])
const visible = ref<boolean>(false)
const router = useRouter()

onMounted(() => {
  fetchList()
})

watch(
  () => props.isNewdemandModal,
  newtitleVal => (visible.value = newtitleVal)
)
const fetchList = async () => {
  const params = {
    pageNum: 1,
    pageSize: 999,
    source: 1,
  }
  const res = await getClassList(params)
  if (res.code === 200) {
    entrances.value = res.data.list
  } else {
    message.warning(res.msg)
  }
}
// 打开新地址
const onOpenUrl = (item: IClassData) => {
  const { href } = router.resolve({
    name: 'DemandAdd',
    params: { id: item.id, processDefineKey: item.processDefineKey },
    query: { ...props.params },
  })
  window.open(href, '_blank')
}
const handleCancel = () => emit('cancel')
</script>
<style lang="scss" scoped>
.modal-box {
  display: flex;
  justify-content: space-between;
  padding: 11px;
  flex-wrap: wrap;
  .card:nth-child(2n) {
    margin-left: 24px;
  }
  .card {
    width: 310px;
    height: 120px;
    background: #ffffff;
    box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    display: flex;
    cursor: pointer;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid transparent;

    & > img {
      width: 30px;
      height: 30px;
      object-fit: contain;
      margin-right: 12px;
    }

    & > .solution-icon {
      width: 30px;
      height: 30px;
      flex-shrink: 0;
      margin-right: 14px;
      flex-shrink: 0;
      background: url('~@/assets/images/bpm/solution-project-icon.png') center no-repeat;
      background-size: 100% 100%;
    }

    & > .aftersale-icon {
      width: 30px;
      height: 30px;
      flex-shrink: 0;
      margin-right: 14px;
      flex-shrink: 0;
      background: url('~@/assets/images/bpm/aftersale-project-icon.png') center no-repeat;
      background-size: 100% 100%;
    }

    & > .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      width: calc(100% - 44px);
      p {
        padding: 0;
        margin: 0;
      }
      .info-title {
        height: 14px;
        font-size: 14px;
        color: #333333;
        line-height: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 2px;
        margin-bottom: 8px;
        font-weight: bold;
      }
      .info-text {
        height: 36px;
        font-size: 12px;
        color: #999999;
        line-height: 18px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }
  }

  .card:hover {
    box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.1);
    border: 1px solid #f5f5f5;
  }

  .empty {
    display: flex;
    justify-content: center;
    width: 100%;
    padding-bottom: 20px;

    & :deep(.empty-box) {
      width: 200px;
    }
  }
}
</style>

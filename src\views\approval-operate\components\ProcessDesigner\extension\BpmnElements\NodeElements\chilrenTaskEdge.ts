import { PolylineEdgeModel, PolylineEdge } from '@logicflow/core'

export class ChildrenTaskEdgeModel extends PolylineEdgeModel {
  static extendKey = 'ChildrenTaskEdgeModel'

  setAttributes() {
    this.isAnimation = true
  }

  getEdgeAnimationStyle() {
    const style = super.getEdgeAnimationStyle()
    style.stroke = 'red'
    style.strokeDasharray = '5 5'
    style.animationDuration = '10s'
    return style
  }
}

const ChildrenTaskEdge = {
  type: 'bpmn:childrenTaskFlow',
  view: PolylineEdge,
  model: ChildrenTaskEdgeModel,
}

export default ChildrenTaskEdge

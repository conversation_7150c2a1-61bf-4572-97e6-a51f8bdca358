import type { BasicFetchResponse } from './common'

export interface BasicPageParams {
  pageNum?: number
  pageSize?: number
  total?: number
}

export interface ProductListParams extends BasicPageParams {
  uuidList?: string[]
  productStatus?: string
  endTime?: string
  demand?: number
  queryInput?: string
  startTime?: string
}

export interface ProductItem {
  createdTime: string
  createdUserName: string
  demand: string
  id: number
  instanceId: number
  manufacturerOffTime: string
  milestone: string
  milestoneUserName: string
  offTime: string
  processInstanceCode: string
  productId: number
  productLine: string
  productStatus: string
  productType: string
  projecStatus: number
  replaceFile: string
  replaceProductId: number
  replaceProductType: string
  topicName: string
  updateOrNot: string
  [key: string]: any
}

export interface ProductListResponse extends BasicFetchResponse {
  data: {
    list: ProductItem[]
    total: number
    [key: string]: any
  }
}

import { NodeConfig, GraphModel } from '@logicflow/core'
import { BpmnBaseModel, BpmnBaseNode } from './bpmnBase'

class ChildrenTaskModel extends BpmnBaseModel {
  constructor(data: NodeConfig, graphModel: GraphModel) {
    super(data, graphModel)
    this.themeColor = '#FA8F23'
  }
}

class ChildrenTaskView extends BpmnBaseNode {}

export default {
  type: 'bpmn:childrenTask',
  view: ChildrenTaskView,
  model: ChildrenTaskModel,
}

<template>
  <div class="milepost-form-render-wrapper" v-if="isShow" @click="handleElementClick">
    <component
      :is="RenderComponent"
      ref="renderRef"
      :schema="schema"
      :global-data="renderProps"
      :fetcher="amisFetcher"
    />
    <SeeImage v-model="seeImageObj.flag" :src="seeImageObj.src" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, markRaw, watch, reactive, onUnmounted, nextTick, computed } from 'vue'
import { BAESURL, S3URL, getToken, getUserInfo, deepClone, CRMURL, MOMURL, getLanguage, getPower } from '@/utils'
import { getFormSchema } from '@/api'
import { getFetcher } from './index'

import AmisRender from './components/AmisRender/index.vue'
import TriggerRender from './components/TriggerRender/index.vue'
import SeeImage from '@/components/SeeImage/index.vue'

import '@fs/trigger-engine/dist/style.css'
import '@fs/trigger-plugin-panel-dock/dist/style.css'
import '@fs/trigger-setters/dist/style.css'
import '@fs/trigger-materials/dist/style.css'

interface IProps {
  id: string | number
  type: 'view' | 'edit'
  data: Record<string, unknown>
}

const props = defineProps<IProps>()
const token = getToken()
const userInfo = markRaw(deepClone(getUserInfo()))
const language = getLanguage()

const isShow = ref(false)
const seeImageObj = reactive({ src: '', flag: false })
const schema = ref<object>()
const renderRef = ref()
const formType = ref<number>(1)
const RenderComponent = computed(() => (formType.value == 1 ? AmisRender : TriggerRender))
const power = getPower()
const amisFetcher = ref(getFetcher(props.id, token))

const renderProps = ref(
  markRaw({
    locale: language,
    data: {
      ...(markRaw(deepClone(props.data)) || {}),
      envType: props.type || 'view',
      envToken: token,
      envUrl: BAESURL,
      envS3Url: S3URL + '/api/s3/upload',
      envS3BaseUrl: S3URL,
      envUserInfo: userInfo,
      envCrmUrl: CRMURL,
      envMomURL: MOMURL,
      envPower: power,
    },
  })
)

watch(
  [props],
  () => {
    updateAmisProps(language)
  },
  { deep: true }
)

watch(
  () => props.id,
  () => {
    initSchema()
  }
)

watch(schema, () => {
  isShow.value = true
})

onMounted(() => {
  initSchema()
  // 微前端环境下，国际化语言变化监听
  if (window.__MICRO_APP_ENVIRONMENT__) {
    // 监听基座下发的数据变化
    window.microApp.addDataListener(emitLanguageChange)
  }
})

onUnmounted(() => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    window.microApp.removeDataListener(emitLanguageChange)
  }
})

const emitLanguageChange = (data: { path: string; language: string }) => {
  if (data.language) {
    isShow.value && (isShow.value = false)
    updateAmisProps(data.language)
    nextTick(() => (isShow.value = true))
  }
}

const initSchema = async () => {
  isShow.value && (isShow.value = false)
  const res = await getFormSchema(props.id)
  const data = res?.data
  formType.value = data?.formType ?? 1
  schema.value = markRaw(JSON.parse((res?.data?.content as string) ?? '{}'))
}

const handleElementClick = (e: any) => {
  if (props.type === 'view' && e.target.nodeName === 'IMG') {
    seeImageObj.src = e.target.currentSrc
    seeImageObj.flag = true
  }
}

// TODO: 切换成英文后，无法动态切换为中文，必须要刷新才行
const updateAmisProps = (language: string) => {
  const currData = markRaw({
    locale: language,
    data: {
      ...(markRaw(deepClone(props.data)) || {}),
      envType: props.type || 'view',
      envToken: token,
      envUrl: BAESURL,
      envS3Url: S3URL + '/api/s3/upload',
      envS3BaseUrl: S3URL,
      envUserInfo: userInfo,
      envCrmUrl: CRMURL,
      envMomURL: MOMURL,
      envPower: power,
    },
  })
  if (JSON.stringify(currData) !== JSON.stringify(renderProps.value)) {
    renderProps.value = currData
  }
}

defineExpose({
  getAmisScoped: () => renderRef.value?.getScoped?.(),
})
</script>

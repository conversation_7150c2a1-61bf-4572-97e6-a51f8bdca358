<template>
  <FPopover trigger="hover" overlay-class-name="popover-select-notes" :get-popup-container="getPopupContainer">
    <template #content>
      <div class="file-box">
        <f-checkbox-group class="marginB12" v-model:value="checkedGroup" @change="checkedGroupChange">
          <div class="file-item" v-for="file in plainOptions" :key="file.url">
            <f-checkbox v-model:value="file.url">
              <span class="file-ellipsis fontS12" @click.prevent="download(file)">
                <span class="iconfont fontS12 marginR4">&#xe656;</span>
                {{ file.name }}
              </span>
            </f-checkbox>
          </div>
        </f-checkbox-group>
        <div class="download-batch">
          <f-checkbox v-model:checked="isCheckAll" @change="checkAll" class="marginR8 fontS12">
            <span class="file-ellipsis fontS12">{{ i18n.t('全选') }}</span>
          </f-checkbox>
          <span class="color378EEF" @click="batchDownload"
            ><span class="iconfont fontS12 marginR4">&#xe63f;</span>{{ i18n.t('批量下载') }}</span
          >
        </div>
      </div>
    </template>
    <slot name="icon">
      <span class="fontS12 color378EEF"
        ><span class="iconfont fontS12 marginR4">&#xe656;</span>{{ i18n.t('附件') }}</span
      >
    </slot>
  </FPopover>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { messageInstance } from '@fs/smart-design'
import { useI18n } from '@/utils'
const i18n = useI18n()

const getPopupContainer = (trigger: HTMLElement) => trigger.parentElement

const props = defineProps({
  data: {
    type: [Object, Array],
    default: () => [],
  },
})

const emit = defineEmits(['download', 'batchDownload'])
const plainOptions = ref<any>(props.data)
const checkedGroup = ref<any>([])
const isCheckAll = ref(false)
const checkedGroupChange = (e: any) => {
  isCheckAll.value = e.length === plainOptions.value.length
}

const checkAll = (e: any) => {
  if (isCheckAll.value) {
    checkedGroup.value = plainOptions.value.map((item: any) => {
      return item.url
    })
  } else {
    checkedGroup.value = []
  }
}

const download = (file: any) => {
  emit('download', file.url, file.name)
}

const batchDownload = () => {
  if (checkedGroup.value.length > 0) {
    const checkList: any[] = []
    plainOptions.value.forEach((item: any) => {
      checkedGroup.value.forEach((items: string) => {
        if (items === item.url) {
          const list: Record<string, string> = {}
          list.url = item.url
          list.fileName = item.name
          checkList.push(list)
        }
      })
    })
    emit('batchDownload', checkList)
    checkedGroup.value = []
    isCheckAll.value = false
  } else {
    messageInstance.error(i18n.t('请勾选文件'))
  }
}
</script>
<style lang="scss" scoped>
.popover-select-notes {
  :deep(.fs-popover-inner-content) {
    padding: 12px !important;
  }
}
.file-box {
  min-width: 200px;
  font-size: 12px;
  .fs-checkbox-group {
    width: 100%;
  }
  .file-item {
    height: 24px;
    width: 100%;
    line-height: 24px;
    padding: 0 4px;
    &:hover {
      background: #f1f4f8;
      border-radius: 2px;
    }
    .fs-checkbox-wrapper {
      height: 100%;
      :deep(.fs-checkbox) {
        top: 0;
      }
      span {
        height: 100%;
      }
    }
  }
  .file-icon {
    color: #fdb926 !important;
  }
  .file-ellipsis {
    vertical-align: -5px;
    display: inline-block;
    max-width: 239px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
    color: #333333;
  }
}
.download-batch {
  padding-top: 12px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  :deep(.fs-checkbox) {
    margin-left: 4px;
    top: 1px;
  }
}
.marginR4 {
  margin-right: 4px;
}
.marginB12 {
  margin-bottom: 12px !important;
}
.fontS12 {
  font-size: 12px !important;
}
.color378EEF {
  color: #378eef;
}
</style>

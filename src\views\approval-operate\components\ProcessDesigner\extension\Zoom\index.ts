import { createApp } from 'vue'
import LogicFlow from '@logicflow/core'
import SmartDesign from '@fs/smart-design'
import ZoomComponent from '@/views/approval-operate/components/ProcessDesigner/components/ZoomPanel/index.vue'

export default class Zoom {
  static pluginName = 'zoom'

  lf: LogicFlow

  app?: ReturnType<typeof createApp>
  $panel?: HTMLElement // 面板 dom 实例
  $container?: HTMLElement // lf 组建容器 dom 实例

  constructor({ lf }: { lf: LogicFlow }) {
    this.lf = lf
  }

  render(lf, domContainer: HTMLElement) {
    this.$container = domContainer
    this.$panel = document.createElement('div')
    this.$panel.className = 'lf-zoom-panel'
    this.$panel.style.position = 'absolute'
    this.$panel.style.top = '24px'
    this.$panel.style.right = '24px'

    this.createZoom()
  }

  createZoom() {
    if (!this.$panel) return

    this.app = createApp(ZoomComponent, { instance: this })
    this.app.use(SmartDesign)
    this.app.mount(this.$panel)
    this.$container?.appendChild(this.$panel)
  }

  setZoom(zoom: number) {
    this.lf.zoom(zoom)
  }
}

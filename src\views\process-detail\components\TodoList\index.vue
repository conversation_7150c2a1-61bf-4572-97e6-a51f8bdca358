<template>
  <div :style="COMPONENT_CRAD_BODY_STYLE">
    <!-- 我是列表本体 -->
    <div style="padding: 24px 0 24px 0">
      <TilingList
        :is-disable-sort="disableSort"
        :currt-milepost-children="(toDoList as ITask[])"
        arrow="expand"
        :role="processRoleInfo"
        :form-query-data="formQueryData"
      />
    </div>
    <TaskModal
      v-model="taskModel.flag"
      :title="(taskModel['title'] as string)"
      :data="(taskModel.data as ITask)"
      :role="processRoleInfo"
      @submit="handleTaskSubmit"
    />
  </div>
</template>

<script lang="ts" setup>
interface IProps {
  processInfo: IProcess[]
  other: unknown
}

import { inject, provide, reactive, Ref, createVNode, ref } from 'vue'
import { COMPONENT_CRAD_BODY_STYLE } from '@/views/process-detail/config'
import { IProcess, ITask } from '@/types/handle'
import TilingList from '@/views/process-operate/components/Main/components/Milepost/components/tiling/components/TilingList.vue'
import { IFormQueryData } from '@/views/process-operate/components/Main/components/Milepost/components/tiling/interface'
import { IProcessRoleAndUser } from '@/types/request'
import { addTask, batchEditTask, editTask } from '@/api'
import { messageInstance as message, FModal } from '@fs/smart-design'
import { EmitType, TaskModalTilte } from '@/views/process-detail/config'
import { submitMission, saveMission, rollingBack, removeTask } from '@/api/handle'
import { IModel } from '@/types/common'
import TaskModal from '@/views/process-operate/components/TaskModal/index.vue'
import { deepClone } from '@/utils'

const props = defineProps<IProps>()
const disableSort = ref<boolean>(true)
const currtMilepost = inject<Ref<IProcess>>('currtMilepost')
const processId = inject<number>('processId') // 流程 id
const processRoleInfo = inject<IProcessRoleAndUser[]>('processRoleInfo') // 流程角色信息
const toDoList = inject<ITask[]>('toDoList')
const initToDoList = inject('initToDoList') as () => void
const paramsWrapper = inject('paramsWrapper') as (data: any) => any
const setCurrtMilepost = inject('setCurrtMilepost') as (data: IProcess) => void // 设置当前里程碑信息
const getProcessInfo = inject('getProcessInfo') as (callBack?: any) => void
const taskModel = reactive<IModel<ITask | ITask[] | IProcess>>({ flag: false, data: {} as ITask, title: '' })
const formQueryData = reactive<IFormQueryData>({
  superviser: null,
  approver: null,
  startTime: '',
  endTime: '',
  status: undefined,
  isSys: undefined,
  group: '',
  incompleteChecked: false, // 我负责的
  responsibleChecked: false, // 未完成的
  isSure: false,
})
// 强制触发watch更新
requestAnimationFrame(() => {
  formQueryData.incompleteChecked = true
  formQueryData.responsibleChecked = true
})
const handleTaskSubmit = async (data: ITask, callBack?: () => void) => {
  let action
  let formData = deepClone(data)
  const currTask = taskModel.data as ITask
  formData.milepostId = currTask.milepostId as number
  if (taskModel.title === TaskModalTilte.edit) {
    action = editTask
    formData.id = currTask.id
    formData.preTask = currTask.childTaskFlag ? (currTask.preTask as number) : formData.preTask
  } else if (taskModel.title === TaskModalTilte.batchEdit) {
    action = batchEditTask
    formData = {
      isSys: data.isSys,
      superviserRoleCode: data.superviserRoleCode,
      superviser: data.superviser,
      approverRoleCode: data.approverRoleCode,
      approver: data.approver,
      forcastTime: data.forcastTime,
      instanceId: processId,
      taskIdList: (currTask as unknown as ITask[]).map(item => item.id),
    } as unknown as ITask
  } else {
    action = addTask
    formData.preTask = formData.preTask ?? 0
    formData.milepostId = (currTask as unknown as IProcess).id // 新增的时候 data 为 里程碑数据
  }
  try {
    const { code } = await action(paramsWrapper(formData))
    if (code !== 200) return
    await refresh()
    await initToDoList()
    message.success('操作成功')
    callBack && callBack()
  } finally {
    taskModel.flag = false
  }
}
const quickEditHandleTaskSubmit = async (data: ITask, callback: (id: string | number) => void) => {
  let formData = {
    taskName: data.taskName,
    superviser: data.superviserUuid,
    superviserRoleCode: data.superviserRoleCode,
    approver: data.approverUuid,
    approverRoleCode: data.approverRoleCode,
    forcastTime: data.forcastTime,
    isSys: data.isSys,
    contentData: JSON.parse(data.contentData as string) ?? {},
    preTask: data.preTask,
    taskType: data.taskType,
    id: data.id,
    milepostId: data.milepostId,
  }
  const { code } = await editTask(paramsWrapper(formData))
  if (code !== 200) return
  await refresh()
  await initToDoList()
  callback(formData.id)
  message.success('操作成功')
}
const refresh = async () => {
  await getProcessInfo((processInfo: IProcess[]) => {
    const currMilepost = processInfo.find(item => item.id === currtMilepost?.value?.id)
    currMilepost && setCurrtMilepost(currMilepost)
  })
}
// 操作方法
const milestoneOperate = {
  // 任务
  [EmitType.createTask]: (data: IProcess) => {
    taskModel.data = data
  },
  [EmitType.editTask]: (data: ITask) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.edit
  },
  [EmitType.revokeTask]: (data: ITask) => {
    rollingBack({ id: data.id }).then(async res => {
      if (res.code == 200) {
        message.success('回滚成功')
        await refresh()
        await initToDoList()
      } else {
        message.error(res.msg)
      }
    })
  },
  [EmitType.handleTask]: (data: Record<string, unknown>) => {
    submitMission(paramsWrapper(data)).then(async res => {
      if (res.code == 200) {
        message.success('提交成功')
        await refresh()
        await initToDoList()
      } else {
        message.error(res.msg)
      }
    })
  },
  [EmitType.saveTask]: (data: Record<string, unknown>) => {
    saveMission(paramsWrapper(data)).then(async res => {
      if (res.code == 200) {
        message.success('保存成功')
        await refresh()
        await initToDoList()
      } else {
        message.error(res.msg)
      }
    })
  },
  [EmitType.delTask]: (data: ITask) => {
    FModal.confirm({
      title: '确认删除当前子节点吗？',
      content: '删除后不可恢复，请谨慎操作！',
      okText: '确定',
      cancelText: '取消',
      icon: createVNode(
        'span',
        { class: 'icon iconfont iconicon_tishi', style: 'float: left;margin-right: 14px;color: #FA8F23;' },
        null
      ),
      onOk: async () => {
        await removeTask(paramsWrapper({ taskId: data.id }))
        message.success('删除成功')
        await refresh()
        await initToDoList()
      },
    })
  },
}
provide('refresh', refresh)
provide('quickEditHandleTaskSubmit', quickEditHandleTaskSubmit) // 设置当前激活的里程碑
provide('milestoneOperate', (key: keyof typeof milestoneOperate, data: any) => milestoneOperate[key](data)) // 新增弹框
</script>

<style lang="scss" scoped></style>

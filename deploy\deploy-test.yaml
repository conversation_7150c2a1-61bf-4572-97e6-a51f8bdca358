kind: Service
apiVersion: v1
metadata:
  labels:
    app: {APP_NAME}
  name: {APP_NAME}
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: {APP_NAME}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {APP_NAME}
  labels:
    app: {APP_NAME}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: {APP_NAME}
  template:
    metadata:
      labels:
        app: {APP_NAME}
    spec:
      containers:
        - name: {APP_NAME}
          image: {IMAGE_URL}:{IMAGE_TAG}
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu:     114m
              memory:  115Mi
            requests:
              cpu:     114m
              memory:  115Mi
          readinessProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 15
            periodSeconds: 20
          livenessProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 15
            periodSeconds: 20
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh","-c","sleep 25"]
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: harbor-secret
import { PageMessageTemplateParams, PageMessageTemplateRes, MessageTemplateParams } from '@/types/messageTemplate'
import { IRes, IResDataList } from '@/types/request'
import { request } from '@/utils'

export const pageMessageTemplate = async (data: PageMessageTemplateParams) => {
  const res = await request.post<IRes<IResDataList<PageMessageTemplateRes>>>('/api/notifyTemplate/getPage', data)
  return res as unknown as IRes<IResDataList<PageMessageTemplateRes>>
}

export const saveMessageTemplate = async (data: MessageTemplateParams) => {
  const res = await request.post<IRes>('/api/notifyTemplate/save', data)
  return res as unknown as IRes
}

export const updateMessageTemplate = async (data: MessageTemplateParams) => {
  const res = await request.post<IRes>('/api/notifyTemplate/update', data)
  return res as unknown as IRes
}

export const deleteMessageTemplate = async (id: string | number) => {
  const res = await request.get<IRes>(`/api/notifyTemplate/delById/${id}`)
  return res as unknown as IRes
}

export const synSendMsgMessageTemplate = async (params: MessageTemplateParams) => {
  const res = await request.get<IRes>('/api/message/synSendMsg', { params })
  return res as unknown as IRes
}

export const rollbackNotifyTemplate = async (data: any) => {
  const res = await request.post(`/api/processSyn/rollbackNotifyTemplate`, data)
  return res as unknown as IRes<any>
}

export const synNotifyTemplate = async (data: any) => {
  const res = await request.post(`/api/processSyn/synNotifyTemplate`, data)
  return res as unknown as IRes<any>
}

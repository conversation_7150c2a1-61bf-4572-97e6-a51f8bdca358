<template>
  <div class="message-list-container">
    <!-- 面包屑 -->
    <!-- <Breadcrumb :data="[i18n.t('首页'), i18n.t('产品停产下架')]" /> -->
    <div class="search-box">
      <SearchList :searchConfigLists="searchConfigLists" />
    </div>
    <div class="content-box">
      <TableBox :loading="tableLoading" :list="list" @onExportProcess="onExportProcess" />
      <div class="fei-su-pagination">
        <FPagination
          v-model:current="pageData.pageNum"
          v-model:pageSize="pageData.pageSize"
          :total="pageData.total"
          @change="onChangeFn"
          show-size-changer
          show-quick-jumper
          :show-total="() => `${i18n.t('共')} ${pageData.total} ${i18n.t('条')}`"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/views/pgb-data-board/components/Breadcrumb/index.vue'
import SearchList from '@/views/pgb-data-board/components/SearchBox/components/SearchList/index.vue'
import TableBox from './components/TableBox/index.vue'
import { ref, reactive } from 'vue'
import { getIpdProductLaunchList, exportProcess } from '@/api/productRemoveList'
import { ProductListParams, ProductItem } from '@/types/productRemoveList'
import { ISearchDataConfig } from '@/types/pgbDataBoard'
import { messageInstance } from '@fs/smart-design'

import { useI18n, deepClone } from '@/utils'
import { Search } from './components/searchConfig'
const i18n = useI18n()

const tableLoading = ref<boolean>(false)
const pageData = reactive<ProductListParams>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const searchData = ref<ProductListParams>({})
const list = ref<ProductItem[]>([])

const onGetSearchData = (data: any) => {
  searchData.value = data
  pageData.pageNum = 1
  getProcessList()
}

const onExportProcess = async () => {
  if (!pageData.total) {
    messageInstance.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const params = searchData.value
  const res = await exportProcess(params)
  if (res.code !== 200) throw new Error(res.msg)
  messageInstance.success(i18n.t('下载成功，请在飞书查看！'))
}

const onChangeFn = (current: number, pageSize: number) => {
  pageData.pageNum = current
  pageData.pageSize = pageSize
  getProcessList()
}

const getProcessList = async () => {
  try {
    tableLoading.value = true
    const params = deepClone(Object.assign({}, pageData, searchData.value)) // 拷贝一份参数
    delete params.total
    const res = await getIpdProductLaunchList(params)
    list.value = res?.data?.list || []
    pageData.total = res?.data?.totalCount || 0
  } finally {
    tableLoading.value = false
  }
}

const searchConfigLists = reactive<ISearchDataConfig>(new Search(onGetSearchData))
</script>

<style scoped lang="scss">
.message-list-container {
  .search-box {
    display: flex;
    justify-content: space-between;
    padding: 22px 24px 10px 24px;
    // margin-top: 24px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
    :deep(.fs-select-selection-placeholder) {
      color: #bbb !important;
    }
  }
  .content-box {
    margin-top: 16px;
    padding: 24px 24px 0;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
}
</style>

<template>
  <div class="dict-tree-node">
    <div
      class="node-content"
      :class="{
        'node-expanded': (node as any).dict_tree_expanded,
        'node-collapsed': !(node as any).dict_tree_expanded,
        'has-children': hasChildren,
        'node-selected': isSelected,
        [`node-status-${(node as any).status}`]: (node as any).status
      }"
      :style="{ paddingLeft: `${level * 20 + 12}px` }"
    >
      <!-- 左侧内容区域 -->
      <div class="node-left" @click="handleToggle">
        <!-- 拖拽手柄 - 保留占位空间，根据条件显示/隐藏图标 -->
        <div
          class="drag-handle"
          v-if="(node as any).dict_tree_type === 'dict_tree_category' || (node as any).dict_tree_type === 'dict_tree_item'"
        >
          <i class="icon iconfont icontubiao_tuodong1 lh22" v-show="canDrag"></i>
        </div>

        <!-- 展开/折叠图标 -->
        <div
          class="expand-icon"
          v-if="hasChildren || (level === 0 && (node as any).dict_tree_type === 'dict_tree_field')"
        >
          <!-- 加载状态 -->
          <LoadingOutlined v-if="loading" class="lh22 pointer ml8 loading-icon" />
          <!-- 展开/折叠图标 -->
          <i
            v-else
            class="icon iconfont lh22 pointer hover-btn-ccc"
            :class="(node as any).dict_tree_expanded ? 'iconjiantouxia1' : 'iconjiantouyou'"
          ></i>
        </div>
        <div class="expand-placeholder" v-else></div>

        <!-- 节点名称 -->
        <div class="node-name" :title="(node as any).dict_tree_name">
          {{ (node as any).dict_tree_name }}
        </div>
      </div>

      <!-- 操作按钮 - 支持插槽自定义 -->
      <div class="node-actions" @click.stop>
        <!-- 自定义操作按钮插槽 -->
        <slot name="actions" :node="node" :level="level" :index="index"> </slot>
      </div>
    </div>

    <!-- 子节点 -->
    <div class="node-children" v-if="hasChildren && (node as any).dict_tree_expanded">
      <draggable
        v-model="nodeChildren"
        :group="getDragGroup()"
        item-key="id"
        handle=".drag-handle"
        @start="handleDragStart"
        @change="handleChildDragChange"
        :animation="200"
        ghost-class="ghost-item"
        chosen-class="chosen-item"
        :disabled="!canChildrenDrag"
      >
        <template #item="{ element, index }">
          <DictTreeNode
            :node="element"
            :level="level + 1"
            :index="index"
            :sibling-count="(node as any).dict_tree_children?.length || 0"
            :accordion="accordion"
            :lazy-load="lazyLoad"
            :selected-node-id="selectedNodeId"
            :drag-api="dragApi"
            @toggle="handleChildToggle"
            @lazy-load="$emit('lazy-load', $event)"
            @node-select="(node, selected) => $emit('node-select', node, selected)"
          >
            <!-- 传递操作按钮插槽 -->
            <template #actions="slotProps">
              <slot name="actions" v-bind="slotProps" />
            </template>
          </DictTreeNode>
        </template>
      </draggable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, withDefaults, toRefs } from 'vue'
import draggable from 'vuedraggable'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { deepClone } from '@/utils'

// 定义组件属性
const props = withDefaults(
  defineProps<{
    node?: any
    level?: number
    index?: number
    siblingCount?: number
    accordion?: boolean
    lazyLoad?: ((params: any) => Promise<any>) | null
    selectedNodeId?: string
    dragApi?: ((params: any) => Promise<any>) | null
  }>(),
  {
    level: 0,
    index: 0,
    siblingCount: 1,
    accordion: false,
    lazyLoad: null,
    selectedNodeId: '',
    dragApi: null,
  }
)

const emit = defineEmits(['toggle', 'lazy-load', 'node-select', 'update:node'])
const loading = ref(false)
const { node } = toRefs(props)
const nodeChildren = computed({
  get: () => {
    return node.value.dict_tree_children || []
  },
  set: value => {
    node.value.dict_tree_children = value
  },
})
const hasChildren = computed(() => {
  return node.value.dict_tree_children && node.value.dict_tree_children.length > 0
})
const canDrag = computed(() => {
  if (props.level === 0) return false
  const nodeType = node.value.dict_tree_type
  const canDragByType = nodeType === 'dict_tree_category' || nodeType === 'dict_tree_item'
  return canDragByType && hasDraggableSiblings.value
})
const hasDraggableSiblings = computed(() => {
  return props.siblingCount > 1
})
const canChildrenDrag = computed(() => {
  if (props.level === 0) return true
  return props.level >= 1
})
const isSelected = computed(() => {
  const nodeId = node.value.dict_tree_id
  return props.selectedNodeId === nodeId
})

// 获取拖拽组
const getDragGroup = () => {
  return `dict-tree-level-${props.level + 1}-parent-${(node.value as any).dict_tree_id}`
}

// 处理展开/折叠
const handleToggle = async () => {
  const shouldLazyLoad = checkShouldLazyLoad()
  if (shouldLazyLoad && props.lazyLoad) {
    if (loading.value) {
      return
    }
    loading.value = true
    try {
      const result = await props.lazyLoad({
        node: props.node,
        level: props.level,
        hasChildren: hasChildren.value,
        isEmpty: !(props.node as any).dict_tree_children || (props.node as any).dict_tree_children.length === 0,
        index: props.index,
      })
      if (result && Array.isArray(result)) {
        ;(props.node as any).dict_tree_children = result
      }
      emit('lazy-load', props.node)
    } catch (error) {
      console.error('懒加载失败:', error)
    } finally {
      loading.value = false
    }
  }

  if (props.accordion) {
    emit('toggle', (props.node as any).dict_tree_id, true)
  } else {
    emit('toggle', (props.node as any).dict_tree_id, false)
  }

  emit('node-select', props.node, !isSelected.value)
}

// 检查是否应该进行懒加载
const checkShouldLazyLoad = () => {
  if (props.level !== 0 || (props.node as any).dict_tree_type !== 'dict_tree_field') return false
  if ((props.node as any).dict_tree_expanded) return false
  if ((props.node as any).dict_tree_children.length) return false

  return true
}

// 处理子节点的展开/折叠
const handleChildToggle = (nodeId: string, isAccordion: boolean) => {
  emit('toggle', nodeId, isAccordion)
}

// 保存拖拽前的原始状态
let originalChildrenState: any[] = []
// 拖拽开始时保存原始状态
const handleDragStart = () => {
  originalChildrenState = deepClone((props.node as any).dict_tree_children || [])
}

// 处理子节点拖拽变化
const handleChildDragChange = async (event: any) => {
  if (props.dragApi && event.moved) {
    try {
      const { element, newIndex, oldIndex } = event.moved
      const originalSiblings =
        originalChildrenState.length > 0 ? originalChildrenState : [...((props.node as any).dict_tree_children || [])]
      const currentSiblings = deepClone((props.node as any).dict_tree_children || [])

      const newSortOrder = currentSiblings.map((item, index) => ({
        id: (item as any).id,
        name: (item as any).dict_tree_name,
        sortIndex: index + 1,
        parentId: (props.node as any).dict_tree_id,
      }))

      const sortChanges = getSortChangesFromDrag(originalSiblings, newSortOrder)

      const dragParams = {
        draggedNode: element,
        targetParent: props.node,
        newIndex,
        oldIndex,
        level: props.level + 1,
        originalSiblings,
        currentSiblings,
        newSortOrder,
        sortChanges,
      }

      await props.dragApi(dragParams)
    } catch (error) {
      console.error('拖拽API调用失败:', error)
      node.value.dict_tree_children = originalChildrenState
      return
    }
  }
}

// 获取排序变化信息
const getSortChangesFromDrag = (originalSiblings: any[], newSortOrder: any[]) => {
  const changes = []

  newSortOrder.forEach((newItem, index) => {
    const originalIndex = originalSiblings.findIndex(item => (item as any).dict_tree_id === newItem.id)
    if (originalIndex !== index) {
      console.log('newItem :>> ', newItem)
      changes.push({
        id: newItem.id,
        name: newItem.name,
        oldIndex: originalIndex,
        newIndex: index,
        oldSortIndex: originalIndex + 1,
        newSortIndex: index + 1,
      })
    }
  })

  return changes
}
</script>

<style scoped lang="scss">
.dict-tree-node {
  .node-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    margin-bottom: 4px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;
    // border-bottom: 1px solid #f0f0f0;

    &:hover {
      background-color: #f1f4f8;

      .drag-handle .icon {
        opacity: 1;
      }

      .node-actions {
        opacity: 1;
      }
    }

    // 选中状态
    &.node-selected {
      background-color: #ebf3fd !important;
      // 去除左侧边框，改为背景色区分
    }

    // 禁用状态
    &.node-disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .drag-handle {
    position: absolute;
    left: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-right: 8px;

    .icon {
      cursor: move;
      color: #999;
      opacity: 0;
      transition: opacity 0.2s;

      &:hover {
        color: #666;
      }
    }
  }

  .expand-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    cursor: pointer;
    color: #666;

    &:hover {
      color: #1890ff;
    }

    .loading-icon {
      color: #1890ff;
      font-size: 14px;
    }
  }

  .expand-placeholder {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }

  .node-name {
    flex: 1;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
  }

  .node-left {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
  }

  .node-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;
    flex-shrink: 0;

    .f-button,
    .action-btn {
      padding: 4px;

      &:hover {
        background-color: #e6f7ff;
      }
    }
  }

  .node-checkbox {
    display: flex;
    align-items: center;
    margin-right: 8px;
  }
}

.ghost-item {
  opacity: 0.5;
  background-color: #f0f0f0;
}

.chosen-item {
  background-color: #e6f7ff;
  box-shadow: 0px 4px 12px 1px rgba(88, 98, 110, 0.14);
}

.ghost-item {
  opacity: 0.5;
}
</style>

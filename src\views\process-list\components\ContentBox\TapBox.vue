<template>
  <ul class="tap-box">
    <li
      v-for="(item, index) in props.list"
      :key="index"
      @click="handleTap(item)"
      :class="[{ active: modelValue === item.id }]"
    >
      <span class="label">{{ item.label }}</span>
      <span class="dot" v-if="item.value !== ''">{{ item.value }}</span>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'

const route = useRoute()
const cacheKey = computed(() => {
  if (route.name === 'ProcessList') return 'all'
  if (route.params?.type) return route.params?.type
  return 'noCache'
})

const store = useStore()
const modelValue = ref<number>(1)
const props = defineProps({
  value: {
    type: String,
    default: '新增',
  },
  disable: {
    type: <PERSON>olean,
    default: false,
  },
  list: {
    type: Object,
    default: null,
  },
})
// watch(
//   () => modelValue.value,
//   (newVal, oldVal) => {
//     emit('input', newVal)
//   }
// )
const emit = defineEmits(['change', 'input'])
const handleTap = (item: any) => {
  if (props.disable) return
  modelValue.value = item.id
  emit('change', item.id)
}

onBeforeMount(() => {
  const cache = store.getters['local/getLocalSearchData']
  if (cache !== undefined && cache !== null) {
    const localSearchData = cache[cacheKey.value as string] ?? {}
    ;(localSearchData.type || localSearchData.type === 0) && (modelValue.value = localSearchData.type)
  }
})
</script>

<style lang="scss" scoped>
.tap-box {
  display: flex;
  height: 49px;
  border-bottom: 1px solid #ececec;
  padding-left: 0;
  overflow: hidden;
  overflow-x: auto;
  & > li {
    padding: 16px 30px;
    height: 49px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .label {
      font-size: 14px;
      font-weight: bold;
      color: #666666;
      line-height: 12px;
      padding-right: 0;
    }

    .dot {
      display: inline-block;
      min-width: 16px;
      height: 16px;
      background: #dadada;
      border-radius: 8px;
      text-align: center;
      font-size: 12px;
      color: #6c6c6c;
      line-height: 16px;
      padding: 0px 4px;
      margin-left: 4px;
    }
  }

  & > .active {
    border-bottom: 1px solid #fff;
    border-top: 2px solid #378eef;
    border-right: 1px solid #ececec;
    border-left: 1px solid #ececec;
    .label {
      color: #378eef;
    }
    .dot {
      background-color: #378eef;
      font-size: 12px;
      font-weight: bold;
      color: #ffffff;
    }
  }
  & > .active:first-child {
    border-left: none;
  }
}
</style>

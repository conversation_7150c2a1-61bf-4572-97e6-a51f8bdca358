import { IRes, ISeeMessage, IMessage, IReceiver, IPageMessage, MessageListResponse } from '@/types/request'
import { request } from '@/utils'

export const getProcessMessage = async (data: string | number) => {
  const res = await request.get<IRes<IMessage[]>>('/api/messageCollaboration/getMessage?instanceId=' + data)
  return res as unknown as IRes<IMessage[]>
}

export interface ISendMessageParams {
  taskId?: number
  instanceId?: number
  milepostId?: number
  processType?: string
  projectNum?: string
  receiverReqs?: IReceiver[]
  content: string
  file?: File[]
}
export const sendMessage = (data: ISendMessageParams) => {
  return request.post('/api/messageCollaboration/sendMessage', data)
}

export const getUnreadMessage = async (params: any = undefined) => {
  const data = await request.get<IRes<ISeeMessage[]>>(`/api/messageCollaboration/getUnreadMessageById`, { params })
  return data as unknown as IRes<ISeeMessage[]>
}

export interface IUrgentMessage {
  uuids: string[]
  messageInfoId: number
  processName: string
}

export const urgentMessage = async (data: IUrgentMessage) => {
  const res = await request.post<IRes>('/api/message/urgentMessage', data)
  return res as unknown as IRes
}

export const pageMessage = async (data: IPageMessage) => {
  const res = await request.post<MessageListResponse>('/api/messageCollaboration/pageMessage', data)
  return res as unknown as MessageListResponse
}

export const importMessage = async (data: IPageMessage) => {
  const res = await request.post<any>('/api/messageCollaboration/importMessage', data)
  return res as any
}

export const batchWithdraw = async (messageId: number) => {
  const data = await request.get<IRes<IMessage>>(`/api/messageCollaboration/batchWithdraw/${messageId}`)
  return data as unknown as IRes<IMessage>
}

export const editMessage = async (messageId: number) => {
  const data = await request.get<IRes<IMessage>>(`/api/messageCollaboration/edit/${messageId}`)
  return data as unknown as IRes<IMessage>
}

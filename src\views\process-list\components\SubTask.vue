<template>
  <FModal
    class="fs-modal positionR"
    :width="1200"
    :visible="showModal"
    @change="handleVisible"
    :title="i18n.t('工作协同')"
  >
    <FTable class="fs-table" :columns="columns" :data-source="dataSource" :loading="loading" :pagination="false">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <div v-if="record.status === 0">{{ i18n.t('未开始') }}</div>
          <div v-else-if="record.status === 1">{{ i18n.t('进行中') }}</div>
          <div v-else-if="record.status === 2">{{ i18n.t('按时完成') }}</div>
          <div v-else-if="record.status === 3">{{ i18n.t('逾期完成') }}</div>
          <div v-else-if="record.status === 4">{{ i18n.t('待指派') }}</div>
        </template>
        <template v-if="column.key === 'prescription' && [2, 5].includes(record.status)">
          <div>
            <span v-if="record.status === 2" style="color: #00d0a4">
              <span class="iconfont fontS12">&#xe632;</span> {{ i18n.t('按时完成') }}
            </span>
            <span v-else-if="record.status === 5" style="color: #ff4a4a">
              <span class="iconfont fontS12">&#xe633;</span>
              {{ `${i18n.t('延期')} ${getOverTiem(record)} ${i18n.t('天')}` }}
            </span>
          </div>
        </template>
        <template v-if="['forcastTime', 'taskCompletedTime'].includes(column.key) && record[column.key]">
          <span>{{ transformDate(record[column.key], 'YYYY-MM-DD') }}</span>
        </template>
        <template v-if="column.key === 'attachmentData'">
          <DownloadFiles
            v-if="JSON.parse(record.attachmentData)?.length > 0"
            class="color1890ff cursor"
            :data="JSON.parse(record.attachmentData)"
            @download="download"
            @batch-download="batchDownload"
          />
          <div v-else>--</div>
        </template>
        <template v-if="column.key === 'actions'">
          <div>
            <p class="link" @click="linkUrl(record)">{{ i18n.t('查看') }}</p>
          </div>
        </template>
      </template>
    </FTable>
    <template #footer></template>
  </FModal>
</template>

<script setup lang="ts">
import DownloadFiles from '../../process-detail/components/DownloadFiles/index.vue'
import { computed, ref, watch } from 'vue'
import { GetCooperateList } from '@/api/process'
import { useRouter } from 'vue-router'
import { useI18n, download, batchDownload, transformDate, jumpToDemand } from '@/utils'

const i18n = useI18n()
const router = useRouter()
const props = defineProps({
  taskModalShow: { type: Boolean, default: false },
  processesUuid: { type: Number, default: null },
  processesName: { type: String, default: null },
  id: { type: Number, default: null },
  processConfigId: { type: Number, default: null },
})
const dataSource = ref<any>([])
const showModal = ref<boolean>(false)
const loading = ref<boolean>(false)
const columns = computed(() => [
  { title: i18n.t('工作协同'), dataIndex: 'taskName', key: 'taskName' },
  { title: i18n.t('时效'), key: 'prescription' },
  { title: i18n.t('状态'), key: 'status' },
  { title: i18n.t('协同人'), dataIndex: 'superviser', key: 'superviser' },
  { title: i18n.t('预计完成时间'), dataIndex: 'forcastTime', key: 'forcastTime' },
  { title: i18n.t('实际完成时间'), dataIndex: 'taskCompletedTime', key: 'taskCompletedTime' },
  { title: i18n.t('附件'), key: 'attachmentData' },
  { title: i18n.t('操作'), key: 'actions' },
])
watch(
  () => props.taskModalShow,
  () => {
    showModal.value = props.taskModalShow
    if (props.taskModalShow) {
      fetchData(props.processesUuid)
    } else {
      dataSource.value = []
    }
  }
)

const emit = defineEmits(['input'])
const getOverTiem = (item: any) => {
  const startTime: any = new Date(item.taskStartTime).getTime()
  const endTime: any = new Date(item.taskCompletedTime).getTime()
  const durationTime = item.duration * 24 * 60 * 60 * 1000
  const res = Number(((endTime - startTime - durationTime) / 1000 / 24 / 60 / 60).toFixed(1))
  return res
}
const handleVisible = (visible: boolean) => {
  emit('input', visible)
}
const linkUrl = (e: any) => {
  jumpToDemand(props.id, props.processConfigId, false, router)
}
const fetchData = async (milepostInfoId: number) => {
  loading.value = true
  const query = { milepostInfoId: milepostInfoId }
  const res = await GetCooperateList(query)
  try {
    if (res.code !== 200) return
    dataSource.value = res.data
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.modal-content {
  text-align: center;
  line-height: 1;
  height: 60px;
  padding: 10px 0;
  .modal-content-text {
    font-size: 16px;
  }
  .modal-content-tips {
    font-size: 12px;
    color: #f88d49;
  }
}
.link {
  cursor: pointer;
  color: #40a9ff;
}
:deep(.fs-table-cell) {
  &:empty {
    &::after {
      content: '--';
    }
  }
}
</style>

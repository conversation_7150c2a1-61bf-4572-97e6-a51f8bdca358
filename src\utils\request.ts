import store from '@/store'
import createRequest from '@fs/request'
import { message } from '@fs/smart-design'
import { BAESURL, getLanguage, getToken, getUserInfo, logout } from '.'

export const request = createRequest({ baseURL: BAESURL, timeout: 60 * 1000 }, { message })
request.interceptors.request.use(async config => {
  // await store.dispatch('user/checkToken')
  const token = getToken()
  const userInfo = getUserInfo()
  const language = getLanguage()
  const stateToken = store.getters['user/getStateToken']

  if (token && userInfo) {
    config?.headers?.set('token', token.startsWith('Bearer') ? token : `Bearer ${token}`)
    config?.headers?.set('admin-id', `${userInfo?.adminId}`)
    config?.headers?.set('admin-name', `${userInfo?.adminName}`)
  }
  stateToken && config?.headers?.set('stats-token', `${stateToken}`)
  language && config?.headers?.set('Accept-Language', `${language}`)
  return config
})

request.interceptors.response.use(
  response => response.data,
  error => {
    // code error 的标识
    if (error?.isCodeError) {
      const { code } = error
      switch (code) {
        case 401:
        case 1002:
        case 1004:
        case 1009:
          message.error(`登录失效: ${error?.message}`)
          logout()
          break
        default:
      }
    }
    return Promise.reject(error)
  }
)

export const fileRequest = createRequest.create({ baseURL: BAESURL, timeout: 600000 })
fileRequest.interceptors.request.use(async config => {
  const token = getToken()
  const userInfo = getUserInfo()
  const language = getLanguage()
  const stateToken = store.getters['user/getStateToken']
  if (token && userInfo) {
    config?.headers?.set('token', `${token}`)
    config?.headers?.set('admin-id', `${userInfo?.adminId}`)
    config?.headers?.set('admin-name', `${userInfo?.adminName}`)
  }
  language && config?.headers?.set('Accept-Language', `${language}`)
  stateToken && config?.headers?.set('stats-token', `${stateToken}`)
  return config
})

<template>
  <!-- 消息协同浮动按钮 -->
  <div class="message-box" v-show="!messageFlag">
    <div class="pointer" :title="i18n.t('展开消息协同')">
      <div class="message-box-hideen" @click="messageFlag = true">
        <span class="iconfont">&#xe68f;</span>
        <span class="num" v-if="unreadMsg.length">{{ unreadMsg.length }}</span>
      </div>
    </div>
  </div>
  <FDrawer
    v-model:visible="messageFlag"
    :title="i18n.t('消息协同')"
    @close="initUnreadMessage"
    :body-style="{ padding: 0 }"
  >
    <!-- <ChatRoom
      :process-no="processNo"
      :instance-id="processId"
      :process-name="processName"
      :params-wrapper="paramsWrapper"
    /> -->
    <Message
      :process-no="(processNo as string)"
      :process-name="(processName as string)"
      :instance-id="(processId as number)"
      :params-wrapper="paramsWrapper"
    >
      <template #header>
        <div class="message-header-box">
          <p class="title">
            <i class="icon" />
            <span>
              <FTooltip placement="top">
                <template #title>
                  <span class="color666">{{ `${processName}：` }}</span>
                  <span class="color333">{{ processNo }}</span>
                </template>
                <span class="color666">{{ `${processName}：` }}</span>
                <span class="color333">{{ processNo }}</span>
              </FTooltip>
            </span>
          </p>
        </div>
      </template>
    </Message>
  </FDrawer>
</template>

<script setup lang="ts">
import { ref, inject, onMounted } from 'vue'
import Message from '@/components/Message/index.vue'
import ChatRoom from '@/components/ChatRoom/index.vue'
import { useI18n } from '@/utils'
import { ISeeMessage } from '@/types/request'
import { getUnreadMessage } from '@/api'

const processId = inject<number>('processId') // 流程 id
const processNo = inject<string>('processNo') // 流程编号
const processName = inject<string>('processName') // 流程名称
const paramsWrapper = inject<any>('paramsWrapper') // 参数包装函数
const i18n = useI18n()
const messageFlag = ref<boolean>(false)
const unreadMsg = ref<ISeeMessage[]>([])
// 初始化未读信息
const initUnreadMessage = async () => {
  const { data = [] } = await getUnreadMessage({ instanceId: processId })
  unreadMsg.value = data
}
onMounted(() => {
  initUnreadMessage()
})
</script>

<style scoped lang="scss">
.message-header-box {
  padding: 16px 4px;
  margin: 0px 20px 0;
  border-bottom: 1px solid #eeeeee;
  display: flex;
  align-items: center;
  .title {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;
    i {
      display: inline-block;
      width: 14px;
      height: 12px;
      background: url('~@/assets/images/forwarder_detial_title01.png') repeat no-repeat;
      background-size: 100% 100%;
      margin-right: 5px;
      flex-shrink: 0;
    }
    span {
      flex: 1;
      font-size: 12px;
      color: #999999;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.message-box {
  position: fixed;
  right: 20px;
  bottom: 100px;
  z-index: 1000;
  .message-box-hideen {
    margin-left: 24px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    background-color: #4699ff;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 6px 24px 0px rgba(55, 120, 206, 0.34);

    .iconfont {
      font-size: 27px;
      color: #fff;
    }
    .num {
      background-color: #ff4a4a;
      min-width: 16px;
      line-height: 16px;
      color: #fff;
      border-radius: 8px;
      padding: 0;
      color: #fff;
      position: absolute;
      top: -4px;
      left: 54px;
      text-align: center;
      font-size: 12px;
    }
  }
}
</style>

<template>
  <div class="milepost-tag warning">
    <slot>
      <div class="time-box">
        <span class="title">{{ (danger && i18n.t('剩余倒计时')) || i18n.t('延期时间') }}:</span>
        <span class="time">{{ day }}</span>
        <span>{{ i18n.t('天') }}</span>
        <span class="time">{{ hour }}</span>
        <span>{{ i18n.t('时') }}</span>
        <span class="time">{{ minute }}</span>
        <span>{{ i18n.t('分') }}</span>
        <span class="time">{{ second }}</span>
        <span>{{ i18n.t('秒') }}</span>
      </div>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from '@/utils'
import dayjs, { Dayjs } from 'dayjs'
import { ref, watch, onBeforeUnmount, onMounted } from 'vue'
interface IProps {
  time: Dayjs
}

const i18n = useI18n()
const props = defineProps<IProps>()
const timer = ref<any>(null)
const danger = ref<boolean>(true)
const day = ref<number>()
const hour = ref<number>()
const minute = ref<number>()
const second = ref<number>()

const countdownFn = () => {
  danger.value = dayjs().isBefore(dayjs(props.time))
  const duration = (danger.value && dayjs(props.time).diff(dayjs())) || dayjs().diff(dayjs(props.time))
  day.value = Math.floor(duration / (24 * 60 * 60 * 1000))
  hour.value = Math.floor((duration % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
  minute.value = Math.floor((duration % (60 * 60 * 1000)) / (60 * 1000))
  second.value = Math.floor((duration % (60 * 1000)) / 1000)
  timer.value = setTimeout(countdownFn, 1000)
}

const hanldeVisiblityChange = () => {
  if (document.visibilityState === 'hidden') {
    clearTimeout(timer.value)
  } else if (document.visibilityState === 'visible') {
    countdownFn()
  }
}

watch(
  () => props.time,
  val => {
    clearTimeout(timer.value)
    val && countdownFn()
  },
  { deep: true, immediate: true }
)

onMounted(() => {
  window.addEventListener('visibilitychange', hanldeVisiblityChange)
})

onBeforeUnmount(() => {
  clearTimeout(timer.value)
  window.removeEventListener('visibilitychange', hanldeVisiblityChange)
})
</script>

<style scoped lang="scss">
.milepost-tag {
  display: inline-block;
  line-height: 24px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 3px;
  transition: color 0.5s, background-color 0.5s;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.warning {
    .time-box {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .title {
        display: inline-block;
        margin-right: 4px;
        color: #999999;
      }
      .time {
        display: inline-block;
        padding: 0 6px;
        margin: 0 4px;
        background: #fef4e9;
        border-radius: 3px;
        color: #fa8f23;
        line-height: 18px;
      }
    }
  }
}
</style>

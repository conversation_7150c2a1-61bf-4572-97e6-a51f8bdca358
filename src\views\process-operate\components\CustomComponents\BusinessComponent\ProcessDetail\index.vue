<template>
  <div class="list-item">
    <span v-if="value?.instanceId" class="code-link marginR4" @click="onJumpDemandDetial(value)">{{
      (componentConfig?.componentAttrs?.showValueKey &&
        componentConfig?.currtRecord?.[componentConfig?.componentAttrs?.showValueKey]) ||
      value?.topicName
    }}</span>
    <span v-else>--</span>
    <FTooltip
      v-if="componentConfig?.componentAttrs?.hasTooltip && value?.instanceId"
      color="#fff"
      :getPopupContainer="target"
      overlayClassName="cust-detail-relate-process-tooltip"
      @visibleChange="open => handleOpenChange(open, value)"
    >
      <template #title>
        <div class="content-box">
          <div class="tip-header">流程进度</div>
          <div class="info">
            <FSpin :spinning="!!value?.loading">
              <div class="process-list">
                <ExpandableTree :items="value?.processData ?? []">
                  <template #label="{ item }">
                    <div class="flex space-between process-item">
                      <div
                        class="flex left"
                        :title="`${item?.topicName ?? item?.taskName ?? '--'}/${item?.superviser ?? '--'}`"
                      >
                        <template v-if="item?.taskName">
                          <img
                            v-if="item?.taskName && [2, 3, 5].includes(item?.status)"
                            src="@/assets/images/process-icon/completed.svg"
                            class="mr8"
                          />
                          <img
                            v-else-if="item?.taskName && [1, 6].includes(item?.status)"
                            src="@/assets/images/process-icon/inProgress.svg"
                            class="mr8"
                          />
                          <img
                            v-else-if="item?.taskName && [null, 0, 4].includes(item?.status)"
                            src="@/assets/images/process-icon/notStarted.svg"
                            class="mr8"
                          />
                        </template>
                        <template v-else>
                          <img
                            v-if="[3, 4].includes(item?.status)"
                            src="@/assets/images/process-icon/completed.svg"
                            class="mr8"
                          />
                          <img
                            v-else-if="[1, 2, 5].includes(item?.status)"
                            src="@/assets/images/process-icon/inProgress.svg"
                            class="mr8"
                          />
                          <img
                            v-else-if="[0].includes(item?.status)"
                            src="@/assets/images/process-icon/notStarted.svg"
                            class="mr8"
                          />
                        </template>
                        <span class="text-name">{{ item?.topicName ?? item?.taskName ?? '--' }}</span>
                        <span class="text-nowrap">/{{ item?.superviser ?? '--' }}</span>
                      </div>
                      <span class="color999 text-nowrap" v-if="item?.taskName">{{
                        (item?.taskCompletedTime && transformDate(item?.taskCompletedTime, 'YYYY-MM-DD HH:mm:ss')) ??
                        '--'
                      }}</span>
                      <span class="color999 text-nowrap" v-else>{{
                        (item?.completeTime && transformDate(item?.completeTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--'
                      }}</span>
                    </div>
                  </template>
                  <template #default="{ item }">
                    <div class="flex space-between process-item">
                      <div
                        class="flex left"
                        :title="`${item?.topicName ?? item?.taskName ?? '--'}/${item?.superviser}`"
                      >
                        <img
                          v-if="[2, 3, 5].includes(item?.status)"
                          src="@/assets/images/process-icon/completed.svg"
                          class="mr8"
                        />
                        <img
                          v-else-if="[1, 6].includes(item?.status)"
                          src="@/assets/images/process-icon/inProgress.svg"
                          class="mr8"
                        />
                        <img
                          v-else-if="[null, 0, 4].includes(item?.status)"
                          src="@/assets/images/process-icon/notStarted.svg"
                          class="mr8"
                        />
                        <span class="text-name">{{ item?.taskName ?? '--' }}</span>
                        <span class="text-nowrap">/{{ item?.superviser ?? '--' }}</span>
                      </div>
                      <span class="color999 text-nowrap">{{
                        (item?.taskCompletedTime && transformDate(item?.taskCompletedTime, 'YYYY-MM-DD HH:mm:ss')) ??
                        '--'
                      }}</span>
                    </div>
                  </template>
                </ExpandableTree>
              </div>
            </FSpin>
          </div>
        </div>
      </template>
      <i class="iconfont icontubiao_tishi colorBBB" />
    </FTooltip>
  </div>
</template>

<script setup lang="ts">
import { FSpin } from '@fs/smart-design'
import { ref, computed } from 'vue'
import { getIpdSubProcessDetails } from '@/api'
import dayjs from 'dayjs'
import { transformDate, jumpToDemand } from '@/utils'
import ExpandableTree from '@/views/process-ipd-list/components/ExpandableTree/index.vue'
import { handleEmptyChildren } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'
import { useRouter } from 'vue-router'

interface IProps {
  value: any
  componentConfig?: any
}

const router = useRouter()
const props = defineProps<IProps>()
const emits = defineEmits(['update:value'])
const value = computed({
  get: () => props?.value ?? {},
  set: val => emits('update:value', val),
})
const target = ref(() => document.querySelector('#container'))

const getProcessData = async item => {
  if (
    item?.loading ||
    item?.pending ||
    (item?.initStartTime && dayjs().diff(dayjs(item.initStartTime), 'seconds') <= 10)
  )
    return
  try {
    item.loading = true
    item.pending = true
    item['processData'] = []
    const { data = [] } = await getIpdSubProcessDetails(item.instanceId)
    item['processData'] = handleEmptyChildren(data)
    item.initStartTime = new Date().getTime()
  } finally {
    item.loading = false
    item.pending = false
  }
}

const handleOpenChange = (open: any, item: any) => {
  open && getProcessData(item)
}

// 需求详情跳转
const onJumpDemandDetial = (record: any) => {
  jumpToDemand(record.instanceId, undefined, true, router)
}
</script>

<style lang="scss">
.cust-detail-relate-process-tooltip {
  max-width: 400px;
  width: 400px;
  .fs-tooltip-inner {
    padding: 0;
    .content-box {
      color: #333333;
      .tip-header {
        padding: 12px 16px;
        border-bottom: 1px solid #eeeeee;
        font-weight: 500;
        font-size: 14px;
      }
      .info {
        padding: 16px;
        min-height: 100px;
        max-height: 400px;
        overflow-y: scroll;
        .left {
          max-width: 66%;
          flex: auto;
        }
        .text-name {
          max-width: 145px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .text-nowrap {
          white-space: nowrap;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.mt8 {
  margin-top: 8px;
}
.mr8 {
  margin-right: 8px;
}
.code-link {
  cursor: pointer;
  color: #378eef;
}
.flex {
  display: flex;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.process-item + .process-item {
  margin-top: 8px;
}
.list-item {
  .icontubiao_tishi {
    font-size: 14px;
    line-height: 16px;
  }
}
</style>

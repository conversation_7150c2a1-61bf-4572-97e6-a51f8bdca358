<template>
  <div class="data-board-search-container">
    <div class="card-search-container">
      <FCascader
        dropdownClassName="country-cust-cascader"
        :placeholder="i18n.t('请选择')"
        :allowClear="false"
        style="width: 168px"
        :field-names="{ label: 'regionName', value: 'regionId', children: 'localizationRegions' }"
        :options="props.countryList"
        showCheckedStrategy="SHOW_CHILD"
        v-model:value="form.country"
        :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
      ></FCascader>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, toRaw, watch } from 'vue'
import { useStore } from 'vuex'
import type { LocalizationRegion } from '@/types/localizationSystem/dataBoard'
import { getDefaultValue } from '@/views/localizationSystem/lib/utils'
import { useI18n } from '@/utils'
const i18n = useI18n()

type TCountry = {
  name: string
  id: number
}

type propsType = {
  countryList: LocalizationRegion[]
}

const props = withDefaults(defineProps<propsType>(), {
  countryList: () => [],
})

type IpForm = {
  country: number[] | undefined
}

const store = useStore()
const form = reactive<IpForm>({
  country: undefined,
})

const emit = defineEmits(['onSearch'])

watch(
  () => props.countryList,
  val => {
    if (val.length) {
      if (store.getters['local/getLocalCountryId']) {
        form.country = store.getters['local/getLocalCountryId']
      } else {
        const keyData = getDefaultValue(val, {}, 'localizationRegions', '', '新加坡区域', 'regionId', 'regionName')
        keyData['新加坡区域'] && (form.country = keyData['新加坡区域'])
      }
    }
  },
  { deep: true }
)

watch(
  () => form,
  val => {
    let defaultName = val.country && val.country[val.country.length - 1]
    let nameData = getDefaultValue(
      props.countryList,
      {},
      'localizationRegions',
      '',
      defaultName,
      'regionName',
      'regionId'
    )
    val && emit('onSearch', { ...toRaw(form), ...{ name: nameData[defaultName as number] || '' } })
    val.country && store.commit('local/SET_LOCALCOUNTRYID', val.country)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.data-board-search-container {
  :deep(.country-cust-cascader) {
    padding-right: 0 !important;
    .fs-cascader-menu {
      font-weight: 500;
      border-right-color: #eee;
      &:last-child {
        border-right: none;
      }
      .fs-cascader-menu-item {
        margin-top: 2px;
        &[aria-checked='true'] {
          background: #ebf3fd;
          border-radius: 3px;
        }
      }
      .fs-cascader-menu-item-active[aria-checked='true'] {
        background: #ebf3fd;
        border-radius: 3px;
      }
    }
  }
}
</style>

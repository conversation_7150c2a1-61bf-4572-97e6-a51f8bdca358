import { IndexedDBCache } from './cache'
import { BaseDevice } from './device'

export interface TFText {
  type: 'text' | 'attr'
  source: string
  translation?: string
  attrName?: string
}

export interface TranslatorOptions {
  root?: HTMLElement
  fromLang?: string
  toLang?: string
  cache?: IndexedDBCache
  device: BaseDevice
  htmlBlackList?: string[]
  attrWhiteList?: string[]
  throttleDelay?: number
  maxChunkLength?: number
}

export interface TranslationStats {
  totalNodes: number
  cachedNodes: number
  failedNodes: number
}

export class Translator {
  private root: HTMLElement
  private cache: IndexedDBCache
  private device: BaseDevice
  private observer!: MutationObserver
  private walker!: TreeWalker

  private _fromLang: string
  private _toLang: string

  private throttleDelay = 300
  private throttleTimer?: ReturnType<typeof setTimeout>
  private maxChunkLength = 1000
  private isObserving = false
  private htmlBlackList: string[] = ['script', 'style', 'pre', 'code', '.no-translate']
  private attrWhiteList: string[] = ['title', 'placeholder', 'alt']
  private isCollecting = false
  private isTranslating = false
  private textCache = new Map<string, string>()
  private MAX_FAILURE_COUNT = 2
  private processedIframes = new Set<string>()

  private readonly observerConfig: MutationObserverInit = {
    subtree: true,
    attributes: true,
    childList: true,
    characterData: true,
    attributeFilter: this.attrWhiteList
  }

  constructor(options: TranslatorOptions) {
    this.root = options.root || document.body
    this.device = options.device
    this.cache = options.cache || new IndexedDBCache()
    this._fromLang = options.fromLang || 'auto'
    this._toLang = options.toLang || 'zh'

    options.throttleDelay && (this.throttleDelay = options.throttleDelay)
    options.htmlBlackList && (this.htmlBlackList = options.htmlBlackList)
    options.attrWhiteList && (this.attrWhiteList = options.attrWhiteList)
    options.maxChunkLength && (this.maxChunkLength = options.maxChunkLength)
  }

  // Getters for readonly properties
  public get fromLang(): string {
    return this._fromLang
  }

  public get toLang(): string {
    return this._toLang
  }

  /**
   * 初始化翻译器
   */
  public async setup(): Promise<void> {
    if (this.isObserving) return

    try {
      await this.cache.waitForInitialization()
      this.initializeComponents()
      this.executeCollect()
    } catch (error) {
      console.error('[Translator] Setup failed:', error)
      this.initializeComponents()
      this.executeCollect()
    }
  }

  /**
   * 停止翻译并还原
   */
  public stop(): void {
    if (!this.isObserving) return

    if (this.observer) {
      this.observer.disconnect()
      this.isObserving = false
    }
    if (this.throttleTimer) {
      clearTimeout(this.throttleTimer)
      this.throttleTimer = undefined
    }
    this.restore()
  }

  /**
   * 还原成中文
   */
  public restore(): void {
    if (this.observer) {
      this.observer.disconnect()
      this.isObserving = false
    }
    if (this.throttleTimer) {
      clearTimeout(this.throttleTimer)
      this.throttleTimer = undefined
    }

    // 重置所有状态
    this._toLang = 'auto'
    this.textCache.clear()
    this.processedIframes.clear()

    this.isCollecting = false
    this.isTranslating = false

    // 清除失败标记
    const failedNodes = document.querySelectorAll('[data-translate-failed]')
    failedNodes.forEach(node => {
      node.removeAttribute('data-translate-failed')
      node.removeAttribute('data-translate-fail-count')
    })

    // 清除成功标记
    const successNodes = document.querySelectorAll('[data-translate-success]')
    successNodes.forEach(node => {
      node.removeAttribute('data-translate-success')
    })

    const nodes = document.querySelectorAll('[data-original-type]')
    nodes.forEach(node => {
      const originalText = node.getAttribute('data-original-text')
      const originalType = node.getAttribute('data-original-type')
      const originalTranslateText = node?.getAttribute?.('data-original-translate-text')

      if (originalType === 'text') {
        for (const child of Array.from(node.childNodes)) {
          if (child.nodeType === Node.TEXT_NODE && child?.textContent === originalTranslateText) {
            child.textContent = originalText
          }
        }
      } else if (originalType === 'attr') {
        const element = node as Element
        this.attrWhiteList.forEach(attr => {
          if (element.hasAttribute(attr)) {
            element.setAttribute(attr, originalText as string)
          }
        })
      }

      node.removeAttribute('data-original-text')
      node.removeAttribute('data-original-translate-text')
      node.removeAttribute('data-original-type')
    })
  }

  /**
   * 设置目标语言
   */
  public setToLang(lang: string) {
    if (this._toLang === lang) return
    this.restore()
    this._toLang = lang

    // 如果没有初始化，则进行初始化
    if (!this.isObserving) {
      this.setup()
    }
  }

  /**
   * 清除缓存
   */
  public async clearCache(): Promise<void> {
    await this.cache.clear(this.toLang)
    this.textCache.clear()
    this.processedIframes.clear()
    // 清除失败标记
    const failedNodes = document.querySelectorAll('[data-translate-failed]')
    failedNodes.forEach(node => {
      node.removeAttribute('data-translate-failed')
      node.removeAttribute('data-translate-fail-count')
    })
  }

  // 私有方法
  private initializeComponents() {
    this.walker = this.initWalker(this.root)
    this.observer = this.initObserver()
    this.isObserving = true
  }

  private initObserver() {
    const observer = new MutationObserver(() => {
      if (this.throttleTimer) clearTimeout(this.throttleTimer)
      this.throttleTimer = setTimeout(() => {
        if (!this.isCollecting) {
          this.executeCollect()
        }
      }, this.throttleDelay)
    })

    observer.observe(this.root, this.observerConfig)
    return observer
  }

  private executeCollect() {
    if (!this.isObserving || this.isCollecting) return

    try {
      this.isCollecting = true
      const nodes = new Map<Node, TFText[]>()
      this.walker.currentNode = this.root
      let currNode = this.walker.nextNode()


      while (currNode) {
        this.processNode(currNode, nodes)
        currNode = this.walker.nextNode()
      }

      if (nodes.size > 0) {
        this.executeTranslate(nodes)
      }
    } catch (error) {
      console.error('[Translator] Collection error:', error)
      this.recoverFromError(error)
    } finally {
      this.isCollecting = false
    }
  }

  private async executeTranslate(nodes: Map<Node, TFText[]>) {
    if (!this.isObserving || this.isTranslating) return

    try {
      this.isTranslating = true
      const allTexts = Array.from(nodes.values()).flatMap(texts => texts.map(text => text.source))
      const { cacheTexts, uncacheTexts } = this.splitCacheTexts(allTexts)

      if (cacheTexts?.length) {
        this.applyTranslations(nodes, new Map(cacheTexts.map(t => [t.source, t])))
      }

      if (uncacheTexts?.length) {
        const waitTranslations: Partial<TFText>[] = []
        const chunks = this.chunkTexts(uncacheTexts)

        for (const chunk of chunks) {
          try {
            const translations = await this.device.translate(chunk, this.toLang)
            translations.forEach((translation) => {
              const translationResult = {
                type: 'text' as const,
                ...translation
              }
              waitTranslations.push(translationResult)
              this.cache.set(translation.source as string, translationResult as TFText, this.toLang)
            })
            if (waitTranslations?.length) {
              const translationsMap = new Map(waitTranslations.map(t => [t.source, t]))
              this.applyTranslations(nodes, translationsMap as Map<string, TFText>)
            }
          } catch (error) {
            console.error('[Translator] Chunk translation failed:', error)
            chunk.forEach(text => {
              const node = Array.from(nodes.entries()).find(([, texts]) =>
                texts.some(t => t.source === text)
              )?.[0]
              if (node) {
                this.markNodeAsFailed(node)
              }
            })
          }
        }
      }
    } finally {
      this.isTranslating = false
    }
  }

  private markNodeAsFailed(node: Node): void {
    if (!node) return

    const targetElement = node.nodeType === Node.TEXT_NODE ? node.parentElement : (node as Element)
    if (!targetElement || !(targetElement instanceof Element)) return

    const currentCount =
      parseInt(targetElement.getAttribute('data-translate-fail-count') || '0') + 1
    targetElement.setAttribute('data-translate-fail-count', currentCount.toString())

    if (currentCount >= this.MAX_FAILURE_COUNT) {
      targetElement.setAttribute('data-translate-failed', 'true')
      console.warn(`[Translator] Node failed ${currentCount} times, marking as failed`)
    }
  }

  // private resetNodeAttributes(node: Node): void {
  //   if (!node || !(node instanceof Element)) return
  //   node.removeAttribute('data-translate-failed')
  //   node.removeAttribute('data-translate-fail-count')
  //   node.removeAttribute('data-translate-success')
  //   node.removeAttribute('data-original-text')
  //   node.removeAttribute('data-original-translate-text')
  //   node.removeAttribute('data-original-type')
  // }

  private isNotRichTextIframe(iframe: HTMLIFrameElement): boolean {
    try {
      const doc = iframe.contentDocument
      if (!doc || !doc.body) return true
      // 判断是否开启了 designMode
      if (doc.designMode === 'on') return false
  
      // 判断 body 或其子元素是否设置了 contenteditable
      if (doc.body.isContentEditable || doc.querySelector('[contenteditable="true"]')) return false
  
      // 判断是否使用 srcdoc，且内容中包含富文本结构
      if (iframe.hasAttribute('srcdoc')) {
        const srcdoc = iframe.getAttribute('srcdoc') || ''
        if (/contenteditable|ql-editor|tox|cke|tinymce/.test(srcdoc)) return false
      }
    } catch (e) {
      // 跨域 iframe（无法访问内容）默认返回 true
      console.warn('无法检测 iframe（可能是跨域）', e)
      return true
    }
    return true
  }

  private isElementInRichTextIframe(el: Node): boolean {
    let doc = el.ownerDocument
  
    while (doc?.defaultView?.frameElement) {
      const iframe = doc.defaultView.frameElement
      if (!this.isNotRichTextIframe(iframe as HTMLIFrameElement)) {
        return false
      }
      doc = iframe.ownerDocument
    }
    return true
  }

  private checkParentHasQlEditor(el: Node): boolean {
    if ((el as Element).closest('.ql-container')) return true
    return false
  }

  private isNodeFailed(node: Node): boolean {
    if (!node || node.nodeType !== Node.ELEMENT_NODE) return true

    if (!this.isElementInRichTextIframe(node) ) return true

    if (this.checkParentHasQlEditor(node)) return true

    if(!this.hasOwnDisplayText(node)) return true

    const nodeText = this.getNodeText(node)
    const originalText = (node as Element)?.getAttribute?.('data-original-text')
    const originalTranslateText = (node as Element)?.getAttribute?.('data-original-translate-text')
    if (this.isChinese(nodeText) && this.isChinese(originalText) && nodeText !== originalText && originalTranslateText === nodeText) {
      return true
    }

    for (let index = 0; index < this.attrWhiteList.length; index++) {
      const attr = this.attrWhiteList[index];
      const value = (node as Element).getAttribute(attr)
      if (this.isChinese(value) && this.isChinese(originalText) && value !== originalText && originalTranslateText === value) {
        return true
      }
    }
    
    if ((node as Element).getAttribute('data-translate-failed') === 'true') {
      return true
    }
    // this.resetNodeAttributes(node)
    return false
  }

  private processNode(node: Node, nodes: Map<Node, TFText[]>) {
    if (node.nodeName === 'IFRAME') {
      this.processIframe(node as HTMLIFrameElement, nodes)
      return
    }

    if(this.isNodeFailed(node)) return

    if (node.nodeType === Node.ELEMENT_NODE) {
      if (this.htmlBlackList.includes(node.nodeName)) return

      const element = node as Element
      const texts: TFText[] = []
      this.attrWhiteList.forEach(attr => {
        const value = element.getAttribute(attr)
        if (value && this.isChinese(value)) {
          texts.push({ type: 'attr', source: value.trim(), attrName: attr })
        }
      })

      // 1. 检查纯文本子节点（不含子元素的文字）
      for (const child of Array.from(element.childNodes)) {
        if (child.nodeType === Node.TEXT_NODE && child.textContent &&this.isChinese(child.textContent)) {
          texts.push({ type: 'text', source: child.textContent })
        }
      }

      if (texts.length) {
        nodes.set(element, texts)
      }
      return
    }

  }

  private isIframeVisible(iframe: HTMLIFrameElement): boolean {
    const rect = iframe.getBoundingClientRect()
    const style = window.getComputedStyle(iframe)

    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0'
    )
  }

  private getIframeIdentifier(iframe: HTMLIFrameElement): string {
    // 优先使用src，如果没有则使用srcdoc的hash，最后使用元素的位置信息
    if (iframe.src && iframe.src !== 'about:blank') {
      return iframe.src
    }

    if (iframe.srcdoc) {
      // 使用srcdoc内容的简单hash作为标识
      return `srcdoc:${iframe.srcdoc.length}:${iframe.srcdoc.substring(0, 100)}`
    }

    // 使用iframe在DOM中的位置作为标识
    const rect = iframe.getBoundingClientRect()
    return `position:${rect.left}:${rect.top}:${rect.width}:${rect.height}`
  }

  private processIframe(iframe: HTMLIFrameElement, nodes: Map<Node, TFText[]>) {
    try {
      // 检查iframe是否可见
      if (!this.isIframeVisible(iframe)) {
        return
      }

      // 检查是否已经处理过相同的iframe，避免递归调用
      const iframeId = this.getIframeIdentifier(iframe)
      if (this.processedIframes.has(iframeId)) {
        return
      }

      const iframeBody = iframe?.contentDocument?.body
      if (!iframeBody) return

      // 标记为已处理
      this.processedIframes.add(iframeId)

      try {
        this.observer.observe(iframeBody, this.observerConfig)

        const walker = this.initWalker(iframeBody)
        let iframeNode = walker.nextNode()
        while (iframeNode) {
          this.processNode(iframeNode, nodes)
          iframeNode = walker.nextNode()
        }
      } finally {
        // 处理完成后移除标记，允许后续重新处理（如果iframe内容发生变化）
        this.processedIframes.delete(iframeId)
      }
    } catch (error) {
      console.warn('[Translator] IFRAME processing error:', error)
    }
  }

  private applyTranslations(nodes: Map<Node, TFText[]>, translationsMap: Map<string, TFText>) {
    try {
      nodes.forEach((texts, node) => {
        texts.forEach(text => {
          const translation = translationsMap.get(text.source)
          if (!translation?.translation) return

          text.translation = translation.translation
          this.cache.set(text.source, text, this.toLang)

          if (text.type === 'text') {
            const parent = node as Element
            if (parent && text.source !== text.translation) {
              for (const child of Array.from(node.childNodes)) {
                if (child.nodeType === Node.TEXT_NODE && child?.textContent === text.source) {
                  child.textContent = text.translation
                }
              }
              parent.setAttribute('data-original-text', text.source)
              parent.setAttribute('data-original-translate-text', text.translation)
              parent.setAttribute('data-original-type', text.type)
              parent.setAttribute('data-translate-success', 'true')
            }
          } else if (text.type === 'attr' && text.source !== text.translation) {
            const element = node as Element
            element.setAttribute(text.attrName as string, text.translation)
            element.setAttribute('data-original-text', text.source)
            element.setAttribute('data-original-translate-text', text.translation)
            element.setAttribute('data-original-type', text.type)
            element.setAttribute('data-translate-success', 'true')
          }
        })
      })
    } catch (error) {
      console.error('[Translator] Apply translations error:', error)
      this.recoverFromError(error)
    }
  }

  private getNodeText(node: Node): string | undefined {
    const text = node.textContent
    if (!text) return undefined
    return text
  }

  private splitCacheTexts(texts: string[]) {
    const cacheTexts: TFText[] = []
    const uncacheTexts: string[] = []
    
    texts.forEach(text => {
      const cachedTranslation = this.cache.get(text, this.toLang)
      if (cachedTranslation) {
        cacheTexts.push(cachedTranslation)
      } else {
        uncacheTexts.push(text)
      }
    })

    return { cacheTexts, uncacheTexts }
  }

  private isChinese(text?: string | null) {
    return text && /[\u4e00-\u9fa5]/.test(text)
  }

  private chunkTexts(texts: string[]): string[][] {
    const chunks: string[][] = []
    let currentChunk: string[] = []
    let currentLength = 0

    for (const text of texts) {
      if (currentLength + text.length > this.maxChunkLength) {
        chunks.push(currentChunk)
        currentChunk = [text]
        currentLength = text.length
      } else {
        currentChunk.push(text)
        currentLength += text.length
      }
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk)
    }

    return chunks
  }

  private async recoverFromError(error: unknown) {
    console.error('[Translator] Error occurred:', error instanceof Error ? error.message : error)

    if (this.observer) {
      this.observer.disconnect()
      this.isObserving = false
    }

    if (this.throttleTimer) {
      clearTimeout(this.throttleTimer)
      this.throttleTimer = undefined
    }

    this.isTranslating = false
    this.isCollecting = false
    this.textCache.clear()
  }

  private hasOwnDisplayText(node: Node): boolean {
    if (node.nodeType !== Node.ELEMENT_NODE) return false;
  
    const el = node as HTMLElement;
  
    // 1. 检查纯文本子节点（不含子元素的文字）
    for (const child of Array.from(el.childNodes)) {
      if (child.nodeType === Node.TEXT_NODE && child.textContent?.trim()) {
        return true;
      }
    }
    return this.attrWhiteList.some(attr => el.getAttribute(attr)?.trim());
  }
  
  private initWalker(root: Node): TreeWalker {
    return document.createTreeWalker(root, NodeFilter.SHOW_ALL, {
      acceptNode: node => {
        if (
          node.nodeType === Node.ELEMENT_NODE &&
          (node as Element).matches(this.htmlBlackList.join(','))
        ) {
          return NodeFilter.FILTER_REJECT
        }
        return NodeFilter.FILTER_ACCEPT
      }
    })
  }
}

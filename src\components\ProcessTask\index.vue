<template>
  <FModal
    v-model:visible="value"
    :title="i18n.t('处理')"
    :footer="null"
    wrap-class-name="process-task-modal"
    width="86%"
    footer-hide
    @cancel="handleCancel"
  >
    <div class="card-datasource">
      <img src="@/assets/images/mail.png" alt="" style="width: 20px; margin-right: 5px" /> {{ i18n.t('来自') }}
      <span style="margin-left: 3px; color: #666666">{{ props.taskRecord.superviser }}&nbsp;</span>
      <pre
        class="remarks pre-warp"
        v-html="props.taskRecord.contentData ? JSON.parse(props.taskRecord.contentData as string).taskDesc : '--'"
      />
    </div>
    <div class="card-manager" style="border-bottom: none">
      <div>
        <img class="avatar" :src="defaultfImg" />
        <span class="card-manager-name color333"> @{{ taskRecord.superviser }} </span>
        <span class="color999">{{ i18n.t('任务标题') }}:</span>
        <span class="card-manager-name color333">{{ taskRecord.taskName }}</span>
        <span class="color999">
          <span class="iconfont color999 fontSize14" style="margin-left: 20px; margin-right: 2px"> &#xe629; </span>
          <span>{{ i18n.t('目标完成时间') }}: {{ transformDate(taskRecord.forcastTime) }}</span>
        </span>
      </div>
    </div>
    <UploadFile class="todo-upload" v-model:value="fileList" />

    <!-- 处理表单 -->
    <template v-if="props.value">
      <FormRender
        ref="formRef"
        v-if="props.taskRecord.formKey"
        :id="props.taskRecord.formKey"
        type="edit"
        :data="taskFormData"
      />
      <MyEditor
        v-else
        ref="editor"
        class="form-editor"
        v-model:value="handleTaskDesc"
        placeholder="请输入详情"
        @input="change"
      />
    </template>

    <div class="footer-button">
      <FButton style="margin-right: 20px" class="grey-btn" @click="onDispatch">转派</FButton>
      <FButton style="margin-right: 20px" @click="onSave()" class="grey-btn">保存</FButton>
      <FButton type="primary" @click="onCommit()">提交</FButton>
    </div>
  </FModal>
  <DispatchModal v-model:value="visible" @submit="dispatchTask"></DispatchModal>
  <NotificationModal
    v-model:value="notificationModel.flag"
    v-model:loading="notificationModel.loading"
    :title="(notificationModel.title as string)"
    :role="(processRoleInfo as IProcessRoleAndUser[])"
    default-role-key="taskId"
    :default-role-value="taskRecord.id"
    :is-delay-status="isDelayStatus"
    @submit="handleNotificationMilepostTask"
  />
</template>
<script lang="ts" setup>
import { ref, watch, computed, markRaw, inject, reactive, toRaw } from 'vue'
import { messageInstance as message } from '@fs/smart-design'
import DispatchModal from '@/components/DispatchModal/index.vue'
import UploadFile from '@/components/UploadFile/index.vue'
import defaultfImg from '@/assets/images/head.png'
import { submitMission, saveMission, dispatchMission } from '@/api/handle'
import { S3URL, transformDate, useI18n } from '@/utils'
import FormRender from '@/components/FormRender/index.vue'
import MyEditor from '@/components/Editor/MyEditor.vue'
import { ITask, IProcess } from '@/types/handle'
import { IProcessRoleAndUser } from '@/types/request'
import { ProcessModalTilte } from '@/views/process-detail/config'
import NotificationModal from '@/components/NotificationModal/index.vue'
import dayjs from 'dayjs'

interface IProps {
  value: boolean
  taskRecord: ITask
  paramsWrapper: (data: unknown) => unknown
}
interface IModel<T = ITask> {
  flag: boolean
  loading?: boolean
  data: T
  [key: string]: unknown
}
const notificationModel = reactive<IModel<IProcess>>({ flag: false, data: {} as any, title: '', loading: false })
const processRoleInfo = inject<IProcessRoleAndUser[]>('processRoleInfo') // 流程角色信息
const i18n = useI18n()
const props = defineProps<IProps>()
const emit = defineEmits(['submit', 'fatherMethod', 'update:value'])
const value = computed({
  get: () => props.value,
  set: (value: boolean) => emit('update:value', value),
})
const isDelayStatus = computed(
  () =>
    props.taskRecord?.invalid === 1 &&
    props.taskRecord?.isDelayReason === 1 &&
    props.taskRecord?.status === 1 &&
    dayjs().isAfter(dayjs(props.taskRecord?.forcastTime))
)

const formRef = ref()
const handleTaskDesc = ref<string>()
const fileList = ref<any[]>([])
const visible = ref(false)

const taskFormData = computed(() => {
  if (Object.keys(props.taskRecord).length === 0 || !props.value) return markRaw({})
  const {
    creator,
    createdTime,
    contentData,
    forcastTime,
    taskCompletedTime,
    taskStartTime,
    updatedUser,
    updatedTime,
    formData,
    ...data
  } = props.taskRecord
  return markRaw({ envData: data, envDefaultFormData: formData })
})

watch(
  () => props.value,
  () => {
    if (props.value) {
      const task = props.taskRecord
      handleTaskDesc.value = task.contentData ? JSON.parse(task.contentData as string).handleTaskDesc : ''

      const attachmentData = task.attachmentData ? JSON.parse(task.attachmentData) : []
      const file = (task.formData?.file || []).map((item: any) => ({
        isFormData: true,
        fileName: item.name,
        fileSize: item.size,
        resourseKey: `${S3URL}/api/s3/getFileByUrl?url=${item.url}&filename=${item.name}`,
      }))
      fileList.value = [...attachmentData, ...file].filter(item => !!item)
    }
  }
)

const change = (val: string) => {
  handleTaskDesc.value = val
}

// 提交
const onCommit = async () => {
  const formKey = props.taskRecord.formKey
  if (!formKey && !handleTaskDesc.value) return message.error('请输入处理说明')

  try {
    const scoped = formRef.value?.getAmisScoped()
    const $form = scoped?.getComponentByName('page.taskForm')
    const formData = await $form?.submit()

    const taskRecord = props.taskRecord
    const contentData = taskRecord.contentData ? JSON.parse(taskRecord.contentData as string) : {}
    const data: Record<string, any> = { id: props.taskRecord.id, attachmentData: fileList.value }
    // 有表单数据的时候
    if (formData) {
      data.formData = { ...formData }
      let file = toRaw(props.taskRecord)?.formData?.file || []
      file && !Array.isArray(file) && (file = JSON.parse(`[${file}]`))
      formData.file && file.push(...JSON.parse(`[${formData.file}]`))
      data.formData.file = file.reduce((fileList: any[], cur: any) => {
        !fileList.find((item: any) => item.url === cur.url) && fileList.push(cur)
        return fileList
      }, [])
    }

    // 没有表单的时候
    !formKey && handleTaskDesc.value && (data.contentData = { ...contentData, handleTaskDesc: handleTaskDesc.value })

    if (taskRecord.approverUuid) {
      submitMission(props.paramsWrapper(data)).then(res => {
        if (res.code == 200) {
          message.success('提交成功')
          handleCancel()
          emit('fatherMethod')
        } else {
          message.error(res.msg)
        }
      })
    } else {
      notificationModel.flag = true
      notificationModel.title = ProcessModalTilte.submit
      notificationModel.data = props.paramsWrapper(data) as any
    }
  } catch (error: any) {
    message.error(error.message)
  }
}

const handleNotificationMilepostTask = async (data: string[]) => {
  try {
    const res = await submitMission({ ...notificationModel.data, ...data })
    if (res.code == 200) {
      message.success(i18n.t('提交成功'))
      handleCancel()
      emit('fatherMethod')
      notificationModel.flag = false
    } else {
      message.error(res.msg)
    }
  } finally {
    notificationModel.loading = false
  }
}

// 保存
const onSave = async () => {
  const formKey = props.taskRecord.formKey
  if (!formKey && !handleTaskDesc.value) return message.error('请输入处理说明')

  try {
    const scoped = formRef.value?.getAmisScoped()
    const $form = scoped?.getComponentByName('page.taskForm')
    const formData = await $form?.submit()

    const taskRecord = props.taskRecord
    const contentData = taskRecord.contentData ? JSON.parse(taskRecord.contentData as string) : {}
    const data: Record<string, any> = { id: props.taskRecord.id, attachmentData: fileList.value }
    // 有表单数据的时候
    if (formData) {
      data.formData = { ...formData }
      let file = toRaw(props.taskRecord)?.formData?.file || []
      file && !Array.isArray(file) && (file = JSON.parse(`[${file}]`))
      formData.file && file.push(...JSON.parse(`[${formData.file}]`))
      data.formData.file = file.reduce((fileList: any[], cur: any) => {
        !fileList.find((item: any) => item.url === cur.url) && fileList.push(cur)
        return fileList
      }, [])
    }

    // 没有表单的时候
    !formKey && handleTaskDesc.value && (data.contentData = { ...contentData, handleTaskDesc: handleTaskDesc.value })

    saveMission(props.paramsWrapper(data)).then(res => {
      if (res.code == 200) {
        message.success('保存成功')
        // handleCancel()
        emit('fatherMethod')
      } else {
        message.error(res.msg)
      }
    })
  } catch (error: any) {
    message.error(error.message)
  }
}

// 唤起转派弹框
const onDispatch = () => {
  visible.value = true
}

// 转派任务
const dispatchTask = (data: any) => {
  const params = {
    superviser: data.superviser,
    taskId: props.taskRecord.id,
  }
  dispatchMission(props.paramsWrapper(params)).then(res => {
    if (res.code == 200) {
      message.success('转派成功')
      visible.value = false
      handleCancel()
      emit('fatherMethod')
    }
  })
}

// 取消
const handleCancel = () => {
  handleTaskDesc.value = ''
  emit('update:value', false)
}
</script>
<style lang="scss">
.process-task-modal {
  .fs-modal {
    max-width: 1360px;
    min-width: 960px;
  }
}
</style>
<style lang="scss" scoped>
:deep(.process-task-modal) {
  .fs-modal {
    max-width: 1360px;
  }
}
.card-datasource {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 10px;
  align-items: center;
  color: #999;
  border-bottom: 1px dashed #e0e0e0;
  font-size: 12px;
  .remarks {
    flex: 0 0 100%;
    margin: 10px 5px 0 5px;
  }
}
.card-manager {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 14px;
  padding-bottom: 10px;
  font-size: 12px;

  .file-btn-placeholder {
    width: 66px;
    flex-shrink: 0;
    height: 20px;
    pointer-events: none;
  }

  > div {
    display: flex;
    align-items: center;
    line-height: 20px;
    > img {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 5px;
    }

    > span {
      display: flex;
      height: 20px;
      align-items: center;
    }
  }
  .card-manager-name {
    margin-right: 20px;
  }
}

.form-editor {
  line-height: 1;
  :deep(.ql-toolbar) {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }
  :deep(.ql-container) {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  :deep(.ql-editor.ql-blank::before) {
    font-style: normal !important;
    color: #bbb;
    font-size: 12px;
  }
  :deep(.ql-editor) {
    height: 200px;
    word-break: break-all;
  }
  :deep(.ql-video) {
    width: 100%;
    height: 434px;
  }
}
.z-form-deitor :deep(.fs-form-item-control-input-content) {
  height: 100% !important;
}

.footer-button {
  margin-top: 20px;
  text-align: right;
  .grey-btn {
    background: #f8f8f8;
  }
}
</style>

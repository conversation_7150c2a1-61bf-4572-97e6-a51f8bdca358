<template>
  <div>
    <div class="fei-su-main">
      <div class="fei-su-main-box">
        <div class="fei-su-main-left">
          <div class="fei-su-left-title">{{ i18n.t('字典目录') }}</div>
          <div class="fei-su-tree">
            <FTree :tree-data="treeData" :default-expand-all="true" v-if="treeData.length > 0"></FTree>
          </div>
        </div>
        <div class="fei-su-main-right">
          <div class="select-options">
            <FForm layout="inline" class="select-btn">
              <div class="btn_div_relative">
                <span class="btn_span_absolute">{{ i18n.t('快速搜索') }}</span>
                <FFormItem class="width240">
                  <FInput :placeholder="i18n.t('输入字典名称')" v-model:value="form.name" :allow-clear="true">
                    <template #suffix>
                      <i class="iconsousuo1 cursor iconfont fontSize12 colord8d8d8" @click="searchDictionary"></i>
                    </template>
                  </FInput>
                </FFormItem>
              </div>
            </FForm>
          </div>
          <div class="fei-su-card">
            <div class="card-btn">
              <div class="fei-su-title">{{ i18n.t('字典列表') }}</div>
              <FButton type="primary" class="add-button" @click="addProject"
                ><i class="iconxinzeng iconfont marginR5 fontSize14"></i>{{ i18n.t('新建项目') }}</FButton
              >
            </div>
            <FTable :data-source="dataList" :columns="columns" :pagination="false" :loading="loading">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <span
                    class="iconfont-hover iconfont cursor color4677C7 icontubiao_xietongbianji marginR5"
                    :title="i18n.t('编辑')"
                    @click="handleUpdate(record)"
                  ></span>
                  <FConfigProvider :auto-insert-space-in-button="false">
                    <FPopconfirm
                      :ok-text="i18n.t('确定')"
                      :cancel-text="i18n.t('取消')"
                      @confirm="confirm(record)"
                      :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
                    >
                      <template #title>
                        <p class="color333 fontSize14 marginB5">
                          {{ i18n.t('确定将') }}{{ record.name }}{{ i18n.t('字典删除吗？') }}
                        </p>
                        <div class="color999 fontSize12">{{ i18n.t('删除后不可恢复,请谨慎操作') }}</div>
                      </template>
                      <span
                        class="iconfont iconfont-hover cursor color4677C7 icontubiao_shanchu1 marginR5"
                        :title="i18n.t('删除')"
                      ></span>
                    </FPopconfirm>
                    <FPopconfirm
                      :ok-text="i18n.t('确定')"
                      :cancel-text="i18n.t('取消')"
                      @confirm="confirmOpen(record)"
                      :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
                    >
                      <template #title>
                        <p class="color333 fontSize14 marginB5">
                          {{ i18n.t('是否') }}{{ record.status == 0 ? i18n.t('启用') : i18n.t('禁用') }}{{ record.name
                          }}{{ i18n.t('字典吗？') }}
                        </p>
                      </template>
                      <span
                        v-if="record.status == 0"
                        class="cursor iconfont icontubiao_zanting1 color4677C7"
                        :title="i18n.t('启用')"
                      ></span>
                      <span
                        v-else
                        class="cursor iconfont icontubiao_jinyong color4677C7"
                        :title="i18n.t('禁用')"
                      ></span>
                    </FPopconfirm>
                  </FConfigProvider>
                </template>
              </template>
              <template #emptyText>
                <img :src="noImgUrl" />
                <p class="colorBBB fontSize12 fei-su-tip">{{ i18n.t('暂无字典，赶快去添加吧~~') }}</p>
              </template>
            </FTable>
            <div class="fei-su-pagination">
              <FPagination
                v-model:current="paging.pageNum"
                @change="onChange"
                v-model:pageSize="paging.pageSize"
                :total="paging.total"
                show-size-changer
                :show-total="() => `${i18n.t('共')} ${paging.total} ${i18n.t('条')}`"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <AddDictionary
      :title="title"
      :type="type"
      @popup-close="close"
      :record="recordList"
      :show="show"
      :tree-data="treeData"
      @submit="submitDictionary"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import noImgUrl from '@/assets/images/no-data.png'
import AddDictionary from './components/AddDictionary.vue'
import { messageInstance as message } from '@fs/smart-design'
import type { IDictionatyData } from '@/types/dictionary'
import {
  createDictionary,
  getDictionaryList,
  deleteDictionary,
  updateDictionary,
  banDictionary,
} from '@/api/dictionary'
import lodash from 'lodash'
import { useI18n } from '@/utils'

const i18n = useI18n()
interface formData {
  name: null | string
}
interface IPage {
  pageNum: number // 当前页
  pageSize: number // 每页条数
  total: number // 总条数
}
const show = ref(false)
const title = ref<string>('')
const type = ref<string>('')
const treeData = ref<any[]>([])
const form = ref<formData>({
  name: '',
})
const paging = ref<IPage>({ pageNum: 1, pageSize: 10, total: 100 })
const loading = ref<boolean>(false)
const dataList = ref<IDictionatyData[]>([])
const columns = computed<TableColumnsType>(() => [
  { title: i18n.t('字典字段'), dataIndex: 'field', key: 'field' },
  { title: i18n.t('字典名称'), dataIndex: 'name', key: 'name' },
  { title: i18n.t('字典值'), dataIndex: 'value', key: 'value' },
  { title: i18n.t('操作'), dataIndex: 'action', key: 'action' },
])
const recordList = ref({})
const onChange = (current: number, pageSize: number) => {
  paging.value.pageSize = pageSize
  fetchData()
}
const handleUpdate = (record: any) => {
  title.value = i18n.t('编辑字典')
  type.value = 'edit'
  show.value = true
  recordList.value = record
}
const addProject = () => {
  title.value = i18n.t('新增字典')
  type.value = 'add'
  show.value = true
}
const close = () => {
  show.value = false
}
const submitDictionary = async (data: IDictionatyData) => {
  if (data.parentId == null) {
    data.parentId = 0
  }
  const { id } = data
  let res
  if (id) {
    res = await updateDictionary(data)
  } else {
    res = await createDictionary(data)
  }
  if (res.code == 200) {
    id ? message.success(i18n.t('编辑成功')) : message.success(i18n.t('新增成功'))
    show.value = false
    fetchData()
    fetchTreeData()
  }
}
onMounted(() => {
  fetchData()
  fetchTreeData()
})
const fetchData = async () => {
  loading.value = true
  const { name } = form.value
  const parmas = {
    name,
    pageNum: paging.value.pageNum,
    pageSize: paging.value.pageSize,
  }
  const res = await getDictionaryList(parmas)
  if (res.code == 200) {
    dataList.value = res.data.list
    paging.value.total = res.data.totalCount
    // paging.value.pageNum = res.data.pageNum
    loading.value = false
  }
}
const fetchTreeData = async () => {
  loading.value = true
  const parmas = {
    name: form.value.name,
    pageNum: 1,
    pageSize: 999,
  }
  const res = await getDictionaryList(parmas)
  if (res.code == 200) {
    treeData.value = formatToTree(lodash.cloneDeep(res.data.list))
  }
}
const formatToTree = (arr: any[]) => {
  let result: any = []
  if (!Array.isArray(arr) || arr.length === 0) {
    return result
  }
  let map: any = {}
  arr.forEach(item => (map[item.id] = item))
  arr.forEach(item => {
    item.title = item.name
    item.value = item.id
    item.name = item.value
    const parent = map[item.parentId]
    if (parent) {
      ;(parent.children || (parent.children = [])).push(item)
    } else {
      result.push(item)
    }
  })
  return result
}
const confirm = (record: any) => {
  deleteDictionary({ id: record.id }).then(res => {
    if (res.code == 200) {
      if (dataList.value.length <= 1 && paging.value.pageNum > 1) paging.value.pageNum--
      message.success(i18n.t('删除成功'))
      fetchData()
      fetchTreeData()
    } else {
      message.warning(res.msg)
    }
  })
}
const confirmOpen = (record: any) => {
  banDictionary({ id: record.id }).then(res => {
    if (res.code == 200) {
      if (record.status == 0) {
        message.success(i18n.t('启用成功'))
      } else {
        message.success(i18n.t('禁用成功'))
      }
      fetchData()
      fetchTreeData()
    } else {
      message.warning(res.msg)
    }
  })
}
const searchDictionary = () => {
  fetchData()
}
</script>
<style scoped lang="scss">
.fei-su-title {
  color: #333;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 0 !important;
}
.fei-su-pagination {
  display: flex;
  justify-content: flex-end;
  padding: 15px 0 25px;
  span {
    line-height: 32px;
    color: #bbb;
    margin-right: 10px;
  }
}
.fei-su-tip {
  margin-top: -30px;
}
.fei-su-main-box {
  display: flex;
  width: 100%;
  .fei-su-main-left {
    width: 320px;
    margin-right: 15px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
    .fei-su-left-title {
      padding: 24px;
      font-size: 14px;
      border-bottom: 1px solid #eeeeee;
      font-weight: 500;
      font-weight: 600;
    }
  }
  .fei-su-main-right {
    flex: 1;
  }
}
.select-btn {
  justify-content: space-between;
}
.fei-su-tree {
  max-height: 77vh;
  overflow: scroll;
  padding: 16px 24px;
  :deep(.fs-tree > .fs-tree-treenode) {
    width: auto;
  }
}
.card-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
:deep(.fs-table-tbody) {
  > tr:hover:not(.fs-table-expanded-row) > td,
  .fs-table-row-hover,
  .fs-table-row-hover > td {
    background: #f1f4f8 !important;
  }
}
.fs-table-fixed {
  .fs-table-row-hover,
  .fs-table-row-hover > td {
    background: #f1f4f8 !important;
  }
}
// .color
</style>

/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from 'axios'
import { getToken } from '@/utils'
import { message } from '@fs/smart-design'

export const DefaultSchema = {
  id: '1',
  type: 'Container',
  ultra: {},
  attribute: {},
  event: {},
  children: [],
}

export const AmisRenderConfig = {
  notify: (type: 'error' | 'success' | 'info' | 'warning', msg: string) => message[type] && message[type](msg),
  useMobileUI: false,
}

export const getFetcher = (id: string | number, token: string) => {
  return ({
    url, // 接口地址
    method, // 请求方法 get、post、put、delete
    config = {}, // 其他配置
    data, // 请求数据
    responseType,
    headers, // 请求头
  }: any) => {
    method = method || 'get'

    // config.withCredentials = true
    responseType && (config.responseType = responseType)

    config.headers = Object.assign(
      {},
      !headers?.token &&
        !headers?.Token && {
          Token: `Bearer ${token}`,
        },
      {
        'Process-Form-Id': id,
      },
      headers || {}
    )
    if (config.cancelExecutor) {
      config.cancelToken = new axios.CancelToken(config.cancelExecutor)
    }

    if (!['post', 'put', 'patch'].includes(method)) {
      if (data) config.params = data
      return (axios as any)[method](url, config)
    } else if (data && data instanceof FormData) {
      config.headers = config.headers || {}
      config.headers['Content-Type'] = 'multipart/form-data'
    } else if (data && typeof data !== 'string' && !(data instanceof Blob) && !(data instanceof ArrayBuffer)) {
      // data = JSON.stringify(data)
      config.headers = config.headers || {}
      config.headers['Content-Type'] = 'application/json'
    }

    return (axios as any)[method](url, data, config)
  }
}

export const fetcher = ({
  url, // 接口地址
  method, // 请求方法 get、post、put、delete
  data, // 请求数据
  responseType,
  config, // 其他配置
  headers, // 请求头
}: {
  url: string
  method: string
  data: any
  responseType: string
  config: any
  headers: any
}) => {
  config = config || {}
  // config.withCredentials = true;
  responseType && (config.responseType = responseType)

  if (config.cancelExecutor) {
    config.cancelToken = new axios.CancelToken(config.cancelExecutor)
  }

  config.headers = headers || {}
  if (!config.headers.token) config.headers.token = `Bearer ${getToken()}`

  if (method !== 'post' && method !== 'put' && method !== 'patch') {
    if (data) {
      config.params = data
    }

    return axios[method as 'get' | 'post' | 'put' | 'delete'](url, config)
  } else if (data && data instanceof FormData) {
    config.headers = config.headers || {}
    Reflect.deleteProperty(config.headers, 'Content-Type')
  } else if (data && typeof data !== 'string' && !(data instanceof Blob) && !(data instanceof ArrayBuffer)) {
    // data = JSON.stringify(data);
    config.headers = config.headers || {}
    config.headers['Content-Type'] = 'application/json'
  }

  return axios[method as 'get' | 'post' | 'put' | 'delete'](url, data, config)
}

export const iconBox =
  'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICA8cGF0aCBmaWxsPSIjNjY2NjY2IiBkPSJNMTIgMkwzIDdsOSA1IDktNXpNMyA3djEwbDkgNSA5LTVWN0wxMiAxMk0zIDdsOSA1IDktNS0tOS01eiIvPgo8L3N2Zz4='
export const iconTree =
  'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICA8cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiM2NjY2NjYiIHN0cm9rZS13aWR0aD0iMiIgZD0iTTUgMTJoMTNNOSAxMnY3TTkgMTJWMk05IDEybDQtNG00IDQgNC00Ii8+Cjwvc3ZnPg=='
export const iconCode =
  'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICA8cGF0aCBmaWxsPSIjNjY2NjY2IiBkPSJNOCAxOGw2LTYtNi02IDEuNDEtMS40MUwxNi4xNiAxMiA5LjQxIDE5LjQxIDggMTh6TTQgMmgxNnYyMEg0eiIvPgo8L3N2Zz4='

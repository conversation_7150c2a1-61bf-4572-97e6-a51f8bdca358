<template>
  <div class="select-box">
    <div class="select-box-left">
      <div class="left-box">
        <div class="marginR12 marginB10" v-if="!props.only">
          <FSelect
            v-model:value="searchData.processConfigId"
            :press-line="i18n.t('流程类型')"
            show-search
            allow-clear
            style="width: 120px"
            :placeholder="i18n.t('请选择')"
            :options="typeList"
            :field-names="{ label: 'processName', value: 'id' }"
            :filter-option="processFilterOption"
            @change="onProcessType($event)"
          >
            <!-- <template #suffixIcon>
              <i class="cursor iconfont colord8d8d8 fontSize12" @click="onSearch">&#xe799;</i>
            </template> -->
          </FSelect>
        </div>

        <div class="marginR12 marginB10" v-if="!systemHiddenFields.includes('tags')">
          <FSelect
            v-model:value="searchData.tags"
            mode="multiple"
            :press-line="i18n.t('需求标签')"
            style="width: 120px"
            show-search
            allow-clear
            :placeholder="i18n.t('请选择')"
            max-tag-count="responsive"
            :options="tagList"
            @change="onSearch"
          >
            <!-- <template #suffixIcon>
              <i class="cursor iconfont colord8d8d8 fontSize12" @click="onSearch">&#xe799;</i>
            </template> -->
          </FSelect>
        </div>

        <div class="marginR12 marginB10" v-if="!systemHiddenFields.includes('nodeId')">
          <FSelect
            v-model:value="searchData.nodeId"
            :press-line="i18n.t('当前节点')"
            show-search
            allow-clear
            style="width: 120px"
            :options="nodeList"
            :placeholder="i18n.t('请选择')"
            @change="onSearch"
          >
            <!-- <template #suffixIcon>
              <i class="cursor iconfont colord8d8d8 fontSize12" @click="onSearch">&#xe799;</i>
            </template> -->
          </FSelect>
        </div>

        <div class="marginR12 marginB10" v-if="!systemHiddenFields.includes('isUrgent')">
          <FSelect
            v-model:value="searchData.isUrgent"
            :press-line="i18n.t('优先级')"
            allow-clear
            style="width: 120px"
            :options="priorityList"
            @change="onSearch"
            :placeholder="i18n.t('请选择')"
          >
            <!-- <template #suffixIcon>
              <i class="cursor iconfont colord8d8d8 fontSize12" @click="onSearch">&#xe799;</i>
            </template> -->
          </FSelect>
        </div>

        <div class="marginR12 marginB10" v-if="!systemHiddenFields.includes('projectUuidList')">
          <FCascader
            class="project-cust-cascader"
            dropdown-class-name="project-cust-cascader-dropdown"
            multiple
            show-arrow
            :press-line="i18n.t('项目成员')"
            :placeholder="i18n.t('请输入')"
            :field-names="{ label: 'name', value: 'uuid', children: 'departmentChildrens' }"
            max-tag-count="responsive"
            :options="personList"
            :show-search="{ filterOption }"
            show-checked-strategy="SHOW_CHILD"
            @change="onChangePerson"
            v-model:value="searchData.projectUuidList"
            :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
          ></FCascader>
        </div>

        <!-- <div class="btn_div_relative marginR12 marginB10" v-if="!systemHiddenFields.includes('sapCode')">
          <div class="serach-item">
            <span class="btn_span_absolute btn_span_absolute_left">{{ i18n.t('PS项目编号') }}</span>
            <FInput
              :placeholder="i18n.t('请输入')"
              style="width: 120px"
              @press-enter="onSearch"
              v-model:value="searchData.sapCode"
            >
              <template #suffix>
                <i class="cursor iconfont colord8d8d8 fontSize12" @click="onSearch">&#xe70e;</i>
              </template>
            </FInput>
          </div>
        </div> -->

        <AsyncSearch
          :search-config-list="searchConfigList"
          :default-search="searchData.searchObj || {}"
          :is-fold="0"
          @on-async-search="onAsyncSearchNoFold"
        />

        <div class="btn_div_relative marginR12 marginB10" v-if="!systemHiddenFields.includes('range')">
          <span class="btn_span_absolute btn_span_absolute_left">{{ i18n.t('时间筛选') }}</span>
          <FRangePicker
            v-model:value="range"
            @change="onSearch"
            format="YYYY-MM-DD"
            style="width: 240px; height: 32px"
            allow-clear
          />
        </div>

        <div class="btn_div_relative marginR12 marginB10" v-if="!systemHiddenFields.includes('queryInput')">
          <div class="serach-item">
            <span class="btn_span_absolute btn_span_absolute_left">{{ i18n.t('搜索') }}</span>
            <FInput
              :placeholder="i18n.t('请输入')"
              style="width: 240px"
              @press-enter="onSearch"
              v-model:value="searchData.queryInput"
            >
              <template #suffix>
                <i class="cursor iconfont colord8d8d8 fontSize12" @click="onSearch">&#xe70e;</i>
              </template>
            </FInput>
          </div>
        </div>
        <FTooltip
          v-if="visibleSearch"
          open
          overlay-class-name="cust-search-tip-box"
          trigger="click"
          placement="bottom"
          color="#fff"
          @visible-change="(visible:boolean) => {visibleStasus = visible}"
          :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
        >
          <template #title>
            <div class="async-search-container">
              <AsyncSearch
                :search-config-list="searchConfigList"
                :default-search="searchData.searchObj || {}"
                @on-async-search="onAsyncSearchFold"
              />
            </div>
          </template>
          <span class="cust-search-title">
            {{ i18n.t('更多筛选') }}<i :class="['iconfont', visibleStasus ? 'iconjiantoushang' : 'iconjiantouxia']" />
          </span>
        </FTooltip>
        <span v-show="showClear && !props.only" class="clear-search" @click="cleaeSearch">{{
          i18n.t('清空已选')
        }}</span>
      </div>
    </div>
    <div class="select-box-right">
      <template v-if="searchData.processConfigId">
        <FDropdown v-if="hasExportTemplate" trigger="click" destroy-popup-on-hide :overlay-style="{ minWidth: 'auto' }">
          <FButton class="download-btn marginR12" :open="true">
            <i class="iconfont icontubiao_xiazai marginR4"></i>
            <span class="marginR4">下载数据</span>
            <span class="btn-line"></span>
            <i class="iconfont iconjiantouxia marginL16"></i>
          </FButton>
          <template #overlay>
            <FMenu @click="({ key }) => $emit('on-export-process', key)">
              <FMenuItem key="common">
                {{ i18n.t('统一格式下载') }}
              </FMenuItem>
              <FMenuItem key="exportTemplate">
                {{ i18n.t('模板格式下载') }}
              </FMenuItem>
            </FMenu>
          </template>
        </FDropdown>
        <FButton v-else class="export-btn" type="default" @click="$emit('on-export-process')">
          <i class="icontubiao_xiazai iconfont"></i>{{ i18n.t('下载数据') }}
        </FButton>
      </template>
      <FButton class="add-btn" type="primary" @click="$emit('add-new-demand')" v-if="props.extend">
        <i class="iconfont icontianjia btn-add-demand"></i>
        {{ i18n.t('新建需求') }}
      </FButton>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, watchEffect, nextTick } from 'vue'
import { useStore } from 'vuex'
import { message } from '@fs/smart-design'
import { SelectProps } from '@fs/smart-design/dist/ant-design-vue_es'
import dayjs from 'dayjs'
import { transformDate, useI18n } from '@/utils'
import { GetTagAndNode, getSearchConfigById } from '@/api/process'
import { ProcessListSearch } from '@/types/processListModel'
import { ISearchConfig } from '@/types/request'
import AsyncSearch from './AsyncSearch.vue'
import { getDepartment } from '@/api'
import { useRoute } from 'vue-router'

const route = useRoute()
const cacheKey = computed(() => {
  if (route.name === 'ProcessList') return 'all'
  if (route.params?.type) return route.params?.type
  return 'noCache'
})

const i18n = useI18n()
const store = useStore()
const props = defineProps({ only: { type: [Number, String], default: null }, extend: { type: Boolean, default: true } })
const emit = defineEmits(['search', 'add-new-demand', 'on-export-process'])

const searchData = reactive<ProcessListSearch>({
  tags: undefined,
  processConfigId: undefined,
  nodeId: undefined,
  isUrgent: undefined,
  projectUuidList: undefined,
  startTime: '',
  endTime: '',
  queryInput: '',
  // sapCode: '',
  searchObj: undefined,
})
const range = ref<[dayjs.Dayjs, dayjs.Dayjs]>()
const typeList = ref<SelectProps['options']>([])
const tagList = ref<SelectProps['options']>([])
const nodeList = ref<SelectProps['options']>([])
const personList = ref([])
const priorityList = ref([
  { value: '0', label: i18n.t('一般') },
  { value: '1', label: i18n.t('加急') },
])
const visibleStasus = ref<boolean>(false)
const visibleSearch = ref<boolean>(false)
const searchConfigList = ref<ISearchConfig[]>([])

const showClear = computed(() => Object.values(searchData).some(item => item != null && item !== ''))
const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
const processFilterOption = (input: string, option: any) =>
  option.processName.toLowerCase().indexOf(input.toLowerCase()) >= 0
const hasExportTemplate = ref(false)
const systemHiddenFields = ref([])
const searchObjFold = ref({})
const searchObjNoFold = ref({})

onMounted(async () => {
  // getProcessNode()
  // onGetDepartment()
  // // !props.only && (await setDefaultSearch())
  // await setDefaultSearch()
  // nextTick(onSearch)
  // console.log('route :>> ', route);
})

watchEffect(() => {
  // if (props.only) nextTick(() => onProcessType(+props.only))
  if (route.path && ['ProcessListOne', 'ProcessList'].includes(route.name as string))
    nextTick(async () => {
      await getProcessNode()
      onGetDepartment()
      await setDefaultSearch()
      nextTick(onSearch)
    })
})

watch(
  () => typeList,
  val => {
    if (val && val.value?.length && searchData.processConfigId) {
      val.value.forEach((item: any) => {
        if (item.id === searchData.processConfigId) {
          tagList.value = (item.dictionarys ?? []).map((tag: any) => ({
            ...tag,
            label: tag.name,
            value: `$.${tag.field}:${tag.value}`,
          }))
          nodeList.value = item.nodes.map((node: { milepostName: any; nodeId: any }) => ({
            ...node,
            label: node.milepostName,
            value: node.nodeId,
          }))
          hasExportTemplate.value = item?.templateIsEnabled
        }
      })
    }
  },
  { deep: true }
)

let selectLists: string | any[] = []
const onChangePerson = (value: any, selectedOptions: any) => {
  selectLists =
    selectedOptions.map((item: any) => {
      return item[item.length - 1]
    }) || []
  onSearch()
}

const checkChildren = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.uuid && cur.name && Object.keys(cur).length === 2) {
      users.push(cur.uuid)
    } else if (cur.departmentChildrens && cur.departmentChildrens.length) {
      checkChildren(cur.departmentChildrens, users)
    }
    return users
  }, users)
}

const handleTree = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      let data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && handleTree(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

const onGetDepartment = async () => {
  const res = await getDepartment()
  if (res.code !== 200) throw new Error(res.msg)
  personList.value = handleTree(res.data)
}

const cleaeSearch = () => {
  Object.assign(searchData, {
    tag: undefined,
    processConfigId: undefined,
    nodeId: undefined,
    isUrgent: undefined,
    projectUuidList: undefined,
    startTime: '',
    endTime: '',
    queryInput: '',
    // sapCode: '',
    searchObj: undefined,
  })
  selectLists = []
  range.value = undefined
  searchConfigList.value = []
  visibleSearch.value = false
  onSearch()
}

const getProcessNode = async () => {
  const res = await GetTagAndNode()
  if (res.code === 200) {
    typeList.value = res.data
  } else {
    message.warn(res.msg)
  }
}
// 选择标签联动
const onProcessType = async (val: any, type = 'change') => {
  searchConfigList.value = []
  visibleSearch.value = false
  type === 'change' && (searchData.searchObj = undefined)
  searchData.tags = undefined
  searchData.nodeId = undefined
  tagList.value = []
  nodeList.value = []
  hasExportTemplate.value = false

  searchData.processConfigId = val
  val && (await getsearchConfig(val))
  if (typeList.value) {
    typeList.value.forEach((item: any) => {
      if (item.id === Number(val)) {
        tagList.value = (item.dictionarys ?? []).map((tag: any) => ({
          ...tag,
          label: tag.name,
          value: `$.${tag.field}:${tag.value}`,
        }))
        nodeList.value = item.nodes.map((node: { milepostName: any; nodeId: any }) => ({
          ...node,
          label: node.milepostName,
          value: node.nodeId,
        }))
        hasExportTemplate.value = item?.templateIsEnabled
      }
    })
  }
  type === 'change' && onSearch()
}

const onAsyncSearch = () => {
  searchData.searchObj = { ...searchObjFold.value, ...searchObjNoFold.value }
  onSearch()
}

const onAsyncSearchFold = (AsyncForm: any) => {
  searchObjFold.value = AsyncForm
  onAsyncSearch()
}

const onAsyncSearchNoFold = (AsyncForm: any) => {
  searchObjNoFold.value = AsyncForm
  onAsyncSearch()
}

const onSearch = () => {
  const params = { ...searchData }
  params.startTime = range.value?.[0] && transformDate(range.value[0], 'YYYY-MM-DD')
  params.endTime = range.value?.[1] && transformDate(range.value[1], 'YYYY-MM-DD')
  let projectUuidList = checkChildren(selectLists || [])
  if (projectUuidList.length) {
    params.projectUuidList = projectUuidList
  } else {
    params.projectUuidList = undefined
    searchData.projectUuidList = undefined
  }
  setCache()
  emit('search', params)
}

const getsearchConfig = async (processConfigId: number) => {
  if (!(typeof Number(processConfigId) === 'number' && Number.isFinite(Number(processConfigId)))) return
  const res = await getSearchConfigById(processConfigId)
  systemHiddenFields.value = []
  if (res.code === 200 && res?.data.length) {
    if (res.data.some(item => item?.moduleType !== 'SYSTEM_FIELDS' && item?.status && item?.fold)) {
      visibleSearch.value = true
    }
    searchConfigList.value = res.data
    res.data.forEach(item => {
      if (item?.moduleType === 'SYSTEM_FIELDS' && item?.field.startsWith('systemFields:') && !item.status) {
        systemHiddenFields.value.push(item?.field?.split(':')[1])
        delete searchData?.[item?.field?.split(':')[1]]
      }
    })
  }
}

const setDefaultSearch = async () => {
  const cache = store.getters['local/getLocalSearchData'] || {}
  const localSearchData = cache[cacheKey.value as string] ?? {}
  if (Object.keys(localSearchData)?.length) {
    const localSearchData = cache[cacheKey.value as string] ?? {}
    searchData['searchObj'] = localSearchData?.searchObj ?? {}
    localSearchData.processConfigId && (await onProcessType(localSearchData.processConfigId, 'init'))
    localSearchData.startTime &&
      localSearchData.endTime &&
      (range.value = [dayjs(localSearchData.startTime), dayjs(localSearchData.endTime)])
    for (const key in searchData) {
      if (!['projectUuidList', 'searchObj'].includes(key) && localSearchData[key]) {
        searchData[key] = localSearchData[key]
      }
    }
  } else if (cacheKey?.value && cacheKey.value !== 'noCache' && cacheKey.value !== 'all') {
    onProcessType(cacheKey.value, 'init')
  }
  const projectUuidCache = store.getters['local/getLocalProjectUuidData']
  if (projectUuidCache !== undefined && projectUuidCache !== null) {
    const projectUuidData = projectUuidCache[cacheKey.value as string] ?? {}
    selectLists = projectUuidData.selectLists
    searchData.projectUuidList = projectUuidData.projectUuidList
  }
}

const setCache = () => {
  // if (props.only) return
  const cacheValue = {
    projectUuidList: searchData.projectUuidList,
    selectLists: selectLists,
  }
  store.commit('local/SET_LOCAL_PROJECT_UUID_DATA', { [cacheKey.value as string]: cacheValue })
}
</script>
<style lang="scss" scoped>
.select-box {
  padding-top: 10px;
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  .async-search-container {
    display: flex;
    flex-wrap: wrap;
    max-width: 264px;
    color: #999;
  }
  .btn-add-demand {
    font-size: 14px;
    color: #fff;
    margin-right: 4px;
    padding: 2px;
  }
  .select-box-left {
    flex: 1;
    display: flex;
    .left-box {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-items: center;
      .btn_div_relative {
        display: inline-block;
        position: relative;
        height: 34px;
        line-height: 1;
        :deep(.btn_span_absolute) {
          display: inline-block;
          position: absolute;
          top: -7px;
          left: 4px;
          z-index: 10;
          padding: 0 3px;
          color: #999;
          background: #fff;
          font-size: 11px;
          -webkit-transform: scale(0.9);
          transform: scale(0.9);
        }
        .btn_span_absolute_left {
          left: 7px;
        }
      }
      :deep(.project-cust-cascader) {
        width: 120px !important;
        .fs-select-selector {
          height: 32px;
        }
      }
      :deep(.project-cust-cascader-dropdown) {
        padding-right: 0 !important;
        .fs-cascader-menu {
          border-right-color: #eee;
          &:last-child {
            border-right: none;
          }
          .fs-cascader-menu-item {
            margin-top: 2px;
            &[aria-checked='true'] {
              background: #ebf3fd;
              border-radius: 3px;
            }
          }
          .fs-cascader-menu-item-active[aria-checked='true'] {
            background: #ebf3fd;
            border-radius: 3px;
          }
        }
      }
      .marginR12 {
        margin-right: 12px;
      }
      .marginB10 {
        margin-bottom: 10px;
      }
      :deep(.cust-search-tip-box) {
        &.fs-tooltip {
          padding-top: 0;
          max-width: 100%;
        }
        .fs-tooltip-arrow {
          display: none;
        }
        .fs-tooltip-inner {
          max-width: 100%;
          padding: 16px 4px 6px 16px;
        }
      }
      .cust-search-title {
        margin-left: 6px;
        padding: 0 2px;
        height: 22px;
        line-height: 22px;
        font-size: 12px;
        border-radius: 2px;
        color: #378eef;
        cursor: pointer;
        &:hover {
          background: #f1f4f8;
        }
        &:active {
          background: #e1e8f0;
        }
        i {
          margin-left: 4px;
          font-size: 12px;
        }
      }
      .clear-search {
        height: 22px;
        margin-left: 16px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #378eef;
        cursor: pointer;
      }
    }
  }
  .export-btn {
    margin-right: 10px;
    .iconfont {
      margin-right: 4px;
    }
  }

  :deep(.fs-select-selection-placeholder) {
    padding-left: 2px !important;
  }
  :deep(.fs-select-multiple .fs-select-selection-placeholder) {
    left: 8px !important;
  }

  :deep(.fs-picker-input input) {
    padding-left: 2px !important;
  }

  :deep(.fs-input-affix-wrapper > input.fs-input) {
    padding-left: 2px !important;
  }
}

.search-tips {
  position: absolute;
  top: 100%;
  left: 0;
  line-height: 1;
  padding-top: 2px;
  color: #ed4014;
  font-size: 12px;
}

:deep.async-form-box {
  width: 500px;
  padding: 10px;
  box-sizing: border-box;
  .async-form-container {
    .ivu-form {
      display: flex;
      text-align: left;
      flex-wrap: wrap;
      justify-content: space-between;
      & > div {
        width: 230px;
      }
    }
  }
}
.async-form-footer {
  display: flex;
  justify-content: flex-end;
}
.more-btn-icon {
  height: 32px;
  width: 50px;
  font-size: 14px;
  font-weight: 400;
  color: #378eef;
  display: flex;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0;
}
.com-poptip-box {
  position: relative;
}
:deep .com-poptip-box > .ivu-poptip-popper {
  top: 36px !important;
}
:deep.com-poptip-box .ivu-poptip-arrow {
  display: none !important;
}
.select-box-right {
  display: flex;
  align-items: center;
  height: 32px;
  .data-export {
    color: #378eef;
    font-size: 14px;
    cursor: pointer;
    .iconfont {
      font-size: 12px;
    }
  }
  .download-btn {
    padding-right: 8px !important;
    .btn-line {
      display: inline-block;
      position: absolute;
      right: 30px;
      height: 100%;
      border-left-width: 1px;
      border-left-style: solid;
      border-left-color: inherit;
    }
  }
}
:deep(.fs-picker) {
  padding: 4px 8px 4px;
}
:deep(.fs-picker-suffix) {
  width: 16px;
  height: 16px;
}
:deep(.fs-picker-input-active) {
  background-color: #fff;
}
</style>

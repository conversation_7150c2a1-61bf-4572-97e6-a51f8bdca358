<template>
  <FModal width="80vw" v-model:visible="visible" title="操作记录" centered :footer="null">
    <LogTable :processId="processId" :key="processId" />
  </FModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LogTable from '@/views/approval-operate/components/LogTable.vue'

const visible = ref<boolean>(false)
const processId = ref<any>()

const onOpenFn = (id: any) => {
  processId.value = id
  visible.value = true
}

defineExpose({ onOpenFn })
</script>

<template>
  <div class="edit-text-container">
    <FTextarea v-if="status" v-model:value="inputText" ref="textareaRef" @blur="onBlurFn" />
    <div class="text" @click="onHandleStatus" v-else>
      <span>{{ text || '--' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getPgbMilepostExtendSave } from '@/api/pgbDataBoard'
import { ref, watch, nextTick } from 'vue'
import { messageInstance } from '@fs/smart-design'
import { useI18n } from '@/utils'
const i18n = useI18n()
type propsType = {
  text: string
  status: boolean
  valueKey: string
  instanceId: number
  milepostId: number
}

const props = withDefaults(defineProps<propsType>(), {
  text: '',
  status: false,
  valueKey: '',
})

const textareaRef = ref()
const inputText = ref<string>('')
const emits = defineEmits(['update:text', 'update:status'])

const onBlurFn = async (value: any) => {
  if (props.text !== inputText.value) {
    const params = {
      instanceId: props.instanceId,
      milepostId: props.milepostId,
      [props.valueKey]: inputText.value,
    }
    const res = await getPgbMilepostExtendSave(params)
    if (res.code === 200) {
      emits('update:text', inputText.value)
      messageInstance.success(i18n.t('修改成功！'))
    }
  }
  emits('update:status', false)
}

const onHandleStatus = () => {
  emits('update:status', true)
  nextTick(() => {
    console.log('getInstance  :>> ', textareaRef.value.getInstance())
    textareaRef.value.focus()
  })
}

watch(
  props,
  val => {
    val.status && (inputText.value = val.text || '')
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.text {
  display: flex;
  align-items: center;
  height: 48px;
  span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    cursor: pointer;
  }
}
</style>

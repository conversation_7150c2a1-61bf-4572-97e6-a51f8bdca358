<template>
  <div class="milepost-base-info">
    <p>
      {{ i18n.t('负责人') }}：<span>{{ props.milepost.superviser }}</span>
      <NoticeInfo
        class="cust-notice-info"
        v-if="[0, 1, 2].includes(milepost.status)"
        style="margin-left: 8px"
        :value-id="milepost.id"
      />
    </p>
    <p v-if="milepost.invalid && milepost.status === 2">
      {{ i18n.t('计划完成时间') }}：<span>{{ TFDate(props.milepost.forcastTime) }}</span>
    </p>
    <p v-else>
      {{ i18n.t('完成时间') }}：<span>{{ TFDate(props.milepost.completeTime) }}</span>
    </p>
    <p v-if="props.milepost.contentData && props.milepost.contentData.rejectMsg">
      {{ i18n.t('备注说明') }}：<span>{{ props.milepost.contentData.rejectMsg }}</span>
    </p>
  </div>
</template>
<script setup lang="ts">
import { transformDate, useI18n } from '@/utils'
import type { IProcess } from '@/types/handle'
import NoticeInfo from '@/components/NoticeInfo/index.vue'

interface IProps {
  milepost: IProcess
}

const i18n = useI18n()
const props = defineProps<IProps>()
const TFDate = (date: string | number = 0) => transformDate(date, 'YYYY-MM-DD')
</script>
<style lang="scss" scoped>
.milepost-base-info {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-bottom: 1px dashed #eee;

  > p {
    margin: 0 62px 0 0;
    padding: 16px 0;
    line-height: 1;
    color: #999;
    font-size: 12px;

    > span {
      color: #666;
    }
  }
  :deep(.cust-notice-info) {
    .iconfont {
      width: 18px !important;
      height: 18px !important;
      font-size: 12px !important;
    }
  }
}
</style>

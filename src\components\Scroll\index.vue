<template>
  <div :class="wrapClasses" style="touch-action: none">
    <div class="content-box" @wheel="onWheel" ref="scrollContainer">
      <div :class="loaderClasses" :style="{ paddingTop: wrapperPadding.paddingTop }" ref="toploader">
        <loader :text="localeLoadingText" :active="showTopLoader"></loader>
      </div>
      <div :class="slotContainerClasses" ref="scrollContent">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import loader from './loading-component.vue'
const props = defineProps({
  onReachTop: {
    type: Function,
    default: function () {
      return 'Default function'
    },
  },
  onReachBottom: {
    type: Function,
    default: function () {
      return 'Default function'
    },
  },
  loadingText: {
    type: String,
    default: '',
  },
  distanceToEdge: {
    type: Number,
    default: 0,
  },
})
const calculateProximityThreshold = () => {
  const dte = props.distanceToEdge
  if (typeof dte == 'undefined') return [20, 20]
  return Array.isArray(dte) ? dte : [dte, dte]
}

const scrollContainer = ref()
const topProximityThreshold = calculateProximityThreshold()[0]
const isLoading = ref(false)
const showTopLoader = ref(false)
const showBodyLoader = ref(false)
const topRubberPadding = ref(0)
const reachedTopScrollLimit = ref(false)
const lastScroll = ref(0)
const rubberRollBackTimeout = ref()
const prefixCls = 'ivu-scroll'
const wrapClasses = computed(() => {
  return `${prefixCls}-wrapper`
})
const slotContainerClasses = computed(() => {
  return [
    `${prefixCls}-content`,
    {
      [`${prefixCls}-content-loading`]: showBodyLoader.value,
    },
  ]
})
const loaderClasses = computed(() => {
  return `${prefixCls}-loader`
})
const localeLoadingText = computed(() => {
  if (props.loadingText == '') {
    return '加载中'
  } else {
    return props.loadingText
  }
})
const wrapperPadding = computed(() => {
  return {
    paddingTop: topRubberPadding.value + 'px',
  }
})

const onWheel = (event: any) => {
  if (isLoading.value) return
  const wheelDelta = event.wheelDelta ? event.wheelDelta : -(event.detail || event.deltaY)
  stretchEdge(wheelDelta)
}
const stretchEdge = (direction: any) => {
  clearTimeout(rubberRollBackTimeout.value)
  rubberRollBackTimeout.value = setTimeout(() => {
    if (!isLoading.value) reset()
  }, 250)
  if (direction > 0 && reachedTopScrollLimit.value) {
    topRubberPadding.value += 5 - topRubberPadding.value / 5
    if (topRubberPadding.value > topProximityThreshold) onCallback(1)
  } else {
    onScroll()
  }
}

const onCallback = (dir: number) => {
  isLoading.value = true
  showBodyLoader.value = true
  if (dir > 0) {
    showTopLoader.value = true
    topRubberPadding.value = 20
  }
  const callbacks = [waitOneSecond()]
  callbacks.push(
    dir > 0 ? (props.onReachTop ? props.onReachTop() : noop()) : props.onReachBottom ? props.onReachBottom() : noop()
  )
  let tooSlow = setTimeout(() => {
    reset()
  }, 5000)
  Promise.all(callbacks).then(() => {
    clearTimeout(tooSlow)
    reset()
  })
}
const noop = () => Promise.resolve()
const reset = () => {
  showTopLoader.value = false
  showBodyLoader.value = false
  isLoading.value = false
  reachedTopScrollLimit.value = false
  lastScroll.value = 0
  topRubberPadding.value = 0
  clearInterval(rubberRollBackTimeout.value)
}
const waitOneSecond = () => {
  return new Promise(resolve => {
    setTimeout(resolve, 1000)
  })
}
const onScroll = () => {
  const el = scrollContainer.value
  if (isLoading.value || !el) return
  const scrollDirection = Math.sign(lastScroll.value - el.scrollTop)
  const topNegativeProximity = topProximityThreshold.value < 0 ? topProximityThreshold.value : 0
  if (scrollDirection >= 0 && el.scrollTop + topNegativeProximity <= 0) {
    reachedTopScrollLimit.value = true
  }
}

defineExpose({
  getScrollContainer: () => scrollContainer.value,
})
</script>
<style lang="scss" scoped>
.ivu-scroll-wrapper {
  overflow-y: auto;
  height: 100% !important;
}
.content-box {
  height: 100%;
  overflow-y: scroll;
}
.ivu-scroll-loader {
  text-align: center;
  padding: 0;
  transition: padding 0.5s;
}
.ivu-scroll-content {
  opacity: 1;
  transition: opacity 0.5s;
  &.ivu-scroll-content-loading {
    opacity: 0.5;
  }
}
</style>

import { IColunmProps } from '.'
import { FIcon, FPopover } from '@fs/smart-design'
import { h } from 'vue'
import SetColunm from './SetColunm.vue'

export const ColumnOperationTitle = (colunmProps: IColunmProps, popoverProps: any = {}) => {
  return h(
    FPopover,
    {
      trigger: 'click',
      placement: 'bottomLeft',
      getPopupContainer: () => document.querySelector('#root') as HTMLElement,
      ...popoverProps,
      overlayClassName: popoverProps?.overlayClassName
        ? `bpm-custom-column-operated-popover ${popoverProps.overlayClassName}`
        : 'bpm-custom-column-operated-popover',
      overlayStyle: {
        padding: 0,
        ...popoverProps?.overlayStyle,
      },
    },
    {
      default: () => {
        return h(FIcon, {
          type: 'icon-guolv',
          class: 'bpm-custom-operated-icon',
        })
      },
      content: () => {
        return h(SetColunm, colunmProps)
      },
    }
  )
}

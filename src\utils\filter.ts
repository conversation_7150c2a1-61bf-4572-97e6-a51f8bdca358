import { TypeTriggerEnum } from '@/types/excutionListModel'
export const transformTrigger = (type: number | string | undefined): string => {
  if (!type) {
    return ''
  }
  let txt = ''
  switch (type) {
    case TypeTriggerEnum.TIME:
      txt = '定时执行'
      break
    case TypeTriggerEnum.TASK_FINISH:
      txt = '任务完成'
      break
    case TypeTriggerEnum.NODE_FININSH:
      txt = '节点完成'
      break
    case TypeTriggerEnum.UPDATE:
      txt = '值更新'
      break
    case TypeTriggerEnum.PROCESS_CREATE:
      txt = '新建流程'
      break
    case TypeTriggerEnum.NODE_SAVE:
      txt = '保存节点'
      break
    case TypeTriggerEnum.TASK_SAVE:
      txt = '保存任务'
      break
    case TypeTriggerEnum.WORK_FLOW_FININSH:
      txt = '流程办结'
      break
    case TypeTriggerEnum.TASK_POOL_FININSH:
      txt = '任务池提交'
  }
  return txt
}
export const transformFieldIdToLabel = (
  list: Array<any>,
  formField: string,
  toField: string,
  text: number | string
) => {
  const processType = list?.filter(item => item[formField] == text)[0]
  if (processType) {
    return processType[toField]
  } else {
    return text
  }
}

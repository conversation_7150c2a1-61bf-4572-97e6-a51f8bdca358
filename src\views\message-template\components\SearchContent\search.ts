import { reactive } from 'vue'
/**
 * @description: 搜索类
 * @class Search
 * @type {options} 筛选配置项，
 * @type {clearFlag} 是否显示清空全部按钮
 * @private {checkConfig} 检查传入筛选项配置是否合规
 * @method {initOptions} 初始化筛选项配置
 * @method {addOptions} 添加筛选项配置
 * @method {deteleOptions} 删除筛选项配置
 * @method {getOptions} 获取筛选项中指定属性值
 * @method {setOptions} 设置筛选项中指定属性值
 * @method {getParams} 获取当前筛选数据
 * @method {clear} 清空当前筛选数据
 * @method {checkClearStatus} 获取当前清空按钮状态
 * @method {getCacheSearch} 获取当前筛选数据作为本地缓存筛选项
 * @method {setDefaultSearch} 初始化设置当前筛选数据，可配合做本地缓存
 * @example
 * const search = new Search(CACHE_KEY)
 * search.initOptions([
  *  {
        componentName?: string // 组件名，可使用组件库相关组件，也可自定义组件传入使用
        componentValueKey?: string // 获取数据的key名
        componentValue?: any // 获取数据的value值
        componentArgsValue?: any // 当筛选项需要有value之外的数据需要存储时可使用该属性存储
        componentAttrs?: any // 组件属性方法
        getComponentValueFormat?: any // 获取数据的value值需要自定义格式化使用的方法
        setComponentValueFormat?: any // // 设置数据的默认value值需要自定义格式化使用的方法
        clearComponentValueFn?: any // 清空全部数据时有复杂的清空逻辑使用的方法
        [key: string]: any
      }
 * ])
 * search.addOptions([
 *  {
        componentName?: string // 组件名，可使用组件库相关组件，也可自定义组件传入使用
        componentValueKey?: string // 获取数据的key名
        componentValue?: any // 获取数据的value值
        componentArgsValue?: any // 当筛选项需要有value之外的数据需要存储时可使用该属性存储
        componentAttrs?: any // 组件属性方法
        getComponentValueFormat?: any // 获取数据的value值需要自定义格式化使用的方法
        setComponentValueFormat?: any // // 设置数据的默认value值需要自定义格式化使用的方法
        clearComponentValueFn?: any // 清空全部数据时有复杂的清空逻辑使用的方法
        [key: string]: any
      }
 * ])
 * search.deteleOptions([componentValueKey1, componentValueKey2])
 * search.getOptions('tag.componentAttrs.options') 入参为options中属性名，多层级以 "·" 选方式传入
 * search.setOptions('tag.componentAttrs.options', [{ label: '进行中', value: 0 }]) 入参为options中属性名，多层级以 "·" 选方式传入, 值为属性所需格式的值，大多用为修改筛选项值和下拉组件选项值
 * search.getParams() 返回当前筛选项选中值
 * search.clear() 清空当前筛选项
 * search.checkClearStatus() 获取当前清空按钮状态
 * search.getCacheSearch() 获取当前筛选数据作为本地缓存筛选项
 * search.setDefaultSearch() 初始化设置当前筛选数据，可配合做本地缓存
 */

export interface ISearchItemConfig {
  componentName?: string // 组件名，可使用组件库相关组件，也可自定义组件传入使用
  componentValueKey?: string // 获取数据的key名
  componentValue?: any // 获取数据的value值
  componentArgsValue?: any // 当筛选项需要有value之外的数据需要存储时可使用该属性存储
  componentAttrs?: any // 组件属性方法
  getComponentValueFormat?: any // 获取数据的value值需要自定义格式化使用的方法
  setComponentValueFormat?: any // // 设置数据的默认value值需要自定义格式化使用的方法
  clearComponentValueFn?: any // 清空全部数据时有复杂的清空逻辑使用的方法
  [key: string]: any
}

interface IOptions {
  [key: string]: ISearchItemConfig
}

export class Search {
  public options: IOptions
  public clearFlag: boolean

  constructor() {
    this.options = reactive<IOptions>({})
    this.clearFlag = false
  }

  private checkConfig(config: any): boolean {
    if (!config || !config?.componentName || !config?.componentValueKey) return false
    return true
  }

  public checkClearStatus() {
    this.clearFlag = Object.values(this.options).some(value => {
      return value.componentValue !== undefined && value.componentValue !== null && value.componentValue !== ''
    })
  }

  public async initOptions(config: any): Promise<void> {
    for (const key in this.options) {
      delete this.options[key]
    }
    config.forEach((item: any) => {
      if (this.checkConfig(item) && !Object.prototype.hasOwnProperty.call(this.options, item.componentValueKey)) {
        this.options[item.componentValueKey] = item
      }
    })
  }

  public async addOptions(config: any): Promise<void> {
    config.forEach((item: any) => {
      if (this.checkConfig(item) && !Object.prototype.hasOwnProperty.call(this.options, item.componentValueKey)) {
        this.options[item.componentValueKey] = item
      }
    })
  }

  public deteleOptions(keys: any): void {
    keys.forEach((item: any) => {
      Object.prototype.hasOwnProperty.call(this.options, item) && delete this.options[item]
    })
  }

  public getOptions(path: string): any {
    let options = this.options
    path = path.replace(/\[(\w+)\]/g, '.$1')
    path = path.replace(/^\./, '')
    const list = path.split('.')
    for (let i = 0, l = list.length; i < l; ++i) {
      const n = list[i]
      if (n in options) {
        options = options[n]
      } else {
        return
      }
    }
    return options
  }

  public setOptions(path: string, value: any): void {
    let schema = this.options
    const pList = path.split('.')
    const len = pList.length
    for (let i = 0; i < len - 1; i++) {
      const elem = pList[i]
      if (!schema[elem]) {
        schema[elem] = {}
      }
      schema = schema[elem]
    }

    schema[pList[len - 1]] = value
  }

  public getParams(): any {
    const params: any = {}
    Object.values(this.options).forEach(value => {
      ;(value.getComponentValueFormat &&
        Object.assign(
          params,
          value.getComponentValueFormat(value?.componentValue ?? undefined, value?.componentArgsValue || null)
        )) ||
        (params[value.componentValueKey as keyof any] = value.componentValue ?? undefined)
    })
    this.checkClearStatus()
    return params
  }

  public clear(): void {
    Object.entries(this.options).forEach(([key, value]) => {
      value.componentValue = undefined
      value.componentArgsValue && (value.componentArgsValue = {})
      value.clearComponentValueFn && value.clearComponentValueFn()
    })
    this.clearFlag = false
  }

  public getCacheSearch(): any {
    const cachData: Record<string, any> = {}
    Object.entries(this.options).forEach(([key, value]) => {
      if ((value.componentValue !== undefined && value.componentValue !== null) || value?.componentArgsValue) {
        cachData[key] = {}
        value.componentValue !== undefined &&
          value.componentValue !== null &&
          (cachData[key].componentValue = value.componentValue)
        value.componentArgsValue && (cachData[key].componentArgsValue = value.componentArgsValue)
      }
    })
    return cachData
  }

  public setDefaultSearch(data: any): void {
    Object.entries(data).forEach(([key, value]) => {
      value && this.options[key]?.setComponentValueFormat(value)
    })
  }
}

<template>
  <div class="i18n-warpper">
    <FDropdown placement="bottom">
      <div class="i18n-icon-warpper">
        <FIcon type="icon-tubiao_yuyan<PERSON>ehuan" @click.prevent />
      </div>
      <template #overlay>
        <FMenu class="i18n-menu-warpper" v-model:selectedKeys="currentLanguage">
          <FMenuItem key="en" @click="handleClick('en')">
            <template #icon><FIcon type="icon-english" /></template>English
          </FMenuItem>
          <FMenuItem key="zh-CN" @click="handleClick('zh-CN')">
            <template #icon><FIcon type="icon-tubiao_zhongwen" /></template>简体中文
          </FMenuItem>
        </FMenu>
      </template>
    </FDropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useTranslator } from '@/translator'

const store = useStore()
const currentLanguage = ref([store.getters.language])
const translator = useTranslator()

const handleClick = async (language: string) => {
  store.commit('global/SET_LANGUAGE', language)
  if (language === 'zh-CN') {
    translator.restore()
  } else {
    // i18n.setLanguage(language)
    // microSendData(window.__MICRO_APP_NAME__, { language })
    translator.setToLang(language)
  }
}

onMounted(() => {
  currentLanguage.value[0] !== 'zh-CN' && translator.setToLang(currentLanguage.value[0])
})
</script>

<style scoped lang="scss">
.i18n-warpper {
  .i18n-icon-warpper {
    display: flex;
    width: 24px;
    height: 24px;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    background-color: #fff;

    &:hover {
      background-color: #f1f4f8;
    }
  }

  :deep(.fs-action-icon) {
    position: relative;
    display: flex;
    width: 18px;
    height: 18px;
    font-size: 18px;
    justify-content: center;
    align-items: center;
    background-color: transparent;

    > svg {
      z-index: 1;
    }
  }
}

.i18n-menu-warpper {
  :deep(.fs-dropdown-menu-item-selected) {
    color: #378eef !important;
    background-color: #ebf3fd !important;
  }
}
</style>

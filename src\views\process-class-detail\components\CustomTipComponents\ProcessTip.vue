<template>
  <div style="width: max-content;">
    <FTooltip 
      :title="tooltipContent" 
      placement="topRight" 
      color="#000"
      :destroy-tooltip-on-hide="true"
    >
      <span style="margin-left: 4px; cursor: pointer;">
        <i class="iconfont icontubiao_tishi_mian" style="margin-right: 2px; font-size: 14px; color: #1890ff;" />
        <span style="color: #1890ff; font-size: 12px;">{{ displayText }}</span>
      </span>
    </FTooltip>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  fieldKey?: string
  fieldValue?: any
  fieldConfig?: any
  formData?: any
  // 自定义属性
  baseText?: string
  showValue?: boolean
  valuePrefix?: string
  valueSuffix?: string
}

const props = withDefaults(defineProps<Props>(), {
  baseText: '提示信息',
  showValue: false,
  valuePrefix: '当前值：',
  valueSuffix: ''
})

// 计算显示文本
const displayText = computed(() => {
  if (props.showValue && props.fieldValue !== undefined && props.fieldValue !== null && props.fieldValue !== '') {
    return `${props.baseText}(${props.valuePrefix}${props.fieldValue}${props.valueSuffix})`
  }
  return props.baseText
})

// 计算提示内容
const tooltipContent = computed(() => {
  const lines = []
  
  // 基础信息
  lines.push(`字段：${props.fieldConfig?.label || props.fieldKey}`)
  
  // 当前值
  if (props.fieldValue !== undefined && props.fieldValue !== null && props.fieldValue !== '') {
    lines.push(`当前值：${props.fieldValue}`)
  } else {
    lines.push('当前值：未设置')
  }
  
  // 字段类型
  if (props.fieldConfig?.dataType) {
    lines.push(`类型：${props.fieldConfig.dataType}`)
  }
  
  // 是否必填
  if (props.fieldConfig?.required) {
    lines.push('必填：是')
  }
  
  // 自定义提示内容
  if (props.baseText !== '提示信息') {
    lines.push(`说明：${props.baseText}`)
  }
  
  return lines.join('\n')
})
</script>

<style scoped>
.custom-tip {
  cursor: pointer;
}
</style>

<template>
  <div class="test-result-container">
    <FModal
      width="80vw"
      v-model:visible="visible"
      :title="i18n.t('执行表达式')"
      :confirmLoading="confirmLoading"
      @cancel="cancelFn"
      @ok="submitFn"
    >
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FRow :gutter="[24, 0]">
          <FCol :span="8">
            <FFormItem :label="i18n.t('流程 InstanceId')" name="instanceId">
              <FInput
                style="width: 100%"
                v-model:value="formState.instanceId"
                :placeholder="i18n.t('请输入流程 InstanceId')"
              />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('BPM表达式')" name="content">
              <FInput style="width: 100%" v-model:value="formState.content" :placeholder="i18n.t('请输入表达式')" />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('审批表达式')" name="content">
              <FInput
                style="width: 100%"
                v-model:value="formState.approvalContent"
                :placeholder="i18n.t('请输入表达式')"
              />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <div style="margin-bottom: 8px">{{ i18n.t('请求结果：') }}</div>
            <i
              v-if="resultData"
              class="iconfont icontubiao_fuzhi copy-text copy-btn"
              :data-clipboard-text="resultData"
              @click="onCopyFn"
            ></i>
            <pre class="result-box">{{ resultData || '--' }}</pre>
          </FCol>
        </FRow>
      </FForm>
      <div></div>
    </FModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useI18n } from '@/utils'
import { MessageTemplateParams } from '@/types/messageTemplate'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { message } from '@fs/smart-design'
import { execute } from '@/api'
import Clipboard from 'clipboard'

interface IProps {
  visible: boolean
}

const i18n = useI18n()
const props = defineProps<IProps>()
const emits = defineEmits(['update:visible'])
const confirmLoading = ref<boolean>(false)
const visible = computed({
  get: () => props.visible,
  set: val => emits('update:visible', val),
})
const formRef = ref<FormInstance>()
const formState = reactive<MessageTemplateParams>({
  instanceId: undefined,
  content: 'default',
  approvalContent: 'default',
})
const rules: Record<string, Rule[]> = {
  instanceId: [{ required: true, message: i18n.t('请输入流程 InstanceId') }],
  content: [{ required: true, message: i18n.t('请输入BPM表达式') }],
  approvalContent: [{ required: true, message: i18n.t('请输入审批表达式') }],
}
const resultData = ref()

const onCopyFn = () => {
  const clipboard = new Clipboard('.copy-text')
  clipboard.on('success', e => {
    message.success(i18n.t('复制成功'))
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    message.warning(i18n.t('复制失败'))
    clipboard.destroy()
  })
}

const cancelFn = () => {
  formRef.value?.resetFields()
  resultData.value = ''
  visible.value = false
}

const submitFn = async () => {
  try {
    confirmLoading.value = true
    if (!formRef.value) {
      return
    }
    await formRef.value.validate()
    const res = await execute(formState)
    if (res.code !== 200) throw new Error(res.msg)
    resultData.value = JSON.stringify(res, null, '\t')
  } finally {
    confirmLoading.value = false
  }
}
</script>
<style scoped lang="scss">
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
.result-box {
  max-height: 360px;
  padding: 12px 16px;
  background: #ebf3fd;
  border-radius: 3px;
  border: 1px solid #afd1f8;
  color: #333333;
  font-size: 12px;
}
.copy-btn {
  position: absolute;
  top: 30px;
  right: 20px;
  cursor: pointer;
}
</style>

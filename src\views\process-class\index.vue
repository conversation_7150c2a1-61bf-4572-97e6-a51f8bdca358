<template>
  <div>
    <div class="select-options">
      <FForm layout="inline">
        <FFormItem>
          <FInput
            placeholder="请输入流程名称"
            press-line="流程名称"
            class="width240"
            v-model:value="form.processName"
            type="search-clear"
            allow-clear
            @search="searchDictionary"
            @clear="searchDictionary"
          />
        </FFormItem>
        <FFormItem>
          <FInput
            placeholder="请输入流程编码"
            press-line="流程编码"
            class="width240"
            v-model:value="form.processCode"
            type="search-clear"
            allow-clear
            @search="searchDictionary"
            @clear="searchDictionary"
          />
        </FFormItem>

        <FFormItem class="width240" v-if="hasEnvPublish">
          <FSelect
            v-model:value="form.publishStatus"
            style="width: 240px"
            press-line="发布状态"
            placeholder="发布状态"
            allow-clear
            :options="[
              { label: '未发布', value: 0 },
              { label: '已发布', value: 1 },
            ]"
            @change="searchDictionary"
          />
        </FFormItem>
      </FForm>
    </div>
    <div class="fei-su-card">
      <div class="card-btn">
        <div class="fei-su-title">流程类型</div>
        <div>
          <FButton
            v-if="hasPublish"
            type="default"
            :disabled="selectedRowKeys.length === 0"
            class="add-button marginR12"
            @click="publishVisible = true"
          >
            <i class="iconpiliangtianjia iconfont marginR5 fontSize14"></i>批量发布
          </FButton>
          <FButton type="primary" class="add-button" @click="addProject">
            <i class="iconxinzeng iconfont marginR5 fontSize14"></i>新建流程配置
          </FButton>
        </div>
      </div>
      <FTable
        :data-source="dataList"
        :columns="columns"
        :scroll="{ x: 'max-content' }"
        :pagination="false"
        :row-selection="getRowSelectionConfig()"
        row-key="processCode"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key === 'publishStatus'">
            <span v-if="record.publishStatus == 1" class="enablePublish">已发布</span>
            <span v-if="record.publishStatus == 0" class="disablePublish">未发布</span>
          </template>
          <template v-if="column.key === 'invalid'">
            <span :class="['stasus', record.invalid == 1 ? '' : 'no-status']">
              {{ record.invalid == 1 ? '有效' : '无效' }}
            </span>
          </template>
          <template v-if="column.key === 'taskEdit'">
            <span :class="['stasus', record.taskEdit == 1 ? '' : 'no-status']">
              {{ record.taskEdit == 1 ? '开启' : '关闭' }}
            </span>
          </template>
          <template v-if="column.key === 'isSendmsg'">
            <span :class="['stasus', record.isSendmsg == 1 ? '' : 'no-status']">
              {{ record.isSendmsg == 1 ? '开启' : '关闭' }}
            </span>
          </template>
          <template v-if="column.key === 'isNotice'">
            <span :class="['stasus', record.isNotice == 1 ? '' : 'no-status']">
              {{ record.isNotice == 1 ? '开启' : '关闭' }}
            </span>
          </template>
          <template v-if="column.key === 'isDelayReason'">
            <span :class="['stasus', record.isDelayReason == 1 ? '' : 'no-status']">
              {{ record.isDelayReason == 1 ? '开启' : '关闭' }}
            </span>
          </template>
          <template v-if="column.key === 'isUpdateCompleteSend'">
            <span :class="['stasus', record.isUpdateCompleteSend == 1 ? '' : 'no-status']">
              {{ record.isUpdateCompleteSend == 1 ? '开启' : '关闭' }}
            </span>
          </template>
          <template v-if="column.key === 'source'">
            <span :style="{ color: ['red', 'green', 'blue'][record.source] }">
              {{
                record.source == 1
                  ? 'BPM'
                  : record.source == 2
                  ? '合同'
                  : record.source == 3
                  ? '权限'
                  : record.source == 4
                  ? '审批流'
                  : '其他'
              }}
            </span>
          </template>
          <template v-if="column.key === 'createdTime'">{{ dayjs(record.createdTime).format('YYYY-MM-DD') }}</template>
          <template v-if="column.key === 'updateTime'">{{ dayjs(record.updateTime).format('YYYY-MM-DD') }}</template>
          <template v-if="column.key === 'action'">
            <span
              class="iconfont-hover iconfont cursor color4677C7 icontubiao_xietongbianji marginR5"
              title="编辑"
              @click="handleUpdate(record)"
            ></span>
            <span
              class="iconfont-hover iconfont icontubiao_sousuo1 cursor color4677C7 marginR5"
              title="配置筛选项"
              @click="setSeachConfig(record)"
            ></span>
            <span
              class="iconfont-hover iconfont iconpeizhimobanicon cursor color4677C7 fontSize14 marginR5"
              title="流程配置管理"
              @click="goProcessManage(record)"
            ></span>
            <span
              v-if="hasPublishReset && record.publishTime && record.publishStatus === 0"
              class="iconfont-hover iconfont icontubiao_shuaxin2 cursor color4677C7 fontSize14 marginR5"
              title="重置"
              @click="reset(record)"
            ></span>
            <TipBtn
              has-pop
              :tip-title="'删除'"
              v-if="record.publishTime === null"
              :pop-title="'确定删除选中的消息模板吗？'"
            >
              <i class="iconfont color4677C7 icontubiao_xietongshanchu hover-btn"></i>
            </TipBtn>
          </template>
          <template v-if="['finishDesc', 'remark', 'rejectDesc'].includes(column.key)">
            <MoreTextTips :line-clamp="3">
              <span>{{ text || '--' }}</span>
            </MoreTextTips>
          </template>
          <template v-if="['admins'].includes(column.key)">
            <span>{{ (text && getAdminName(text)) || '--' }}</span>
          </template>
        </template>
        <template #emptyText>
          <img :src="noImgUrl" />
          <p class="colorBBB fontSize12 fs-tip">暂无流程类型~</p>
        </template>
      </FTable>
      <div class="fei-su-pagination">
        <FPagination
          v-model:current="paging.pageNum"
          @change="onChange"
          v-model:page-size="paging.pageSize"
          :total="paging.total"
          show-size-changer
          :show-total="() => `共 ${paging.total} 条`"
        />
      </div>
    </div>
    <AddClass :title="title" :show="show" :record="recordList" @submit="submitClass" @popup-close="close"></AddClass>
    <PublishModal v-model:value="publishVisible" :publish-options="publishEnvOptions" @submit="allPublish" />
  </div>
</template>

<script setup lang="ts">
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import AddClass from './components/AddClass.vue'
import noImgUrl from '@/assets/images/no-data.png'
import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { ref, onMounted, computed, provide } from 'vue'
import {
  getQueryClassList,
  updateClass,
  createClass,
  selectFieldDictionaryItem,
  synProcessConfig,
  rollbackProcessConfig,
} from '@/api'
import { messageInstance as message } from '@fs/smart-design'
import MoreTextTips from '@/components/MoreTextTips/index'
import type { IClassData } from '@/types/class'
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { StoreUserType } from '@/store/modules/user'
import { getValueFn } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'
import PublishModal from '@/components/PublishModal/index.vue'
import usePublish from '@/hooks/usePublish'

const { hasEnvPublish, hasPublish, hasPublishReset, publishEnvOptions, publishVisible } = usePublish()
interface IPage {
  pageNum: number // 当前页
  pageSize: number // 每页条数
  total: number // 总条数
}
interface formData {
  processName: null | string
  processCode: null | string
  source?: any
  publishStatus?: any
}
const route = useRouter()
const paging = ref<IPage>({ pageNum: 1, pageSize: 10, total: 0 })
const recordList = ref({})
const processTypeOptions = ref<any[]>([])
const title = ref<string>('')
const loading = ref<boolean>(false)
const show = ref(false)
const form = ref<formData>({
  processName: '',
  processCode: '',
  source: undefined,
  publishStatus: undefined,
})
// 表格勾选配置项
const selectedRowKeys = ref<string[]>([])

// 表格勾选
function onSelectChange(keys: string[], _selectedRows: object[]) {
  selectedRowKeys.value = keys
}
const getRowSelectionConfig = () => {
  return {
    type: 'radio',
    selectedRowKeys: selectedRowKeys.value,
    onChange: onSelectChange,
    getCheckboxProps: record => ({
      disabled: record.publishStatus === 1,
    }),
  }
}
const reset = record => {
  rollbackProcessConfig({
    type: 1,
    codeList: [record.processCode],
  }).then(res => {
    if (res.code == 200) {
      message.success('重置成功')
      fetchData()
    }
  })
}
const allPublish = (publishData: any) => {
  synProcessConfig({
    type: publishData?.type || 1,
    codeList: selectedRowKeys.value,
  }).then(res => {
    if (res.code == 200) {
      message.success('批量发布成功')
      fetchData()
    }
  })
}
const columns = ref<any>(
  [
    { title: '流程名称', dataIndex: 'processName', key: 'processName', fixed: 'left' },
    { title: '流程编码', dataIndex: 'processCode', key: 'processCode' },
    { title: 'ID', dataIndex: 'id', key: 'processCode' },
    { title: '对应配置表标签', dataIndex: 'tag', key: 'tag' },
    { title: '流程编号前缀', dataIndex: 'prefix', key: 'prefix' },
    hasEnvPublish.value && { title: '发布状态', dataIndex: 'publishStatus', key: 'publishStatus' },
    { title: '普通权限标识', dataIndex: 'readMark', key: 'readMark' },
    { title: '高级权限标识', dataIndex: 'writeMark', key: 'writeMark' },
    { title: '时效管理', dataIndex: 'invalid', key: 'invalid' },
    { title: '飞书消息开关', dataIndex: 'isSendmsg', key: 'isSendmsg' },
    { title: '延期通知', dataIndex: 'isNotice', key: 'isNotice' },
    { title: '填写延期原因', dataIndex: 'isDelayReason', key: 'isDelayReason' },
    { title: '更新预计完成时间发送消息', dataIndex: 'isUpdateCompleteSend', key: 'isUpdateCompleteSend' },
    { title: '删除权限标识', dataIndex: 'isDeleteMake', key: 'isDeleteMake' },
    { title: '开放任务编辑', dataIndex: 'taskEdit', key: 'taskEdit' },
    { title: '来源', dataIndex: 'source', key: 'source' },
    { title: '流程编码', dataIndex: 'processCode', key: 'processCode' },
    { title: '流程类型', dataIndex: 'businessType', key: 'businessType' },
    { title: '流程管理员', dataIndex: 'admins', key: 'admins' },
    { title: '概要信息', dataIndex: 'outline', key: 'outline' },
    { title: '流程终止标签', dataIndex: 'finishDesc', key: 'finishDesc', width: 210 },
    { title: '流程驳回标签', dataIndex: 'rejectDesc', key: 'rejectDesc', width: 210 },
    { title: '流程链接', dataIndex: 'detailsUrl', key: 'detailsUrl' },
    { title: '创建时间', dataIndex: 'createdTime', key: 'createdTime' },
    { title: '修改时间', dataIndex: 'updateTime', key: 'updateTime' },
    { title: '备注', dataIndex: 'remark', key: 'remark', width: 300 },
    { title: '操作', dataIndex: 'action', key: 'action', fixed: 'right' },
  ].filter(Boolean)
)
const dataList = ref<IClassData[]>([])
const store = useStore()
const userList = computed<StoreUserType[]>(() => store.state.user.allUser || [])

const getAdminName = (admins: string) => {
  return admins
    .split(',')
    .map(item => {
      return getValueFn(userList.value, item, 'uuid')?.name || ''
    })
    .join(',')
}
onMounted(() => {
  fetchData()
})
const searchDictionary = () => {
  paging.value.pageNum = 1
  fetchData()
}
const handleUpdate = (record: any) => {
  title.value = '编辑流程配置'
  show.value = true
  recordList.value = record
}
const close = () => {
  show.value = false
}
const addProject = () => {
  show.value = true
  title.value = '新增流程配置'
}
const fetchData = async () => {
  try {
    // if (!params) {
    //   params = paramsWithoutPage()
    // }
    loading.value = true
    const { processName, source, publishStatus, processCode } = form.value
    const parmas = {
      processName,
      processCode,
      source,
      publishStatus,
      pageNum: paging.value.pageNum,
      pageSize: paging.value.pageSize,
    }
    const res = await getQueryClassList(parmas)
    if (res.code == 200) {
      dataList.value = res.data.list
      paging.value.total = res.data.totalCount
    }
  } finally {
    loading.value = false
    selectedRowKeys.value = []
  }
}
const submitClass = async (data: any, title: string) => {
  let res
  if (title == '编辑流程配置') {
    res = await updateClass(data)
  } else {
    delete data.id
    res = await createClass(data)
  }
  if (res.code == 200) {
    title == '编辑流程配置' ? message.success('编辑成功') : message.success('新增成功')
    show.value = false
    fetchData()
  } else {
    message.error(res.msg)
  }
}
const onChange = (current: number, pageSize: number) => {
  paging.value.pageSize = pageSize
  fetchData()
}
// const paramsWithoutPage = () => {
//   let params: any = {}
//   params = lodash.cloneDeep(form.value)
//   for (const k in params) {
//     if (params[k] === -1 || params[k] === '') {
//       params[k] = ''
//     }
//   }
//   params.currPage = paging.value.page
//   params.pageSize = paging.value.pageSize
//   return params
// }

const setSeachConfig = (record: any) => {
  const query: { id?: string | number } = {}
  record.id && (query.id = record.id)
  route.push({
    name: 'setSeachConfig',
    query,
  })
}

const goProcessManage = (record: any) => {
  const params: { id?: string | number } = {}
  record.id && (params.id = record.id)
  route.push({
    name: 'processClassDetail',
    params,
  })
}

const getFieldItem = async () => {
  const res = await selectFieldDictionaryItem('process_group')
  processTypeOptions.value = res?.data || []
}

onMounted(() => {
  requestIdleCallback(getFieldItem)
})

provide('processTypeOptions', processTypeOptions)
</script>

<style lang="scss" scoped>
:deep(.fei-su-title) {
  margin-bottom: 0;
}

:deep(.select-options) {
  padding: 24px !important;
}
// .fs-form-inline {
//   justify-content: space-between;
// }

:deep(.fs-col) {
  .fs-form-item-label {
    padding-bottom: 8px !important;
  }
}

.card-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

:deep(.fs-modal-body) {
  padding: 24px 24px 0 24px !important;
  .fs-input-affix-wrapper {
    padding-left: 8px !important;
  }
  .fs-textarea .fs-form-item-control-input-content {
    height: 50px !important;
  }
}

:deep(.fs-table-tbody) {
  > tr:hover:not(.fs-table-expanded-row) > td,
  .fs-table-row-hover,
  .fs-table-row-hover > td {
    background: #f1f4f8 !important;
  }
}
.stasus {
  display: inline-block;
  padding: 0 3px;
  background: #eafaf2;
  border-radius: 2px;
  border: 1px solid #2fcc83;
  font-weight: 400;
  color: #2fcc83;
  line-height: 18px;
  font-size: 12px;
  white-space: nowrap;
  &.no-status {
    border: 1px solid #f04141;
    background: #fdecec;
    color: #f04141;
  }
}
.enablePublish {
  display: inline-block;
  width: 44px;
  height: 18px;
  text-align: center;
  background: #eafaf2;
  border-radius: 3px;
  font-size: 12px;
  color: #2fcc83;
  line-height: 18px;
}
.disablePublish {
  display: inline-block;
  width: 44px;
  height: 18px;
  text-align: center;
  background: #eeeeee;
  border-radius: 3px;
  font-size: 12px;
  color: #999999;
  line-height: 18px;
}
</style>

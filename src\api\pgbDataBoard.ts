import { IResData, SuccessIRes } from '@/types/handle'
import {
  ProcessListParams,
  ProcessListResponse,
  countResponse,
  MilepostExtendSaveParams,
  ScreenedResponse,
} from '@/types/pgbDataBoard'
import { request } from '@/utils'

export function getPgbProcessList(data: ProcessListParams): Promise<ProcessListResponse> {
  return request.post('/api/pbg/page', data)
}

export function getPgbCount(): Promise<countResponse> {
  return request.get('/api/pbg/count')
}

export function getPgbScreened(): Promise<ScreenedResponse> {
  return request.get('/api/pbg/screened')
}

export function getPgbExport(data: ProcessListParams): Promise<IResData> {
  return request.post('/api/pbg/export', data)
}

export function getPgbMilepostExtendSave(data: MilepostExtendSaveParams): Promise<SuccessIRes> {
  return request.post('/api/pbg/milepostExtend/save', data)
}

export function getPgbMilepostbatchDownload(data: string[]): Promise<IResData> {
  return request.post('/api/file/batchDownload', data)
}

<template>
  <div class="message-item-box">
    <div class="message-left">
      <div class="header">
        <img :src="defaultfImg" />
      </div>
    </div>
    <div class="message-right">
      <template v-if="!messageData?.messageOpType">
        <div class="info">
          <p class="name" :title="messageData.sendBy">
            <span>{{ messageData.sendBy }} </span>
          </p>
          <p class="time">{{ transformDate(messageData.sendDate, 'YYYY-MM-DD HH:mm') }}</p>
          <template v-if="messageData.sendByUuid">
            <FTooltip>
              <template #title>{{ i18n.t('回复') }}</template>
              <i
                class="iconfont icontubiao_xiaoxi iconfont-hover cursor color4677C7 icon-box"
                @click="sendMessageFn"
              ></i>
            </FTooltip>
            <FPopconfirm
              overlay-class-name="z-pover"
              :ok-text="i18n.t('确定')"
              :cancel-text="i18n.t('取消')"
              @confirm="handleMessageFn"
              :get-popup-container="(triggerNode: Element) => { return triggerNode}"
            >
              <template #title>
                <p class="color333 fontSize14 marginB5">{{ i18n.t('确定加急给被@人吗？') }}</p>
                <div class="color999 fontSize12">{{ i18n.t('点击确定后将触发飞书提醒') }}</div>
              </template>
              <FTooltip>
                <template #title>{{ i18n.t('加急') }}</template>
                <i class="iconfont iconfont-hover cursor color4677C7 iconcaidanbeifen icon-box"></i>
              </FTooltip>
            </FPopconfirm>
          </template>
          <FPopconfirm
            v-if="revocationFlag"
            overlay-class-name="z-pover"
            :ok-text="i18n.t('确定')"
            :cancel-text="i18n.t('取消')"
            @confirm="revocationMessageFn"
            :get-popup-container="(triggerNode: Element) => { return triggerNode}"
          >
            <template #title>
              <p class="color333 fontSize14 marginB5">{{ i18n.t('确定撤回消息吗？') }}</p>
              <div class="color999 fontSize12">{{ i18n.t('撤回消息后将不可恢复') }}</div>
            </template>
            <FTooltip v-if="messageItem?.messageOpType === 0 && revocationFlag">
              <template #title>{{ i18n.t('撤回') }}</template>
              <i class="iconfont icontubiao_chehui iconfont-hover cursor color4677C7 icon-box"></i>
            </FTooltip>
          </FPopconfirm>
        </div>
        <div class="message-txt">
          <MyViewer :html="props.messageItem.content" />
          <a v-if="props.isGoto" @click="onJumpDemandDetial(props.messageItem)">
            [{{ props.messageItem.processType }}-{{ props.messageItem.projectNum }}]
          </a>
        </div>
        <div class="message-file" v-if="messageData.file && props.messageItem.file.length">
          <div class="file" v-for="file in props.messageItem.file" :key="file.resourseKey">
            <span class="iconfont icon">&#xe655;</span>
            <a class="txt" :title="file.name" @click="download(file.resourseKey, file.name)">{{ file.name }}</a>
            <span class="file-size">({{ (file.size / 1024).toFixed(2) }} KB)</span>
            <i
              v-if="getPreviewShow(file.name)"
              class="iconfont icontubiao_yanjing cursor color4677C7"
              @click="handlePreview(file.resourseKey)"
            ></i>
          </div>
        </div>
        <div class="message-call" v-if="messageItem.messageReadRsps && messageItem.messageReadRsps.length > 0">
          <template v-if="messageItem.messageReadRsps && messageItem.messageReadRsps.length == 1">
            <span class="call-people">@ {{ messageItem.messageReadRsps[0].receiver }}</span>
            <span class="txt-read is-read-color" v-if="messageItem.messageReadRsps[0].isRead === 0">{{
              i18n.t('已读')
            }}</span>
            <span class="txt-read" v-if="messageItem.messageReadRsps[0].isRead === 1">{{ i18n.t('未读') }}</span>
          </template>
          <template v-if="messageItem.messageReadRsps && messageItem.messageReadRsps.length > 1">
            <ReadingPop :reading-list="messageItem.messageReadRsps.filter(item => item.isRead === 0)" :reading="true" />
            <ReadingPop
              :reading-list="messageItem.messageReadRsps.filter(item => item.isRead === 1)"
              :reading="false"
            />
          </template>
        </div>
      </template>
      <template v-else>
        <div class="info" :class="editFlag ? '' : 'no-edit'">
          <p class="name" :title="messageData.sendBy">
            <span>{{ messageData.sendBy }} </span>
            <span class="revocation-text" :title="messageData.content">{{ messageData.content }}</span>
          </p>
          <p class="time">{{ transformDate(messageData.sendDate, 'MM-DD HH:mm') }}</p>
        </div>
        <div class="message-txt edit-text" v-if="editFlag" @click="editMessageFn">{{ i18n.t('重新编辑') }}</div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { IMessage } from '@/types/request'
import { transformDate, download } from '@/utils'
import MyViewer from '@/components/Editor/MyViewer.vue'
import defaultfImg from '@/assets/images/head.png'
import ReadingPop from './ReadingPop.vue'
import { useI18n, getUserInfo } from '@/utils'
import { urgentMessage, batchWithdraw, editMessage } from '@/api'
import { messageInstance } from '@fs/smart-design'
import dayjs from 'dayjs'

interface IProps {
  isGoto?: boolean
  messageItem: IMessage
}

const emits = defineEmits(['on-select-role-fn', 'on-set-content-fn', 'on-init'])

const i18n = useI18n()
const router = useRouter()
const props = defineProps<IProps>()
const messageData = computed(() => props.messageItem ?? {})
const userInfo = getUserInfo()
let timer = ref<any>(null)
let time = ref<boolean>(false)
const revocationFlag = computed(() => {
  clearTimeout(timer.value)
  const flag = messageData.value?.messageOpType === 0 && userInfo.adminId === messageData.value?.sendByUuid
  if (flag) countdownFn()
  return flag && time.value
})
const editFlag = computed(() => {
  clearTimeout(timer.value)
  const flag = messageData.value?.messageOpType === 1 && userInfo.adminId === messageData.value?.sendByUuid
  if (flag) countdownFn()
  return flag && time.value
})

const countdownFn = () => {
  if (!dayjs().isBefore(dayjs(messageData.value?.opExpire))) {
    time.value = false
    clearTimeout(timer.value)
    return false
  }
  const interval = dayjs(messageData.value?.opExpire).diff(dayjs()) / 1000 / 60
  time.value = true
  timer.value = setTimeout(countdownFn, (interval > 10 && 1000 * 60 * 5) || 1000)
  return true
}

// 需求详情跳转
const onJumpDemandDetial = (record: any) => {
  router.push({ name: 'DemandHandle', params: { id: record.instanceId } })
}

const handleMessageFn = async () => {
  let params = {
    uuids: (messageData.value.messageReadRsps || []).map(item => item.uuid),
    messageInfoId: messageData.value.id,
    processName: messageData.value.processName,
  }
  const res = await urgentMessage(params)
  if (res.code !== 200) throw new Error(res.msg)
  messageInstance.success(i18n.t('已发送加急通知'))
}

const revocationMessageFn = async () => {
  if (!dayjs().isBefore(dayjs(messageData.value?.opExpire))) {
    messageInstance.success(i18n.t('仅一天内可撤回消息！'))
    return
  }
  const res = await batchWithdraw(messageData.value.id)
  if (res.code !== 200) throw new Error(res.msg)
  clearTimeout(timer.value)
  messageInstance.success(i18n.t('已撤回消息'))
  emits('on-init')
}

const editMessageFn = async () => {
  if (!dayjs().isBefore(dayjs(messageData.value?.opExpire))) {
    messageInstance.success(i18n.t('仅10分钟内可重新编辑撤回消息！'))
    return
  }
  const res = await editMessage(messageData.value.id)
  if (res.code !== 200) throw new Error(res.msg)
  emits('on-set-content-fn', { content: res.data.content, file: res.data.file })
}

const sendMessageFn = () => {
  emits('on-select-role-fn', {
    index: 0,
    id: messageData.value.sendByUuid,
    value: messageData.value.sendBy,
    denotationChar: '@',
  })
}

const getPreviewShow = (name: string) => {
  return ['jpg', 'png', 'tif', 'gif', 'svg', 'pdf', 'txt', 'mp4'].includes(
    (name.split('.').pop() as string).toLowerCase()
  )
}

const handlePreview = (url: string) => {
  window.open(url)
}

onBeforeUnmount(() => {
  clearTimeout(timer.value)
})
</script>
<style lang="scss" scoped>
.message-item-box {
  display: flex;
  justify-content: flex-start;
  position: relative;

  :deep(.mention) {
    padding: 0;
    background-color: transparent;
  }
  .message-left {
    .header {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      overflow: hidden;
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }
  .message-right {
    margin: 0 16px 32px 16px;
    width: 100%;
    overflow: hidden;
    font-size: 12px;
    color: #333;
    line-height: 18px;
    .info {
      display: flex;
      justify-content: flex-start;
      font-size: 12px;
      line-height: 24px;
      &.no-edit {
        line-height: 32px;
        .name,
        .time {
          color: #999;
        }
      }

      .name,
      .time {
        margin: 0;
      }
      .name {
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .revocation-text {
          display: inline-block;
          margin-left: 4px;
        }
      }
      .time {
        color: #666;
        margin-left: 8px;
        flex-shrink: 0;
      }
      .icon-box {
        width: 20px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        margin-left: 4px;
      }
    }
    .message-txt {
      margin-top: 8px;
      line-height: 18px;
      word-break: break-all;
      white-space: pre-line;
      :deep(.ql-container) {
        font-size: 12px;
      }
      :deep(img) {
        cursor: zoom-in;
      }
    }
    .message-file {
      margin-bottom: 10px;
      .file {
        height: 24px;
        line-height: 24px;
        display: flex;
        justify-content: flex-start;
        cursor: pointer;
        width: fit-content;
        white-space: nowrap;
        .icon {
          color: #fdb926;
          padding-right: 4px;
          font-size: 12px;
        }
        .txt {
          margin: 0;
          font-size: 12px;
          max-width: 140px;
          overflow: hidden;
          padding: 0 3px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .file-size {
          font-size: 12px;
          color: #bbbbbb;
        }
        .icontubiao_yanjing {
          margin-left: 6px;
        }
      }
    }
    .message-call {
      display: flex;
      justify-content: flex-start;
      font-size: 12px;
      margin-top: 8px;
      margin-bottom: 10px;
      line-height: 12px;
      .call-people {
        color: #378eef;
        padding-right: 4px;
      }
      .txt-read {
        color: #999999;
      }
      .is-read-color {
        color: #3dcca6;
      }
      .is-read {
        color: #3dcca6;
        padding-right: 10px;
      }
      .no-read {
        color: #999999;
      }
      .read-name {
        font-size: 12px;
        padding-right: 4px;
        color: #666666;
        display: inline-block;
        word-wrap: break-word;
      }
      .read-status {
        color: #bbbbbb;
        font-size: 12px;
      }
    }
  }
}
.edit-text {
  cursor: pointer;
  color: #378eef;
}
.reply {
  display: none;
  cursor: pointer;
  color: #378eef;
}
.message-item-box:hover {
  .reply {
    display: block;
  }
}
</style>

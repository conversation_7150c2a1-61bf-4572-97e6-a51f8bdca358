<template>
  <div class="input-box">
    <div class="input-resizable" @mousedown="startResize"></div>
    <Editor ref="editorRef" :height="height" />
    <UpLoadS3 ref="uploadRef" v-model:value="fileList" :file-types="FILT_TYPE" />
    <div class="input-footer">
      <div class="input-upload-file">
        <FUpload :file-list="[]" :before-upload="(file: any) => handleUpload(file)">
          <span class="iconfont icon">&#xe625;</span>
        </FUpload>
      </div>
      <div class="input-send">
        <FButton type="link" :loading="sendLoad" @click="sendHandle()">发送</FButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from '@fs/smart-design'
import { BPM_MESSAGE_EDITOR, useI18n } from '@/utils'

import Editor from '../Editor/index.vue'
import UpLoadS3 from '../UpLoadS3/index.vue'

const FILT_TYPE = [
  'PDF',
  'JPG',
  'JPEG',
  'PNG',
  'DOC',
  'DOCX',
  'XLS',
  'XLSX',
  'TXT',
  'PPT',
  'PPTX',
  'ZIP',
  'PCAP',
  'RAR',
  'MP4',
]

// const props = defineProps<IProps>()
const emits = defineEmits(['send'])
const i18n = useI18n()

const editorRef = ref()
const uploadRef = ref()
const height = ref(140)
const fileList = ref<any[]>([])
const sendLoad = ref(false)

const handleUpload = (file: any) => {
  uploadRef.value.handleUpload(file)
  return false
}

const clear = () => {
  const editor = editorRef.value.getEditor()
  fileList.value = []
  editor.setHtml('<p></p>')
  localStorage.removeItem(BPM_MESSAGE_EDITOR)
}

const sendHandle = () => {
  const editor = editorRef.value.getEditor()
  const mention = editor.getElemsByType('mention')

  if (editor.isEmpty() || (!editor.getText().trim() && !mention?.length))
    return message.warn(i18n.t('请输入要发送的信息'))
  if (fileList.value.some(item => item.status !== 'success')) message.warn(i18n.t('请检查文件是否上传完成或上传失败'))

  const content = editor.getHtml()
  const receiver = mention
    .map((item: any) => {
      if (!item.value.startsWith('角色')) return { receiver: item.value, receiverUuid: item.info.uuid }
      return item.info.uuid.split(',').map((id: string) => {
        const [receiver, receiverUuid] = id.split('|')
        return { receiver, receiverUuid }
      })
    })
    .flat()

  emits('send', { file: fileList.value, content, receiver })
}

const startResize = (e: MouseEvent) => {
  const startY = e.y
  const startH = height.value
  const handleMouseMove = (e: MouseEvent) => {
    const currY = e.y
    height.value = startH + startY - currY
  }
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

defineExpose({
  getEditor: () => editorRef.value.getEditor(),
  setFileList: (files: any[]) => (fileList.value = files),
  clearEditor: clear,
})
</script>

<style scoped lang="scss">
.input-box {
  position: relative;
  > .input-resizable {
    position: absolute;
    top: 0;
    left: 0;
    height: 2px;
    width: 100%;
    cursor: row-resize;
    z-index: 99;
    background-color: #f0f0f0;
  }

  .input-footer {
    display: flex;
    justify-content: space-between;

    > .input-upload-file {
      padding-left: 10px;
    }
  }
}
</style>

<template>
  <FCard
    class="milepost-card"
    v-bind="$attrs"
    :head-style="COMPONENT_CRAD_BODY_STYLE"
    :body-style="COMPONENT_CRAD_BODY_STYLE"
  >
    <template #title>
      <Icon icon="iconchakan-jiedianicon" :style="{ color: color }" />
      {{ milepost.topicName }}
    </template>

    <!-- 非进行中的节点默认收起 -->
    <template #extra>
      <a class="noSelect" @click="handleCollapseClick">{{ isShow ? i18n.t('收起') : i18n.t('展开') }}</a>
    </template>
    <div class="milepost-card-wrapper" :style="TFHeight()">
      <slot></slot>
    </div>
  </FCard>
</template>
<script setup lang="ts">
import { computed, watch, ref } from 'vue'
import Icon from '@/components/Icon/index.vue'
import { COMPONENT_CRAD_BODY_STYLE } from '@/views/process-detail/config'
import type { IProcess } from '@/types/handle'
import { useI18n } from '@/utils'

const i18n = useI18n()

interface IProps {
  milepost: IProcess
}

const props = defineProps<IProps>()
const color = computed(() => (props.milepost.status === 2 ? '#1890ff' : '#333'))
const isShow = ref(false)

watch(
  () => props.milepost.status,
  () => {
    isShow.value = props.milepost.status === 2
  },
  { immediate: true }
)

const handleCollapseClick = () => {
  isShow.value = !isShow.value
}

const TFHeight = () => {
  return isShow.value ? { height: 'auto' } : { height: 0, padding: 0 }
}
</script>
<style lang="scss" scoped>
.noSelect {
  user-select: none;
}

.milepost-card {
  margin-bottom: 20px !important;

  .milepost-card-wrapper {
    width: 100%;
    padding-bottom: 20px;
    overflow: hidden;

    :deep(.cxd-Page-body) {
      padding-left: 0;
    }
  }

  :deep(.fs-btn) {
    padding: 0 24px !important;
  }
}
</style>
<style lang="scss">
.contentDataClass {
  max-width: 350px;
  p {
    img {
      width: 100px;
      height: 100px;
    }
  }
}
</style>

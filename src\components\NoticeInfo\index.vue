<template>
  <div class="notice-info-container">
    <FTooltip
      placement="top"
      overlay-class-name="cust-notice-tooltip"
      :get-popup-container="(triggerNode: Element) => { return triggerNode?.parentNode}"
    >
      <template #title>
        <span>{{ i18n.t('催一催') }}</span>
      </template>
      <i class="iconfont icontubiao_shangchuan_mian1" @click.stop="handleNotice"></i>
    </FTooltip>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { hurryMessage } from '@/api'
import { messageInstance as message } from '@fs/smart-design'
import { useI18n } from '@/utils'
const i18n = useI18n()
type propsType = {
  keyId?: string
  valueId?: string | number
}

const props = withDefaults(defineProps<propsType>(), {
  keyId: 'milepostId',
  valueId: undefined,
})

const loading = ref<boolean>(false)

const handleNotice = async () => {
  try {
    if (!props.valueId && loading.value) return
    loading.value = true
    const res = await hurryMessage({ [props.keyId]: props.valueId })
    if (res.code === 200) {
      message.success(i18n.t('给当前负责人发送消息成功') + '！')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.notice-info-container {
  display: inline-block;
  .iconfont {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid #eeeeee;
    font-size: 14px;
    color: #fba54f;
    background: #ffffff;
    cursor: pointer;
    &:hover {
      background: #f1f4f8;
    }
    &:active {
      background: #e1e8f0;
    }
  }
}
</style>

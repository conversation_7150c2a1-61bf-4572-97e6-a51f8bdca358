<template>
  <div class="milepost-task">
    <div class="milepost-task-header">
      <p>{{ i18n.t('工作协同') }}：</p>
      <div class="milepost-task-extra" v-if="props.milepost.status !== 4">
        <FCheckbox class="f12" v-model:checked="incompleteChecked" @change="checkedBoxChenge">{{
          i18n.t('我负责的')
        }}</FCheckbox>
        <FCheckbox class="f12" v-model:checked="responsibleChecked" @change="checkedBoxChenge">{{
          i18n.t('未完成的')
        }}</FCheckbox>
        <LinkButton
          :text="i18n.t('添加子节点')"
          icon="icontubiao_tianjia1"
          @click="emitOperate(EmitType.createTask, props.milepost)"
        />
        <LinkButton
          :class="{ disabled: !selected.length }"
          :text="i18n.t('删除任务')"
          icon="iconshanchu2"
          @click="selected.length && emitOperate(EmitType.batchDelTask, { milepost: props.milepost, tasks: selected })"
        />
        <LinkButton
          :class="{ disabled: !selected.length }"
          :text="i18n.t('编辑子节点')"
          icon="iconicon_piliangbianji"
          @click="selected.length && emitOperate(EmitType.batchEditTask, { tasks: selected })"
        />
      </div>
    </div>
    <div class="milepost-task-main">
      <FTable
        ref="tableRef"
        v-model:expandedRowKeys="tableExpanded"
        :loading="loading"
        :row-selection="taskRowSelection"
        :columns="taskTableColumn"
        :data-source="taskList"
        :pagination="false"
        :scroll="{ x: 1500 }"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'taskName'">
            <span class="task-table-expanded">
              <PlusSquareOutlined v-show="!tableExpanded.length" @click="handleTaskTableAllExpanded" />
              <MinusSquareOutlined v-show="tableExpanded.length" @click="tableExpanded = []" />
            </span>
            <span>{{ i18n.t('任务名称') }}</span>
          </template>
        </template>
        <template #bodyCell="{ column, record: task }">
          <LinkButton
            v-if="column.key === 'taskName'"
            class="ellipsis"
            :text="task.taskName"
            @click="emitOperate(EmitType.viewTask, task)"
          />
          <template v-if="column.key === 'status'">
            <Icon :icon="task.statusIcon" :color="TFTaskStatusColor(task.status)" />
            {{ task.statusName }}
          </template>
          <template v-if="column.key === 'superviser'">
            {{ task.superviser }}
            <NoticeInfo
              v-if="[0, 1, 4].includes(task.status)"
              class="cust-notice-info"
              style="margin-left: 8px"
              key-id="taskId"
              :value-id="task.id"
            />
          </template>
          <template v-if="column.key === 'approver'">
            {{ task.approver }}
            <NoticeInfo
              v-if="[6].includes(task.status)"
              class="cust-notice-info"
              style="margin-left: 8px"
              key-id="taskId"
              :value-id="task.id"
            />
          </template>
          <template v-if="column.key == 'attachmentData'">
            <div v-if="!task.attachmentData || !JSON.parse(task.attachmentData)?.length">--</div>
            <DownloadFiles
              v-else
              class="color1890ff cursor"
              :data="JSON.parse(task.attachmentData)"
              @download="download"
              @batch-download="batchDownload"
            />
          </template>
          <template v-if="column.key == 'contentData'">
            <div v-if="!task.contentData || !JSON.parse(task.contentData)?.handleTaskDesc">--</div>
            <FPopover
              v-else
              trigger="hover"
              placement="top"
              :overlay-style="{ width: '300px', maxHeight: '500px', overflow: 'auto' }"
            >
              <template #content>
                <div v-html="JSON.parse(task.contentData)?.handleTaskDesc" @click="handleElementClick"></div>
              </template>
              <a @click="emitOperate(EmitType.viewTask, task)">{{ i18n.t('查看提交内容') }}</a>
            </FPopover>
          </template>
          <template v-if="column.key === 'action'">
            <!-- <Icon
              v-bind="iconAttrs"
              :title="i18n.t('添加子任务')"
              icon="icontianjiazirenwu"
              v-if="(task.creatorRole == 1 || task.superviserRole == 1) && [0, 1, 4, 6].includes(task.status)"
              @click="emitOperate(EmitType.createChildTask, task)"
            /> -->
            <Icon
              v-bind="iconAttrs"
              :title="i18n.t('编辑')"
              icon="iconicon_piliangbianji"
              v-if="(task.creatorRole == 1 || task.taskEditConfig == 1) && [0, 1, 4, 6].includes(task.status)"
              @click="emitOperate(EmitType.editTask, task)"
            />
            <Icon
              v-bind="iconAttrs"
              :title="i18n.t('处理')"
              icon="icondaiban"
              v-if="task.superviserRole == 1 && [0, 1, 4, 6].includes(task.status)"
              @click="emitOperate(EmitType.handleTask, task)"
            />
            <Icon
              v-bind="iconAttrs"
              :title="i18n.t('审核')"
              icon="iconshenhe1"
              v-if="task.approverRole == 1 && task.status == 6"
              @click="emitOperate(EmitType.judgeTask, task)"
            />
            <FPopconfirm
              class="pointer"
              :title="i18n.t('当前删除的任务中所包含的子任务会一并删除，请谨慎操作！')"
              v-if="task.creatorRole == 1 && [0, 1, 4].includes(task.status)"
              @confirm="emitOperate(EmitType.delTask, task)"
            >
              <Icon :title="i18n.t('移除')" size="12px" color="#378eef" icon="iconshanchu2" />
            </FPopconfirm>
          </template>
        </template>
      </FTable>
    </div>
    <SeeImage v-model="seeImageObj.flag" :src="seeImageObj.src" />
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, watch, reactive } from 'vue'
import { PlusSquareOutlined, MinusSquareOutlined } from '@ant-design/icons-vue'
import { getUserInfo, deepClone, useI18n, download, batchDownload } from '@/utils'
import { EmitType, TASK_STATUS_COLOR } from '@/views/process-detail/config'
import Icon from '@/components/Icon/index.vue'
import LinkButton from '../LinkButton/index.vue'
import DownloadFiles from '../../../DownloadFiles/index.vue'
import SeeImage from '@/components/SeeImage/index.vue'
import NoticeInfo from '@/components/NoticeInfo/index.vue'
import type { IProcess, ITask } from '@/types/handle'

interface IProps {
  milepost: IProcess
}

const i18n = useI18n()
const props = defineProps<IProps>()
const instanceId = computed(() => props.milepost.instanceId)
const handleOperate = inject('handleOperate') as (value: { type: EmitType; data: unknown }) => void
const seeImageObj = reactive({ src: '', flag: false })
const tableExpanded = ref<number[]>([])

const tableRef = ref()
const selected = ref<ITask[]>([])
const selectedKeys = ref<string[]>([])

const iconAttrs = { class: 'mr10 pointer', size: '12px', color: '#378eef' }

const taskTableColumn = computed(() => [
  { title: i18n.t('任务名称'), dataIndex: 'taskName', key: 'taskName' },
  { title: i18n.t('状态'), dataIndex: 'status', key: 'status', width: '110px' },
  { title: i18n.t('前置任务'), dataIndex: 'prefixTaskName', key: 'prefixTaskName', width: '80px' },
  { title: i18n.t('负责人'), dataIndex: 'superviser', key: 'superviser', width: '180px' },
  { title: i18n.t('审核人'), dataIndex: 'approver', key: 'approver', width: '180px' },
  { title: i18n.t('当前节点完成'), dataIndex: 'isSysName', key: 'isSysName', width: '60px' },
  { title: i18n.t('预计完成日期'), dataIndex: 'forcastTimeStr', key: 'forcastTimeStr', width: '115px' },
  { title: i18n.t('任务提交内容'), dataIndex: 'contentData', key: 'contentData', width: '115px' },
  { title: i18n.t('实际完成日期'), dataIndex: 'taskCompletedTimeStr', key: 'taskCompletedTimeStr', width: '115px' },
  { title: i18n.t('附件'), dataIndex: 'attachmentData', key: 'attachmentData', width: '150px' },
  { title: i18n.t('操作'), dataIndex: 'action', key: 'action', width: '120px', fixed: 'right' },
])

// 任务表格多选配置
const taskRowSelection = computed(() => ({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[], selectedRows: ITask[]) => {
    selected.value = selectedRows
    selectedKeys.value = selectedRowKeys
  },
  getCheckboxProps: (task: ITask) => ({
    disabled: task.creatorRole !== 1 || ![0, 1, 4, 6].includes(task.status),
    name: task.taskName,
  }),
}))

watch(
  () => props.milepost,
  () => {
    selected.value = []
    selectedKeys.value = []
    checkedBoxChenge()
  }
)

// 事件操作发送
const emitOperate = (type: EmitType, data: any) =>
  handleOperate({ type, data: { ...data, instanceId: instanceId.value } })

// 表格全部展开
const handleTaskTableAllExpanded = () => {
  const v: number[] = []
  const s: ITask[] = [...taskList.value]
  while (s.length) {
    const item = s.pop() as ITask
    v.push(item.key as number)
    Array.isArray(item.children) && item.children.length && s.push(...item.children)
  }

  tableExpanded.value = v
}

// 图片查看
const handleElementClick = (e: any) => {
  if (e.target.nodeName === 'IMG') {
    seeImageObj.src = e.target.currentSrc
    seeImageObj.flag = true
  }
}
//数据筛选
const loading = ref(false)
const userInfo = getUserInfo()
const taskList = ref<ITask[]>(JSON.parse(JSON.stringify(props.milepost.children ?? [])))
const incompleteChecked = ref<boolean>(false)
const responsibleChecked = ref<boolean>(false)
const taskMap = new Map()
const taskOneArr = computed<ITask[]>(() => {
  const data: ITask[] = []
  const df = (arr: ITask[], parentId?: number) => {
    arr.forEach(item => {
      parentId && (item.parentId = parentId)
      if (Array.isArray(item.children) && item.children.length) item.children = df(item.children, item.id) as undefined
      data.push(item)
      taskMap.set(item.id, item)
    })
  }
  df(JSON.parse(JSON.stringify(props.milepost.children ?? [])))
  return data
})

const filterTask = () => {
  let data: ITask[] = taskOneArr.value
  incompleteChecked.value &&
    (data = data.filter((item: ITask) => item.superviserUuid === userInfo.uuid || item.approverUuid === userInfo.uuid))
  responsibleChecked.value && (data = data.filter((item: ITask) => ![2, 3, 4].includes(item.status)))
  return data.map(item => ({ ...item }))
}

const toTaskTree = (data: ITask[]) => {
  const map: Record<string, ITask> = {}
  const res: ITask[] = []
  data.forEach((item: ITask) => (map[item.id] = item))

  for (let i = 0; i < data.length; i++) {
    const item = data[i]
    if (item.parentId) {
      if (!map[item.parentId as string]) handleParentTask(item, map, data)
      const parent = map[item.parentId as string]
      parent.children ? parent.children.push(item) : (parent.children = [item])
    } else {
      res.push(item)
    }
  }
  return res
}

const handleParentTask = (task: ITask, map: Record<string, ITask>, data: ITask[]) => {
  const parentId = task.parentId as number
  const parentTask = deepClone(taskMap.get(parentId))
  if (parentTask) {
    map[parentId] = parentTask
    data.push(parentTask)
  }
  parentTask.parentId && handleParentTask(parentTask, map, data)
}

const checkedBoxChenge = () => {
  loading.value = true
  taskList.value = toTaskTree(filterTask())
  loading.value = false
}

const TFTaskStatusColor = (status: keyof typeof TASK_STATUS_COLOR) => TASK_STATUS_COLOR[status]
</script>

<style lang="scss" scoped>
.f12 {
  font-size: 12px;
}
.mr10 {
  margin-right: 10px;
}
.pointer {
  cursor: pointer;
}
.disabled {
  color: #ccc !important;
  cursor: not-allowed !important;
}
.milepost-task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .milepost-task-extra {
    font-size: 12px;
    > * {
      padding: 0 8px;
    }
  }
}
:deep(.cust-notice-info) {
  .iconfont {
    width: 18px !important;
    height: 18px !important;
    font-size: 12px !important;
  }
}
.task-table-expanded {
  margin-right: 10px;
  font-size: 14px;
  vertical-align: middle;
  cursor: pointer;
  &:hover {
    color: #1890ff;
  }
}
</style>

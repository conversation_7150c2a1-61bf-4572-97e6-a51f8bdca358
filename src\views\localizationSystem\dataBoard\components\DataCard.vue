<template>
  <div class="board-data-container" :style="{ backgroundColor: props.pageColor }">
    <div class="board-data-container__left">
      <div class="board-data-container__left__text">
        <TextCard :title="info.firstName" :num="info.firstPrice" :label-num="info.firstRate" />
      </div>
      <div class="board-data-container__left__base-charts">
        <BaseCharts :options="options" />
      </div>
    </div>
    <div class="board-data-container__right">
      <div class="board-data-container__right__top">
        <TextCard :title="info.twoName" :num="info.twoPrice" :label-num="info.twoRate" />
        <TextCard :title="info.threeName" :num="info.threePrice" :label-num="info.threeRate" />
      </div>
      <div class="board-data-container__right__bottom" v-if="hasBottom">
        <TextCard :title="info.fourName" :num="info.fourPrice" :label-num="info.fourRate" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref } from 'vue'
import TextCard from './TextCard.vue'
import BaseCharts from './BaseCharts.vue'
import { DataCard } from '@/types/localizationSystem/dataBoard'
import { useI18n } from '@/utils'
const i18n = useI18n()

interface ChartsData {
  dataName: any[]
  dataValue: any[]
}

type propsType = {
  info: DataCard
  pageColor: string
  chartsColors: Array<string>
  hasBottom?: boolean
  chartsTitle: string
  chartsUnit?: string
}

const props = withDefaults(defineProps<propsType>(), {
  info: () => ({
    firstName: '',
    firstPrice: 0,
    firstRate: undefined,
    twoName: '',
    twoPrice: 0,
    twoRate: undefined,
    threeName: '',
    threePrice: 0,
    threeRate: undefined,
    chartsData: {
      dataName: [],
      dataValue: [],
    } as ChartsData,
  }),
  pageColor: '',
  chartsColors: () => [],
  hasBottom: false,
  chartsTitle: '',
  chartsUnit: '',
})

const options = reactive<any>({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#666',
        type: 'solid',
      },
    },
    backgroundColor: '#000000B3',
    formatter: function (params: any) {
      return `<div style="color: #fff">${params[0].name}</div>
      <div style="color: #fff"><span style="display: inline-block; margin-right: 24px">${
        props.chartsTitle
      }</span><span>${parseFloat(parseFloat(params[0].value || 0).toFixed(2))}${props.chartsUnit}</span></div>`
    },
  },
  grid: {
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      show: false,
      interval: 0,
    },
    data: [],
  },
  yAxis: {
    type: 'value',
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      show: false,
    },
    splitLine: {
      show: false,
    },
  },
  series: {
    type: 'line',
    smooth: true,
    lineStyle: {
      width: 1,
      color: props.chartsColors[1],
    },
    showSymbol: false,
    symbol: 'circle',
    symbolSize: 8,
    itemStyle: {
      color: props.chartsColors[1],
      borderColor: '#fff',
      borderWidth: 1,
    },
    areaStyle: {
      opacity: 0.8,
      color: props.chartsColors[0],
    },
    emphasis: {
      focus: 'series',
    },
    data: [],
  },
})

watch(
  () => props.info,
  newVal => {
    options.xAxis.data = newVal?.chartsData?.dataName
    options.series.data = newVal?.chartsData?.dataValue.map(item => item || 0)
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.board-data-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 24px;
  padding-right: 32px;
  background: #ffffff;
  border-radius: 4px;
  &__left {
    position: relative;
    display: flex;
    width: 48%;
    align-items: center;
    flex: 1 1 528px;
    padding-right: 68px;
    &__text {
      flex: 1 1 116px;
      margin-right: 30px;
    }
    &__base-charts {
      flex: 1 1 290px;
    }
    &::after {
      content: '';
      position: absolute;
      height: 100%;
      width: 1px;
      right: 0;
      background-color: #eee;
    }
  }
  &__right {
    display: flex;
    width: 47%;
    flex-direction: column;
    flex: 1 1 515px;
    padding-left: 71px;
    &__top {
      display: flex;
      justify-content: space-between;
      .board-text-container {
        width: calc(50% - 71px);
      }
    }
    &__bottom {
      margin-top: 32px;
    }
  }
}
</style>

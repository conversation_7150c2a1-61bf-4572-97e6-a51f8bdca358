<template>
  <div>
    <FModal width="480px" v-model:visible="visible" title="操作记录" centered destroyOnClose :footer="null">
      <OperationLogs :instanceId="instanceId" />
      <i />
    </FModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import OperationLogs from '@/views/process-detail/components/OperationLogNew/components/OperationLogs.vue'

const visible = ref<boolean>(false)
const instanceId = ref<any>()

const onOpenFn = (id: any) => {
  visible.value = true
  instanceId.value = id
}

defineExpose({ onOpenFn })
</script>
<style scoped lang="scss">
:deep(.operation-logs-container) {
  min-height: auto;
}
</style>

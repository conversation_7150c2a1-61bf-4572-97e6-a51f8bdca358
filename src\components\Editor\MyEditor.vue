<template>
  <div class="editor-box" ref="refEditorBox">
    <QuillEditor
      v-model:content="content"
      @text-change="onEditorChange"
      ref="editor"
      :content-type="'html'"
      :options="editorOption"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import '@vueup/vue-quill/dist/vue-quill.core.css'
import '@vueup/vue-quill/dist/vue-quill.bubble.css'
import { QuillEditor, Quill } from '@vueup/vue-quill'
import './imageBlot.js'
import { ImageExtend } from '@/components/Message/Dialogbox/imageExtend.js'
import ImageResize from 'quill-blot-formatter'
import ImageUploader from 'quill-image-uploader'
import MagicUrl from 'quill-magic-url'
import { upload } from '@/api'
Quill.register('modules/ImageResize', ImageResize)
Quill.register('modules/ImageExtend', ImageExtend)
Quill.register('modules/ImageUploader', ImageUploader)
Quill.register('modules/magicUrl', MagicUrl)
const toolbarOptions = [
  ['bold', 'italic', 'underline', 'strike'], // toggled buttons
  [{ header: 1 }, { header: 2 }], // custom button values
  [{ list: 'ordered' }, { list: 'bullet' }],
  [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
  [{ direction: 'rtl' }], // text direction
  [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  [{ color: [] }, { background: [] }], // dropdown with defaults from theme
  [{ align: [] }],
  ['link', 'image'],
  ['clean'],
]
const emit = defineEmits(['input'])
const props = defineProps({
  placeholder: {
    type: String,
    default: '请输入详情',
  },
  modelUuid: {
    type: String,
    default: '',
  },
  options: {
    type: Array,
    default: () => [],
  },
  matchers: {
    type: Array,
    default: () => [],
  },
  value: {
    type: String,
    default: '',
  },
})
const content = ref(props.value)
const refEditorBox = ref()
const editor = ref()

const editorOption = {
  modules: {
    toolbar: {
      container: toolbarOptions,
    },
    magicUrl: true,
    ImageExtend: {
      name: 'file',
      response: (res: any) => {
        return res.data.url
      },
    },
    ImageResize: {
      // 添加
      displayStyles: {
        // 添加
        backgroundColor: 'black',
        border: 'none',
        color: 'white',
      },
      modules: ['Resize', 'DisplaySize', 'Toolbar'], // 添加
    },
    ImageUploader: {
      name: 'imageUploader',
      upload: (file: File) => {
        return new Promise(resolve => {
          const formData = new FormData()
          formData.append('isOpen', 'false')
          formData.append('expire', '0')
          formData.append('file', file)
          upload(formData).then(res => {
            resolve(res)
          })
        })
      },
    },
    clipboard: {
      // 粘贴板，处理粘贴时候的自带样式
      matchers: props.matchers,
    },
  },
  placeholder: props.placeholder,
}
const onEditorChange = () => {
  if (content.value == '<p><br></p>') {
    content.value = ''
    emit('input', content.value)
  } else {
    emit('input', content.value)
  }
}

watch(
  () => props.value,
  newValue => {
    content.value = newValue
  }
)
</script>

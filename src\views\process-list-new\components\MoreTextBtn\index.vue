<template>
  <div ref="contentRef" class="more-text-btn-box" :style="[{ '-webkit-line-clamp': moreFlag ? 'none' : lineClamp }]">
    <slot />
  </div>
  <div v-if="showMoreBtn" class="more-text-handle-btn-box">
    <span class="cursor color4677C7" @click="moreFlag = !moreFlag">
      {{ moreFlag ? '收起' : '展开' }}
      <i :class="['iconfont', moreFlag ? 'iconjiantoushang' : 'iconjiantouxia']"></i
    ></span>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

interface IProps {
  lineClamp?: number
}
withDefaults(defineProps<IProps>(), {
  lineClamp: 2,
})
const contentRef = ref()
const showMoreBtn = ref(false)
const moreFlag = ref(false)

const handleMoreIn = () => {
  const $content = contentRef.value
  let range = document.createRange()
  range.setStart($content, 0)
  range.setEnd($content, $content.childNodes.length)
  const rangeHeight = range.getBoundingClientRect().height
  showMoreBtn.value = rangeHeight > $content.offsetHeight
  range = null as any
}

onMounted(() => {
  handleMoreIn()
})
</script>

<style scoped lang="scss">
.more-text-btn-box {
  display: -webkit-box;
  overflow: hidden;
  width: 100%;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
</style>

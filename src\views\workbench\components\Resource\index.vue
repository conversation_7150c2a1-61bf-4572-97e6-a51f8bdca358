<template>
  <div class="resource-file-wrapper">
    <div class="resource-file-container">
      <div class="resource-file" v-for="item in props.data" :key="item.uuid">
        <div class="resource-file-view">
          <img
            v-if="item.mime_type === 'png'"
            :src="`${imgBaseUrl}${item.file_uuid}?token=${token}&fileType=png`"
            :alt="i18n.t('文件图片')"
          />
          <img
            v-else
            class="resource-file-img"
            :src="BaseImgs[item.mime_type as keyof typeof BaseImgs] || wordPng"
            :alt="i18n.t('文件图片')"
          />
          <div class="resource-file-tips">{{ item.name }}</div>
          <div class="resource-file-download" @click="() => handleDownload(item)"></div>
        </div>
        <div class="resource-file-info">
          <p class="resource-file-title" :title="item.file_name">{{ item.file_name }}</p>
          <div class="resource-file-label">
            <template v-if="item.labels && item.labels.length > 0">
              <div class="file-tag">
                <div
                  v-for="(info, index) in item.labels.slice(0, 2)"
                  :key="index"
                  class="info-type"
                  :class="'info-type' + (index % 4)"
                  :title="info.fullname"
                >
                  {{ info.fullname }}
                </div>
                <FPopover
                  v-if="item.labels.length > 2"
                  trigger="click"
                  placement="bottom-end"
                  theme="light"
                  max-width="320"
                >
                  <!-- :get-popup-container="getPopupContainer" -->
                  <div class="info-type info-type0" style="line-height: 19px">...</div>
                  <!-- <template #content>
                    <div class="label-more-box">
                      <div
                        v-for="(info, index) in labels"
                        :key="index"
                        class="info-type"
                        :class="'info-type' + (index % 4)"
                        :title="info.fullname"
                      >
                        {{ info.fullname }}
                      </div>
                    </div>
                  </template> -->
                </FPopover>
              </div>
            </template>
            <p v-else v-show="!item.labels.length">
              <i class="iconfont icontubiao_biaoqian marginR4" /> {{ i18n.t('暂无标签') }}
            </p>
          </div>
          <div class="resource-file-time">
            <time>{{ transformDate(item.file_created_at, 'YYYY-MM-DD') }}</time>
            <span class="resource-file-download-number">
              <i class="iconfont icontubiao_xiazai" />
              {{ item.internal_downloads + item.external_downloads }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IResource } from '@/types/request'

import pdfPng from '@/assets/images/ppt.png'
import wordPng from '@/assets/images/word.png'
import excelPng from '@/assets/images/excel.png'
import { transformDate, getToken, useI18n } from '@/utils'

const token = getToken()
const imgBaseUrl = process.env.VUE_APP_API_URL + '/api/workbench/downloadFile/'

const BaseImgs = {
  pdf: pdfPng,
  ppt: pdfPng,
  word: wordPng,
  excel: excelPng,
  doc: wordPng,
  docx: wordPng,
  xls: excelPng,
  xlsx: excelPng,
}

interface IProps {
  data: IResource[]
}

const i18n = useI18n()
const props = defineProps<IProps>()
const emits = defineEmits(['download'])

const handleDownload = (data: IResource) => {
  emits('download', data)
}
// const getPopupContainer = parent => parent
</script>

<style lang="scss" scoped>
.resource-file-wrapper {
  overflow-x: hidden;
  .resource-file-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    margin-right: -16px;
    .resource-file {
      width: 262px;
      height: 264px;
      margin: 0 16px 16px 0;
      border-radius: 4px;
      border: 1px solid #f8f8f8;
      background-color: #ffffff;
      &:hover {
        box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
      }
      > .resource-file-view {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 152px;
        overflow: hidden;
        background-color: #f8f8f8;

        // > .resource-file-img {}

        > .resource-file-tips {
          position: absolute;
          top: 0;
          left: 0;
          height: 24px;
          line-height: 24px;
          padding: 0 8px;
          color: white;
          font-size: 12px;
          background: rgba(0, 0, 0, 0.2);
          border-radius: 4px 0px 4px 0px;
        }

        .resource-file-download {
          position: absolute;
          display: none;
          bottom: 0;
          width: 100%;
          height: 36px;
          line-height: 36px;
          text-align: center;
          background: rgba(0, 0, 0, 0.2);
          cursor: pointer;
          z-index: 1;

          &::after {
            content: '\e7c4';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            font-size: 22px;
            color: white;
            font-family: 'bpm-iconfont' !important;
          }
        }

        &:hover {
          .resource-file-download {
            display: block;
          }
        }
      }

      > .resource-file-info {
        padding: 16px;
        > .resource-file-title {
          height: 20px;
          line-height: 20px;
          margin-bottom: 6px;
          color: #333333;
          font-size: 14px;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        > .resource-file-label {
          height: 24px;
          margin-bottom: 12px;
          color: #bbbbbb;
          font-size: 12px;
          .marginR4 {
            margin-right: 4px;
          }
          > p {
            display: flex;
            align-items: center;
          }
        }

        > .resource-file-time {
          height: 18px;
          line-height: 18px;
          color: #999999;
          font-size: 12px;
        }

        .resource-file-download-number {
          float: right;
        }
      }
    }
  }
}
.file-tag {
  display: flex;
  width: 100%;
  margin-top: 6px;
  margin-bottom: 12px;
  height: 24px;
  align-items: center;

  .label-more-box {
    display: flex;
    flex-wrap: wrap;
    padding: 0px 4px;
  }
  .empty-tag {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #999999;
    & > .iconfont {
      font-size: 12px;
      margin-right: 2px;
    }
  }

  .info-type {
    height: 24px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 24px;
    padding: 0px 6px;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .info-type0 {
    color: #60b6ff;
    background: #f1f7fb;
  }
  .info-type1 {
    color: #3dcca6;
    background: #eaf9f6;
  }
  .info-type2 {
    color: #f8af11;
    background: #fdf7e9;
  }
  .info-type3 {
    color: #a697fe;
    background: rgba(166, 151, 254, 0.2);
  }
}
</style>

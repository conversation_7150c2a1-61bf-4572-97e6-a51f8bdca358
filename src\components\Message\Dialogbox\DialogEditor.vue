<template>
  <div class="editor-box" ref="refEditorBox">
    <QuillEditor
      ref="editor"
      v-model:content="content"
      :content-type="'html'"
      :options="editorOption"
      :key="quillKey"
      @update:content="handleEditorChange"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, computed, inject, Ref } from 'vue'
import defaultfImg from '@/assets/images/head.png'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import '@vueup/vue-quill/dist/vue-quill.core.css'
import '@vueup/vue-quill/dist/vue-quill.bubble.css'
import { QuillEditor, Quill } from '@vueup/vue-quill'
import 'quill-mention'
import 'quill-mention/dist/quill.mention.min.css'
import './imageBlot.js'
import { ImageExtend } from './imageExtend.js'
import AutoLinks from './autoLinks.js'
import { useStore } from 'vuex'
import { StoreUserType } from '@/store/modules/user'
import { IProcessRoleAndUser } from '@/types/request'
import lodash from 'lodash'
import { BPM_MESSAGE_EDITOR } from '@/utils'

type IUserData = StoreUserType & { value: string; id: string }

Quill.register('modules/ImageExtend1', ImageExtend)
Quill.register('modules/autoLinks', AutoLinks)

const store = useStore()
const props = defineProps({
  placeholder: { type: String, default: '请输入内容' },
})
const content = ref(localStorage.getItem(BPM_MESSAGE_EDITOR))
const quillKey = Date.now() + ' ' + Math.random()
const uploadLoading = ref(false)
const refEditorBox = ref()
const editor = ref()

const processRoleInfo = inject('processRoleInfo') as Ref<IProcessRoleAndUser[]>
const userList = computed<StoreUserType[]>(() => store.state.user.allUser || [])
const projectUserList = computed<StoreUserType[]>(() => {
  let proArr: any = []
  let storeArr: StoreUserType[] = []
  processRoleInfo.value.forEach(item => {
    if (item.users) {
      proArr = [...proArr, ...item.users]
    }
  })
  proArr = lodash.uniqBy(proArr, (item: any) => item.uuid)
  proArr.forEach((pro: any) => {
    userList.value.forEach((user: StoreUserType) => {
      if (pro.uuid == user.uuid) {
        storeArr.push(user)
      }
    })
  })
  storeArr = storeArr.map(user => ({ ...user, value: user.name, id: user.uuid }))
  return storeArr
})
onMounted(async () => {
  refEditorBox.value.addEventListener('keydown', onKeyboard)
})
onBeforeUnmount(() => {
  refEditorBox.value.addEventListener('keydown', onKeyboard)
})
// 历史记录
const handleEditorChange = (data: any) => {
  localStorage.setItem(BPM_MESSAGE_EDITOR, data)
}
// 检查图片是否在上传中
const isImageUpload = () => {
  return uploadLoading.value
}
// 处理滚动元素视口显示
const onKeyboard = () => {
  const e: any = event || window.event
  if (e && (e.keyCode === 38 || e.keyCode === 40)) {
    const selected = editor.value.$el.querySelector('.ql-mention-list .ql-mention-list-item.selected')
    if (selected) {
      const oft = selected.offsetTop - 160
      selected.parentNode.scrollTop = oft > 0 ? oft : 0
    }
  }
}
const handleCustomMatcher = (node: any, delta: any) => {
  const opsList: any = []
  delta.ops.forEach((op: any) => {
    if (op.insert && (typeof op.insert === 'string' || op.insert.image1 || op.insert.mention)) {
      if (op.attributes) {
        opsList.push({ insert: op.insert, attributes: op.attributes })
      } else {
        opsList.push({ insert: op.insert })
      }
    }
  })
  delta.ops = opsList
  return delta
}

// 清空内容
const clearContent = () => {
  editor.value.setText('')
}

// 获取内容
const getContent = () => {
  const $div = document.createElement('div')
  $div.innerHTML = content.value
  const $mentionList = $div.querySelectorAll('.mention')
  const selectUser: { receiver: string; receiverUuid: string }[] = []
  $mentionList.forEach($mention => {
    if ($mention.getAttribute('data-value')?.startsWith('角色:')) {
      const users =
        $mention
          .getAttribute('data-id')
          ?.split(',')
          .map(item => {
            const [receiver, receiverUuid] = item.split('|')
            return { receiver, receiverUuid }
          }) || []
      selectUser.push(...users)
    } else {
      selectUser.push({
        receiver: $mention.getAttribute('data-value') as string,
        receiverUuid: $mention.getAttribute('data-id') as string,
      })
    }

    // 去掉每行第一项@的空格
    if (
      !$mention.previousSibling &&
      $mention.nextSibling?.nodeType === 3 &&
      $mention.nextSibling?.nodeValue?.[0] === ' '
    ) {
      $mention.nextSibling.nodeValue = $mention.nextSibling.nodeValue.substring(1)
    }

    // $mention.remove()
  })

  // 处理 links
  // const $links = $div.querySelectorAll('a[target="_blank"]')
  // $links.forEach($a => {
  //   const link = $a.getAttribute('href')
  //   const text = ($a as HTMLAnchorElement).innerText
  //   if (link !== text) {
  //     $a.setAttribute('href', text)
  //   }
  // })

  return {
    content: content.value,
    reminders: selectUser,
  }
}

// 编辑器配置
const editorOption = {
  modules: {
    toolbar: true,
    autoLinks: true,
    mention: {
      allowedChars: /^[\u4e00-\u9fa5A-Za-z\sÅÄÖåäö.]*$/,
      mentionDenotationChars: ['@'],
      source: async (searchTerm: string, renderList: (list: IUserData[]) => void) => {
        const roleList = processRoleInfo.value
          .filter(role => role.roleName.toUpperCase().includes(searchTerm.toUpperCase()) && role.name && role.uuid)
          .slice(0, 30)
          .map(item => ({
            value: '角色: ' + item.roleName,
            id: item.users.map(user => `${user.name}|${user.uuid}`).join(','),
          }))
        if (!searchTerm) {
          renderList([...roleList, ...projectUserList.value] as IUserData[])
          return
        }
        const matchedPeople = userList.value
          .filter(user => user.name.toUpperCase().includes(searchTerm.toUpperCase()))
          .slice(0, 30)
          .map(user => ({ ...user, value: user.name, id: user.uuid }))
        renderList([...roleList, ...matchedPeople] as IUserData[])
      },
      renderItem: (user: IUserData) => {
        return `
          <div class="at-item">
            <img class="avatar" src=${defaultfImg} />
            <span class="info" title="${user.value}${user.organizationsName ? `-${user.organizationsName}` : ''}">
              ${user.value}${user.organizationsName ? `-${user.organizationsName}` : ''}
            </span>
          </div>
        `
      },
      fixMentionsToQuill: true,
      defaultMenuOrientation: 'top',
      // spaceAfterInsert: false
      // offsetTop: -14
    },
    ImageExtend1: {
      name: 'file',
      response: (res: string) => res,
      start: () => (uploadLoading.value = true),
      end: () => (uploadLoading.value = false),
      isImageUpload: isImageUpload,
    },
    clipboard: {
      // 粘贴板，处理粘贴时候的自带样式
      matchers: [[Node.ELEMENT_NODE, handleCustomMatcher]],
    },
  },
  placeholder: props.placeholder,
}

const onSelectRoleFn = (data: any) => {
  editor.value.getQuill().getModule('mention').insertItem(data, true)
}

const onSetContentFn = (data: any) => {
  editor.value.setContents(data)
}

defineExpose({
  getContent,
  clearContent,
  onSelectRoleFn,
  onSetContentFn,
})
</script>

<style lang="scss" scoped>
.editor-box {
  display: flex;
  flex: 1;
  width: 100%;
  flex-direction: column;
  position: relative;
  margin-top: 16px;
  :deep(.ql-editor) {
    overflow: auto;
    position: absolute;
    height: 100%;
    width: 100%;
    min-height: 42px;
    padding-top: 0;
  }
  :deep(.ql-formats) {
    margin-right: 10px !important;
  }
  :deep(.quill-editor) {
    display: flex;
    flex: 1;
    width: 100%;
    flex-direction: column;
    word-break: break-all;
  }
  :deep(.ql-container.ql-snow) {
    border: none;
  }
  :deep(.ql-editor.ql-blank::before) {
    font-style: normal !important;
    color: #bbb;
    font-size: 12px;
  }

  :deep(.ql-mention-list-item.selected) {
    background-color: #eeeeee;
  }
  :deep(.ql-mention-list-item) {
    height: 40px;
    line-height: 18px;
    .at-item {
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 12px;
      .avatar {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        margin-right: 10px;
      }
      .info {
        font-size: 12px;
        font-weight: 400;
        color: #666666;
        line-height: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .my-tag {
        color: #378eef;
        flex-shrink: 0;
      }
    }
  }
  :deep(.ql-mention-list-container) {
    // padding: 0px 16px;
    border: none;
    border-radius: 0;
    box-shadow: none;
    overflow: visible;
  }
  :deep(.mention) {
    background-color: transparent;
    color: #378eef;
  }
  :deep(.ql-container) {
    font-size: 12px;
  }
}
.editor-box :deep(.ql-mention-list) {
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0px 4px 10px 0px rgba(102, 102, 102, 0.2);
  border-radius: 3px;
}

:deep(.image-blot-div) {
  img {
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
  }
}
</style>

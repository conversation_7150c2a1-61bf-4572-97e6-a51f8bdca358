<template>
  <div class="add-card-container">
    <div class="add-box" @click="openPopver">
      <span class="iconfont icontubiao_tianjia1"></span>
      {{ i18n.t('添加需求') }}
    </div>
    <FModal
      v-model:visible="visible"
      class="cust-add-pop"
      :title="i18n.t('添加需求分类')"
      :width="400"
      @cancel="onCancel"
      :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
    >
      <div class="tip-box">
        <i class="iconfont icontishi"></i>
        <span>{{
          i18n.t('此处为区域负责人新增流程下需求分类入口，非需求提交入口。如需提交需求任务，请点击卡片右侧的“+”。')
        }}</span>
      </div>
      <div class="add-content">
        <FForm ref="formValidate" :model="formData" label-position="top" :rules="ruleValidate" layout="vertical">
          <FFormItem :label="i18n.t('标题名称：')" class="item" name="name">
            <FInput v-model:value="formData.name" :placeholder="i18n.t('请输入')" :maxlength="50" />
          </FFormItem>
          <FFormItem :label="i18n.t('内容说明:')" class="item" name="desc">
            <FTextarea
              v-model:value="formData.desc"
              :placeholder="i18n.t('请输入')"
              :maxlength="1000"
              :autosize="{ minRows: 3, maxRows: 6 }"
            />
          </FFormItem>
        </FForm>
      </div>
      <template #footer>
        <div class="footer">
          <FButton class="close-btn" @click="onCancel" :loading="loading">{{ i18n.t('取消') }}</FButton>
          <FButton class="submit-btn" type="primary" @click="onSave" :loading="loading">{{ i18n.t('确认') }}</FButton>
        </div>
      </template>
    </FModal>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, inject, computed } from 'vue'
import { submit } from '@/api/localizationSystem/wordbenchBoard'
import type { LabelParams } from '@/types/localizationSystem/wordbenchBoard'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { messageInstance as message } from '@fs/smart-design'
import { useI18n } from '@/utils'
const i18n = useI18n()

type FromItem = {
  name: string
  desc: string
}

const getProcessLocals: any = inject('getProcessLocal')

const props = defineProps({
  addInfo: { type: Object, default: () => ({}) },
})

const formData = reactive<FromItem>({ name: '', desc: '' })
const ruleValidate = computed<Record<string, Rule[]>>(() => ({
  name: [{ required: true, message: i18n.t('请输入') }],
  desc: [{ required: true, message: i18n.t('请输入') }],
}))
let visible = ref<boolean>(false)
let loading = ref<boolean>(false)
const formValidate = ref<FormInstance>()

const openPopver = () => {
  formData.name = ''
  formData.desc = ''
  visible.value = true
  loading.value = false
  formValidate.value?.resetFields()
}

const onCancel = () => {
  formData.name = ''
  formData.desc = ''
  visible.value = false
  loading.value = false
  formValidate.value?.resetFields()
}

const onSave = async () => {
  try {
    if (!formValidate.value) return
    await formValidate.value?.validate().then(async () => {
      if (loading.value) return
      const params: LabelParams = {
        processConfigId: props.addInfo?.processConfigId || '',
        localizationRegionId: props.addInfo?.localizationRegionId || '',
        labelName: formData.name,
      }
      formData.desc && (params.ext = formData.desc)
      loading.value = true
      await submit(params)
      message.success(i18n.t('提交成功'))
      onCancel()
      getProcessLocals()
    })
  } finally {
    loading.value = false
  }
}
</script>
<style lang="scss" scoped>
.add-card-container {
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  margin-top: 8px;
  padding: 11px 0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #378eef;
  .add-box {
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      color: #5fa4f2 !important;
    }
  }
  .iconfont {
    font-size: 16px;
    margin-right: 4px;
  }
  :deep(.cust-add-pop) {
    .fs-modal-body {
      padding-bottom: 16px;
    }
    .tip-box {
      display: flex;
      padding: 12px 16px;
      margin-bottom: 24px;
      font-size: 12px;
      background: #fef4e9;
      border-radius: 3px;
      border: 1px solid #fdd2a7;
      i {
        color: #fa8f23;
        margin-top: -2px;
        margin-right: 8px;
      }
    }
    .add-content {
      .fs-form-item {
        margin-bottom: 16px;
        .fs-form-item-labe {
          width: 100%;
          text-align: left;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
        }
        .fs-form-item-control-input-content {
          height: 100% !important;
          textarea {
            height: 88px !important;
          }
          input {
            height: 32px !important;
          }
        }
        .fs-form-item-control-input {
          min-height: auto;
        }
      }
    }
    .footer {
      display: flex;
      justify-content: flex-end;
      button {
        width: 64px;
        height: 24px;
        border-radius: 3px;
      }
      .close-btn {
        border: 1px solid #cccccc;
        background: #ffffff;
        color: #333333;
      }
      .submit-btn {
        background: #378eef;
        color: #ffffff;
      }
    }
  }
}
</style>

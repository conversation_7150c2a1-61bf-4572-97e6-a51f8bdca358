// 请求
export interface IRes<T = null> {
  code: number
  msg: string
  data: T
}

export interface IDictionatyDataList<T = null> {
  pageNum: number
  pageSize: number
  total: number
  totalCount: number
  list: T[]
}
export interface IDictionatyData {
  parentId?: number | string
  name: string
  value: string
  id: number
  status: number
  isDirect: number
}

export interface SearchDictionary {
  children?: SearchDictionary[]
  ext?: string
  field?: string
  id?: number
  name?: string
  parentId?: number | null
  processConfigId?: number
  status?: number
  value?: string
  describe?: string
}

export interface ISearchDictionatyData {
  processConfigId: number
  processConfigName: string
  searchDictionarys: SearchDictionary[]
}

export interface IDictionaryConfigId {
  input?: string
  pageNum: number
  pageSize: number
  processConfigId: number
}

export interface IDictionaryConfigIdData<T = null> {
  pageSize: number
  totalCount: number
  totalPage: number
  currPage: number
  list: T[]
}

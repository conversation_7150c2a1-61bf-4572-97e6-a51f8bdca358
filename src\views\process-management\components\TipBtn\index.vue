<template>
  <div class="tip-btn-container">
    <FPopconfirm
      :disabled="!hasPop"
      :title="popTitle"
      :content="popContent || i18n.t('删除后不可恢复,请谨慎操作')"
      :ok-text="popOkText || i18n.t('确定')"
      :cancel-text="popOkText || i18n.t('取消')"
      @confirm="confirm"
      :destroy-tooltip-on-hide="true"
    >
      <FTooltip :destroy-tooltip-on-hide="true">
        <template #title>
          <span>{{ tipTitle }}</span>
        </template>
        <slot></slot>
      </FTooltip>
    </FPopconfirm>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from '@/utils'

interface IProps {
  hasPop?: boolean
  popTitle?: string
  popContent?: string
  popOkText?: string
  popCancelText?: string
  tipTitle: string
}

const props = defineProps<IProps>()
const i18n = useI18n()
const emits = defineEmits(['onConfirmFn'])
const confirm = () => {
  emits('onConfirmFn')
}
</script>

<style scoped lang="scss">
.tip-btn-container {
}
</style>

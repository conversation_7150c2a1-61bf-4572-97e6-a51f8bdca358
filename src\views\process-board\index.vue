<template>
  <div class="process-board-container">
    <!-- <Breadcrumb :data="[i18n.t('首页'), i18n.t('BPM项目看板'), i18n.t('关键绩效指标看板')]" /> -->
    <SearchBox @onGetSearchData="onGetSearchData" />
    <TipBoard
      :process-loading="processLoading"
      :task-loading="taskLoading"
      :process-data="processData"
      :task-data="taskData"
    />
    <ContentBoard
      :process-loading="processLoading"
      :task-loading="taskLoading"
      :process-data="processData"
      :task-data="taskData"
    />
    <TaskDetail
      v-if="searchData.processConfigId"
      v-model:taskDetailPageData="taskDetailPageData"
      :task-detail-loading="taskDetailLoading"
      :task-detail-data="taskDetailData"
      :process-config-id="searchData.processConfigId"
      @onExportProcess="onTaskDetailExportProcess"
      @onPageChange="getTaskDetailFn"
    />
    <ProcessDetail
      v-if="searchData.processConfigId"
      v-model:processDetailPageData="processDetailPageData"
      :process-detail-loading="processDetailLoading"
      :process-detail-data="processDetailData"
      :process-config-id="searchData.processConfigId"
      @onExportProcess="onProcessDetailExportProcess"
      @onPageChange="getProcessDetailFn"
    />
    <TableBox
      :loading="tableLoading"
      v-model:page="pageData"
      :list="list"
      @onExportProcess="onExportProcess"
      @onPageChange="getProcessList"
    />
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/views/pgb-data-board/components/Breadcrumb/index.vue'
import SearchBox from './components/SearchBox/index.vue'
import TipBoard from './components/TipBoard/index.vue'
import TableBox from './components/TableBox/index.vue'
import TaskDetail from './components/TaskDetail/index.vue'
import ProcessDetail from './components/ProcessDetail/index.vue'
import ContentBoard from './components/ContentBoard/index.vue'
import { ProcessItem, ProcessListParams, BasicPageParams } from '@/types/processBoard'
import {
  getTaskSituationList,
  getTaskSituationExport,
  getProcessSituation,
  getTaskSituationChart,
  getTaskDetailChart,
  getTaskDetailExport,
  getProcessDetailChart,
  getProcessDetailExport,
} from '@/api/processBoard'
import { messageInstance } from '@fs/smart-design'
import { ref, reactive } from 'vue'
import { useI18n } from '@/utils'
const i18n = useI18n()

const tableLoading = ref<boolean>(false)
const taskLoading = ref<boolean>(false)
const taskDetailLoading = ref<boolean>(false)
const processDetailLoading = ref<boolean>(false)
const processLoading = ref<boolean>(false)
const taskDetailPageData = reactive<BasicPageParams>({
  currPage: 1,
  pageSize: 10,
  total: 0,
})
const processDetailPageData = reactive<BasicPageParams>({
  currPage: 1,
  pageSize: 10,
  total: 0,
})
const pageData = reactive<BasicPageParams>({
  currPage: 1,
  pageSize: 10,
  total: 0,
})
const searchData = ref<ProcessListParams>({})
const processData = ref<any>(null)
const taskData = ref<any>(null)
const taskDetailData = ref<any>(null)
const processDetailData = ref<any>(null)
const list = ref<ProcessItem[]>([])

const getProcessSituationFn = async () => {
  try {
    processLoading.value = true
    const params = Object.assign({}, searchData.value) // 拷贝一份参数
    delete params.total
    const res = await getProcessSituation(params)
    processData.value = res?.data || {}
  } finally {
    processLoading.value = false
  }
}

const getTaskSituationChartFn = async () => {
  try {
    taskLoading.value = true
    const params = Object.assign({}, searchData.value) // 拷贝一份参数
    delete params.total
    const res = await getTaskSituationChart(params)
    taskData.value = res?.data || {}
  } finally {
    taskLoading.value = false
  }
}

const getProcessList = async (data = null) => {
  try {
    tableLoading.value = true
    const params: any = Object.assign({}, pageData, data, searchData.value) // 拷贝一份参数
    delete params.total
    const res = await getTaskSituationList(params)
    list.value = res?.data?.list || []
    pageData.total = res?.data?.totalCount || 0
  } finally {
    tableLoading.value = false
  }
}

const getTaskDetailFn = async (data = null) => {
  try {
    if (!searchData.value?.processConfigId) return
    taskDetailLoading.value = true
    taskDetailData.value = []
    const params: any = Object.assign({}, taskDetailPageData, data, searchData.value) // 拷贝一份参数
    delete params.total
    const res = await getTaskDetailChart(params)
    taskDetailData.value = res?.data?.list || []
    taskDetailPageData.total = res?.data?.totalCount || 0
  } finally {
    taskDetailLoading.value = false
  }
}

const getProcessDetailFn = async (data = null) => {
  try {
    if (!searchData.value?.processConfigId) return
    processDetailLoading.value = true
    processDetailData.value = []
    const params: any = Object.assign({}, processDetailPageData, data, searchData.value) // 拷贝一份参数
    delete params.total
    const res = await getProcessDetailChart(params)
    processDetailData.value = res?.data?.list || []
    processDetailPageData.total = res?.data?.totalCount || 0
  } finally {
    processDetailLoading.value = false
  }
}

const onGetSearchData = (data: any) => {
  searchData.value = data
  getProcessList()
  getProcessSituationFn()
  getTaskSituationChartFn()
  getTaskDetailFn()
  getProcessDetailFn()
}

const onTaskDetailExportProcess = async (data = null) => {
  if (!taskDetailData.value.length) {
    messageInstance.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const params: any = Object.assign({}, data, searchData.value) // 拷贝一份参数
  delete params.total
  const res = await getTaskDetailExport(params)
  const blob = new Blob([res as any])
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `file${new Date()}.xlsx`
  a.click()
  URL.revokeObjectURL(url)
}

const onProcessDetailExportProcess = async (data = null) => {
  if (!processDetailData.value.length) {
    messageInstance.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const params: any = Object.assign({}, data, searchData.value) // 拷贝一份参数
  delete params.total
  const res = await getProcessDetailExport(params)
  const blob = new Blob([res as any])
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `file${new Date()}.xlsx`
  a.click()
  URL.revokeObjectURL(url)
}

const onExportProcess = async (data = null) => {
  if (!list.value.length) {
    messageInstance.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const params = Object.assign({}, data, searchData.value) // 拷贝一份参数
  const res = await getTaskSituationExport(params)
  const blob = new Blob([res as any])
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `file${new Date()}.xlsx`
  a.click()
  URL.revokeObjectURL(url)
}
</script>

<style scoped></style>

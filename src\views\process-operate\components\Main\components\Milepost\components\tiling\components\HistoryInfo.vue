<template>
  <div class="history-info-container">
    <div class="task-item-remark">
      <p class="fontSize12 color999">{{ i18n.t('处理说明') }}：</p>
      <MyViewer :html="taskRecord.contentData ? JSON.parse(taskRecord.contentData as string).handleTaskDesc : ''" />
    </div>
    <div class="card-datasource border-none paddingB16" v-if="taskRecord.approver">
      <img src="@/assets/images/mail.png" alt="" style="width: 20px; margin-right: 5px" />
      <span style="color: #999">{{ i18n.t('审核人') }}：</span>
      <span style="margin-left: 3px; color: #333">{{ taskRecord.approver }}&nbsp;</span>
    </div>
    <div class="showFile marginT10" v-if="fileList && fileList.length > 0">
      <div class="fileList">
        <div class="content-box">
          <div class="checkbox-item" v-for="(item, index) in fileList" :key="index">
            <div class="txt">
              <p class="txt-top">
                <span class="iconfont fontSize12 marginR5" style="color: #fdb824">&#xe655;</span>
                <a @click="download(item.resourseKey)">{{ item.fileName }}</a>
                <span class="size">({{ (item.fileSize / 1024).toFixed(0) }}KB)</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import MyViewer from '@/components/Editor/MyViewer.vue' // 富文本解析
import { useI18n } from '@/utils'
import { ITask } from '@/types/handle'

interface IProps {
  taskRecord: ITask
}

const i18n = useI18n()
const props = defineProps<IProps>()
const fileList = ref<any>([])
const download = (record: string) => {
  window.open(record, '_blank')
}
watch(
  () => props.taskRecord,
  () => {
    fileList.value = props.taskRecord.attachmentData ? JSON.parse(props.taskRecord.attachmentData) : []
  },
  {
    immediate: true,
  }
)
</script>

<style lang="scss" scoped>
.history-info-container {
  .card-datasource {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 10px;
    align-items: center;
    color: #999;
    border-bottom: 1px dashed #e0e0e0;
    font-size: 12px;
    .remarks {
      flex: 0 0 100%;
      margin: 10px 5px 0 5px;
    }
  }

  .border-none {
    border: none;
  }
  .showFile {
    border-radius: 3px;
    box-sizing: border-box;
  }
  .content-box {
    display: flex;
    flex-wrap: wrap;
    .checkbox-item {
      width: 33.3333%;
      margin-bottom: 16px;

      :deep(.ivu-checkbox-wrapper) {
        display: flex;
        align-items: center;
      }
    }
    .txt {
      width: calc(100% - 26px);
      .txt-top {
        height: 12px;
        font-size: 12px;
        line-height: 12px;
        display: flex;
        margin-bottom: 5px;
        & > .icon-file {
          margin-right: 5px;
          width: 11px;
          height: 11px;
          flex-shrink: 0;
        }
        & > .file-name {
          font-size: 12px;
          color: #378eef;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
          display: inline-block;
        }
        & > .size {
          color: #bbbbbb;
          flex-shrink: 0;
          width: auto;
        }
      }
      .txt-bottom {
        height: 12px;
        font-size: 12px;
        color: #666666;
        line-height: 12px;
        .time {
          margin-left: 10px;
        }
      }
    }
  }
}
</style>

<template>
  <FCard class="add-apprival-container marginB16" :title="i18n.t('发起流程')">
    <template #extra><FButton type="link" size="small" @click="handleAllApproval">更多</FButton></template>
    <FSpin :spinning="addLoading">
      <FRow :gutter="[16, 16]" v-if="props.addApprovalData?.length">
        <FCol :span="12" v-for="(item, index) in props.addApprovalData" :key="item.id">
          <div class="add-box" @click="addApprovalFn(item)">
            <img :src="imgs[index % 4]" />
            <MoreTextTips :line-clamp="1">
              <span>{{ item.processName }}</span>
            </MoreTextTips>
          </div>
        </FCol>
      </FRow>
      <FEmpty v-else />
    </FSpin>

    <CreateProcessModal v-model="createProcessModalVisible" source="approval" :params="queryData" />
  </FCard>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { LocationQueryRaw, useRouter } from 'vue-router'
import { useI18n } from '@/utils'
import { FlowChartProcessAllRes, getApprovalCreateForm } from '@/api'
import MoreTextTips from '@/components/MoreTextTips/index'
import CreateProcessModal from '@/components/CreateProcessModal/index.vue'
import one from '../../images/one.png'
import two from '../../images/two.png'
import three from '../../images/three.png'
import zero from '../../images/zero.png'

interface IProps {
  addApprovalData?: FlowChartProcessAllRes[]
}
const imgs = { 0: zero, 1: one, 2: two, 3: three }

const props = defineProps<IProps>()
const i18n = useI18n()
const router = useRouter()
const addLoading = ref<boolean>(false)
const createProcessModalVisible = ref<boolean>(false)
const queryData = ref<LocationQueryRaw>({ localName: 'approvalWorkbench', keyType: 'formId' })

const addApprovalFn = async (item: FlowChartProcessAllRes) => {
  try {
    addLoading.value = true
    const res = await getApprovalCreateForm(item.id)
    if (res.code !== 200) throw new Error(res.msg)
    const params = { id: item.id, processDefineKey: res?.data?.id }
    router.push({
      name: 'DemandAdd',
      params,
      query: queryData.value,
    })
  } finally {
    addLoading.value = false
  }
}

const handleAllApproval = () => {
  createProcessModalVisible.value = true
}
</script>

<style scoped lang="scss">
.add-apprival-container {
  border-radius: 4px;
  overflow: hidden;
  :deep(.fs-card-body) {
    height: calc(100vh - 586px);
    min-height: 289px;
    overflow-y: auto;
    padding: 16px !important;
  }
  .add-box {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    padding: 16px;
    border-radius: 3px;
    border: 1px solid #e1e8f0;
    color: #333333;
    cursor: pointer;
    img {
      width: 16px;
      margin-right: 8px;
    }
  }
}
</style>

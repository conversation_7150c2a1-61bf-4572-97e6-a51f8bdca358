import { Translator, TFText, TranslatorOptions } from './translatro'
import { BaseDevice, GoogleDevice } from './device'
// import { useTranslator, initTranslator } from './composables/useTranslator'
// import { TranslatorPlugin } from './plugin'

// 导出类型
export type { TFText, TranslatorOptions }

// 导出基础类
export { BaseDevice, GoogleDevice }
export { Translator }

// 导出组合式函数和插件
// export { useTranslator, initTranslator }
// export { TranslatorPlugin }

// 默认导出
export default {
  // useTranslator,
  // initTranslator,
  Translator,
  BaseDevice,
  GoogleDevice,
  // TranslatorPlugin
}

<template>
  <div class="role-info card-content-shadow">
    <div
      v-sticky="{
        container: '#container',
        offset: 0,
        type: 'sticky',
        top: 0,
        zIndex: 5,
      }"
      class="header flex space-between align-items card-content-header"
    >
      <span class="title color333 fontSize16">基本属性</span>
      <div class="extra-box">
        <FSpace :size="[8]">
          <FButton @click="back" size="small">
            <template #icon>
              <i class="iconfont icontubiao_chehui marginR4 fontSize14"></i>
            </template>
            返回
          </FButton>
          <FButton v-if="isDetail" type="primary" size="small" @click="isDetail = !isDetail">编辑</FButton>
          <FButton v-if="!isDetail" size="small" @click="onCloseFn">取消</FButton>
          <FButton v-if="!isDetail" type="primary" size="small" :loading="roleConfigLoading" @click="onSubmitFn"
            >提交</FButton
          >
        </FSpace>
      </div>
    </div>
    <div class="content-box padding24">
      <FForm ref="formRef" :model="fromData" :layout="isDetail ? 'horizontal' : 'vertical'" validateTrigger="none">
        <BaseForm v-model:form-data="fromData" :components="components" :is-detail="isDetail" />
      </FForm>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted, reactive, ref, toRefs, watch, computed } from 'vue'
import { manageUpdateRole, GetTagAndNode } from '@/api'
import BaseForm from '@/views/process-class-detail/components/BaseForm/index.vue'
import CustomInput from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInput/index.vue'
import CustomSelect from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomSelect/index.vue'
import CustomInputTextarea from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInputTextarea/index.vue'
import { deepClone, trimObjectStrings } from '@/utils'
import { isNotEmpty } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'

const props = defineProps<{
  roleConfig: any
  roleId: string
  roleConfigLoading?: boolean
}>()

const { roleId, roleConfigLoading } = toRefs(props)
const fromData = ref<any>({})
const formRef = ref()
const onGetByIdRoleConfig = inject<() => Promise<void>>('onGetByIdRoleConfig')
const setRoleConfigLoading = inject<(loading: boolean) => void>('setRoleConfigLoading')
const back = inject<() => void>('back')
const isDetail = ref<boolean>(true)
const processList = ref<any[]>([])
const statusOptions = ref([
  { label: '否', value: 0, color: 'error' },
  { label: '是', value: 1, color: 'success' },
])
const roleTypeList = ref<any[]>([
  { value: 1, label: '铁六角' },
  { value: 2, label: '轮询' },
  { value: 3, label: '表达式' },
  { value: 4, label: '抄送' },
  { value: 5, label: '接口类型' },
  { value: 6, label: '抄送包含创建人' },
])
const synTypeList = ref<any[]>([
  { value: '1', label: '同步工时' },
  { value: '2', label: '同步mom' },
  { value: '3', label: '同步关联流程' },
  { value: '4', label: '同步SAP' },
])

const components = reactive<Record<string, any>>({
  baseParams: {
    fieldKey: 'baseParams',
    label: '此处为基础参数，设置isShowComponent为返回false，将不在页面显示',
    isShowComponent: () => false,
    onSubmitValueFormatFn: (config, fromData) => {
      return {
        id: roleId.value,
      }
    },
  },
  roleName: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'roleName',
    label: '角色名称',
    required: true,
    componentAttrs: {
      placeholder: '请输入角色名称',
    },
  },
  roleCode: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'roleCode',
    label: '角色编码',
    required: true,
    componentAttrs: {
      placeholder: '请输入角色编码',
    },
  },
  type: {
    component: CustomSelect,
    dataType: 'select',
    fieldKey: 'type',
    label: '角色类型',
    required: true,
    componentAttrs: {
      placeholder: '请选择角色类型',
      options: roleTypeList,
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  status: {
    component: CustomSelect,
    dataType: 'select',
    fieldKey: 'status',
    label: '启用状态',
    required: true,
    componentAttrs: {
      placeholder: '请选择启用状态',
      options: statusOptions,
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  isSyn: {
    component: CustomSelect,
    dataType: 'select',
    fieldKey: 'isSyn',
    label: '外部同步类型',
    required: true,
    isShowComponent: config => fromData.value?.type === 5,
    componentAttrs: {
      placeholder: '请选择外部同步类型',
      options: synTypeList,
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  url: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'url',
    label: '接口地址',
    required: true,
    isShowComponent: config => fromData.value?.type === 5,
    componentAttrs: {
      placeholder: '请输入角色编码',
    },
  },
  processConfigId: {
    component: CustomSelect,
    dataType: 'select',
    fieldKey: 'processConfigId',
    label: '所属流程',
    componentAttrs: {
      placeholder: '请选择所属流程',
      options: processList,
      fieldNames: { label: 'processName', value: 'id' },
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'processName',
    },
    initComponentValueFormatFn: (config, value) => value || undefined,
  },
  roleDesc: {
    component: CustomInputTextarea,
    dataType: 'textarea',
    fieldKey: 'roleDesc',
    label: '角色说明',
    colSpan: () => (isDetail.value ? 6 : 24),
    componentAttrs: {
      style: computed(() => ({
        width: isDetail.value ? 'auto' : '75%',
      })),
      placeholder: '请输入角色说明',
    },
  },
})

const onCloseFn = () => {
  // fromData.value = deepClone(props.roleConfig)
  initData()
  formRef.value?.resetFields()
  isDetail.value = true
}

const onSubmitFn = async () => {
  await formRef?.value?.validate()
  setRoleConfigLoading?.(true)
  try {
    const params = Object.entries(components).reduce((acc, [key, config]) => {
      if (config?.onSubmitValueFormatFn) {
        Object.assign(acc, config?.onSubmitValueFormatFn(config, fromData.value))
      } else if (isNotEmpty(fromData.value[key])) {
        acc[key] = fromData.value[key]
      }
      return acc
    }, {})
    await manageUpdateRole(trimObjectStrings(params as any))
    await onGetByIdRoleConfig?.()
    isDetail.value = true
  } finally {
    setRoleConfigLoading?.(false)
  }
}

const getProcessListFn = async () => {
  const res = await GetTagAndNode()
  if (res.code !== 200) throw new Error(res.msg)
  processList.value = res.data || []
}

const initData = () => {
  const data = deepClone(props.roleConfig)
  Object.entries(components).forEach(([key, config]) => {
    if (config?.initComponentValueFormatFn) {
      data[key] = config?.initComponentValueFormatFn(config, data[key])
    }
  })
  fromData.value = data
}

watch(
  () => props.roleConfig,
  newVal => {
    newVal && initData()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  requestIdleCallback(getProcessListFn)
})
</script>

<style lang="scss" scoped>
.role-info {
}

.card-content-shadow {
  background: #ffffff;
  // box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
.card-content-header {
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
  background-color: #ffffff;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.flex {
  display: flex;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.align-items {
  align-items: center;
}
.padding24 {
  padding: 24px 24px 0;
}
</style>

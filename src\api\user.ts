import { IRes } from '@/types/dictionary'
import { request } from '@/utils'

interface IUserAiPermission {
  avatarUrl: string
  fsId: string
  isShow: number // 1-可见 0-不可见
  name: string
  uuid: string
  appId: string
}

export async function checkUserAiPermission(uuid: string) {
  const res = await request.get<IRes<IUserAiPermission>>(`/api/user/isAiShow/${uuid}`)
  return res as unknown as IRes<IUserAiPermission>
}

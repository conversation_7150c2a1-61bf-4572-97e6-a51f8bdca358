<template>
  <div class="message-template-list-container">
    <!-- 面包屑 -->
    <!-- <Breadcrumb :data="[i18n.t('首页'), i18n.t('消息模板')]" /> -->
    <div class="search-box">
      <SearchContent v-model:queryData="searchData" />
    </div>
    <div class="content-box">
      <TableBox :loading="tableLoading" :list="list" @getProcessList="getProcessList" />
      <div class="fei-su-pagination">
        <FPagination
          v-model:current="pageData.pageNum"
          v-model:pageSize="pageData.pageSize"
          :total="pageData.total"
          @change="onChangeFn"
          show-size-changer
          show-quick-jumper
          :show-total="() => `${i18n.t('共')} ${pageData.total} ${i18n.t('条')}`"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/views/pgb-data-board/components/Breadcrumb/index.vue'
import SearchContent from './components/SearchContent/index.vue'
import TableBox from './components/TableBox/index.vue'
import { ref, reactive, watch } from 'vue'
import { pageMessageTemplate } from '@/api'
import { ProductListParams } from '@/types/productRemoveList'
import { PageMessageTemplateRes, PageMessageTemplateParams } from '@/types/messageTemplate'

import { useI18n, deepClone } from '@/utils'
const i18n = useI18n()

const tableLoading = ref<boolean>(false)
const pageData = reactive<ProductListParams>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const searchData = ref<PageMessageTemplateParams>({})
const list = ref<PageMessageTemplateRes[]>([])

const onGetSearchData = (data: any) => {
  searchData.value = data
  pageData.pageNum = 1
  getProcessList()
}

const onChangeFn = (current: number, pageSize: number) => {
  pageData.pageNum = current
  pageData.pageSize = pageSize
  getProcessList()
}

const getProcessList = async () => {
  try {
    tableLoading.value = true
    const params = deepClone(Object.assign({}, pageData, searchData.value)) // 拷贝一份参数
    delete params.total
    const res = await pageMessageTemplate(params)
    list.value = res?.data?.list || []
    pageData.total = res?.data?.totalCount || 0
  } finally {
    tableLoading.value = false
  }
}

watch(searchData, (val: any) => {
  onGetSearchData(val)
})
</script>

<style scoped lang="scss">
.message-template-list-container {
  .search-box {
    display: flex;
    justify-content: space-between;
    padding: 22px 24px 10px 24px;
    // margin-top: 24px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
    :deep(.fs-select-selection-placeholder) {
      color: #bbb !important;
    }
  }
  .content-box {
    margin-top: 16px;
    padding: 24px 24px 0;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
}
</style>

<template>
  <div class="add-field-container">
    <FModal
      width="720px"
      v-model:visible="visible"
      :title="(type && i18n.t('创建模板')) || i18n.t('编辑模板')"
      :confirmLoading="confirmLoading"
      @cancel="cancelFn"
      @ok="submitFn"
    >
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FRow :gutter="[24, 0]">
          <FCol :span="12">
            <FFormItem :label="i18n.t('模板名称')" name="templateName">
              <FInput
                style="width: 100%"
                v-model:value="formState.templateName"
                :placeholder="i18n.t('请输入模板名称')"
              />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('模板编码')" name="templateCode">
              <FInput
                style="width: 100%"
                v-model:value="formState.templateCode"
                :placeholder="i18n.t('请输入模板编码')"
              />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('通知类型')" name="type">
              <FSelect
                :placeholder="i18n.t('请选择通知类型')"
                v-model:value="formState.type"
                :options="typeOptions"
                allow-clear
              />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('模板状态')" name="status">
              <FSelect
                :placeholder="i18n.t('请选择模板状态')"
                v-model:value="formState.status"
                :options="statusOptions"
                allow-clear
              />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('飞书模板ID')" name="feishuId">
              <FInput v-model:value="formState.feishuId" :placeholder="i18n.t('请输入飞书模板ID')" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('参数字段')" name="fields">
              <FInput v-model:value="formState.fields" :placeholder="i18n.t('请输入参数字段')" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('字典字段')" name="enumFields">
              <JsonData v-model:value="formState.enumFields" />
              <!-- <FInput v-model:value="formState.enumFields" :placeholder="i18n.t('请输入字典字段')" /> -->
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('回显飞书模板ID')" name="showTemplateCode">
              <FSelect
                :placeholder="i18n.t('请选择回显飞书模板')"
                v-model:value="formState.showTemplateCode"
                :options="allCodeList"
                allow-clear
                show-search
                option-filter-prop="label"
              />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('模板说明')" name="remarks">
              <FTextarea
                v-model:value="formState.remarks"
                :auto-size="{ minRows: 4 }"
                :placeholder="i18n.t('请输入模板说明')"
              />
            </FFormItem>
          </FCol>
        </FRow>
      </FForm>
      <div></div>
    </FModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { useI18n } from '@/utils'
import { MessageTemplateParams } from '@/types/messageTemplate'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { message } from '@fs/smart-design'
import { saveMessageTemplate, updateMessageTemplate, getAllCode } from '@/api'
import JsonData from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomJsonData/components/JsonData/index.vue'

interface IProps {
  visible: boolean
  data?: MessageTemplateParams
  type?: boolean
}

const i18n = useI18n()
const props = defineProps<IProps>()
const emits = defineEmits(['update:visible', 'getFieldList'])
const confirmLoading = ref<boolean>(false)
const visible = computed({
  get: () => props.visible,
  set: val => emits('update:visible', val),
})
const options = computed(() => [
  { label: i18n.t('禁用'), value: 0 },
  { label: i18n.t('启用'), value: 1 },
])
const statusOptions = computed(() => [
  { label: i18n.t('禁用'), value: 0 },
  { label: i18n.t('启用'), value: 1 },
])
const formRef = ref<FormInstance>()
const formState = reactive<MessageTemplateParams>({
  templateName: undefined,
  templateCode: undefined,
  type: undefined,
  status: undefined,
  feishuId: undefined,
  fields: undefined,
  enumFields: undefined,
  remarks: undefined,
  id: undefined,
  createdTime: undefined,
  createdUserId: undefined,
  createdUserName: undefined,
  showTemplateCode: undefined,
})

const validatorKeys = reactive<Record<string, any>>({
  enumFields: async rule => {
    await nextTick()
    const data = formState?.[rule?.field]
    if (data && Object.keys(JSON.parse(data)).some(key => !key.trim())) {
      return Promise.reject('key不能为空格')
    }
    return Promise.resolve()
  },
})
const rules: Record<string, Rule[]> = {
  templateName: [{ required: true, message: i18n.t('请输入模板名称') }],
  templateCode: [{ required: true, message: i18n.t('请输入模板编码') }],
  type: [{ required: true, message: i18n.t('请选择通知类型') }],
  status: [{ required: true, message: i18n.t('请选择模板状态') }],
  feishuId: [{ required: true, message: i18n.t('请输入飞书模板ID') }],
  fields: [{ required: true, message: i18n.t('请输入参数字段') }],
  enumFields: [{ required: true, validator: validatorKeys.enumFields }],
}
const typeOptions = ref<any>([{ label: i18n.t('飞书通知'), value: 1 }])
const allCodeList = ref<any[]>([])

const getAllCodeFn = async () => {
  const res = await getAllCode()
  if (res.code !== 200) throw new Error(res.msg)
  allCodeList.value = res?.data || []
}

const cancelFn = () => {
  formRef.value?.resetFields()
  visible.value = false
}

const submitFn = async () => {
  try {
    confirmLoading.value = true
    if (!formRef.value) {
      return
    }
    await formRef.value.validate()
    let res
    if (props.type) {
      res = await saveMessageTemplate(formState)
    } else {
      res = await updateMessageTemplate(formState)
    }
    if (res.code !== 200) throw new Error(res.msg)
    message.success((props.type && i18n.t('创建模板成功')) || i18n.t('编辑模板成功'))
    cancelFn()
    emits('getFieldList')
  } finally {
    confirmLoading.value = false
  }
}

const setFieldFn = (data: MessageTemplateParams) => {
  Object.entries(formState).forEach(([key, value]) => {
    formState[key as keyof typeof formState] = data[key as keyof typeof data]
  })
}

watch(
  () => visible.value,
  val => {
    val && setFieldFn((props.data as MessageTemplateParams) || {})
  }
)

onMounted(() => {
  requestIdleCallback(getAllCodeFn)
})
</script>
<style scoped lang="scss">
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
</style>

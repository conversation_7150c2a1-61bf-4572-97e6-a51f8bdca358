import { NodeConfig, GraphModel } from '@logicflow/core'
import { BpmnBaseModel, BpmnBaseNode } from './bpmnBase'

class ApprovalModel extends BpmnBaseModel {
  constructor(data: NodeConfig, graphModel: GraphModel) {
    super(data, graphModel)
    this.themeColor = '#2FCC83'
  }
}

class ApprovalView extends BpmnBaseNode {}

export default {
  type: 'bpmn:approval',
  view: ApprovalView,
  model: ApprovalModel,
}

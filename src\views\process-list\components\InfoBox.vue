<template>
  <Card :title="`${i18n.t('消息协同')}(${props.data.length})`" icon="iconxiaoxixietong">
    <div class="info-box" v-if="!props.data.length">
      <div class="empty-box">
        <img style="height: 80px" src="@/assets/images/empty.png" />
        <p class="text">{{ i18n.t('暂无数据') }}</p>
      </div>
    </div>
    <!-- 无缝滚动效果 -->
    <div class="marquee-wrap" v-else>
      <ul class="marquee-list" :class="{ 'animate-up': animateUp }">
        <li v-for="(item, index) in listData" :key="index" @click="goToDetail(item)">
          <div class="info-title">
            <i class="tips" /> 【{{ `${item.processType}-${item.projectNum}` }}】 <a>{{ item.sendBy }}</a
            >--{{ i18n.t('向您提了一个问题') }}
            <span class="info-time">{{ transformDate(item.sendDate) }}</span>
          </div>
          <div class="info-content">{{ getHTMLSpingtext(item.content) }}</div>
        </li>
      </ul>
    </div>
    <template #titleRight>
      <i class="iconfont icontubiao_zhankai1 cursor" @click="goToMessageList"></i>
    </template>
  </Card>
</template>
<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { transformDate, getHTMLSpingtext, useI18n } from '@/utils'
import { ISeeMessage } from '@/types/request'
import Card from '@/components/Card/index.vue'

interface IProps {
  data: ISeeMessage[]
}

const i18n = useI18n()
const router = useRouter()
const props = defineProps<IProps>()
const listData = ref<ISeeMessage[]>([])
const animateUp = ref<boolean>(false)
const timer = ref<any>(null)

watch(
  () => props.data,
  () => {
    listData.value = props.data
  }
)
onMounted(() => {
  timer.value = null
  timer.value = setInterval(scrollAnimate, 3000)
})

const scrollAnimate = () => {
  animateUp.value = true
  setTimeout(() => {
    listData.value.push(listData.value[0])
    listData.value.shift()
    animateUp.value = false
  }, 500)
}

// 需求详情跳转
const goToDetail = (row: ISeeMessage) => {
  router.push({
    name: 'ProcessDetail',
    params: { id: row.instanceId },
  })
}

const goToMessageList = (record: any) => {
  router.push({
    name: 'messageList',
  })
}
</script>
<style lang="scss" scoped>
.empty-box {
  text-align: center;
  position: relative;
  padding-top: 24px;
  .text {
    color: #b2b7bf;
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
  }
}
.marquee-wrap {
  width: 100%;
  height: 90px;
  overflow: hidden;
  .marquee-list {
    li {
      cursor: pointer;
      width: 100%;
      min-width: 300px;
      height: 100px;
      text-overflow: ellipsis;
      overflow: hidden;
      // white-space: nowrap;
      padding: 30px 0px;
      list-style: none;
      color: #333;
      font-size: 12px;
      .info-title {
        color: #666;
        margin-bottom: 6px;
        .tips {
          display: inline-block;
          width: 6px;
          height: 6px;
          background: #378eef;
          border-radius: 50%;
          margin-right: 10px;
        }
        .info-time {
          color: #999;
          float: right;
        }
      }
      .info-title:hover {
        color: #378eef;
      }
      .info-content {
        padding-left: 20px;
        max-width: 600px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .animate-up {
    transition: all 0.5s ease-in-out;
    transform: translateY(-100px);
  }
}
</style>

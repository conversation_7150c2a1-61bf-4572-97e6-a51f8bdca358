<template>
  <span :class="['milepost-tag', props.type]" :title="props.text">
    <slot>{{ props.text }}</slot>
  </span>
</template>

<script setup lang="ts">
interface IProps {
  type: 'default' | 'success' | 'warning' | 'danger'
  text?: string
}

const props = defineProps<IProps>()
</script>

<style scoped lang="scss">
.milepost-tag {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 3px;
  transition: color 0.5s, background-color 0.5s;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.default {
    color: #666;
    background-color: #f8f8f8;
  }

  &.success {
    color: #2fcc83;
    background-color: #eafaf2;
  }

  &.warning {
    color: #fa8f23;
    background-color: #fef4e9;
  }

  &.danger {
    color: #f04141;
    background-color: #fdecec;
  }
}
</style>

import { ref } from 'vue'
import { SelectProps } from '@fs/smart-design/dist/ant-design-vue_es'

export interface IPage {
  pageNum: number // 当前页
  pageSize: number // 每页条数
  total: number // 总条数
}
export interface ExcutionListSearch {
  processConfigId?: number | string
  status?: number | string
  triggerType?: number | string
  startTime?: string
  endTime?: string
  input?: string
  pageNum?: number
  pageSize?: number
}
export interface triggerListSearch {
  processConfigId?: number
  status?: number
  triggerType?: number
  createdUserId?: string
  startTime?: string
  endTime?: string
  input?: string
  isSyn?: number
  publishStatus?: number
}
export interface event {
  condition?: string
  createdTime?: string
  createdUserId?: string
  createdUserName?: string
  eventName?: string
  eventPriority: number
  eventType: number
  id: number | string
  milepostNodeId?: string | number
  processConfigId: number | string
  taskNodeId?: string
  updateCondition?: string
  updateField?: string
  updateTime?: string
  updateUserId?: string
  updateUserName?: string
  url?: string
  milepostNodeName: string
  taskNodeName: string
  processDefineKey: string | number | null
  fields: string
  processDefineName: string
  templateCode?: string | number | null
  roleCode?: any
  chatsId?: string
  [key: string]: any
}

export interface ExcutionForm extends ExcutionListSearch {
  pageNum: number
  pageSize: number
}
export interface IResExcution<T = null> {
  code: number
  msg: string
  traceId: string
  data: T
}
export interface TriggerDataList<T = null> {
  endRow: number
  nextPage: number
  pageNum: number
  pageSize: number
  pages: number
  prePage: number
  size: number
  startRow: number
  total: number
  totalCount: number
  list: T[]
}

export interface TriggerData<T = null> {
  condition: string
  createdTime: string
  createdUserId: string
  createdUserName: string
  eventIds: string
  id: number
  milepostNodeId: string
  processConfigId: number
  remarks: string
  status: number
  taskNodeId: string
  triggerName: string
  triggerType: number
  updateCondition: string
  updateTime: string
  updateUserId: string
  updateUserName: string
  events: T[]
  processDefineKey?: number
  isSyn: number
  [key: string]: any
}

export interface LogData {
  createdTime: string
  eventName: string
  eventType: number
  id: number
  remarks: string
  sourceInstanceId: number
  sourceNumber: string
  sourceProcessConfigId: number | string
  sourceProcessConfigName: string
  sourceTopicName: string
  status: number
  targetInstanceId: number
  targetNumber: string
  targetProcessConfigId: number | string
  targetProcessConfigName: string
  targetTopicName: string
  triggerName: string
  triggerType: number
}

export interface addOrEditTriggerData {
  condition?: string
  createdUserId?: string
  endTime?: string
  eventIds?: string
  id?: number
  input?: string
  milepostNodeId?: string
  pageNum?: number
  pageSize?: number
  processConfigId?: number
  remarks?: string
  startTime?: string
  status?: number
  taskNodeId?: string
  triggerName?: string
  triggerType?: number
  updateCondition?: string
}
export interface addOrEditAction {
  condition?: string
  eventName?: string
  eventPriority?: number | string
  eventType?: number | string
  id: number | string
  milepostNodeId?: string | string
  processConfigId?: number | string
  taskNodeId?: number | string
  updateCondition?: string
  updateField?: string
  url?: string
  milepostNodeName?: string
  taskNodeName?: string
  processDefineKey: string
  processDefineName: string
  fields: string
  templateCode?: string | number | null
  roleCode?: string | number | null
  chatsId?: string | null
}
export interface processTye {
  businessType: string
  createdTime: string
  dictionaryId: number
  id: number | string
  invalid: number
  isSendmsg: 0 | 1
  prefix: string
  processDefineKey: string
  processDefineKeyName: string
  processName: string
  readMark: string
  remark: string
  sort: number
  source: number
  tag: string | null
  updateTime: string
  writeMark: string | null
}
export interface nodeType {
  createdTime?: string
  id: number
  milepostName: string
  nodeId: string
  nodeMode?: number
  parentId?: number
  processDefineNodeKey: string
  sort?: number
  superviser?: string
  tag?: string
  updatedTime?: string
}
export interface taskType {
  id: string
  name: string
}
interface labelValue {
  value: number
  label: string
}
export enum TypeActionEnum {
  ADD_NO_CHECK_TASK = 12,
  PROCESS_FININSH = 11,
  GROUP_TASK_MESSAGE_NOTIFICATION = 10,
  TASK_MESSAGE_NOTIFICATION = 9,
  GROUP_NODE_MESSAGE_NOTIFICATION = 8,
  NODE_MESSAGE_NOTIFICATION = 7,
  NODE_REJECT = 6,
  ADD_TASK = 5,
  UPDATE = 4,
  CALL_INTERFACE = 3,
  NODE_FININSH = 2,
  TASK_FINISH = 1,
}
export const actionTypeList = ref<labelValue[]>([
  { value: TypeActionEnum.UPDATE, label: '值更新' },
  { value: TypeActionEnum.NODE_FININSH, label: '节点完成' },
  { value: TypeActionEnum.TASK_FINISH, label: '任务完成' },
  { value: TypeActionEnum.CALL_INTERFACE, label: '调用接口' },
  { value: TypeActionEnum.ADD_TASK, label: '添加任务' },
  { value: TypeActionEnum.NODE_REJECT, label: '节点驳回' },
  { value: TypeActionEnum.NODE_MESSAGE_NOTIFICATION, label: '节点消息通知' },
  { value: TypeActionEnum.GROUP_NODE_MESSAGE_NOTIFICATION, label: '节点群消息通知' },
  { value: TypeActionEnum.TASK_MESSAGE_NOTIFICATION, label: '任务消息通知' },
  { value: TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION, label: '任务群消息通知' },
  { value: TypeActionEnum.PROCESS_FININSH, label: '办结' },
  { value: TypeActionEnum.ADD_NO_CHECK_TASK, label: '添加任务(无检验)' },
])
export enum TypeTriggerEnum {
  FEISHU_APPROVAL_CALLBACK = 10,
  TASK_POOL_FININSH = 9,
  WORK_FLOW_FININSH = 8,
  TASK_SAVE = 7,
  NODE_SAVE = 6,
  PROCESS_CREATE = 5,
  UPDATE = 4,
  NODE_FININSH = 3,
  TASK_FINISH = 2,
  TIME = 1,
}
export const triggerTypeRadioList = ref<labelValue[]>([
  { value: TypeTriggerEnum.UPDATE, label: '值更新' },
  { value: TypeTriggerEnum.NODE_FININSH, label: '节点完成' },
  { value: TypeTriggerEnum.TASK_FINISH, label: '任务完成' },
  { value: TypeTriggerEnum.TIME, label: '定时执行' },
  { value: TypeTriggerEnum.PROCESS_CREATE, label: '新建流程' },
  { value: TypeTriggerEnum.NODE_SAVE, label: '保存节点' },
  { value: TypeTriggerEnum.TASK_SAVE, label: '保存任务' },
  { value: TypeTriggerEnum.WORK_FLOW_FININSH, label: '流程办结' },
  { value: TypeTriggerEnum.TASK_POOL_FININSH, label: '任务池提交' },
  { value: TypeTriggerEnum.FEISHU_APPROVAL_CALLBACK, label: '飞书审批回调' },
])

export const FAKE_ID = 'FAKE_ID'

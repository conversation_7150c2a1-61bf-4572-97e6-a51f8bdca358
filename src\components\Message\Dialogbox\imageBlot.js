/* eslint-disable */
// 重新扩展自定义 img 标签
import { quillEditor, Quill } from '@vueup/vue-quill'
const BlockEmbed = Quill.import('formats/image')
class ImageBlot extends BlockEmbed {
  static create(value) {
    const node = super.create(value.imgURL)
    if (typeof value === 'string') {
      node.setAttribute('src', this.sanitize(value))
    } else {
      node.setAttribute('src', this.sanitize(value.imgURL))
    }
    node.setAttribute('attr-uuid', value.uuid)
    node.setAttribute('style', 'max-width:200px;max-height:150px;margin:5px 0;display:block')
    node.addEventListener('click', () => window.open(node.src))
    // 'width:100%;border: 1px solid rgba(0,0,0,0.025);border-radius: 3px;'
    return node
  }

  static value(domNode) {
    return {
      imgURL: domNode.getAttribute('src'),
      uuid: domNode.getAttribute('attr-uuid'),
    }
  }
}
ImageBlot.blotName = 'image1'
Quill.register({
  'formats/image1': ImageBlot,
})

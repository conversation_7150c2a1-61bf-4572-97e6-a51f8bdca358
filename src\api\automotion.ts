import { request } from '@/utils'
import type {
  IResExcution,
  ExcutionForm,
  TriggerDataList,
  TriggerData,
  event,
  taskType,
  addOrEditAction,
  addOrEditTriggerData,
  nodeType,
  processTye,
  LogData,
} from '@/types/excutionListModel'
import { IResData } from '@/types/class'
export function queryTriggerList(data: ExcutionForm): Promise<IResExcution<TriggerDataList<TriggerData<event>>>> {
  return request.post('/api/eventTrigger/getPage', data)
}

export function queryExcutionList(data: ExcutionForm): Promise<IResExcution<TriggerDataList<LogData>>> {
  return request.post('/api/eventLog/getEventLog', data)
}

export function addApiTrigger(data: addOrEditTriggerData): Promise<IResData> {
  return request.post('/api/eventTrigger/save', data)
}

export function editApiTrigger(data: addOrEditTriggerData): Promise<IResData> {
  return request.post('/api/eventTrigger/update', data)
}

export function deleteTrigger(id: number): Promise<IResExcution> {
  return request.get(`/api/eventTrigger/delById/${id}`)
}

// 不带权限的流程列表
export function getProcessTypeList(): Promise<IResExcution<processTye[]>> {
  return request.get(`/api/bpmDefine/getAllProcessConfig`)
}

// 新不带权限的流程列表
export function getProcessTypeListNew(): Promise<IResExcution<processTye[]>> {
  return request.get(`/api/eventTrigger/getAllProcessConfig`)
}

// 里程碑节点
export function getResNodeList(processConfigId: number | string): Promise<IResExcution<nodeType[]>> {
  const hostFix = processConfigId ? `?processConfigId=${processConfigId}` : ``
  return request.get(`/api/bpmDefine/getNode${hostFix}`)
}

export function getTaskList(processDefineNodeKey: string): Promise<IResExcution<taskType[]>> {
  return request.get(`/api/bpmDefine/selectByTaskNode/${processDefineNodeKey}`)
}

export function getTriggerById(id: any): Promise<IResExcution<TriggerData<event>>> {
  return request.get(`/api/eventTrigger/getTriggerById/${id}`)
}

export function addApiAction(data: addOrEditAction): Promise<IResExcution<event>> {
  return request.post('/api/event/save', data)
}

export function editApiAction(data: addOrEditAction): Promise<IResExcution<event>> {
  return request.post('/api/event/update', data)
}
export function deleteApiAction(id: number): Promise<IResData> {
  return request.get(`/api/event/delById/${id}`)
}

// copy触发器
export function copyTrigger(data: { id: number; triggerName: string }): Promise<IResData> {
  return request.post('/api/eventTrigger/copy', data)
}

// copy触发器
export function callApiWithRetriesTrigger(params: { id: number }): Promise<IResData> {
  return request.post('/api/eventLog/callApiWithRetries', {}, { params })
}

// 测试触发器
export function testTrigger(id: number, params: { instanceCode: string }): Promise<IResExcution> {
  return request.get(`/api/eventTrigger/executeJob/${id}`, { params })
}

// 重置触发器
export function rollbackTrigger(data): Promise<IResExcution> {
  return request.post(`/api/processSyn/rollbackTrigger`, data)
}

// 同步触发器
export function synTrigger(data): Promise<IResExcution> {
  return request.post(`/api/processSyn/synTrigger`, data)
}

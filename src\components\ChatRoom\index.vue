<template>
  <div class="chat-room">
    <!-- 聊天信息展示列表 -->
    <ChatLogs :data="chatLogs" @operate="handleOperate" />
    <!-- 聊天输入框 -->
    <InputBox ref="inputBoxRef" @send="handleSend" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, provide, ref, watch } from 'vue'
import { IMessage, IProcessRoleAndUser, IReceiver } from '@/types/request'
import { message } from '@fs/smart-design'
import { useI18n } from '@/utils'
import {
  getProcessMessage,
  sendMessage,
  ISendMessageParams,
  getProcessRoleAndUser,
  urgentMessage,
  batchWithdraw,
  editMessage,
} from '@/api'

import ChatLogs from './components/ChatLogs/index.vue'
import InputBox from './components/InputBox/index.vue'

interface IProps {
  processNo: string // 流程编号
  instanceId: number // 流程实例id
  processName: string // 流程名称
  paramsWrapper: <T>(data: T) => T
}

const i18n = useI18n()
const props = defineProps<IProps>()
const inputBoxRef = ref()
const chatLogs = ref<IMessage[]>([])
const roleList = ref<IProcessRoleAndUser[]>([])
const charLogsloading = ref(true)

watch(
  () => props,
  () => init(),
  { deep: true }
)

onMounted(() => init())

const init = () => {
  charLogsloading.value = true
  chatLogs.value = []
  getMessageList().finally(() => (charLogsloading.value = false))
  getProcessRoleInfo()
}

const handleOperate = async (params: { type: string; item: IMessage }) => {
  const { type, item } = params
  const editor = inputBoxRef.value.getEditor()

  // 回复
  if (type === 'reply') {
    const mentionNode = {
      type: 'mention',
      value: item.sendBy,
      info: { uuid: item.sendByUuid },
      children: [{ text: '' }],
    }
    editor.restoreSelection()
    editor.insertNode(mentionNode)
    editor.move(1)
  }

  // 加急
  if (type === 'urgent') {
    const data = {
      uuids: (item.messageReadRsps || []).map(item => item.uuid),
      processName: props.processName,
      messageInfoId: item.id,
    }

    await urgentMessage(data)
    message.success(i18n.t('已发送加急通知'))
  }

  // 撤回
  if (type === 'revoke') {
    const now = new Date().getTime()
    const sendTime = new Date(item.sendDate).getTime()
    const diff = (now - sendTime) / 1000 / 60
    if (diff > 10) return message.warn(i18n.t('仅10分钟内可重新编辑撤回消息！'))
    await batchWithdraw(item.id)
    message.success(i18n.t('已撤回消息'))
    editor.setHtml(item.content)
    inputBoxRef.value.setFileList(item.file)
    await getMessageList()
  }
}

const handleSend = async ({ file, content, receiver }: { file: File[]; content: string; receiver: IReceiver[] }) => {
  const data: ISendMessageParams = props.paramsWrapper({
    projectNum: props.processNo,
    instanceId: props.instanceId,
    content,
    receiverReqs: receiver,
    file,
  })
  await sendMessage(data)
  inputBoxRef.value.clearEditor()
  await getMessageList()
}

// 获取流程角色信息
const getProcessRoleInfo = async () => {
  const { data = [] } = await getProcessRoleAndUser(props.instanceId)
  roleList.value = data
}

// 获取消息列表
const getMessageList = async () => {
  if (!props?.processNo) return
  const { data = [] } = await getProcessMessage(props.instanceId)
  chatLogs.value = data
}

provide('roleList', roleList)
provide('charLogsloading', charLogsloading)
</script>

<style scoped lang="scss">
.chat-room {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding-top: 20px;
  box-sizing: border-box;
  overflow: hidden;
}
</style>

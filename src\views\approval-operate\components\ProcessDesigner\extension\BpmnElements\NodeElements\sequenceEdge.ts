import { EdgeConfig, GraphModel, PolylineEdge, PolylineEdgeModel, h } from '@logicflow/core'
import { genBpmnId } from '@logicflow/extension/es/bpmn-elements/utils'

class model extends PolylineEdgeModel {
  static extendKey = 'SequenceFlowModel'
  constructor(data: EdgeConfig, graphModel: GraphModel) {
    if (!data.id) data.id = `Flow_${genBpmnId()}`
    super(data, graphModel)
  }

  getEdgeStyle() {
    const style = super.getEdgeStyle()
    style.stroke = '#CCCCCC'
    return style
  }
}

class view extends PolylineEdge {
  static extendKey = 'SequenceFlowEdge'
}

const SequenceFlow = {
  type: 'bpmn:sequenceFlow',
  view,
  model,
}

export default SequenceFlow

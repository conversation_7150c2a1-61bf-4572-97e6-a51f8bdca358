import type { BasicFetchResponse, BasicPageParams } from '../common'

export interface TreeParams {
  localizationConfigId?: string | symbol | number
  localizationRegionId?: string | symbol | number
}

export interface TreeLabelItem {
  id?: number
  labelCode?: string
  labelName?: string
  localizationConfigId?: number
  localizationRegionId?: number
  processConfigId?: string
  [key: string]: any
}

export interface TreeItem {
  id?: number
  localizationConfigId?: number
  localizationRegionId?: number
  localizationLabels: TreeLabelItem[]
  processConfigId: number
  processConfigName: string
  [key: string]: any
}

export interface ProcessTree extends BasicFetchResponse {
  data: TreeItem[]
  [key: string]: any
}

export interface ListParams extends BasicPageParams {
  labelCode?: string
  localizationConfigId?: number | string
  localizationRegionId?: number | string
  processConfigId?: number | string
  status?: number | string
}

export interface LabelListItem {
  [key: string]: any
}

export interface ProcessLabel extends BasicFetchResponse {
  data: {
    currPage?: number | string
    pageSize?: number | string
    totalCount?: number | string
    totalPage?: number | string
    list?: LabelListItem[]
  }
  [key: string]: any
}

export interface LabelParams {
  ext?: string
  labelName?: string
  localizationConfigId?: number | string
  localizationRegionId?: number | string
  processConfigId?: number | string
  [key: string]: any
}

export interface UpdateLabelParams {
  id: number
  sort: number
}

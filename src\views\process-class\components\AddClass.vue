<template>
  <FModal
    v-model:visible="dialogVisible"
    :bodyStyle="{ maxHeight: '80vh', overflow: 'scroll' }"
    centered
    :title="title"
    :width="700"
    @cancel="close"
  >
    <div class="process-class-modal-wrapper">
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <f-row>
          <f-col :span="12">
            <FFormItem label="流程名称" name="processName">
              <FInput v-model:value="formState.processName" placeholder="请输入流程名称" />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="字典标签" name="dictionaryId">
              <FSelect
                v-model:value="formState.dictionaryId"
                :options="dictionary"
                :field-names="{ label: 'name', value: 'id' }"
                allow-clear
                placeholder="请选择字典标签"
              />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem class="handle-process-workflow-box" name="processDefineKey">
              <template #label>
                <span>关联流程图</span>
                <HandleProcess :processWorkflowId="formState.processDefineKey" />
              </template>
              <FSelect
                placeholder="请选择"
                v-model:value="formState.processDefineKey"
                :options="processList"
                :field-names="{ label: 'processName', value: 'id' }"
                show-search
                option-filter-prop="processName"
                allow-clear
              />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="普通权限标识" name="readMark">
              <FInput v-model:value="formState.readMark" placeholder="请输入普通权限标识" />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="高级权限标识" name="writeMark">
              <FInput v-model:value="formState.writeMark" placeholder="请输入高级权限标识" />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="流程编号前缀" name="prefix">
              <FInput v-model:value="formState.prefix" placeholder="请输入流程编号权限" />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="排序" name="sort">
              <FInputNumber v-model:value="formState.sort" style="width: 100%" placeholder="排序" />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="流程类型" name="groupCode">
              <FSelect
                v-model:value="formState.groupCode"
                :options="processTypeOptions"
                allow-clear
                show-search
                option-filter-prop="label"
                placeholder="请选择流程类型"
              />
              <!-- <FInput v-model:value="formState.businessType" placeholder="排序" /> -->
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="延期通知" name="isNotice">
              <FSelect placeholder="请选择" v-model:value="formState.isNotice" :options="isNoticeLists" allow-clear />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="删除权限标识" name="isDeleteMake">
              <FInput v-model:value="formState.isDeleteMake" placeholder="请输入删除权限标识" />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="流程来源" name="source">
              <FSelect
                v-model:value="formState.source"
                :options="[
                  { label: 'BPM', value: 1 },
                  { label: '合同', value: 2 },
                  { label: '权限', value: 3 },
                  { label: '审批流', value: 4 },
                  { label: '外部审批流', value: 5 },
                ]"
              />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="流程编码" name="processCode">
              <FInput v-model:value="formState.processCode" placeholder="请输入流程编码" />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="流程链接" name="detailsUrl">
              <FInput v-model:value="formState.detailsUrl" placeholder="请输入流程链接" />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem class="height-auto-box" label="概要信息" name="outline">
              <JsonData v-model:value="formState.outline" />
              <!-- <FInput v-model:value="formState.outline" placeholder="请输入概要信息" /> -->
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem class="height-auto-box" label="标签" name="tag">
              <!-- <FTextarea v-model:value="formState.tag" placeholder="请输入标签" /> -->
              <JsonData v-model:value="formState.tag" />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="是否启动时效管理" name="invalid">
              <f-space direction="vertical">
                <FRadioGroup v-model:value="formState.invalid" :options="plainOptions" />
              </f-space>
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="飞书消息开关" name="isSendmsg">
              <FRadioGroup
                v-model:value="formState.isSendmsg"
                :options="[
                  { label: '开启', value: 1 },
                  { label: '关闭', value: 0 },
                ]"
              />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="开放任务编辑权限" name="taskEdit">
              <FRadioGroup
                v-model:value="formState.taskEdit"
                :options="[
                  { label: '开启', value: 1 },
                  { label: '关闭', value: 0 },
                ]"
              />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="是否填写延期原因" name="isDelayReason">
              <FRadioGroup
                v-model:value="formState.isDelayReason"
                :options="[
                  { label: '开启', value: 1 },
                  { label: '关闭', value: 0 },
                ]"
              />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="更新预计完成时间是否发送消息" name="isUpdateCompleteSend">
              <FRadioGroup
                v-model:value="formState.isUpdateCompleteSend"
                :options="[
                  { label: '开启', value: 1 },
                  { label: '关闭', value: 0 },
                ]"
              />
            </FFormItem>
          </f-col>
          <f-col :span="12">
            <FFormItem label="流程管理员" name="admins">
              <FSelect
                placeholder="请选择"
                v-model:value="formState.admins"
                :options="userList"
                :field-names="{ label: 'name', value: 'uuid' }"
                show-search
                option-filter-prop="name"
                allow-clear
                mode="multiple"
                maxTagCount="responsive"
              />
            </FFormItem>
          </f-col>
          <f-col :span="24">
            <FFormItem class="config-json" label="流程终止标签" name="finishDesc">
              <FTextarea v-model:value="formState.finishDesc" placeholder="请输入流程终止标签" />
            </FFormItem>
          </f-col>
          <f-col :span="24">
            <FFormItem class="config-json" label="流程驳回标签" name="rejectDesc">
              <FTextarea v-model:value="formState.rejectDesc" placeholder="请输入流程驳回标签" />
            </FFormItem>
          </f-col>
          <f-col :span="24">
            <FFormItem label="备注" name="remark" class="config-json">
              <FTextarea v-model:value="formState.remark" placeholder="请输入备注" />
            </FFormItem>
          </f-col>
          <f-col :span="24" class="config-json">
            <FFormItem label="tab页配置" name="tabConfig">
              <FTextarea
                v-model:value="formState.tabConfig"
                placeholder='请输入配置JSON, 例如:&#10; {&#10;  "tab名称":表单ID,&#10;  "项目成果文件":123&#10; }'
              />
            </FFormItem>
          </f-col>
        </f-row>
      </FForm>
    </div>
    <template #footer>
      <FConfigProvider :auto-insert-space-in-button="false">
        <FButton key="back" @click="close">取消</FButton>
        <FButton key="submit" type="primary" @click="handleOk(formRef)">确定</FButton>
      </FConfigProvider>
    </template>
  </FModal>
</template>
<script setup lang="ts">
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { ref, reactive, watch, onMounted, nextTick, computed, inject, Ref } from 'vue'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { getProcess } from '@/api/handle'
import type { ProcessModel } from '@/types/handle'
import { getRootDictionary } from '@/api'
import { IDictionatyData } from '@/types/dictionary'
import JsonData from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomJsonData/components/JsonData/index.vue'
import HandleProcess from '@/views/process-class/components/HandleProcess.vue'
import { useStore } from 'vuex'
import { StoreUserType } from '@/store/modules/user'
import { deepClone } from '@/utils'
import { getValueFn } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'

interface FormState {
  processName: string
  invalid: null | number
  dictionaryId: number | null
  remark: string
  processDefineKey: null | number
  id?: null | number
  readMark: string
  prefix: string
  writeMark: string
  sort: number
  isSendmsg: 0 | 1
  taskEdit: 0 | 1
  isDeleteMake: 0 | 1
  businessType: string
  source: 1 | 2 | 3 | 4
  tag: ''
  tabConfig: ''
  isNotice: 0 | 1 | 2 | 3
  isDelayReason: 0 | 1
  isUpdateCompleteSend: 0 | 1
  outline?: string
  detailsUrl?: string
  finishDesc?: string
  rejectDesc?: string
  [key: string]: any
}
const formRef = ref<FormInstance>()
const plainOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]
const props = defineProps({
  title: { type: String, default: '新增流程配置' },
  show: { type: Boolean },
  record: { type: Object, default: () => ({}) },
})
const emit = defineEmits(['submit', 'popupClose'])

const formState = reactive<FormState>({
  processName: '',
  dictionaryId: null,
  processDefineKey: null,
  remark: '',
  writeMark: '',
  readMark: '',
  prefix: '',
  sort: 1,
  invalid: 1,
  isSendmsg: 1,
  taskEdit: 0,
  isDeleteMake: 0,
  businessType: '',
  groupCode: '',
  source: 1,
  tag: '',
  tabConfig: '',
  isNotice: 0,
  isDelayReason: 0,
  isUpdateCompleteSend: 0,
  processCode: undefined,
  outline: '',
  detailsUrl: '',
  finishDesc: '',
  rejectDesc: '',
  admins: undefined,
})
const isNoticeLists = ref([
  { label: '关闭', value: 0 },
  { label: '延期提醒', value: 1 },
  { label: '即将延期提醒', value: 2 },
  { label: '全部提醒', value: 3 },
])
const dialogVisible = ref<boolean>(false)
const processList = ref<ProcessModel[]>([])
const dictionary = ref<IDictionatyData[]>([])
const processTypeOptions = inject<Ref<any[]>>('processTypeOptions')
const validatorKeys = reactive<Record<string, any>>({
  tag: async rule => {
    await nextTick()
    const data = formState?.[rule?.field]
    if (data && Object.keys(JSON.parse(data)).some(key => !key.trim())) {
      return Promise.reject('key不能为空格')
    }
    return Promise.resolve()
  },
  outline: async rule => {
    await nextTick()
    const data = formState?.[rule?.field]
    if (data && Object.keys(JSON.parse(data)).some(key => !key.trim())) {
      return Promise.reject('key不能为空格')
    }
    return Promise.resolve()
  },
})
const rules: Record<string, Rule[]> = {
  processName: [{ required: true, message: '请输入流程名称' }],
  processDefineKey: [{ required: true, message: '请关联流程图', trigger: 'change' }],
  prefix: [{ required: true, message: '请输入流程前缀' }],
  sort: [{ required: true, message: '请输入排序' }],
  isSendmsg: [{ required: true, message: '请选择是否开启消息提醒' }],
  taskEdit: [{ required: true, message: '请选择是否开启数据删除权限' }],
  source: [{ required: true, message: '请选择流程来源' }],
  outline: [{ validator: validatorKeys.outline }],
  tag: [{ validator: validatorKeys.tag }],
  processCode: [{ required: true, message: '请输入流程编码' }],
}
const createCreative = ref<HTMLElement>()
const store = useStore()
const userList = computed<StoreUserType[]>(() => store.state.user.allUser || [])

const clearData = () => {
  formRef.value?.resetFields()
  formState.id = null
  formState.processName = ''
  formState.invalid = 1
  formState.remark = ''
  formState.dictionaryId = null
  formState.processDefineKey = null
  formState.readMark = ''
  formState.prefix = ''
  formState.writeMark = ''
  formState.sort = 1
  formState.isSendmsg = 1
  formState.businessType = ''
  formState.groupCode = ''
  formState.taskEdit = 0
  formState.isDeleteMake = 0
  formState.source = 1
  formState.tag = ''
  formState.tabConfig = ''
  formState.isNotice = 0
  formState.isDelayReason = 0
  formState.isUpdateCompleteSend = 0
  formState.processCode = undefined
  formState.outline = ''
  formState.detailsUrl = ''
  formState.finishDesc = ''
  formState.rejectDesc = ''
  formState.admins = undefined
}

const close = () => {
  clearData()
  emit('popupClose')
}
onMounted(() => {
  getProcess({ currPage: 1, pageSize: 9999, processName: '' }).then(res => {
    processList.value = res.data.list
  })
  initDictionary()
})
const handleOk = async (formRef: FormInstance | undefined) => {
  if (!formRef) {
    return
  }
  await formRef.validate()
  const params = deepClone(formState)
  params.admins = params?.admins?.join(',') || undefined
  params.businessType = getValueFn(processTypeOptions.value, params.groupCode)?.label || undefined
  emit('submit', params, props.title)
}
watch([() => props.show, () => props.title], newValue => {
  formRef.value?.resetFields()
  dialogVisible.value = newValue[0]
  if (newValue[1] == '编辑流程配置') {
    formState.processName = props.record.processName
    formState.invalid = props.record.invalid
    formState.remark = props.record.remark
    formState.dictionaryId = props.record.dictionaryId
    formState.id = props.record.id
    formState.processDefineKey = Number(props.record.processDefineKey)
    formState.readMark = props.record.readMark
    formState.prefix = props.record.prefix
    formState.writeMark = props.record.writeMark
    formState.sort = props.record.sort
    formState.isSendmsg = props.record.isSendmsg
    formState.businessType = props.record.businessType
    formState.groupCode = props.record.groupCode
    formState.taskEdit = props.record.taskEdit ?? 0
    formState.isDeleteMake = props.record.isDeleteMake ?? 0
    formState.source = props.record.source ?? 1
    formState.tag = props.record.tag ?? ''
    formState.tabConfig = props.record.tabConfig ?? ''
    formState.isNotice = props.record.isNotice
    formState.isDelayReason = props.record.isDelayReason
    formState.isUpdateCompleteSend = props.record.isUpdateCompleteSend
    formState.processCode = props?.record?.processCode ?? ''
    formState.outline = props.record.outline
    formState.detailsUrl = props.record.detailsUrl
    formState.finishDesc = props?.record?.finishDesc ?? ''
    formState.rejectDesc = props?.record?.rejectDesc ?? ''
    formState.admins = (props?.record?.admins || undefined)?.split(',') || []
  } else {
    clearData()
  }
})

const initDictionary = async () => {
  const { data = [] } = await getRootDictionary()
  dictionary.value = data
}
</script>

<style scoped lang="scss">
:deep(.fs-modal-content) {
  .fs-modal-footer .fs-btn {
    padding: 0 24px !important;
    width: auto !important;
  }
  .fs-input {
    padding-left: 8px;
  }
}
.radioClass {
  :deep(.fs-form-item-control-input) {
    min-height: 0;
    .fs-form-item-control-input-content {
      height: auto !important;
    }
  }
}
</style>

<style lang="scss">
.process-class-modal-wrapper {
  .fs-form-item {
    margin-bottom: 22px !important;
  }
  .fs-form-item-control-input-content textarea.fs-input {
    height: auto !important;
  }

  .fs-modal-footer .fs-btn {
    display: inline-block;
  }

  .fs-picker {
    height: 32px;
  }

  .fs-row > .fs-col {
    padding: 0 10px;
  }

  .fs-form-item > .fs-col {
    padding: 0;
  }
  .fs-form .fs-row.fs-form-item {
    margin-bottom: 24px !important;
  }

  .fs-form .fs-row.fs-form-item-with-help {
    margin-bottom: 0 !important;
  }
  .config-json .fs-form-item-control {
    min-height: 88px;
  }
  .config-json textarea.fs-input {
    min-height: 88px !important;
  }
  .height-auto-box .fs-form-item-control-input-content {
    height: auto !important;
  }
}
</style>

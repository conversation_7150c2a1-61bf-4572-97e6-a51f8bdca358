<template>
  <div class="feishu-process-detail-container">
    <FEmpty v-if="noProcess" />
    <FSpin style="width: 100%" :spinning="loading" :tip="i18n.t('加载中...')" />
    <FormRender
      :key="updateKey"
      v-if="fromId && !loading && !noProcess"
      ref="formRenderRef"
      :id="fromId"
      :type="type"
      :data="{ envData, envDefaultFormData: formData }"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import FormRender from '@/components/FormRender/index.vue'
import { getInstanceInfo, getMakePerson, commitTask, transferrTask, rejectTask, deleteProcessFn } from '@/api'
import { useI18n } from '@/utils'
import { IProcess } from '@/types/handle'
import { message } from '@fs/smart-design'

const i18n = useI18n()
const route = useRoute()
const { processId, nodeId, fromId } = route.params as {
  processId: string
  nodeId: string
  fromId: string
}
const loading = ref(false)
const noProcess = ref(false)
const formRenderRef = ref<any>()
const processInfo = ref<IProcess[]>([])
const formData = ref<Record<string, unknown>>({})
const updateKey = ref<number>(Date.now() + Math.random())
const type = ref<'view' | 'edit'>('view')
const envData = reactive<Record<string, any>>({
  fromId,
  instanceId: processId,
  id: nodeId,
  handleSubmit: () => {
    init()
  },
  submitNode: (data: any) => {
    submitNode(data)
  },
  closeProcess: (data: any) => {
    closeProcess(data)
  },
  rejectProcess: (data: any) => {
    rejectProcess(data)
  },
  deleteProcess: (data: any) => {
    deleteProcess(data)
  },
})

onMounted(() => {
  init()
  requestIdleCallback(getDefaultRole)
})

const init = async () => {
  loading.value = true
  await initProcess()
  loading.value = false
}

const getDefaultRole = async () => {
  if (!nodeId) return
  const res = await getMakePerson({ milepostId: Number(nodeId) })
  if (res.code !== 200) throw new Error(res.msg)
  const defaultMakeRole = res?.data || []
  defaultMakeRole.length && (envData.defaultMakeRole = defaultMakeRole)
}

const submitNode = async (data: any) => {
  try {
    const scoped = formRenderRef.value?.getAmisScoped()
    const $form = scoped?.getComponentByName('page.nodeForm')
    if (!$form) throw new Error(i18n.t(`表单实例获取失败，请联系管理员！`))

    await $form?.submit()
    const res = await $form?.doAction({ actionType: 'submit' })

    if (!res || !res.code) throw new Error(i18n.t('表单提交接口配置异常，请联系管理员！'))
    if (res.code !== 200) throw new Error(i18n.t('表单信息提交失败'))

    message.success(i18n.t('表单信息提交成功'))
  } catch (error: any) {
    console.log('[submit node modal]:', error.message)
    message.error(error.message)
  }
  const makeUuidList = Array.from(
    new Set([...(data?.defaultMakeRole || []).map((item: any) => item.uuid), ...(data?.role?.split(',') ?? [])])
  )
  const params = {
    processType: envData?.currtMilepost?.processType,
    instanceTopicName: envData?.currtMilepost?.instanceTopicName,
    processInstanceCode: envData?.currtMilepost?.processInstanceCode,
    milepostInfoId: nodeId,
    isTransfer: 1,
    makeUuidList,
  }
  const res = await commitTask(params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('提交成功'))
  initProcess()
}

const closeProcess = async (data: any) => {
  const makeUuidList = Array.from(
    new Set([...(data?.defaultMakeRole || []).map((item: any) => item.uuid), ...(data?.role?.split(',') ?? [])])
  )
  const params = {
    processType: envData?.currtMilepost?.processType,
    instanceTopicName: envData?.currtMilepost?.instanceTopicName,
    processInstanceCode: envData?.currtMilepost?.processInstanceCode,
    milepostId: nodeId,
    instanceId: processId,
    finishDesc: data?.finishDesc,
    makeUuidList,
  }
  const res = await transferrTask(params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('办结成功'))
  initProcess()
}

const rejectProcess = async (data: any) => {
  const params = {
    ...data,
    instanceId: processId,
    sourceMilepostId: nodeId,
  }
  const res = await rejectTask(params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('驳回成功'))
  initProcess()
}

const deleteProcess = async (data: any) => {
  const res = await deleteProcessFn(Number(processId))
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('删除成功'))
  initProcess()
}

const initProcess = async () => {
  try {
    if (!processId) return
    const { data = [] } = await getInstanceInfo({ instanceId: processId, isTree: 0 })
    processInfo.value = data
    envData.currtMilepost = processInfo.value.find(item => nodeId === item.id.toString()) || {}
    type.value = (envData.currtMilepost?.status === 2 && 'edit') || 'view'
    envData.processRejectInfo = processInfo.value
      .filter(item => item.status == 3 || item.status == 5)
      .map(item => ({ label: item.topicName, value: item.id }))
    envData.targetMilepostId =
      (envData.processRejectInfo?.length && envData.processRejectInfo[envData.processRejectInfo.length - 1].value) ||
      undefined
    document.title = envData?.currtMilepost?.instanceTopicName
    const form = envData?.currtMilepost?.formData ?? {}
    formData.value = typeof data === 'string' ? JSON.parse(envData?.currtMilepost?.formData ?? {}) : form
    updateKey.value = Date.now() + Math.random()
  } catch (error) {
    noProcess.value = true
  }
}
</script>
<style lang="scss">
.fs-message {
  z-index: 99999;
}
.feishu-process-detail-container {
  width: calc(100% + 40px);
  margin-top: -20px;
  margin-left: -20px;
}
</style>

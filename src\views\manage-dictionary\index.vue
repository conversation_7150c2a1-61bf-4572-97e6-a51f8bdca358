<template>
  <div>
    <!-- <Breadcrumb class="none-container-padding" /> -->
    <div class="select-box shadow-radius">
      <div class="select-box-left">
        <div class="search-box">
          <div class="marginR12" v-if="hasEnvPublish">
            <FSelect
              :press-line="'发布状态'"
              :options="publishStatus"
              style="width: 120px"
              allow-clear
              :placeholder="'请选择'"
              @change="onSearch"
              v-model:value="searchData.publishStatus"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </div>
          <div class="marginR12">
            <FSelect
              :press-line="'启用状态'"
              :options="statusList"
              style="width: 120px"
              allow-clear
              :placeholder="'请选择'"
              @change="onSearch"
              v-model:value="searchData.status"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </div>
          <div class="marginR12">
            <FSelect
              :press-line="'数据类型'"
              style="width: 120px"
              allow-clear
              :placeholder="'请选择'"
              @change="onSearch"
              :options="[
                { value: 0, label: '文本' },
                { value: 1, label: '下拉选择' },
                { value: 2, label: '附件' },
                { value: 3, label: '数值' },
                { value: 4, label: '日期' },
                { value: 5, label: '日期区间' },
              ]"
              v-model:value="searchData.dataType"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </div>
          <div class="marginR12">
            <FSelect
              :press-line="'修改人'"
              style="width: 120px"
              allow-clear
              :placeholder="'请选择'"
              show-search
              option-filter-prop="label"
              @change="onSearch"
              v-model:value="searchData.updater"
              :options="createUserList.map(item => ({ value: item.uuid, label: item.nameCh }))"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </div>
          <div class="btn_div_relative marginR12">
            <span class="icon_span_absolute icon_span_absolute_left">修改日期</span>
            <FRangePicker
              format="YYYY-MM-DD"
              @change="onSearch"
              v-model:value="range"
              style="width: 240px; height: 32px"
              allow-clear
            />
          </div>
          <div class="btn_div_relative marginR12">
            <div class="serach-item">
              <span class="icon_span_absolute icon_span_absolute_left">快速搜索</span>
              <FInput
                :placeholder="'属性编码/属性名称/属性说明'"
                @press-enter="onSearch"
                v-model:value="searchData.searchKeyword"
                style="width: 240px"
              >
                <template #suffix>
                  <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe70e;</i>
                </template>
              </FInput>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-content-shadow pt24 pl24 pr24">
      <!-- <div class="left-sidebar" :class="[isCollapsed ? 'block' : 'none']">
          <div class="header flex align-center content-between p16">
            <span class="f14 fw-500 lh22 color333">字典管理</span>
            <div class="header-collapse flex">
              <TipBtn tip-title="新增字典字段">
                <i
                  class="icon iconfont iconxinzeng lh22 pointer hover-btn-ccc"
                  @click="batchAddDictModalRef?.onOpenFn('addBatchFieldDictionary')"
                />
              </TipBtn>
              <TipBtn tip-title="收起" v-if="isCollapsed">
                <i class="icon iconfont iconshouqi1 lh22 pointer ml8 hover-btn-ccc" @click="toggleCollapse" />
              </TipBtn>
            </div>
          </div>
          <div class="pl16 pr16">
            <FInput
              v-model:value="searchValue"
              placeholder="请输入"
              type="search-clear"
              allow-clear
              @search="onGetMenuListFn"
              @clear="onGetMenuListFn('init')"
            />
          </div>
          <div class="tree-container mt16 pl16 pr12">
            <FSpin :spinning="menuLoading">
              <DictTree
                :data="treeData"
                :accordion="true"
                :selected-node-id="selectedNodeId"
                :lazy-load="handleLazyLoad"
                :drag-api="handleDragApi"
                @node-select="handleNodeSelect"
              >
                <template #actions="{ node }">
                  <FSpace>
                    <TipBtn v-if="node.dict_tree_type === 'dict_tree_field'" tip-title="新建字典">
                      <i
                        class="iconfont icontianjia-01 color666 hover-btn-ccc"
                        @click="addDataModalRef?.onOpenFn('addOneFieldDictionary', treeData, node)"
                      ></i>
                    </TipBtn>
                    <FDropdown
                      v-if="node.dict_tree_type === 'dict_tree_field'"
                      trigger="click"
                      destroy-popup-on-hide
                      :overlay-style="{ minWidth: 'auto' }"
                      :get-popup-container="target"
                    >
                      <TipBtn tip-title="更多">
                        <i class="iconfont icongengduo21 color666 hover-btn-ccc"></i>
                      </TipBtn>
                      <template #overlay>
                        <FMenu @click="({ key }) => HandleFiledFn(key, node)">
                          <FMenuItem key="edit">
                            <i class="iconfont icontubiao_xietongbianji fontSize14"></i>
                            编辑字典字段
                          </FMenuItem>
                          <FMenuItem key="delete">
                            <i class="iconfont icontubiao_xietongshanchu fontSize14"></i>
                            删除
                          </FMenuItem>
                          <FMenuItem key="copyApi">
                            <i class="iconfont icontubiao_fuzhi fontSize14"></i>
                            复制字典接口
                          </FMenuItem>
                        </FMenu>
                      </template>
                    </FDropdown>

                    <TipBtn
                      v-if="node.dict_tree_type === 'dict_tree_item' || node.dict_tree_type === 'dict_tree_category'"
                      has-pop
                      tip-title="删除"
                      pop-title="确定删除选中的数据吗？"
                      @on-confirm-fn="onDeleteIconConfirmFn(node)"
                    >
                      <i class="iconfont icontubiao_xietongshanchu cursor color666 hover-btn-ccc"></i>
                    </TipBtn>
                  </FSpace>
                </template>
              </DictTree>
            </FSpin>
          </div>
        </div> -->

      <!-- <div class="table-title flex align-center pl24 pt16 pb16 f14 f-w500">
            <TipBtn tip-title="展开" v-if="!isCollapsed">
              <i class="icon iconfont iconzhankai1 lh22 pointer mr8 hover-btn-ccc" @click="toggleCollapse" />
            </TipBtn>
            <span class="lh22 fw-500 color333">字典列表</span>
          </div> -->

      <!-- <FInput
              class="width240 ml24"
              placeholder="请输入"
              type="search-clear"
              allow-clear
              @search="value => onGetTableListFn(selectedNode, value)"
              @clear="() => onGetTableListFn(selectedNode)"
            /> -->
      <div class="card-btn">
        <div class="fei-su-title">字典列表</div>
        <div>
          <FButton
            v-if="hasPublish"
            type="default"
            @click="publishVisible = true"
            :disabled="selectedRowKeys.length === 0"
            class="add-button marginR12"
          >
            <i class="iconpiliangtianjia iconfont marginR5 fontSize14"></i>批量发布
          </FButton>
          <FButton type="primary" @click="batchAddDictModalRef?.onOpenFn('addBatchFieldDictionary')">
            <template #icon><i class="icon iconfont iconxinzeng mr4" /></template>
            创建字段
          </FButton>
        </div>
      </div>

      <div>
        <FTable
          :columns="columns"
          :row-selection="getRowSelectionConfig()"
          :data-source="tableData"
          :row-key="(data:any) => data.field"
          sticky
          :scroll="{ x: 'min-content' }"
          :loading="loading"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'createdTime'">
              <div>
                {{ record.createdTime ? dayjs(record.createdTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}
              </div>
            </template>
            <template v-if="column.key === 'updatedTime'">
              <div>
                {{ record.updatedTime ? dayjs(record.updatedTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}
              </div>
            </template>
            <template v-if="column.key === 'status'">
              <span v-if="record.status == 1" class="enable">启用</span>
              <span v-if="record.status == 0" class="disable">禁用</span>
            </template>
            <template v-if="column.key === 'dataType'">
              <div>
                {{ record.dataType === 0 ? '文本' : '--' }}
              </div>
            </template>
            <template v-if="column.key === 'publishStatus'">
              <span v-if="record.publishStatus == 1" class="enablePublish">已发布</span>
              <span v-if="record.publishStatus == 0" class="disablePublish">未发布</span>
            </template>
            <template v-if="column.dataIndex === 'operate'">
              <FSpace :size="[8]" wrap class="handle-row-box">
                <TipBtn tip-title="编辑">
                  <i
                    class="iconfont icontubiao_xietongbianji hover-btn-ccc color4677C7"
                    @click="HandleFiledFn('edit', record)"
                  ></i>
                </TipBtn>
                <TipBtn
                  has-pop
                  tip-title="删除"
                  pop-title="确定删除该数据列吗？"
                  v-if="record.publishTime === null"
                  @on-confirm-fn="HandleFiledFn('delete', record)"
                >
                  <i class="iconfont icontubiao_xietongshanchu hover-btn-ccc color4677C7"></i>
                </TipBtn>
                <TipBtn tip-title="重置" v-if="hasPublishReset && record.publishTime && record.publishStatus === 0">
                  <i class="iconfont icontubiao_shuaxin2 hover-btn-ccc color4677C7" @click="reset(record)"></i>
                </TipBtn>
                <TipBtn tip-title="复制字典字段接口地址">
                  <i
                    class="iconfont icontubiao_lianjie2 hover-btn-ccc color4677C7"
                    @click="HandleFiledFn('copyApi', record)"
                  ></i>
                </TipBtn>
              </FSpace>
            </template>
          </template>
        </FTable>
        <div class="fei-su-pagination">
          <span class="fontSize12">共{{ paging.total }}条</span>
          <FPagination
            v-model:current="paging.currPage"
            @change="onChange"
            v-model:page-size="paging.pageSize"
            :total="paging.total"
            show-size-changer
          />
        </div>
      </div>
    </div>

    <!-- <AddDictModal ref="addDataModalRef" @update-change="onGetTreeListFn" /> -->
    <BatchAddDictModal ref="batchAddDictModalRef" @init="onGetDictionary" @update-change="onGetDictionary" />
    <PublishModal v-model:value="publishVisible" :publish-options="publishEnvOptions" @submit="allPublish" />
  </div>
</template>

<script lang="ts" setup>
import { transformDate } from '@/utils'
import dayjs from 'dayjs'
import { ref, computed, onMounted, reactive } from 'vue'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import { FSpace, FSpin, message, FModal } from '@fs/smart-design'
// import Breadcrumb from '@/components/Breadcrumb/index.vue'
// import DictTree from './components/DictTree/index.vue'
// import AddDictModal from './components/AddDictModal/index.vue'
import BatchAddDictModal from './components/BatchAddDictModal/index.vue'
// import { flattenTree, processTreeData } from './components/DictTree/utils'
import { SelectProps } from '@fs/smart-design/dist/ant-design-vue_es'
import { BasicPageParams } from '@/types/processBoard'
import {
  deleteFieldAllDictionaryItem,
  selectDictionaryPage,
  getAllUsers,
  synDictionary,
  rollbackDictionary,
} from '@/api'
// import { deepClone } from '@/utils'
import Clipboard from 'clipboard'
import PublishModal from '@/components/PublishModal/index.vue'
import usePublish from '@/hooks/usePublish'

const { hasEnvPublish, hasPublish, hasPublishReset, publishEnvOptions, publishVisible } = usePublish()
// const isCollapsed = ref(true)
// const searchValue = ref('')
// const menuLoading = ref(false)
// const addDataModalRef = ref()
const batchAddDictModalRef = ref()
const loading = ref(false)
const tableData = ref<any[]>([])
// const treeData = ref<any[]>([])
// const selectedNodeId = ref('')
// const selectedNode = ref<any>({})
import { IUser } from '@/types/handle'
const createUserList = ref<IUser[]>([])
const paging = ref<BasicPageParams>({ currPage: 1, pageSize: 10, total: 0 })
const columns = computed(() =>
  [
    { title: '属性名称', dataIndex: 'title', width: 120 },
    { title: '属性编码', dataIndex: 'field', width: 120 },
    hasEnvPublish.value && { title: '发布状态', dataIndex: 'publishStatus', key: 'publishStatus', width: 120 },
    { title: '数据类型', dataIndex: 'dataType', key: 'dataType', width: 120 },
    { title: '启用状态', dataIndex: 'status', key: 'status', width: 120 },
    { title: '属性说明', dataIndex: 'describe', width: 168 },
    { title: '创建人', dataIndex: 'creator', width: 168 },
    { title: '创建时间', dataIndex: 'createdTime', key: 'createdTime', width: 168 },
    { title: '修改人', dataIndex: 'updater', width: 168 },
    { title: '修改时间', dataIndex: 'updatedTime', key: 'updatedTime', width: 168 },
    { title: '操作', dataIndex: 'operate', width: 120, fixed: 'right' },
  ].filter(Boolean)
)
const statusList = ref<SelectProps['options']>([
  { value: 0, label: '停用' },
  { value: 1, label: '启用' },
])
const publishStatus = ref<SelectProps['options']>([
  { value: 1, label: '已发布' },
  { value: 0, label: '未发布' },
])
const reset = record => {
  rollbackDictionary({
    type: 1,
    codeList: [record.field],
  }).then(res => {
    if (res.code == 200) {
      message.success('重置成功')
      onGetDictionary()
    }
  })
}
const allPublish = (publishData: any) => {
  synDictionary({
    type: publishData?.type || 1,
    codeList: selectedRowKeys.value,
  }).then(res => {
    if (res.code == 200) {
      message.success('批量发布成功')
      onGetDictionary()
    }
  })
}
// const target = ref<any>(() => document.querySelector('#container'))

const copy = node => {
  if (!node?.field) {
    message.warning('复制失败')
    return
  }
  const text = '${envUrl}/api/searchDictionary/dictionary/getByField/' + node?.field
  const btn = document.createElement('button')
  btn.style.display = 'none'
  document.body.appendChild(btn)
  const clipboard = new Clipboard(btn, {
    text: () => text,
  })
  clipboard.on('success', e => {
    message.success('复制成功')
    clipboard.destroy()
    btn.remove()
  })
  clipboard.on('error', e => {
    message.warning('复制失败')
    clipboard.destroy()
    btn.remove()
  })
  btn.click()
}
const range = ref<[dayjs.Dayjs, dayjs.Dayjs]>()
const searchData = reactive({
  publishStatus: undefined,
  status: undefined,
  dataType: undefined,
  updater: undefined,
  updatedTimeEnd: undefined,
  updatedTimeStart: undefined,
  searchKeyword: '',
})
const getRowSelectionConfig = () => {
  return {
    selectedRowKeys: selectedRowKeys.value,
    onChange: onSelectChange,
    getCheckboxProps: record => ({
      disabled: record.publishStatus === 1,
    }),
  }
}
// 表格勾选配置项
const selectedRowKeys = ref<string[]>([])

// 表格勾选
function onSelectChange(keys: string[]) {
  selectedRowKeys.value = keys
}
const onChange = (current: number, pageSize: number) => {
  paging.value.pageSize = pageSize
  paging.value.currPage = current
  onGetDictionary()
}
const HandleFiledFn = (key, node) => {
  key === 'delete' && onDeleteFieldIconConfirmFn(node)
  key === 'edit' && batchAddDictModalRef?.value?.onOpenFn('updateBatchFieldDictionary', node)
  key === 'copyApi' && copy(node)
}
const onDeleteFieldIconConfirmFn = async (node: any) => {
  FModal.confirm({
    title: '是否确认删除该数据吗？',
    content: '删除后不可恢复，请谨慎操作！',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      const res = await deleteFieldAllDictionaryItem(node?.field)
      if (res.code !== 200) throw new Error(res.msg)
      message.success('删除成功')
      onGetDictionary()
    },
  })
}

// const HandleFiledFn = (key, node) => {
//   key === 'delete' && onDeleteFieldIconConfirmFn(node)
//   key === 'edit' && batchAddDictModalRef?.value?.onOpenFn('updateBatchFieldDictionary', node)
//   key === 'copyApi' && copy(node)
// }

// const onDeleteIconConfirmFn = async (record: any) => {
//   if (record?.dict_tree_children?.length) {
//     message.warning('请先删除子节点')
//     return
//   }
//   const fieldInfo = treeData.value.find(item => item.dict_tree_id === record.field)
//   if (
//     fieldInfo?.dict_tree_children?.length === 1 &&
//     fieldInfo?.dict_tree_children?.[0]?.dict_tree_id === record.dict_tree_id
//   ) {
//     message.warning('至少保留一个字典项')
//     return
//   }
//   const res = await deleteFieldDictionaryItem(record.id)
//   if (res.code !== 200) throw new Error(res.msg)
//   message.success('删除成功')
//   onGetTreeListFn(record)
// }

// const onGetTableListFn = async (node, value = undefined) => {
//   try {
//     loading.value = true
//     const params = {
//       field: node.dict_tree_type === 'dict_tree_field' ? node.id : undefined,
//       id: node.dict_tree_type !== 'dict_tree_field' ? node.id : undefined,
//       queryInput: value?.trim(),
//     }
//     const res = await getFiledDictionaryList(params)
//     tableData.value = flattenTree(res?.data || [], 'children', 'id').map(item => {
//       const originalData = deepClone(item)
//       originalData['dict_tree_children'] = item?.children || []
//       delete originalData.children
//       return originalData
//     })
//   } finally {
//     loading.value = false
//   }
// }

// const onGetTreeListFn = async node => {
//   if (!node) return
//   const fieldIndex = treeData.value.findIndex(item => item.dict_tree_id === node.field)
//   if (fieldIndex === -1) return
//   const res = await selectFieldDictionaryItem(treeData.value[fieldIndex].dict_tree_id)
//   const rawData = res?.data || []
//   const children = rawData.length > 0 ? processTreeData(rawData, 'dict_tree_field') : []
//   treeData.value[fieldIndex].dict_tree_children = children
//   treeData.value[fieldIndex].title = children?.[0]?.title
//   treeData.value[fieldIndex].field = children?.[0]?.field
//   treeData.value[fieldIndex].dict_tree_name =
//     treeData.value[fieldIndex].title +
//     ((treeData.value[fieldIndex]?.field && `(${treeData.value[fieldIndex]?.field})`) || '')
//   if (treeData.value[fieldIndex].dict_tree_id === selectedNode.value.field) {
//     treeData.value.forEach(item => {
//       if (treeData.value[fieldIndex].dict_tree_id !== item.dict_tree_id) {
//         item.dict_tree_expanded = false
//       } else {
//         item.dict_tree_expanded = true
//       }
//     })
//     onGetTableListFn(selectedNode.value)
//   }
// }
const onSearch = () => {
  paging.value.currPage = 1
  searchData.updatedTimeStart = range.value?.[0] && transformDate(range.value[0], 'YYYY-MM-DD') + ' 00:00:00'
  searchData.updatedTimeEnd = range.value?.[1] && transformDate(range.value[1], 'YYYY-MM-DD') + ' 00:00:00'
  onGetDictionary()
}
const onGetDictionary = async () => {
  selectedRowKeys.value = []
  const parmas = {
    currPage: paging.value.currPage,
    pageSize: paging.value.pageSize,
    ...searchData,
  }
  await selectDictionaryPage(parmas).then(res => {
    const fieldList = res?.data?.list || []
    const processedFieldList = fieldList.map(item => {
      const updaterUser = createUserList.value.find(user => user.uuid === item.updater)
      const creatorUser = createUserList.value.find(user => user.uuid === item.creator)
      return {
        ...item,
        updater: updaterUser ? updaterUser.nameCh : '--',
        creator: creatorUser ? creatorUser.nameCh : '--',
      }
    })
    tableData.value = processedFieldList
    paging.value.total = res.data.totalCount
  })
}
const getCreatUserList = async () => {
  await getAllUsers().then(res => {
    createUserList.value = res.data
  })
}
onMounted(async () => {
  await getCreatUserList()
  await onGetDictionary()
})
// const onGetMenuListFn = async (type = 'init') => {
//   try {
//     menuLoading.value = true
//     const input = type === 'init' ? '' : searchValue.value.trim()
// if (type !== 'init' && !input) return
//     const data = { input }
//     const res = await selectFieldDictionary(data)
//     const fieldList = res?.data || []
//     const processedFields = fieldList.map((item: any) => ({
//       dict_tree_id: item?.field,
//       dict_tree_name: item?.title + ((item?.field && `(${item?.field})`) || ''),
//       dict_tree_type: 'dict_tree_field',
//       dict_tree_children: [],
//       dict_tree_expanded: false,
//       dict_tree_original_data: item,
//       field: item?.field,
//       id: item?.field,
//     }))
//     treeData.value = processedFields
//     if (treeData.value.length > 0) {
//       const [node] = treeData.value
//       selectedNodeId.value = node.dict_tree_id
//       selectedNode.value = node
//       await onGetTreeListFn(node)
//     }
//   } finally {
//     menuLoading.value = false
//   }
// }

// // 左侧树形菜单事件处理
// const toggleCollapse = () => {
//   isCollapsed.value = !isCollapsed.value
// }

// // 拖拽API处理函数
// const handleDragApi = async (params: any) => {
//   const { targetParent, sortChanges } = params
//   const data = sortChanges.map(item => ({
//     id: item.id,
//     sort: item.newSortIndex,
//   }))
//   const res = await sortFiledDictionaryList(data)
//   if (res.code !== 200) throw new Error('排序数据失败')
//   message.success('排序成功')
//   onGetTreeListFn(targetParent)
// }

// // 节点选择处理（使用组件前缀属性）
// const handleNodeSelect = (node: any, selected: boolean) => {
//   if (selected && selectedNodeId.value !== node.dict_tree_id) {
//     selectedNodeId.value = node.dict_tree_id
//     selectedNode.value = node
//     onGetTableListFn(node)
//   }
// }

// const handleLazyLoad = async (params: any) => {
//   const { node } = params
//   try {
//     const originalField = node?.field
//     const res = await selectFieldDictionaryItem(originalField)
//     if ((res?.data || []).length === 0) return new Error('没有子节点')
//     const childData = res?.data || []
//     node.title = childData?.[0]?.title

//     selectedNodeId.value && selectedNodeId.value === node.dict_tree_id && onGetTableListFn(node)
//     return processTreeData(childData, node.dict_tree_type)
//   } catch (error) {
//     console.error('懒加载失败:', error)
//     throw error
//   }
// }
</script>

<style lang="scss" scoped>
.none-container-padding {
  margin-top: -20px;
  // margin-left: -20px;
  // width: calc(100% + 40px);
}
.card-content-shadow {
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
.flex {
  display: flex;
}
.align-center {
  align-items: center;
}
.content-between {
  justify-content: space-between;
}
.hover-btn-ccc {
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  &:hover {
    background-color: #d8d8d8;
  }
}
.manage-dictionary-container {
  .content-container {
    height: calc(100vh - 146px);
  }

  .left-sidebar {
    width: 280px;
    transition: all 0.3s;
    border-right: 1px solid #eee;
    .tree-container {
      max-height: calc(100% - 116px);
      overflow-y: scroll;
    }
  }

  .right-content {
    flex: 1;
    min-width: 0;
    .table-title {
      border-bottom: 1px solid #eee;
    }
  }
}
.fei-su-title {
  color: #333;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 0px;
}
.card-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.select-box {
  padding-top: 24px;
  padding-bottom: 24px;
  padding-left: 24px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  margin-bottom: 16px;
  .select-box-left {
    flex: 1;
    display: flex;
    .search-box {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      .btn_div_relative {
        display: inline-block;
        position: relative;
        height: 34px;
        line-height: 1;
        :deep(.fs-picker) {
          .fs-picker-input-active {
            background-color: #fff;
          }
        }
        :deep(.fs-picker-focused) {
          .fs-picker-input-active {
            background-color: #f1f4f8 !important;
          }
        }
        :deep(.icon_span_absolute) {
          display: inline-block;
          position: absolute;
          top: -7px;
          left: 4px;
          z-index: 10;
          padding: 0 3px;
          color: #999;
          background: #fff;
          font-size: 11px;
          -webkit-transform: scale(0.9);
          transform: scale(0.9);
        }
        .icon_span_absolute_left {
          left: 7px;
        }
      }
      .marginR12 {
        margin-right: 12px;
      }
    }
  }
}
.shadow-radius {
  background: #ffffff;
  box-shadow: 0 2px 8px 0 rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
.fei-su-pagination {
  display: flex;
  justify-content: flex-end;
  padding: 15px 0 25px;
  > span {
    line-height: 32px;
    color: #bbb;
    margin-right: 10px;
  }
}
.enable {
  display: inline-block;
  width: 30px;
  height: 18px;
  font-size: 12px;
  color: #2fcc83;
  border: 1px solid #2fcc83;
  background-color: #eafaf2;
  text-align: center;
  border-radius: 2px;
}
.disable {
  display: inline-block;
  width: 30px;
  height: 18px;
  color: #f04141;
  border: 1px solid #f04141;
  background-color: #fdecec;
  text-align: center;
  border-radius: 2px;
}
.enablePublish {
  display: inline-block;
  width: 44px;
  height: 18px;
  text-align: center;
  background: #eafaf2;
  border-radius: 3px;
  font-size: 12px;
  color: #2fcc83;
  line-height: 18px;
}
.disablePublish {
  display: inline-block;
  width: 44px;
  height: 18px;
  text-align: center;
  background: #eeeeee;
  border-radius: 3px;
  font-size: 12px;
  color: #999999;
  line-height: 18px;
}
</style>

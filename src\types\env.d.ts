/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}
declare module 'lodash'
declare module '*.png'
declare module '*.svg'
declare module '*.js'
declare module 'lodash.throttle'
declare module 'quill-image-uploader'

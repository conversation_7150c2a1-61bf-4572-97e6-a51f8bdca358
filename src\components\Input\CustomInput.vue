<template>
  <div class="container">
    <div class="custom-input-container">
      <slot name="left"></slot>
      <FInput style="height: 46px; border: none" v-model:value="taskName" :placeholder="i18n.t('请输入任务名称')" />
      <slot name="right"></slot>
    </div>
    <FButton type="primary" size="small" style="margin-left: 16px; margin-right: 8px" @click="save">{{
      i18n.t('保存')
    }}</FButton>
    <FButton type="text" size="small" @click="cancel">{{ i18n.t('取消') }}</FButton>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from '@/utils'

const i18n = useI18n()
const emits = defineEmits(['cancel', 'save'])
const taskName = ref<string>('')
const save = () => {
  emits('save', taskName.value)
}
const cancel = () => {
  emits('cancel')
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  align-items: center;
  .custom-input-container {
    height: 48px;
    border-radius: 4px;
    border: 1px solid #378eef;
    display: flex;
    align-items: center;
    flex: 1 1 auto;
    input:focus {
      box-shadow: none !important;
      border: none !important;
    }
  }
}
</style>

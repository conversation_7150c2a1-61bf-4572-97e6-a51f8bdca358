import { createApp } from 'vue'

import SmartDesign from '@fs/smart-design'
import { TranslatorPlugin, GoogleDevice } from '@fs/translator'

import router from '@/router'
import store from '@/store'
import { TRANSLATE_URL } from '@/utils'

import App from '@/App.vue'
import V<PERSON><PERSON>iewer from 'v-viewer'
import directives from '@/directives'

import { setInit } from '@/init'

import './micro-public-path'

import 'normalize.css/normalize.css'
import '@fs/smart-design/dist/style.css'
import 'fs-menu/dist/index.css'
import 'viewerjs/dist/viewer.css'
import '@logicflow/core/dist/style/index.css'
import '@logicflow/extension/lib/style/index.css'

import './assets/fonts/iconfont.css'
import './styles/index.scss'

// eslint-disable-next-line prettier/prettier
const app = createApp(App)
app.use(store)
app.use(router)
setInit(app)
app.use(directives)
app.use(Vue<PERSON>iewer)
app.use(SmartDesign)
app.use(TranslatorPlugin, { device: new GoogleDevice(TRANSLATE_URL) })
app.mount('#root')

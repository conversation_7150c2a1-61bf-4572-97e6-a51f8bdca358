import { createApp } from 'vue'

import SmartDesign from '@fs/smart-design'
import { TranslatorPlugin, GoogleDevice } from '@/translator'

import router from '@/router'
import store from '@/store'
import { TRANSLATE_URL } from '@/utils'

import App from '@/App.vue'
import VueViewer from 'v-viewer'
import directives from '@/directives'

import { setInit } from '@/init'

import './micro-public-path'

import 'normalize.css/normalize.css'
import '@fs/smart-design/dist/style.css'
import 'fs-menu/dist/index.css'
import 'viewerjs/dist/viewer.css'
import '@logicflow/core/dist/style/index.css'
import '@logicflow/extension/lib/style/index.css'

import './assets/fonts/iconfont.css'
import './styles/index.scss'

// eslint-disable-next-line prettier/prettier
const app = createApp(App)
app.use(store)
app.use(router)
setInit(app)
app.use(directives)
app.use(Vue<PERSON>iewer)
app.use(SmartDesign)
app.use(TranslatorPlugin, { device: new GoogleDevice(TRANSLATE_URL, 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOjY3NCwiZGVwdE5hbWUiOiLph4fotK3nrqHnkIbnoJTlj5HmtYvor5Xnu4QiLCJmaXJzdEVudHJ5RGF0ZSI6MTQ2NDcxMDQwMCwic2V4IjowLCJkZXB0SWQiOjEyMzM3LCJqb2JQb3N0SWQiOjY2NDU0MSwiZGVwdFRyZWVQYXRoTmFtZSI6IkZT77yI6aOe6YCf5Yib5paw77yJL-ezu-e7n-eglOWPkeS4reW_gy9NQkfmlbDlrZfljJbns7vnu5_pg6gv6YeH6LSt566h55CG56CU5Y-R5rWL6K-V57uEIiwiZGVwdFR5cGUiOjAsInVzZXJOYW1lIjoi5r2Y5LyfKEJ1Y2suUGFuKSIsInV1aWQiOiJmNzE2NTY0YmIyZmE0Yzk5ODUyOTgxMzAzMmE5YzdjZSIsInVpZCI6Njc0LCJuYmYiOjE3NTcwNDE3NzYsImpvYlBvc3QiOiLnoJTlj5Hnu4_nkIYiLCJlbk5hbWUiOiJCdWNrLlBhbiIsImFkbWluSWQiOiIyNTM2IiwiZnNJZCI6Ijc0ZzgyYzU3IiwiaWQiOjI0MiwiZXhwIjoxNzU3MDcwNTc2LCJpYXQiOjE3NTcwNDE3NzYsImNoTmFtZSI6Iua9mOS8nyIsImZzTm8iOiJGUzAwMDIxNiIsImVtYWlsIjoiYnVjay5wYW5AZmVpc3UuY29tIn0.mHS9m9zjL6NJGQF1EBr58-T4wracumTfWd_1V32POTo') })
app.mount('#root')

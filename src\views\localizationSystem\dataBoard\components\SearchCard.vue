<template>
  <div class="data-board-header-container">
    <FCard class="f-card marginB16">
      <FCascader
        dropdownClassName="country-cust-cascader"
        :placeholder="i18n.t('请选择')"
        :press-line="i18n.t('CBU区域')"
        :allowClear="false"
        style="width: 240px; margin-right: 12px"
        :field-names="{ label: 'countryName', value: 'id', children: 'localizationCountrys' }"
        :options="props.cbuCountryList"
        showCheckedStrategy="SHOW_CHILD"
        v-model:value="cbuCountry"
        change-on-select
        expand-trigger="hover"
        @change="cbuCountryChange"
        :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
      ></FCascader>

      <FCascader
        dropdownClassName="country-cust-cascader"
        :placeholder="i18n.t('请选择')"
        :press-line="i18n.t('大洲/国家/州')"
        :allowClear="false"
        style="width: 240px; margin-right: 12px"
        :field-names="{ label: 'countryName', value: 'id', children: 'localizationCountrys' }"
        :options="props.countryList"
        showCheckedStrategy="SHOW_CHILD"
        v-model:value="country"
        change-on-select
        expand-trigger="hover"
        @change="countryChange"
        :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
      ></FCascader>
      <FRangePicker
        v-model:value="form.time"
        class="width240 displayInline"
        :press-line="i18n.t('选择日期')"
        style="height: 32px"
        @change="onChange"
        :allow-clear="false"
      >
      </FRangePicker>
    </FCard>
  </div>
</template>

<script setup lang="ts">
import { reactive, toRaw, watch, ref } from 'vue'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { IAllLocalizationCountryData } from '@/types/localizationSystem/dataBoard'
import { getDefaultValue } from '@/views/localizationSystem/lib/utils'
import { useI18n } from '@/utils'
const i18n = useI18n()

type propsType = {
  countryList: IAllLocalizationCountryData[]
  cbuCountryList: IAllLocalizationCountryData[]
}

const props = withDefaults(defineProps<propsType>(), {
  countryList: () => [],
  cbuCountryList: () => [],
})

type IpForm = {
  country: number[] | undefined
  state: string[] | undefined
  time: Dayjs[]
}

const cbuCountry = ref<number[] | undefined>(undefined)
const country = ref<number[] | undefined>(undefined)

const form = reactive<IpForm>({
  country: undefined,
  state: undefined,
  time: [dayjs().startOf('month'), dayjs()],
})

watch(
  () => props.cbuCountryList,
  val => {
    if (val.length) {
      const keyData = getDefaultValue(val, {}, 'localizationCountrys', '', '新加坡', 'id', 'countryName')
      keyData['新加坡'] && (cbuCountry.value = keyData['新加坡'])
      const valueData = getDefaultValue(val, {}, 'localizationCountrys', '', '新加坡', 'countryId', 'countryName')
      valueData['新加坡'] && (form.country = [valueData['新加坡'].at(-1)])
    }
  },
  { deep: true }
)

const getCountry = (
  node: any,
  data: any = {
    country: [],
    state: [],
  }
) => {
  node.forEach((item: any) => {
    if (item.countryId) {
      data.country.push(item.countryId)
    } else if (!item.localizationCountrys || !item.localizationCountrys.length) {
      data.state.push(item.countryName)
    } else if (item.localizationCountrys && item.localizationCountrys.length) {
      getCountry(item.localizationCountrys, data)
    }
  })
  return data
}

const cbuCountryChange = (value: any, selectedOptions: any) => {
  country.value && (country.value = undefined)
  const node = selectedOptions.at(-1)
  const selectCountrys = getCountry([node])
  form.country = selectCountrys.country.length ? selectCountrys.country : undefined
  form.state = selectCountrys.state.length ? selectCountrys.state : undefined
  onChange()
}

const countryChange = (value: any, selectedOptions: any) => {
  cbuCountry.value && (cbuCountry.value = undefined)
  const node = selectedOptions.at(-1)
  const selectCountrys = getCountry([node])
  form.country = selectCountrys.country.length ? selectCountrys.country : undefined
  form.state = selectCountrys.state.length ? selectCountrys.state : undefined
  onChange()
}

const onChange = () => {
  emit('onSearch', toRaw(form))
}

const emit = defineEmits(['onSearch'])
</script>
<style scoped lang="scss">
.data-board-header-container {
  width: 100%;
  .f-card {
    box-shadow: none;
    :deep(.country-cust-cascader) {
      padding-right: 0 !important;
      .fs-cascader-menu {
        font-weight: 500;
        border-right-color: #eee;
        &:last-child {
          border-right: none;
        }
        .fs-cascader-menu-item {
          margin-top: 2px;
          &[aria-checked='true'] {
            background: #ebf3fd;
            border-radius: 3px;
          }
        }
        .fs-cascader-menu-item-active[aria-checked='true'] {
          background: #ebf3fd;
          border-radius: 3px;
        }
      }
    }
  }
}
</style>

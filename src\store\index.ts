import { createStore } from 'vuex'
import { IUserStore } from './modules/user'
import { IMicroState } from './modules/micro'
import getters from './getters'

export interface IStoreState {
  global: { [key: string]: unknown }
  user: IUserStore
  micro: IMicroState
}

const modulesFiles = require.context('./modules', true, /\.ts$/)

const modules = modulesFiles.keys().reduce((modules: { [key: string]: any }, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

export default createStore<IStoreState>({
  getters,
  modules,
})

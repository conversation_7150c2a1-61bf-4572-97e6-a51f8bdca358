<template>
  <FModal
    width="400px"
    wrapClassName="bom-modal-wrap-container"
    v-model:visible="visible"
    :title="handleData?.[handleType]?.title ?? '新增字典'"
    centered
    :confirm-loading="loading"
    @cancel="onCancelFn"
    @ok="onSubmitFn"
  >
    <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <FRow :gutter="[24, 0]">
        <FCol :span="24">
          <FFormItem label="字典字段" name="field">
            <FSelect
              v-model:value="formState.field"
              placeholder="请选择"
              :options="filedList"
              :field-names="{ label: 'dict_tree_name', value: 'dict_tree_id' }"
              optionFilterProp="dict_tree_name"
              show-search
              @change="onChangeFiledFn"
              :disabled="handleType === 'updateOneFieldDictionary'"
            />
          </FFormItem>
        </FCol>
        <FCol :span="24">
          <FFormItem label="父级字典" name="parentId">
            <FTreeSelect
              v-model:value="formState.parentId"
              placeholder="请选择"
              :tree-data="parentFiledList"
              :fieldNames="{ label: 'originalName', value: 'id' }"
              optionFilterProp="originalName"
              allow-clear
              show-search
            />
          </FFormItem>
        </FCol>
        <FCol :span="24">
          <FFormItem label="字典名称" name="name">
            <FInput v-model:value="formState.name" placeholder="请输入" />
          </FFormItem>
        </FCol>
        <FCol :span="24">
          <FFormItem label="字典值" name="value">
            <FInput v-model:value="formState.value" placeholder="请输入" />
          </FFormItem>
        </FCol>
      </FRow>
    </FForm>
    <div></div>
  </FModal>
</template>

<script setup lang="ts">
import { message } from '@fs/smart-design'
import { ref, reactive, onMounted, computed } from 'vue'
import { selectFieldDictionaryItem, addOneFieldDictionary, updateOneFieldDictionary } from '@/api'

const emits = defineEmits(['updateChange'])
const visible = ref<boolean>(false)
const activeNode = ref()
const filedList = ref([])
const parentFiledList = ref([])
const loading = ref<boolean>(false)
const formRef = ref()
const formState = reactive<any>({
  field: undefined,
  parentId: undefined,
  name: undefined,
  value: undefined,
  title: undefined,
  status: undefined,
})
const currentRowRecord = ref<any>()
const handleType = ref('')
const handleData = computed(() => ({
  addOneFieldDictionary: {
    title: '新增字典',
    msg: '新增字典成功！',
    defaultForm: {
      field: activeNode?.value?.field,
    },
    initFn: async () => {
      await initParentFiledListFn(activeNode?.value?.field)
    },
    baseParams: {
      parentId: formState?.parentId ?? 0,
      name: formState?.name?.trim?.(),
      value: formState?.value?.trim?.(),
    },
    apiUrl: addOneFieldDictionary,
  },
  updateOneFieldDictionary: {
    title: '编辑字典',
    msg: '编辑字典成功！',
    defaultForm: {
      name: currentRowRecord?.value?.originalName,
      parentId: currentRowRecord?.value?.parentId || undefined,
    },
    baseParams: {
      id: currentRowRecord?.value?.id,
      parentId: formState?.parentId ?? 0,
      name: formState?.name?.trim?.(),
      value: formState?.value?.trim?.(),
    },
    initFn: async () => {
      await initParentFiledListFn(currentRowRecord?.value?.field)
    },
    apiUrl: updateOneFieldDictionary,
  },
}))

const rules: Record<string, any[]> = {
  field: [{ required: true, message: '请选择' }],
  name: [{ required: true, message: '请输入' }],
  value: [{ required: true, message: '请输入' }],
}

const initParentFiledListFn = async value => {
  parentFiledList.value = []
  const res = await selectFieldDictionaryItem(value)
  parentFiledList.value = (res?.data ?? []).map(item => ({
    disabled: item.id === currentRowRecord?.value?.id,
    ...item,
  }))
  const [firstItem = {}] = parentFiledList.value
  formState.title = firstItem?.title
  formState.status = firstItem?.status
}

const onChangeFiledFn = (value, option) => {
  initParentFiledListFn(value)
  formState.parentId = undefined
  formState.title = undefined
  formState.status = undefined
  activeNode.value = option
}

const onCancelFn = () => {
  visible.value = false
}

const onSubmitFn = async () => {
  try {
    loading.value = true
    if (!formRef.value) return
    await formRef.value.validate()
    const params: any = Object.assign({}, formState, handleData?.value?.[handleType?.value]?.baseParams)
    await handleData?.value?.[handleType?.value]?.apiUrl(params)
    message.success(handleData?.value?.[handleType?.value]?.msg ?? '操作成功')
    onCancelFn()
    emits('updateChange', activeNode.value)
  } finally {
    loading.value = false
  }
}

const onInitData = async () => {
  Object.entries(formState).forEach(([key, value]) => {
    formState[key] =
      handleData?.value?.[handleType?.value]?.defaultForm?.[key] || (currentRowRecord.value || {})[key] || undefined
  })
}

const onOpenFn = async (typeValue: string, list: any, node: any = {}, data: any = {}) => {
  visible.value = true
  currentRowRecord.value = data
  handleType.value = typeValue
  filedList.value = list
  activeNode.value = node
  await onInitData()
  handleData?.value?.[handleType?.value]?.initFn?.()
}

defineExpose({ onOpenFn })
</script>

<style scoped lang="scss"></style>

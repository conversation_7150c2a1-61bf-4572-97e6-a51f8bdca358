import type { EChartsOption } from '@/components/BaseEchart/config'
import imgEmpty from './images/empty.svg'

/**
 * 金额格式化，添加千位分隔符
 * @param {number | string} value 要格式化的数字
 * @returns {string} 格式化后的字符串
 */
export function formatAmountWithCommas(value: number | string): string {
  if (value === null || value === undefined || value === '') {
    return '--'
  }
  const parts = String(value).split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return parts.join('.')
}

// 公共配置
const commonTooltip: EChartsOption['tooltip'] = {
  appendToBody: true,
  textStyle: { color: '#fff' },
  backgroundColor: '#000000B3',
  borderColor: 'transparent',
  borderRadius: 4, // 圆角
  padding: [8, 12, 8, 12], // 内边距
}

export const commonLegend: EChartsOption['legend'] = {
  orient: 'horizontal', // 横向排列
  bottom: 0, // 放在底部
  left: 'center',
  // icon: 'pie',
  icon: 'path://M 2,0 L 6,0 A 2,2 0 0,1 8,2 L 8,6 A 2,2 0 0,1 6,8 L 2,8 A 2,2 0 0,1 0,6 L 0,2 A 2,2 0 0,1 2,0 Z',
  itemWidth: 8,
  itemHeight: 8,
  // formatter 可自定义显示内容
  // formatter: (name: string) => name,
  padding: [0, 0, 0, 0], // 图例内边距
  itemGap: 12, // 图例项之间的间距
  textStyle: {
    fontSize: 11,
    color: '#666666',
  },
}

const commonColors = ['#37BBEF', '#2FCC83', '#FA8F23', '#F5CC38', '#6762FC', '#04BEC4', '#378EEF', '#F04141']

export const commonTitle: EChartsOption['title'] = {
  show: true,
  text: '',
  subtext: '',
  left: '49%',
  top: '26%',
  textAlign: 'center',
  itemGap: 6,
  textStyle: {
    fontSize: '12px',
    color: '#666666',
    fontWeight: 400,
  },
  subtextStyle: {
    fontSize: '16px',
    color: '#333333',
    fontWeight: 500,
  },
}

// 折线图公共配置
const commonLineGrid = {
  left: 0,
  right: 4,
  top: 22,
  bottom: 0,
  containLabel: true,
}
const commonLineXAxis = {
  type: 'category' as const,
  boundaryGap: false,
  data: [],
  axisLine: { show: false },
  axisTick: { show: false },
  axisLabel: {
    color: '#999',
    fontSize: 12,
  },
  splitLine: { show: false },
}
const commonLineYAxis = {
  type: 'value' as const,
  axisLine: { show: false },
  axisTick: { show: false },
  axisLabel: {
    color: '#999',
    fontSize: 12,
  },
  splitLine: {
    show: true,
    lineStyle: {
      color: '#F1F2F4',
      width: 1,
    },
  },
}
const commonLineTooltip = {
  trigger: 'axis' as const,
  backgroundColor: '#000000B3',
  borderColor: 'transparent',
  textStyle: { color: '#fff' },
  borderRadius: 4, // 圆角
  padding: [8, 12, 8, 12], // 内边距
  axisPointer: {
    type: 'line' as const,
    lineStyle: {
      color: '#666666',
      type: 'solid' as const,
      width: 1,
    },
  },
}

const commonGraphic = {
  type: 'image',
  left: 'center',
  top: '25%',
  style: {
    image: imgEmpty,
    width: 100,
    height: 100,
  },
}

export const pieOptions: EChartsOption = {
  tooltip: {
    ...commonTooltip,
    trigger: 'item',
    valueFormatter: (value: any) => value + '个',
    formatter: (params: any) => {
      // params.marker 是自带的颜色方块图标
      // params.name 是扇区名称 (如 '园区交换机')
      // params.value 是扇区数值 (如 17)
      // params.percent 是扇区百分比 (如 14)
      // 获取当前数据项的颜色
      const itemColor = params.color || '#fff' // 确保有颜色，默认为白色

      // 自定义 marker 的 HTML 结构和样式
      const customMarker = `
        <span style="
          display: inline-block;
          margin-right: 8px;
          width: 8px;
          height: 8px;
          border-radius: 2px;
          background-color: ${itemColor};
          vertical-align: middle;
        "></span>
      `
      return `
        ${customMarker} <span>${params.name}</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left;">项目数</span> <span>${params.value}</span><br/>
        <span style="display: inline-block; width: 60px; text-align: left;">占比</span> <span>${params.percent}%</span>
      `
    },
  },
  legend: {
    ...commonLegend,
  },
  color: commonColors,
  series: [
    {
      name: 'outerPie',
      type: 'pie',
      silent: true, // 关闭 hover 和 tooltip
      label: { show: false },
      labelLine: { show: false },
      radius: ['60%', '65%'], // 外环
      center: ['50%', '35%'], // 中上位置
      data: [{ value: 1, itemStyle: { color: '#F1F2F4' } }],
    },
    {
      type: 'pie',
      radius: ['40%', '60%'], // 主环
      center: ['50%', '35%'], // 中上位置
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'inside',
        formatter: '{c}',
        color: '#fff',
        fontSize: 12,
        fontWeight: 400,
      },
      // emphasis: { scaleSize: 15 },
      labelLine: { show: false },
      data: [],
    },
    {
      name: 'innerPie',
      type: 'pie',
      silent: true, // 关闭 hover 和 tooltip
      label: { show: false },
      labelLine: { show: false },
      radius: ['35%', '40%'], // 内环
      center: ['50%', '35%'], // 中上位置
      data: [{ value: 1, itemStyle: { color: '#F1F2F4' } }],
    },
    {
      type: 'pie',
      radius: ['0%', '65%'], // 主环
      center: ['50%', '45%'], // 中上位置
      label: {
        show: true,
        color: '#999999',
      },
      labelLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC', // 线颜色
          width: 1,
        },
      },
      avoidLabelOverlap: false,
      data: [],
    },
  ],
}

/**
 * 生成折线图 option
 * @param {Object} params
 * @param {string} params.title 图表标题
 * @param {string} params.yName Y轴名称
 * @param {string[]} params.xData X轴类目
 * @param {number[]} params.seriesData 折线数据
 * @param {string} params.lineColor 折线颜色
 * @param {string} params.unit 单位
 * @returns {EChartsOption}
 */
export function getLineOptions({
  title,
  yName,
  xData,
  seriesData,
  lineColor = '#378EEF',
  unit,
  formatter,
}: {
  title: string
  yName: string
  xData: string[]
  seriesData: number[]
  lineColor?: string
  unit?: string
  formatter?: any
}): EChartsOption {
  const isEmpty = !seriesData || seriesData.length === 0
  return {
    title: {
      show: true,
      text: '',
      subtext: unit || yName,
      left: -4,
      top: -14,
      subtextStyle: {
        color: '#BBBBBB',
        fontSize: 11,
        fontWeight: 400,
      },
      textStyle: {
        fontSize: 0,
      },
    },
    grid: { ...commonLineGrid },
    graphic: isEmpty ? commonGraphic : [],
    xAxis: isEmpty ? undefined : { ...commonLineXAxis, data: xData },
    yAxis: isEmpty
      ? undefined
      : {
          ...commonLineYAxis,
          name: yName,
          nameTextStyle: {
            color: '#A6B1C2',
            fontSize: 12,
            fontWeight: 400,
            align: 'left',
            padding: [0, 0, 8, 0],
          },
        },
    tooltip: {
      ...commonLineTooltip,
      formatter: formatter,
    },
    series: isEmpty
      ? []
      : [
          {
            name: title,
            type: 'line',
            data: seriesData,
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            showSymbol: true,
            lineStyle: {
              color: lineColor,
              width: 2,
            },
            itemStyle: {
              color: lineColor,
              borderColor: '#fff',
              borderWidth: 2,
            },
            emphasis: {
              focus: 'series',
            },
          },
        ],
  }
}

/**
 * 生成柱状图 option
 * @param {Object} params
 * @param {string} params.title 图表标题
 * @param {string} params.unit 单位
 * @param {string[]} params.xData x轴类目
 * @param {number[]} params.seriesData 柱状数据
 * @param {string[]} params.color 柱状颜色
 * @returns {EChartsOption}
 */
export function getBarOptions({
  title = '',
  unit = '',
  xData = [],
  seriesData = [],
  color = ['#378EEF', '#22C993', '#F5CC38', '#FA8F23'],
  formatter,
}: {
  title?: string
  unit?: string
  xData: string[]
  seriesData: number[]
  color?: string[]
  formatter?: any
}): EChartsOption {
  // 颜色轮询处理
  const coloredSeriesData = seriesData.map((value, idx) => ({
    value,
    itemStyle: { color: color[idx % color.length] },
  }))
  const isEmpty = !seriesData || seriesData.length === 0
  return {
    title: {
      show: true,
      text: '',
      subtext: unit,
      left: -4,
      top: -14,
      subtextStyle: {
        color: '#BBBBBB',
        fontSize: 11,
        fontWeight: 400,
      },
      textStyle: {
        fontSize: 0,
      },
    },
    color,
    grid: {
      left: 0,
      right: 0,
      top: 22,
      bottom: 0,
      containLabel: true,
    },
    legend: {
      show: false,
    },
    graphic: isEmpty ? commonGraphic : [],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#666666',
          type: 'solid',
          width: 1,
        },
      },
      backgroundColor: '#000000B3',
      borderColor: 'transparent',
      textStyle: { color: '#fff' },
      borderRadius: 4, // 圆角
      padding: [8, 12, 8, 12], // 内边距
      formatter: formatter,
    },
    xAxis: isEmpty
      ? undefined
      : {
          type: 'category',
          data: xData,
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: '#999',
            fontSize: 12,
            interval: 0, // 强制显示所有标签
          },
          splitLine: { show: false },
        },
    yAxis: isEmpty
      ? undefined
      : {
          type: 'value',
          name: unit,
          nameTextStyle: {
            color: '#BBBBBB',
            fontSize: 11,
            fontWeight: 400,
            align: 'left',
            padding: [0, 0, 8, 0],
          },
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F1F2F4',
              width: 1,
            },
          },
        },
    series: isEmpty
      ? []
      : [
          {
            name: title,
            type: 'bar',
            data: coloredSeriesData,
            barWidth: 32,
            barMinHeight: 2,
            itemStyle: {
              borderRadius: [4, 4, 0, 0],
            },
            emphasis: {
              itemStyle: {
                opacity: 1,
              },
            },
          },
        ],
  }
}

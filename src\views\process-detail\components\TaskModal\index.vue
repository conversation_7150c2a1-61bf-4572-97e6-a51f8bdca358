<template>
  <FModal
    class="task-modal-wrapper"
    width="700px"
    v-model:visible="visible"
    :title="i18n.t(props.title)"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <FForm v-if="isBatchEdit" ref="formRef" :model="formData" layout="vertical">
      <FRow>
        <FCol :span="12">
          <FFormItem :label="i18n.t('预计完成时间')" name="forcastTime">
            <FDatePicker
              v-model:value="formData.forcastTime"
              format="YYYY-MM-DD"
              show-time
              style="width: 100%"
              :disabled-date="disabledDate"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem :label="i18n.t('当前节点完成')" name="isSys">
            <FSelect
              v-model:value="formData.isSys"
              :options="[
                { label: '是', value: 1 },
                { label: '否', value: 0 },
              ]"
              :placeholder="i18n.t('请选择')"
            />
          </FFormItem>
        </FCol>
      </FRow>
      <FRow>
        <FCol :span="12">
          <FFormItem :label="i18n.t('跟进角色')" name="superviserRoleCode">
            <FSelect
              v-model:value="formData.superviserRoleCode"
              :options="props.role"
              :field-names="{ label: 'roleName', value: 'roleCode' }"
              allow-clear
              :placeholder="i18n.t('请选择角色')"
              @change="handleSuperviserRoleChange"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem :label="i18n.t('跟进人')" name="superviser">
            <FSelect
              v-model:value="formData.superviser"
              :options="superviserUserList"
              :filter-option="filterOption"
              :field-names="{ label: 'name', value: 'uuid' }"
              show-search
              allow-clear
              :placeholder="i18n.t('请选择人员')"
            />
          </FFormItem>
        </FCol>
      </FRow>
      <FRow>
        <FCol :span="12">
          <FFormItem :label="i18n.t('审核角色')" name="approverRoleCode">
            <FSelect
              v-model:value="formData.approverRoleCode"
              :options="props.role"
              :field-names="{ label: 'roleName', value: 'roleCode' }"
              allow-clear
              :placeholder="i18n.t('请选择角色')"
              @change="handleapproverRoleChange"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem style="margin-bottom: 0 !important" :label="i18n.t('审核人')" name="approver">
            <FSelect
              v-model:value="formData.approver"
              :options="approverUserList"
              :filter-option="filterOption"
              :field-names="{ label: 'name', value: 'uuid' }"
              show-search
              allow-clear
              :placeholder="i18n.t('请选择人员')"
            />
          </FFormItem>
        </FCol>
      </FRow>
    </FForm>
    <FForm v-else ref="formRef" :model="formData" :rules="formRules" layout="vertical">
      <FRow>
        <FCol :span="12">
          <FFormItem :label="i18n.t('协同标题')" name="taskName">
            <FInput v-model:value="formData.taskName" :placeholder="i18n.t('请输入标题')" />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem :label="i18n.t('当前节点完成')" name="isSys">
            <FSelect
              v-model:value="formData.isSys"
              :options="[
                { label: i18n.t('是'), value: 1 },
                { label: i18n.t('否'), value: 0 },
              ]"
              :placeholder="i18n.t('请选择')"
            />
          </FFormItem>
        </FCol>
      </FRow>
      <FRow>
        <FCol :span="12">
          <FFormItem :label="i18n.t('跟进角色')" name="superviserRoleCode">
            <FSelect
              v-model:value="formData.superviserRoleCode"
              :options="props.role"
              :field-names="{ label: 'roleName', value: 'roleCode' }"
              allow-clear
              :placeholder="i18n.t('请选择角色')"
              @change="handleSuperviserRoleChange"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem :label="i18n.t('跟进人')" name="superviser">
            <FSelect
              v-model:value="formData.superviser"
              :options="superviserUserList"
              :filter-option="filterOption"
              :field-names="{ label: 'name', value: 'uuid' }"
              show-search
              allow-clear
              :placeholder="i18n.t('请选择人员')"
            />
          </FFormItem>
        </FCol>
      </FRow>
      <FRow>
        <FCol :span="12">
          <FFormItem :label="i18n.t('审核角色')" name="approverRoleCode">
            <FSelect
              v-model:value="formData.approverRoleCode"
              :options="props.role"
              :field-names="{ label: 'roleName', value: 'roleCode' }"
              allow-clear
              :placeholder="i18n.t('请选择角色')"
              @change="handleapproverRoleChange"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem :label="i18n.t('审核人')" name="approver">
            <FSelect
              v-model:value="formData.approver"
              :options="approverUserList"
              :filter-option="filterOption"
              :field-names="{ label: 'name', value: 'uuid' }"
              show-search
              allow-clear
              :placeholder="i18n.t('请选择人员')"
            />
          </FFormItem>
        </FCol>
      </FRow>
      <FRow>
        <FCol :span="12">
          <FFormItem :label="i18n.t('预计完成时间')" name="forcastTime">
            <FDatePicker
              v-model:value="formData.forcastTime"
              format="YYYY-MM-DD"
              show-time
              style="width: 100%"
              :disabled-date="disabledDate"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem :label="i18n.t('前置任务')" name="preTask">
            <FSelect
              :placeholder="i18n.t('请选择')"
              v-model:value="formData.preTask"
              :options="taskData"
              :disabled="props.title !== TaskModalTilte.add"
              allow-clear
            />
          </FFormItem>
        </FCol>
      </FRow>
      <FRow>
        <FCol :span="24">
          <FFormItem style="margin-bottom: 0 !important" :label="i18n.t('任务说明')" name="taskDesc">
            <FTextarea
              v-model:value="(formData.contentData as Record<string, unknown>).taskDesc"
              :placeholder="i18n.t('请输入任务说明')"
            />
          </FFormItem>
        </FCol>
      </FRow>
    </FForm>
  </FModal>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'
import dayjs, { Dayjs } from 'dayjs'
import { TaskModalTilte } from '../../config'
import type { ITask, IUser, IProcess } from '@/types/handle'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { IProcessRoleAndUser } from '@/types/request'
import { useI18n } from '@/utils'

type UserType = IUser & { name: string }

interface IProps {
  modelValue: boolean
  title: TaskModalTilte
  role?: IProcessRoleAndUser[]
  data: ITask | IProcess
}

interface IFormData {
  taskName: string
  superviser: string | null
  superviserRoleCode: string | null
  approver: string | null
  approverRoleCode: string | null
  forcastTime: string | Dayjs | null
  isSys: 0 | 1
  contentData: { taskDesc: string }
  preTask: number | null
}
const i18n = useI18n()
const store = useStore()
const props = defineProps<IProps>()
const emits = defineEmits(['update:modelValue', 'submit'])

const confirmLoading = ref(false)
const taskData = ref<{ label: string; value: number }[]>([])
const userData = computed<UserType[]>(() => store.state.user.allUser || [])
const roleMap = computed<Map<string, IProcessRoleAndUser>>(() => {
  const map = new Map()
  props.role.forEach(item => map.set(item.roleCode, item))
  return map
})
const formRef = ref<FormInstance>()
const formData = reactive<IFormData>({
  taskName: '',
  superviser: null,
  superviserRoleCode: null,
  approver: null,
  approverRoleCode: null,
  forcastTime: null,
  isSys: 1,
  contentData: { taskDesc: '' },
  preTask: null,
})
const formRules = {
  taskName: [{ required: true, message: i18n.t('请输入标题') }],
  superviser: [{ required: false, message: i18n.t('请选择跟进人'), trigger: 'change' }],
  forcastTime: [{ required: false, message: i18n.t('请选择预计完成时间'), trigger: 'change' }],
  isSys: [{ required: true, message: i18n.t('是否当前阶段完成'), trigger: 'change' }],
}
const superviserUserList = ref<UserType[]>([])
const approverUserList = ref<UserType[]>([])
const isBatchEdit = computed(() => props.title === TaskModalTilte.batchEdit)
const disabledDate = (current: Dayjs) => current && current < dayjs().subtract(1, 'days').endOf('day')
const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emits('update:modelValue', val),
})

// 初始化 select 的列表数据
watch(
  () => userData.value,
  () => {
    superviserUserList.value = userData.value
    approverUserList.value = userData.value
  },
  { immediate: true }
)

watch(
  () => props.modelValue,
  () => {
    if (!props.modelValue) handleCancel()
    if (props.modelValue) {
      getPreTaskData()
      if (props.title === TaskModalTilte.edit) {
        const task = props.data as ITask
        const contentData = JSON.parse(task.contentData as string)
        formData.taskName = task.taskName
        formData.superviser = (task.superviserUuid as string) || null
        formData.superviserRoleCode = task.superviserRoleCode
        formData.approver = (task.approverUuid as string) || null
        formData.approverRoleCode = task.approverRoleCode
        formData.forcastTime = task.forcastTime ? dayjs(task.forcastTime) : ''
        formData.isSys = task.isSys
        formData.contentData = { taskDesc: contentData?.taskDesc }
        formData.preTask = task.preTask

        // false 避免顶掉了 接口返回的 人员数据
        handleSuperviserRoleChange(formData.superviserRoleCode, false)
        handleapproverRoleChange(formData.approverRoleCode, false)
      } else if (props.title === TaskModalTilte.addChild) {
        const task = props.data as ITask
        formData.preTask = task.id
      } else if (isBatchEdit.value) {
        formData.isSys = undefined as unknown as 0 | 1
      }
    }
  }
)

watch(
  () => formData.superviser,
  val => {
    !formData.superviser && (formData.superviserRoleCode = null)
  }
)

// 处理负责人角色变更
const handleSuperviserRoleChange = (roleCode: string | null, flag = true) => {
  superviserUserList.value = userData.value
  if (roleCode && roleMap.value.has(roleCode)) {
    const role = roleMap.value.get(roleCode)
    flag && (formData.superviser = role?.users?.[0].uuid ?? null)
  }
}

// 处理审核人角色变更
const handleapproverRoleChange = (roleCode: string | null, flag = true) => {
  approverUserList.value = userData.value
  if (roleCode && roleMap.value.has(roleCode)) {
    const role = roleMap.value.get(roleCode)
    flag && (formData.approver = role?.users?.[0].uuid ?? null)
  }
}

// 确认
const handleOk = async () => {
  const $form = formRef.value as FormInstance
  await $form.validate()
  confirmLoading.value = true
  const data = { ...formData }
  data.forcastTime = data.forcastTime ? dayjs(data.forcastTime).format('YYYY-MM-DD HH:mm:ss') : null

  if (props.title === TaskModalTilte.edit) {
    // 需要保留其他的信息，否则会被覆盖掉
    const contentData = JSON.parse(props.data.contentData as string) ?? {}
    data.contentData = { ...contentData, taskDesc: (data.contentData as Record<string, string>).taskDesc }
  }
  emits('submit', data)
}

// 取消
const handleCancel = () => {
  const $form = formRef.value as FormInstance
  $form.resetFields()
  visible.value = false
  confirmLoading.value = false
  superviserUserList.value = userData.value
  approverUserList.value = userData.value
  formData.taskName = ''
  formData.superviser = null
  formData.superviserRoleCode = null
  formData.approver = null
  formData.approverRoleCode = null
  formData.forcastTime = null
  formData.isSys = 1
  formData.contentData = { taskDesc: '' }
}

// 获取前置任务数据
const getPreTaskData = async () => {
  let children: { label: string; value: number }[] = []
  if (props.title === TaskModalTilte.add) {
    const data = props.data as IProcess
    children = (data.children ?? []).map(item => ({ label: item.taskName, value: item.id }))
  } else if (props.title === TaskModalTilte.addChild) {
    const data = props.data as ITask
    children = [{ label: data.taskName, value: data.id }]
  } else if (props.title !== TaskModalTilte.batchEdit) {
    const data = props.data as ITask
    const label = data.prefixTaskName as string
    children = [{ label: label !== '--' ? label : i18n.t('无'), value: data.preTask }]
  }
  taskData.value = [{ label: i18n.t('无'), value: 0 }, ...children]
}

const filterOption = (input: string, option: UserType) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
</script>

<style lang="scss">
.task-modal-wrapper {
  .fs-form-item {
    margin-bottom: 22px !important;
  }
  .fs-form-item-control-input-content textarea.fs-input {
    height: auto !important;
  }

  .fs-modal-footer .fs-btn {
    display: inline-block;
  }

  .fs-picker {
    height: 32px;
  }

  .fs-row > .fs-col {
    padding: 0 10px !important;
  }

  .fs-form-item > .fs-col {
    padding: 0;
  }
  .fs-form .fs-row.fs-form-item {
    margin-bottom: 24px !important;
  }

  .fs-form .fs-row.fs-form-item-with-help {
    margin-bottom: 0px !important;
  }
}
</style>

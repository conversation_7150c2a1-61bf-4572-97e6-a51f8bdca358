<template>
  <div class="process-mm-list">
    <!-- <Breadcrumb class="none-container-padding" /> -->
    <div class="card-content-shadow pt24 pl24 pr24 mb16">
      <SearchContent v-model:query-data="queryData" />
    </div>
    <div class="card-content-shadow pt24 pl24 pr24 mb24">
      <div class="flex space-between mb16">
        <div class="fw-500 f14">员工大会流程列表</div>
        <FSpace :size="[12]" wrap class="handle-row-box">
          <FButton type="primary" class="mr6" @click="HandleProcessFn({ key: STAFFMEETING_CONFIG_ID })">
            <template #icon><i class="iconfont icontubiao_tianjia1" /></template>
            创建流程</FButton
          >
          <!-- <component :is="columnsConfig.Operation"></component> -->
        </FSpace>
      </div>
      <FTable
        class="table-warp"
        :columns="columnsConfig.tableColumns"
        :loading="loading"
        :data-source="dataList"
        :row-key="(data:any) => data.id"
        :row-selection="rowSelection"
        :sticky="{ offsetHeader: 0 }"
        :scroll="{ x: 'min-content' }"
        :pagination="{
          total: paging.total,
          current: paging.pageNum,
          pageSize: paging.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
          onChange: onPaginationChangeFn
        }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'creatorInfo'">
            <div class="label-box">
              <span class="label">[录入时间]</span>
              <span>{{
                (record?.createdTime && transformDate(record?.createdTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--'
              }}</span>
            </div>
            <div class="label-box">
              <span class="label">[操作日志]</span>
              <span class="code-link" @click="logModalRef?.onOpenFn(record?.id)">查看日志</span>
            </div>
          </template>

          <template v-if="column.dataIndex === 'processInstanceCode'">
            <span :class="[record?.id ? 'code-link' : '']" @click="onJumpDemandDetial(record)">{{
              record?.processInstanceCode ?? '--'
            }}</span>
          </template>

          <template v-if="column.dataIndex === 'topicName'">
            <span :class="[record?.id ? 'code-link' : '']" @click="onJumpDemandDetial(record)">{{
              record?.topicName ?? '--'
            }}</span>
          </template>

          <template v-if="column.dataIndex === 'describe'">
            <MoreTextTips :line-clamp="5">
              <span v-html="record?.describe ?? '--'"></span>
            </MoreTextTips>
          </template>

          <template v-if="column.dataIndex === 'currentMilepost'">
            <ProcessDetail v-model:value="record.currentMilepost" />
            <div class="label-box">
              <span class="label">[责任人]</span>
              <span>{{ record?.currentMilepost?.superviser ?? '--' }}</span>
            </div>
            <div class="label-box">
              <span class="label">[DDL]</span>
              <span>{{
                (record?.currentMilepost?.forcastTime &&
                  transformDate(record?.currentMilepost?.forcastTime, 'YYYY-MM-DD HH:mm:ss')) ??
                '--'
              }}</span>
            </div>
          </template>

          <template v-if="column.dataIndex === 'stpInfo'">
            <div :class="[record?.tgifDhwd ? 'code-link' : '']" @click="onOpenDocument(record?.tgifDhwd)">
              员工大会定稿
            </div>
            <div :class="[record?.tgifWtqdjd ? 'code-link' : '']" @click="onOpenDocument(record?.tgifWtqdjd)">
              问题清单解答版
            </div>
            <div :class="[record?.tgifDhsytk ? 'code-link' : '']" @click="onOpenDocument(record?.tgifDhsytk)">
              大会摄影图库
            </div>
            <div :class="[record?.tgifDyhwzCn ? 'code-link' : '']" @click="onOpenDocument(record?.tgifDyhwzCn)">
              订阅号文章
            </div>
            <div :class="[record?.tgifWjsj ? 'code-link' : '']" @click="onOpenDocument(record?.tgifWjsj)">问卷收集</div>
          </template>

          <template v-if="column.dataIndex === 'relevanceList'">
            <div class="label-box" v-for="(item, index) in record?.relevanceList ?? []" :key="index">
              <span class="label">{{ `[流程${index + 1}]` }}</span>
              <span :class="[item?.id ? 'code-link' : '']" @click="onJumpDemandDetial(item, 'relevanceInstanceId')">{{
                item?.relevanceNumber ?? '--'
              }}</span>
            </div>
          </template>
        </template>
      </FTable>
    </div>
    <LogModal ref="logModalRef" />
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, reactive, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { FButton, message } from '@fs/smart-design'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import SearchContent from './components/SearchContent/index.vue'
import LogModal from '@/views/process-ipd-list/components/LogModal/index.vue'
import ProcessDetail from './components/ProcessDetail/index.vue'
import { tableColumnKeys, useTableColumn } from '@/components/TableOperation/index'
import MoreTextTips from '@/components/MoreTextTips/index'
import { getStaffmeetingProcessList } from '@/api'
import { getByIdProcessConfig } from '@/api/processManagement'
import { cache, transformDate, jumpToDemand, STAFFMEETING_CONFIG_ID } from '@/utils'

const { currentRoute, resolve } = useRouter()
const route = useRoute()
const router = useRouter()
const routerName = computed<any>(() => currentRoute.value?.name)
const loading = ref(false)
const paging = reactive<any>({ pageNum: 1, pageSize: 10, total: 0 })
const dataList = ref<any[]>([])
const selectedKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[]) => (selectedKeys.value = selectedRowKeys),
}))
const columnsConfig = reactive(
  useTableColumn(
    [
      { title: '录入时间/操作日志', dataIndex: 'creatorInfo', key: 'creatorInfo', width: 260 },
      { title: '流程编号', dataIndex: 'processInstanceCode', key: 'processInstanceCode', width: 139 },
      { title: '一条龙经理', dataIndex: 'pdOwner', key: 'pdOwner', width: 139 },
      { title: '流程名称', dataIndex: 'topicName', key: 'topicName', width: 180 },
      { title: '背景&目标', dataIndex: 'describe', key: 'describe', width: 260 },
      { title: '流程状态', dataIndex: 'currentMilepost', key: 'currentMilepost', width: 260 },
      { title: '关键产出', dataIndex: 'outputsInfo', key: 'outputsInfo', width: 260 },
      { title: '关联流程', dataIndex: 'relevanceList', key: 'relevanceList', width: 260 },
    ],
    tableColumnKeys.processListTable_staffmeeting
  )
)
const queryData = ref<any>({})
const logModalRef = ref()
const target = ref(() => document.querySelector('#container'))

const getStatusInfo = data => {
  let info = {
    icon: 'icontubiao_jinggao error-color', // 图标
  }
  if ([2, 3].includes(data?.status)) {
    info.icon = 'icontubiao_chenggong sucess-color'
  }
  return info
}

const onOpenDocument = (url: any) => {
  if (url) {
    window.open(url, '_blank')
  } else {
    message.error('文档不存在')
  }
}

// 需求详情跳转
const onJumpDemandDetial = (record: any, idKey = 'id') => {
  record?.[idKey] && jumpToDemand(record[idKey], undefined, true, router)
}

const goCreatePage = (id, processDefineKey) => {
  const { href } = resolve({
    name: 'DemandAdd',
    params: {
      id: id,
      processDefineKey: processDefineKey,
    },
    query: {
      localName: route.name as string,
    },
  })
  window.open(href, '_blank')
}

const HandleProcessFn = async ({ key }) => {
  if (!key) return
  const { data = {} } = (await getByIdProcessConfig(key)) as any
  data?.processDefineKey && goCreatePage(key, data?.processDefineKey)
}

// 查询列表
const queryDataList = async () => {
  try {
    loading.value = true
    const data = { ...queryData.value }
    cache.set(
      routerName?.value,
      JSON.stringify({
        ...(data?.cacheValue ?? {}),
        pageNum: paging.pageNum,
        pageSize: paging.pageSize,
      })
    )
    delete data.cacheValue
    const res = await getStaffmeetingProcessList(data, paging)
    dataList.value = res?.data?.list || []
    paging.total = res?.data?.totalCount || 0
  } finally {
    selectedKeys.value = []
    loading.value = false
  }
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  paging.pageNum = current
  paging.pageSize = pageSize
  queryDataList()
}

// 查询列表
const onGetSearchData = (data: any) => {
  queryData.value = data
  paging.pageNum = data?.cacheValue?.pageNum || 1
  paging.pageSize = data?.cacheValue?.pageSize || 10
  queryDataList()
}

watch(
  () => queryData.value,
  val => {
    onGetSearchData(val)
  },
  { deep: true }
)
</script>
<style lang="scss">
.label-box {
  display: flex;
  color: #333;
  .label {
    margin-right: 4px;
    color: #999;
    white-space: nowrap;
  }
}
</style>
<style scoped lang="scss">
.process-mm-list {
  .none-container-padding {
    margin-top: -20px;
    // margin-left: -20px;
    // width: calc(100% + 40px);
  }
  .card-content-shadow {
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .code-link {
    cursor: pointer;
    color: #378eef;
  }
  .label-box {
    display: flex;
    color: #333;
    .label {
      margin-right: 4px;
      color: #999;
      white-space: nowrap;
    }
  }
  .mr6 {
    margin-right: 6px;
  }
  .mr4 {
    margin-right: 4px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .error-color {
    color: #f04141;
  }
  .sucess-color {
    color: #2fcc83;
  }
  .empty-content {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .count-info-content {
    line-height: 18px;
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
}
</style>

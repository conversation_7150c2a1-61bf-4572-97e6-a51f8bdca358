<template>
  <MoreTextTips :lineClamp="1" bgColor="#000" textColor="#fff"
    ><div style="white-space: pre-line">{{ displayValue || '--' }}</div></MoreTextTips
  >
</template>

<script setup lang="ts">
import { computed } from 'vue'
import MoreTextTips from '@/components/MoreTextTips/index'
import { transformDate } from '@/utils'
import { getValueFn } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'

interface FieldNames {
  label?: string
  value?: string
  children?: string
}

interface ComponentAttrs {
  options?: any[]
  treeData?: any[]
  fieldNames?: FieldNames
  [key: string]: any
}

interface ComponentConfig {
  dataType: string
  viewPower?: boolean
  componentAttrs?: ComponentAttrs
  [key: string]: any
}

interface IProps {
  value: string | string[] | number | undefined
  componentConfig?: ComponentConfig
}

const props = defineProps<IProps>()

const getCurrentValueFn = (componentInfo: ComponentConfig, value: any): string => {
  if (!componentInfo) return '--'

  const { dataType } = componentInfo

  if (value === undefined || value === null || value === '') {
    return '--'
  }

  switch (dataType) {
    case 'input':
    case 'inputNumber':
    case 'textarea':
    case 'customJsonData':
      return String(value)

    case 'select':
    case 'radio':
      return (
        (props?.componentConfig?.valueFormatFn &&
          props?.componentConfig?.valueFormatFn?.(value, props?.componentConfig)) ||
        handleSelect(componentInfo, value)
      )

    case 'selectMultiple':
      return handleMultipleSelect(componentInfo, value)

    case 'selectTree':
      return handleTreeSelect(componentInfo, value)

    case 'datePicker':
      return (
        (props?.componentConfig?.valueFormatFn && props?.componentConfig?.valueFormatFn(value)) || transformDate(value)
      )

    case 'switch':
      return value ? '是' : '否'

    default:
      return String(value) || '--'
  }
}

const handleSelect = (componentInfo: ComponentConfig, value: any): string => {
  const options = componentInfo?.componentAttrs?.options || []
  const valueKey = componentInfo?.componentAttrs?.fieldNames?.value || 'value'
  const labelKey = componentInfo?.componentAttrs?.fieldNames?.label || 'label'

  const item = getValueFn(options, value, valueKey)
  return item?.[labelKey] || '--'
}

const handleMultipleSelect = (componentInfo: ComponentConfig, value: any): string => {
  const options = componentInfo?.componentAttrs?.options || []
  const valueKey = componentInfo?.componentAttrs?.fieldNames?.value || 'value'
  const labelKey = componentInfo?.componentAttrs?.fieldNames?.label || 'label'

  if (!value) return '--'

  const valueArray = Array.isArray(value) ? value : value.split(',')

  const labels = valueArray
    .map((val: string) => {
      const item = getValueFn(options, val, valueKey)
      return item?.[labelKey] || '--'
    })
    .filter((label: string) => label !== '--')

  return labels.length > 0 ? labels.join(', ') : '--'
}

const handleTreeSelect = (componentInfo: ComponentConfig, value: any): string => {
  const treeData = componentInfo?.componentAttrs?.treeData || []
  const valueKey = componentInfo?.componentAttrs?.fieldNames?.value || 'value'
  const labelKey = componentInfo?.componentAttrs?.fieldNames?.label || 'label'

  const item = getValueFn(treeData, value, valueKey)
  return item?.[labelKey] || '--'
}

const displayValue = computed(() => {
  return getCurrentValueFn(props.componentConfig as ComponentConfig, props.value)
})
</script>

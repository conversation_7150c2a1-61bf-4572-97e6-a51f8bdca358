<template>
  <FPopover :getPopupContainer="target" trigger="hover" overlay-class-name="popover-select-notes">
    <template #content>
      <div class="file-box">
        <f-checkbox-group class="marginB12" v-model:value="checkedGroup" @change="checkedGroupChange">
          <div
            class="file-item"
            v-for="file in plainOptions"
            :key="file.url"
            :style="{ 'pointer-events': (['uploading', 'error'].includes(file.status) && 'none') || 'all' }"
          >
            <f-checkbox v-model:value="file.url">
              <span
                class="file-ellipsis file-text fontSize12"
                :title="file.fileName"
                @click.prevent="onPreviewFn(file)"
              >
                <LoadingOutlined v-if="['uploading', 'error'].includes(file.status)" />
                <span v-else class="iconfont icontubiao_lianjie3 fontSize14 marginR4"></span>
                <span class="blue">{{ file.fileName }}</span>
              </span>
            </f-checkbox>
            <i class="iconfont icontubiao_xiazai fontSize14 marginL4" @click="downLoadFile(file)"></i>
          </div>
        </f-checkbox-group>
        <div class="download-batch">
          <f-checkbox v-model:checked="isCheckAll" @change="checkAll" class="marginR8 fontSize12">
            <span class="file-ellipsis fontSize12">{{ i18n.t('全选') }}</span>
          </f-checkbox>
          <span class="blue file-text" @click="batchDownloadFile"
            ><i class="iconfont icontubiao_xiazai marginR4"></i>{{ i18n.t('批量下载') }}</span
          >
          <FUpload
            v-if="hasUpload"
            v-model:file-list="plainOptions"
            action=""
            :showUploadList="false"
            :before-upload="handleUpload"
          >
            <span class="blue file-text marginL12">
              <span class="iconfont icontubiao_shangchuan"></span>{{ i18n.t('上传') }}
            </span>
          </FUpload>
        </div>
      </div>
    </template>
    <slot name="iconfont">
      <span class="file-text fontSize12 blue"
        ><span class="iconfont icontubiao_lianjie3 marginR4"></span>{{ i18n.t('附件') }}</span
      >
    </slot>
  </FPopover>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from '@fs/smart-design'
import { useI18n } from '@/utils'
import { download, batchDownload, fileLimit, previewFile } from '@/utils'
import { upload } from '@/api'
import { LoadingOutlined } from '@ant-design/icons-vue'
const i18n = useI18n()

const props = defineProps({
  modelValue: {
    type: [Object, Array],
    default: () => [],
  },
  hasUpload: {
    type: [Boolean],
    default: () => false,
  },
})
const emits = defineEmits(['update:modelValue'])
const target = ref(() => document.querySelector('#container'))
const plainOptions = computed({
  get: () => props.modelValue,
  set: val => emits('update:modelValue', val),
})
const checkedGroup = ref<any>([])
const isCheckAll = ref(false)
const checkedGroupChange = (e: any) => {
  isCheckAll.value = e.length === plainOptions.value.length
}

const checkAll = (e: any) => {
  if (isCheckAll.value) {
    checkedGroup.value = plainOptions.value.map((item: any) => {
      return item.url
    })
  } else {
    checkedGroup.value = []
  }
}

const handleUpload = (file: any) => {
  if (!fileLimit(file)) {
    file.status = 'error'
    return false
  }
  let key = Date.now() + '' + Math.random()
  file.status = 'uploading'
  file.key = key
  file.url = key
  file.fileName = file.name
  uploadItem(file, key)
  return false
}

const uploadItem = (file: any, key: string) => {
  let data = new FormData()
  data.append('file', file)
  data.append('isOpen', 'false')
  data.append('expire', '0')
  upload(data, undefined, key).then((res: any) => {
    const index = plainOptions.value.findIndex((item: any) => item.key === key)
    if (res && typeof res === 'string' && index !== -1) {
      plainOptions.value[index].url = res
      plainOptions.value[index].status = 'success'
    } else {
      plainOptions.value.splice(index, 1)
      message.error(i18n.t('上传失败，请重新上传！'))
    }
  })
}

const onPreviewFn = (item: any) => {
  previewFile(item.url, item.fileName)
}

const downLoadFile = (file: any) => {
  download(file.url, file.fileName)
}

const batchDownloadFile = () => {
  if (checkedGroup.value.length > 0) {
    const checkList: any[] = []
    plainOptions.value.forEach((item: any) => {
      checkedGroup.value.forEach((items: string) => {
        if (items === item.url) {
          const list: Record<string, string> = {}
          list.url = item.url
          list.fileName = item.fileName
          checkList.push(list)
        }
      })
    })
    batchDownload(checkList)
    checkedGroup.value = []
    isCheckAll.value = false
  } else {
    message.error(i18n.t('请勾选文件'))
  }
}
</script>
<style lang="scss" scoped>
.popover-select-notes {
  :deep(.fs-popover-inner-content) {
    padding: 12px !important;
  }
}
.file-text {
  display: flex;
  align-items: center;
  width: max-content;
  cursor: pointer;
}
.file-box {
  min-width: 200px;
  font-size: 12px;
  .fs-checkbox-group {
    width: 100%;
  }
  .file-item {
    display: flex;
    justify-content: space-between;
    height: 24px;
    width: 100%;
    line-height: 24px;
    padding: 0 4px;
    .icontubiao_xiazai {
      visibility: hidden;
      cursor: pointer;
    }
    &:hover {
      background: #f1f4f8;
      border-radius: 2px;
      .icontubiao_xiazai {
        visibility: visible;
      }
    }
    .fs-checkbox-wrapper {
      height: 100%;
      span {
        height: 100%;
      }
    }
  }
  .file-icon {
    color: #fdb926 !important;
  }
  .file-ellipsis {
    vertical-align: -5px;
    display: inline-block;
    max-width: 180px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
    color: #333333;
  }
}
.download-batch {
  padding-top: 12px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  :deep(.fs-checkbox) {
    margin-left: 4px;
  }
}
.marginR4 {
  margin-right: 4px;
}
.marginB12 {
  margin-bottom: 12px !important;
}
.fontS12 {
  font-size: 12px !important;
}
.color378EEF {
  color: #378eef;
}
.marginR8 {
  margin-right: 8px;
}
.blue {
  color: #378eef;
}
</style>

export interface IRes<T = null> {
  code: number
  msg: string
  data: T
}
export interface IClassDataList<T = null> {
  pageNum: number
  pageSize: number
  total: number
  totalCount: number
  list: T[]
}
export interface IClassData {
  [x: string]: unknown
  createdTime: string
  id: number
  invalid: number
  processDefineKey: string
  processName: string
  remark: string
  tag: string
  businessType: string
  updateTime: string
}
export interface IResData {
  code: number
  msg: string
  traceId: string
}
export interface IClassParams {
  id?: number
  invalid: number
  processDefineKey: string
  processName: string
  remark: string
  tag: string
}

export interface IProcessCollect {
  id: number
  ext: string
  uuid: string
  configId: number
  configName: string
}

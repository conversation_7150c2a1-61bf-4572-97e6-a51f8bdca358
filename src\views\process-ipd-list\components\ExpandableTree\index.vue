<template>
  <div class="expandable-tree">
    <ExpandableTreeItem
      v-for="item in items"
      :key="item.id"
      :item="item"
      :level="0"
      :icon-class-open="iconClassOpen"
      :icon-class-closed="iconClassClosed"
      :indent="indent"
      :expanded-ids="expandedIds"
      :has-line="hasLine"
      ref="treeRefs"
    >
      <template #label="slotProps">
        <slot name="label" v-bind="slotProps" />
      </template>
      <template #default="slotProps">
        <slot v-bind="slotProps" />
      </template>
    </ExpandableTreeItem>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, ref, defineExpose } from 'vue'
import ExpandableTreeItem from './ExpandableTreeItem.vue'

interface TreeItem {
  id: string | number
  label: string
  children?: TreeItem[]
  [key: string]: any
}

const props = defineProps<{
  items: TreeItem[]
  iconClassOpen?: string
  iconClassClosed?: string
  indent?: number
  expandedIds?: (string | number)[]
  hasLine?: boolean
}>()

const treeRefs = ref<InstanceType<typeof ExpandableTreeItem>[]>([])

function expandAll() {
  treeRefs.value.forEach(ref => ref.expandAll?.())
}

function collapseAll() {
  treeRefs.value.forEach(ref => ref.collapseAll?.())
}

defineExpose({
  expandAll,
  collapseAll,
})
</script>
<style scoped lang="scss">
:deep(.expandable-item) {
  position: relative;
  &:last-child {
    padding-bottom: 0;
    .line {
      &::after {
        content: '';
        display: none;
      }
    }
  }
  .line {
    flex: 0 0 6px;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    margin-top: 6px;
    background: #378eef;
    border-radius: 3px;
    &::after {
      content: '';
      position: absolute;
      width: 1px;
      height: 100%;
      margin-top: 12px;
      margin-left: 3px;
      transform: translateX(-50%);
      background: #eeeeee;
    }
    &.hidden-line {
      background: transparent;
      &::after {
        content: '';
        display: none;
      }
    }
  }
}
</style>

import { getAllUsers } from '@/api/handle'
import { login } from '@/init'
import { IUser } from '@/types/handle'
import { cache, debounce, feishuOAuth, gotoSSO, updatePower, USER_INFO, USER_TOKEN } from '@/utils'
import { message } from '@fs/smart-design'
import { ActionContext, Commit } from 'vuex'
import type { IStoreState } from '..'

export type StoreUserType = IUser & { name: string }
export interface IUserStore {
  allUser: StoreUserType[]
  stateToken: string | undefined
  userInfo: Record<string, unknown> | null
  token: string | null
}

const mapCache = new Map<string, unknown>()

const state: IUserStore = {
  allUser: [],
  stateToken: undefined,
  userInfo: null,
  token: null,
}
const mutations = {
  SET_USERS: (state: IUserStore, allUser: StoreUserType[]) => {
    state.allUser = allUser
  },
  SET_STATETOKEN: (state: IUserStore, stateToken: string | undefined) => {
    state.stateToken = stateToken
  },
  SET_USER_INFO: (state: IUserStore, data: Record<string, unknown>) => {
    if (data && data === state.userInfo) return

    state.userInfo = data
    data ? cache.set(USER_INFO, JSON.stringify(data)) : cache.remove(USER_INFO)
  },
  SET_USER_TOKEN: (state: IUserStore, data: string) => {
    if (data && data === state.token) return

    state.token = data
    data ? cache.set(USER_TOKEN, data) : cache.remove(USER_TOKEN)
  },
}

const getters = {
  getStateToken: (state: IUserStore) => state.stateToken,
}

const actions = {
  async getUsers({ commit }: { commit: Commit }) {
    if (mapCache.has('allUser')) return Promise.resolve(mapCache.get('allUser'))
    const { data = [] } = await getAllUsers()
    const userList = data.map(item => ({
      ...item,
      // name: `${item.nameCh}(${item.nameEn})`,
      name: item.feiShuName || `${item.nameCh}(${item.nameEn})`,
    }))
    mapCache.set('allUser', userList)
    commit('SET_USERS', userList)
    return userList
  },
  async setStateToken({ commit }: { commit: Commit }, stateToken: string) {
    if (stateToken) commit('SET_STATETOKEN', stateToken)
  },

  clearTicket({ commit }: ActionContext<IUserStore, IStoreState>) {
    commit('SET_USER_INFO', null)
    commit('SET_USER_TOKEN', null)
    gotoSSO()
  },

  async loginByCode({ dispatch }: ActionContext<IUserStore, IStoreState>, code: string) {
    const res = await login.codeLogin({ code })
    if (res.code !== 200) {
      message.error(`登陆失败: ${res.msg}`)
      feishuOAuth()
      return new Error('登录失败！')
    }

    const { token } = res
    await dispatch('resetToken', token)
  },

  async resetToken({ commit }: ActionContext<IUserStore, IStoreState>, token: string) {
    const userInfoRes = await login.getUserInfo(token)
    if (userInfoRes.code !== 200) return message.error(`获取用户信息失败: ${userInfoRes.msg}`)

    const userInfo = userInfoRes.data

    commit('SET_USER_INFO', userInfo)
    commit('SET_USER_TOKEN', token)

    await updatePower() // 重新当前 token 用户的获取权限数据
  },

  // 获取 bom 用户信息 token
  async getUserInfo({ commit }: ActionContext<IUserStore, IStoreState>) {
    const userInfoRes = await login.getUserInfo()
    if (userInfoRes.code !== 200) return message.error(`获取用户信息失败: ${userInfoRes.msg}`)

    const userInfo = userInfoRes.data
    commit('SET_USER_INFO', userInfo)

    await updatePower()

    return userInfo
  },

  // 登出
  async logout({ dispatch }: ActionContext<IUserStore, IStoreState>) {
    try {
      await login.logout()
    } finally {
      dispatch('clearTicket')
    }
  },

  // 更新 token
  checkToken: debounce<({ commit }: ActionContext<IUserStore, IStoreState>, isForce?: boolean) => Promise<void>>(
    async ({ commit }: ActionContext<IUserStore, IStoreState>, isForce = false) => {
      try {
        const res = await login.updateToken(isForce)
        if (res && res.code === 200) commit('SET_USER_TOKEN', res.token)
      } catch (error) {
        console.error('更新 token 失败', error)
      }
    }
  ),
}
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}

import { message } from '@fs/smart-design'

const defaultLimitFileTypes = [
  'PDF',
  'JPG',
  'JPEG',
  'PNG',
  'DOC',
  'DOCX',
  'PPT',
  'PPTX',
  'XLS',
  'XLSX',
  'TXT',
  'ZIP',
  'RAR',
]

export const fileLimit = (file: any, limitFileTypes = undefined) => {
  const fileType = file.name.replace(/.+\./, '')
  const allowTypes = limitFileTypes ?? defaultLimitFileTypes
  const allowSize = 2 * 1024
  if (allowTypes.indexOf(fileType.toUpperCase()) < 0) {
    message.error('文件类型错误')
    return false
  }
  if (file.size / (1024 * 1024) > allowSize) {
    message.error('最大文件2G')
    return false
  } else {
    return true
  }
}

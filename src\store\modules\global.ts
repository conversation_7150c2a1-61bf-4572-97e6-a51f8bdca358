import { IHistoryRecord } from '@/types/common'
import { i18nSign, cache, HISTORY_RECORD } from '@/utils'

const getHistoryRecord = (): IHistoryRecord[] => {
  const historyRecord = cache.get(HISTORY_RECORD)
  try {
    return historyRecord ? JSON.parse(historyRecord as string) : []
  } catch (error) {
    return []
  }
}

const saveHistoryRecord = (historyRecord: IHistoryRecord[]) => {
  cache.set(HISTORY_RECORD, JSON.stringify(historyRecord))
}
export interface IGlobalState {
  [key: string]: unknown
  isHeaderShow: boolean
  isSidebarShow: boolean
  language: string
  historyRecord: IHistoryRecord[]
  historyRecordMax: number
}

const state: IGlobalState = {
  isHeaderShow: false,
  isSidebarShow: false,
  language: localStorage.getItem(i18nSign) || 'zh-CN',
  historyRecord: getHistoryRecord(),
  historyRecordMax: 9,
}

const mutations = {
  SET_GLOBAL_STATE: (state: IGlobalState, data: Record<string, unknown>) => {
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        state[key] = data[key]
      }
    }
  },

  // header or sidebar
  SET_IS_HEADER_SHOW: (state: IGlobalState, data: boolean) => {
    state.isHeaderShow = data
  },
  SET_IS_SIDEBAR_SHOW: (state: IGlobalState, data: boolean) => {
    state.isSidebarShow = data
  },

  // language
  SET_LANGUAGE: (state: IGlobalState, data: string) => {
    state.language = data
    localStorage.setItem(i18nSign, data)
  },

  // history
  ADD_HISTORY_RECORD: (state: IGlobalState, data: IHistoryRecord) => {
    const index = state.historyRecord.findIndex(item => item.fullPath === data.fullPath)
    // if (index !== -1) state.historyRecord.splice(index, 1)
    if (index === -1) state.historyRecord.push(data)
    if (state.historyRecord.length > state.historyRecordMax) state.historyRecord.shift()
    saveHistoryRecord(state.historyRecord)
  },
  REMOVE_HISTORY_RECORD: (state: IGlobalState, data: IHistoryRecord) => {
    state.historyRecord = state.historyRecord.filter(item => item.fullPath !== data.fullPath)
    saveHistoryRecord(state.historyRecord)
  },
  CLEAR_HISTORY_RECORD: (state: IGlobalState) => {
    state.historyRecord = []
    saveHistoryRecord(state.historyRecord)
  },
  CLEAR_OTHER_HISTORY_RECORD: (state: IGlobalState, data: IHistoryRecord) => {
    state.historyRecord = [data]
    saveHistoryRecord(state.historyRecord)
  },
}

const actions = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}

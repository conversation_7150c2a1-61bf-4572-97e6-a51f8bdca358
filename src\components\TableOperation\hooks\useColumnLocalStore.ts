import { cloneColumns } from '..'
import { getUserInfo } from '@/utils'

/**
 * table列数据持久化
 * @param columns
 * @param key
 * @returns
 */
export default function useColumnLocalStore(columns, key) {
  const userInfo = getUserInfo()
  const tableColumnKey = 'bpmTableColumns_' + userInfo.adminId

  const formatColumns = columns => {
    return (columns || []).map(item => [item.key, item.checked, item.fixed])
  }

  const sortColumns = columnEntries => {
    const defaultColumns = cloneColumns([...columns])

    // 1. 对存在的旧数据还原排序
    const useColumns = columnEntries.map(([key, checked, fixed]) => {
      const temp = defaultColumns.find(item => item.key === key)
      return temp ? { ...temp, checked, fixed } : null
    })

    // 2. 对新增的数据添加对应位置
    for (let i = 0; i < defaultColumns.length; i++) {
      const column = defaultColumns[i]
      const idx = columnEntries.findIndex(([key]) => key === column.key)
      if (idx === -1) {
        useColumns.splice(i, 0, column)
      }
    }

    // 3. 删掉移除列数据
    // 4. 计算新插入的位置是不是在左国定还是右固定的范围内
    return useColumns.filter(Boolean).map((item, index) => {
      const nextFixed = useColumns[index + 1]?.fixed
      if (nextFixed === 'left' || nextFixed === true) {
        item.fixed = nextFixed
      }
      const prevFixed = useColumns[index - 1]?.fixed
      if (prevFixed === 'right') {
        item.fixed = prevFixed
      }
      return item
    })
  }

  const getColumns = () => {
    const tables = localStorage.getItem(tableColumnKey)
    const columnEntries = JSON.parse(tables || '{}')[key]
    if (columnEntries?.length) {
      return cloneColumns(sortColumns(columnEntries))
    } else {
      return cloneColumns([...columns])
    }
  }

  const setColunms = columns => {
    const tableMapsStr = localStorage.getItem(tableColumnKey)
    const tableMaps = JSON.parse(tableMapsStr || '{}')
    tableMaps[key] = formatColumns(columns)
    localStorage.setItem(tableColumnKey, JSON.stringify(tableMaps))
  }

  return {
    getColumns,
    setColunms,
  }
}

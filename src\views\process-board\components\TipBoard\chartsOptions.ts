import type { EChartsOption } from '@/components/BaseEchart/config'

export const barOptions: EChartsOption = {
  color: ['#378EEF'],
  tooltip: {
    trigger: 'axis',
    appendToBody: true,
    axisPointer: {
      type: 'none',
    },
    textStyle: {
      color: '#fff',
    },
    valueFormatter: (value: any) => {
      return value + '个'
    },
    backgroundColor: '#000000B3',
    borderColor: 'transparent',
  },
  grid: {
    left: '0',
    right: '0',
    top: '12%',
    bottom: '0',
  },
  xAxis: {
    show: false,
    data: [],
  },
  yAxis: {
    type: 'value',
    show: false,
  },
  series: [],
}

export const pieOptions: EChartsOption = {
  tooltip: {
    trigger: 'item',
    appendToBody: true,
    textStyle: {
      color: '#fff',
    },
    valueFormatter: (value: any) => {
      return value + '个'
    },
    backgroundColor: '#000000B3',
    borderColor: 'transparent',
  },
  legend: {
    orient: 'vertical',
    right: 0,
    top: 'middle',
    icon: 'circle',
    itemWidth: 8,
    itemHeight: 8,
  },
}

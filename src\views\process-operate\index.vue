<template>
  <FSpin :spinning="spinning">
    <div class="process-operate" v-if="hasViewPower">
      <!-- 标题 -->
      <Header :title="processName" :sub-title="processNo" />
      <!-- 内容主体 -->
      <Main v-if="currtMilepost" />
      <!-- 转派 -->
      <DispatchModal v-model:value="dispatchModel.flag" :is-remark="true" @submit="handleDispatchMilepost" />
      <!-- 驳回 -->
      <RejectModal v-model:value="rejectModel.flag" :reject-list="processRejectInfo" @submit="handleRejectMilepost" />
      <!-- 新增关联流程 -->
      <CreateProcessModal v-model="createProcessModal.flag" :params="{ processNo }" :is-detail="true" />
      <!-- 办结/提交 -->
      <NotificationModal
        v-model:value="notificationModel.flag"
        v-model:loading="notificationModel.loading"
        :title="(notificationModel.title as string)"
        :role="processRoleInfo"
        default-role-key="milepostId"
        :default-role-value="notificationModel.data.id"
        :is-delay-status="(isDelayStatus as boolean)"
        @submit="handleNotificationMilepost"
      />
      <!-- 撤回 -->
      <RemarkModal
        v-model="remarkModal.flag"
        :title="(remarkModal.title as string)"
        :text="i18n.t('撤回说明')"
        @submit="handleRecall"
      />
      <!-- 新增/编辑任务 -->
      <TaskModal
        v-model="taskModel.flag"
        :title="(taskModel.title as typeof TaskModalTilte)"
        :data="(taskModel.data as ITask)"
        :role="processRoleInfo"
        @submit="handleTaskSubmit"
      />
      <!-- 任务详情 -->
      <TaskDetail v-model:value="viewTaskModel.flag" :task-record="viewTaskModel.data" />
      <!-- 处理 -->
      <ProcessTask
        v-model:value="handleTaskModel.flag"
        :task-record="handleTaskModel.data"
        :params-wrapper="paramsWrapper"
        @father-method="getProcessInfo"
      />
      <!-- 审批 -->
      <TaskTodo
        v-model:value="judgeTaskModel.flag"
        :task-record="judgeTaskModel.data"
        :params-wrapper="paramsWrapper"
        @father-method="getProcessInfo"
      />

      <!-- 消息协同 -->
      <Message />
    </div>
    <FResult
      v-else-if="!hasViewPower && !spinning"
      status="403"
      title="无查看权限"
      sub-title="抱歉，暂无查看权限，请联系相应产品查看！"
    ></FResult>
  </FSpin>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, provide, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { messageInstance as message, FModal } from '@fs/smart-design'
import {
  EmitType,
  ProcessModalTilte,
  TASK_STATUS_ICON,
  TASK_STATUS_NAME,
  ProcessViewType,
  TaskModalTilte,
} from '@/views/process-detail/config'
import {
  commitTask,
  getInstanceInfo,
  getProcessRoleAndUser,
  recallProcess,
  rejectTask,
  // saveMilepostFormData,
  transferrTask,
  turnTask,
  batchDelTask,
  deleteProcessFn,
  editTask,
  addChildTask,
  batchEditTask,
  addTask,
  removeTask,
  rollingBack,
  getToDoList,
  isItConfigured,
  getProcessConfigByInstanceId,
  verifyPermissions,
  getNewIpdRelevanceProcess,
} from '@/api'
import { IProcess, ITask } from '@/types/handle'
import { IProcessRoleAndUser } from '@/types/request'
import { transformDate, deepClone, useI18n } from '@/utils'

import TaskModal from '@/views/process-detail/components/TaskModal/index.vue'
import TaskTodo from '@/components/TaskTodo/index.vue'
import TaskDetail from '@/components/TaskDetail/index.vue'
import ProcessTask from '@/components/ProcessTask/index.vue'

import Header from './components/Header/index.vue'
import Main from './components/Main/index.vue'
import NotificationModal from '@/components/NotificationModal/index.vue'
import CreateProcessModal from '@/components/CreateProcessModal/index.vue'
import RemarkModal from '../process-detail/components/RemarkModal/index.vue'
import DispatchModal from '@/components/DispatchModal/index.vue'
import RejectModal from '@/components/RejectModal/index.vue'
import Message from './components/Message/index.vue'
import { useStore } from 'vuex'

interface IModel<T = ITask> {
  flag: boolean
  loading?: boolean
  data: T
  [key: string]: unknown
}
const i18n = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const global = window.rawWindow || window

const spinning = ref<boolean>(false)
const processId = +(route.params.id as string)
const processNo = computed(() => processInfo.value[0]?.processInstanceCode ?? '')
const processName = computed(() => processInfo.value[0]?.instanceTopicName ?? '')
const processType = computed(() => processInfo.value[0]?.processType)

const processInfo = ref<IProcess[]>([])
const processConfigInfo = reactive<Record<string, any>>({})
const processRoleInfo = ref<IProcessRoleAndUser[]>([])
const processRejectInfo = computed(() => processInfo.value.filter(item => item.status == 3 || item.status == 5))

const currtMilepost = ref<IProcess>()

const remarkModal = reactive<IModel<IProcess>>({ flag: false, title: '', data: {} as IProcess })
const notificationModel = reactive<IModel<IProcess>>({ flag: false, data: {} as IProcess, title: '', loading: false })
const createProcessModal = reactive<IModel<Record<string, unknown>>>({ flag: false, data: {} })
const dispatchModel = reactive<IModel<IProcess>>({ flag: false, data: {} as IProcess })
const rejectModel = reactive<IModel<IProcess>>({ flag: false, data: {} as IProcess })
const layoutView = ref<ProcessViewType>(ProcessViewType.tiling) // 默认表格视图
const taskModel = reactive<IModel<ITask | ITask[] | IProcess>>({ flag: false, data: {} as ITask, title: '' })
const viewTaskModel = reactive<IModel>({ flag: false, data: {} as ITask })
const handleTaskModel = reactive<IModel>({ flag: false, data: {} as ITask })
const judgeTaskModel = reactive<IModel>({ flag: false, data: {} as ITask })
const toDoList = ref<ITask[]>([])
const newIpdRelevanceProcessInfo = ref<any>({})
const hasViewPower = ref<boolean>(false)
const paramsWrapper = <T>(
  data: T
): T & { processType: string; processInstanceCode: string; instanceTopicName: string } => {
  return {
    ...data,
    processType: processType.value,
    instanceTopicName: processName.value,
    processInstanceCode: processNo.value,
  }
}
const isDelayStatus = computed(
  () =>
    notificationModel.data?.invalid === 1 &&
    notificationModel.data?.isDelayReason === 1 &&
    notificationModel.data?.status === 2 &&
    notificationModel.data?.overdueDuration
)
const firstMilepost = computed(() => currtMilepost?.value?.id === processInfo?.value?.[0]?.id)
const initToDoList = async () => {
  const { data = [] } = await getToDoList(processId as number)
  toDoList.value = data.map((item: any) => {
    item.processInstanceCode = processNo
    return item
  })
}
const initNewIpdRelevanceProcessInfo = async () => {
  if (
    (new Function(`return ${process.env.VUE_APP_PROCESS_NEW_IPD_CONFIG_ID}`)() || '') ===
    processInfo?.value[0]?.processConfigId
  ) {
    const { data = {} } = await getNewIpdRelevanceProcess(processId as number)
    newIpdRelevanceProcessInfo.value = data
  }
}
const getProcessConfigByInstanceIdFn = async () => {
  const { data = {} } = await getProcessConfigByInstanceId(processId as number)
  processConfigInfo.value = data
}
const initAddTaskList = async () => {
  const { data = [] } = await isItConfigured(processId as number)
  processInfo.value = processInfo.value.map(item => {
    let config = data.find((info: any) => info.milepostId === item.id) || {}
    item.isItConfigured = config?.isItConfigured || false
    item.nodeConfig = config?.nodeConfig ?? {}
    return item
  })
}
const initViewPower = async () => {
  try {
    spinning.value = true
    const { data = false } = await verifyPermissions(processId as number)
    if (!data) throw new Error('无查看权限！')
    hasViewPower.value = data
  } finally {
    spinning.value = false
  }
}
provide('processId', processId) // 流程 id
provide('processNo', processNo) // 流程编号
provide('processName', processName) // 流程名称
provide('processType', processType) // 流程类型
provide('processInfo', processInfo) // 流程信息
provide('processRoleInfo', processRoleInfo) // 流程角色信息
provide('paramsWrapper', paramsWrapper) // 处理参数方法

provide('operate', (key: keyof typeof operate, data: any) => operate[key](data)) // 新增弹框

provide('currtMilepost', currtMilepost) // 设置当前激活的里程碑
provide('setCurrtMilepost', (data: IProcess) => (currtMilepost.value = data)) // 设置当前激活的里程碑
provide('setLayoutView', (type: ProcessViewType) => (layoutView.value = type)) // 设置当前激活的里程碑
provide('initToDoList', initToDoList)
provide('initNewIpdRelevanceProcessInfo', initNewIpdRelevanceProcessInfo)
provide('initAddTaskList', initAddTaskList)
provide('toDoList', toDoList)
provide('newIpdRelevanceProcessInfo', newIpdRelevanceProcessInfo)
provide('processConfigInfo', processConfigInfo) // 流程配置
provide('firstMilepost', firstMilepost)
onMounted(async () => {
  await initViewPower()
  const localUserCache = store.getters['local/getLocalUserData']
  if (localUserCache !== undefined && localUserCache !== null && localUserCache.processDetailLayoutView !== undefined) {
    layoutView.value = localUserCache.processDetailLayoutView
  }
  getProcessRoleInfo()
  await getProcessInfo()
  requestIdleCallback(initToDoList)
  requestIdleCallback(getProcessConfigByInstanceIdFn)
  // requestIdleCallback(initAddTaskList)
  requestIdleCallback(initNewIpdRelevanceProcessInfo)
})

// 操作方法
const operate = {
  // 转派
  [EmitType.dispatch]: (info: { type: string; data: unknown }) => {
    const { type, data } = info
    if (type === 'switch') {
      handleDispatchMilepost({
        milepostId: currtMilepost.value!.id,
        superviser: (data as { [key: string]: string }).id,
        isUpdateRole: (data as { [key: string]: string }).isUpdateRole,
        transferReason: i18n.t('新详情页，面板快速切换负责人'),
      })
    }
    // dispatchModel.data = data
    // dispatchModel.flag = true
  },
  // 驳回
  [EmitType.reject]: (data: IProcess) => {
    rejectModel.data = data
    rejectModel.flag = true
  },
  // 失单
  [EmitType.lostSingle]: async (data: IProcess) => {
    notificationModel.data = data
    notificationModel.flag = true
    notificationModel.title = ProcessModalTilte.lostSingle
  },
  // 办结
  [EmitType.finish]: async (data: IProcess) => {
    notificationModel.data = data
    notificationModel.flag = true
    notificationModel.title = ProcessModalTilte.finish
  },
  // 提交
  [EmitType.submit]: async (data: IProcess) => {
    notificationModel.data = data
    notificationModel.flag = true
    notificationModel.title = ProcessModalTilte.submit
  },
  // 撤回
  [EmitType.revoke]: async (data: IProcess) => {
    remarkModal.data = data
    remarkModal.flag = true
    remarkModal.title = i18n.t('撤回')
  },
  // 新增
  [EmitType.createProcess]: async (data: IProcess) => {
    createProcessModal.flag = true
    createProcessModal.data = data
  },
  // 修改角色
  [EmitType.updateRole]: async () => {
    await getProcessRoleInfo()
    await getProcessInfo()
    currtMilepost.value = processInfo.value.find(item => item.id === currtMilepost?.value?.id)
  },
  [EmitType.revokeTask]: (data: ITask) => {
    rollingBack({ id: data.id }).then(async res => {
      if (res.code == 200) {
        message.success(i18n.t('回滚成功'))
        await getProcessInfo()
        currtMilepost.value = processInfo.value.find(item => item.id === currtMilepost?.value?.id)
      } else {
        message.error(res.msg)
      }
    })
  },
  [EmitType.batchDelTask]: async (data: { milepost: IProcess; tasks: ITask[] }) => {
    const { milepost, tasks = [] } = data
    FModal.confirm({
      title: i18n.t('是否确认删除所选的任务？'),
      content: i18n.t('当前删除的任务中所包含的子任务会一并删除，请谨慎操作！'),
      okText: i18n.t('确定'),
      cancelText: i18n.t('取消'),
      onOk: async () => {
        await batchDelTask({
          instanceId: processId,
          instanceTopicName: processName.value,
          processInstanceCode: processNo.value,
          processType: processType.value,
          topicName: milepost.topicName,
          taskIdList: tasks.map((item: any) => item.id),
        })
        message.success(i18n.t('删除成功'))
        getProcessInfo()
      },
    })
  },
  [EmitType.batchEditTask]: (data: ITask[]) => {
    taskModel.data = deepClone(data)
    taskModel.flag = true
    taskModel.title = TaskModalTilte.batchEdit
  },
  [EmitType.createTask]: (data: IProcess) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.add
  },
  [EmitType.createChildTask]: (data: ITask) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.addChild
  },
  [EmitType.viewTask]: (data: ITask) => {
    viewTaskModel.data = data
    viewTaskModel.flag = true
  },
  [EmitType.editTask]: (data: ITask) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.edit
  },
  [EmitType.handleTask]: (data: ITask) => {
    handleTaskModel.data = data
    handleTaskModel.flag = true
  },
  [EmitType.judgeTask]: (data: ITask) => {
    judgeTaskModel.data = data
    judgeTaskModel.flag = true
  },
  [EmitType.delTask]: (data: ITask) => {
    removeTask(paramsWrapper({ taskId: data.id })).then(res => {
      if (res.code == 200) {
        message.success(i18n.t('删除成功'))
        getProcessInfo()
      } else {
        message.error(res.msg)
      }
    })
  },
  [EmitType.deleteProcess]: async () => {
    FModal.confirm({
      title: i18n.t('是否确认删除当前流程？'),
      content: i18n.t('一旦操作后数据将不可恢复，是否确认操作！'),
      okText: i18n.t('确定'),
      cancelText: i18n.t('取消'),
      onOk: async () => {
        const res = await deleteProcessFn(processId)
        if (res.code == 200) {
          message.success(res.msg)
          router.back()
        } else {
          message.error(res.msg)
        }
      },
    })
  },
}

// 提交任务
const handleTaskSubmit = async (data: ITask) => {
  let action
  let formData = deepClone(data)
  const currTask = taskModel.data as ITask
  formData.milepostId = currTask.milepostId as number
  if (taskModel.title === TaskModalTilte.edit) {
    action = editTask
    formData.id = currTask.id
    formData.preTask = currTask.childTaskFlag ? (currTask.preTask as number) : formData.preTask
  } else if (taskModel.title === TaskModalTilte.addChild) {
    action = addChildTask
    formData.preTask = currTask.id
  } else if (taskModel.title === TaskModalTilte.batchEdit) {
    action = batchEditTask
    formData = {
      isSys: data.isSys,
      superviserRoleCode: data.superviserRoleCode,
      superviser: data.superviser,
      approverRoleCode: data.approverRoleCode,
      approver: data.approver,
      forcastTime: data.forcastTime,
      instanceId: processId,
      taskIdList: ((currTask.tasks ?? []) as unknown as ITask[]).map(item => item.id),
    } as unknown as ITask
  } else {
    action = addTask
    formData.preTask = formData.preTask ?? 0
    formData.milepostId = (currTask as unknown as IProcess).id // 新增的时候 data 为 里程碑数据
  }
  spinning.value = true
  try {
    const { code } = await action(paramsWrapper(formData) as any)
    if (code !== 200) return
    getProcessInfo()
    message.success(i18n.t('操作成功'))
  } finally {
    taskModel.flag = false
    spinning.value = false
  }
}

// 处理里程碑提交/办结
const handleNotificationMilepost = async (data: string[]) => {
  try {
    // await handleFormSubmit(notificationModel.data.id, notificationModel.data.formData || {})
    if (
      notificationModel.title === ProcessModalTilte.finish ||
      notificationModel.title === ProcessModalTilte.lostSingle
    ) {
      const params = paramsWrapper({
        milepostId: notificationModel.data.id,
        instanceId: notificationModel.data.instanceId,
        ...data,
      })
      const res = await transferrTask(params)
      if (res.code !== 200) throw new Error(res.msg)
      message.success(i18n.t('操作成功'))
    } else {
      const params = paramsWrapper({ milepostInfoId: notificationModel.data.id, ...data })
      const res = await commitTask(params)
      if (res.code !== 200) throw new Error(res.msg)
      message.success(i18n.t('提交成功'))
    }
    global.FS_BPM_PROCESS_MILEPOST = {} // 清空表单数据
    notificationModel.flag = false
    getProcessInfo()
  } finally {
    notificationModel.loading = false
  }
}

// 转派里程碑
const handleDispatchMilepost = async (data: any) => {
  spinning.value = true
  const params = { ...data, milepostId: dispatchModel.data.id || data.milepostId }
  try {
    const res = await turnTask(paramsWrapper(params))
    if (res.code !== 200) return
    getProcessInfo()
    message.success(i18n.t('转派成功'))
  } finally {
    dispatchModel.flag = false
    spinning.value = false
  }
}
// 驳回
const handleRejectMilepost = async (data: any) => {
  spinning.value = true
  const params = { ...data, instanceId: processId, sourceMilepostId: rejectModel.data.id }
  try {
    const res = await rejectTask(params)
    if (res.code !== 200) return
    await getProcessInfo()
    message.success(i18n.t('驳回成功'))
  } finally {
    rejectModel.flag = false
    spinning.value = false
  }
}

// 处理撤回
const handleRecall = async (data: { remark: string; isSave: 0 | 1 }) => {
  try {
    spinning.value = true
    const params = { instanceId: processId, milepostId: remarkModal.data.id, msg: data.remark, isSave: data.isSave }
    const res = await recallProcess(paramsWrapper(params))
    if (res.code !== 200) return
    message.success(i18n.t('撤回成功'))
    getProcessInfo()
    if (data.isSave === 1) {
      message.success(i18n.t('3 秒后将跳转到草稿页面'))
      setTimeout(() => {
        router.push({
          name: 'DemandAdd',
          params: { id: processId, processDefineKey: processInfo.value[0].formKey },
          query: { draftId: res.data, keyType: 'formId' },
        })
      }, 3000)
    }
  } finally {
    remarkModal.flag = false
    remarkModal.data = {} as IProcess
    remarkModal.title = ''
    spinning.value = false
  }
}

// // 当前提交表单
// const handleFormSubmit = async (id: number, oldFormData: Record<string, unknown>) => {
//   const formData = global.FS_BPM_PROCESS_MILEPOST
//   if (!formData || !Object.keys(formData).length) return
//   const { file } = formData
//   const { oldFile } = oldFormData
//   let currFile =
//     oldFile && typeof oldFile === 'string'
//       ? JSON.parse(`[${oldFile}]`)
//       : ((oldFile as []) || []).map((item: Record<string, unknown>) => ({ ...item })) // 防止表单数据变更导致表单数据还原成修改前
//   if (file && oldFile) {
//     currFile.push(...JSON.parse(`[${file}]`))
//   } else if (file && !oldFile) {
//     currFile = JSON.parse(`[${file}]`)
//   }
//   try {
//     await saveMilepostFormData({ id, formData: { ...oldFormData, ...formData, file: currFile } })
//     message.success(i18n.t('表单信息提交成功'))
//   } catch (error) {
//     message.success(i18n.t('表单信息提交失败'))
//   }
// }

// 处理流程信息数据
const handleProcessInfo = (data: IProcess[]) => {
  // 处理结办后节点的状态数 和 字符串文件的 hack
  let flag = false
  data.forEach(item => {
    if (flag) item.status = -99999 // 办结后的节点
    else if (item.status === 4) flag = true

    if (item.formData && item.formData.file && typeof item.formData.file === 'string') {
      item.formData.file = JSON.parse(`[${item.formData.file}]`)
    }
    item.firstMilepostId = data[0].id
  })
}

// 处理树形表格需要的 Key 字段
const handleTableTreeKey = (
  data: IProcess[] | ITask[] = [],
  index = 0,
  processInstanceCode: string | null = null,
  isDelayReason: number | null = null,
  invalid: number | null = null
) => {
  data?.forEach(item => {
    item.key = ++index
    processInstanceCode = (processInstanceCode || item.processInstanceCode) as string
    isDelayReason = (isDelayReason || item.isDelayReason) as number
    invalid = (invalid || item.invalid) as number
    item.children && handleTableTreeKey(item.children, item.key * 10, processInstanceCode, isDelayReason, invalid)
    item.children?.forEach(task => {
      task.statusName = TASK_STATUS_NAME[task.status as keyof typeof TASK_STATUS_NAME]
      task.statusIcon = TASK_STATUS_ICON[task.status as keyof typeof TASK_STATUS_ICON]
      task.isSysName = task.isSys ? i18n.t('是') : i18n.t('否')
      task.superviser = task.superviser || '--'
      task.approver = task.approver || ''
      task.prefixTaskName = task.prefixTaskName || '--'
      task.forcastTimeStr = transformDate(task.forcastTime, 'YYYY-MM-DD')
      task.taskCompletedTimeStr = transformDate(task.taskCompletedTime, 'YYYY-MM-DD')
      task.processInstanceCode = task?.processInstanceCode || processInstanceCode
      task.isDelayReason = task?.isDelayReason || isDelayReason
      task.invalid = task?.invalid || invalid
    })
  })
}

// 重置当前激活的里程碑
const resetCurrMilepost = (data: IProcess[]) => {
  currtMilepost.value = (data.find(item => [2, 4].includes(item.status)) || data.at(-1)) as IProcess
}

// 获取流程信息
const getProcessInfo = async (callback = resetCurrMilepost) => {
  spinning.value = true
  const isTree = (layoutView.value == ProcessViewType.table && 1) || 0
  const { data = [] } = await getInstanceInfo({ instanceId: processId, isTree })
  handleProcessInfo(data)
  handleTableTreeKey(data)
  processInfo.value = data
  spinning.value = false
  initAddTaskList()
  nextTick(() => callback(processInfo.value)) // 重置当前激活的里程碑
}

// 获取流程角色信息
const getProcessRoleInfo = async () => {
  spinning.value = true
  try {
    const { data = [] } = await getProcessRoleAndUser(processId)
    processRoleInfo.value = data
  } finally {
    spinning.value = false
  }
}
provide('getProcessInfo', getProcessInfo)
</script>

<style scoped lang="scss"></style>

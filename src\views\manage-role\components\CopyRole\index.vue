<template>
  <FModal
    width="400px"
    wrapClassName="bom-modal-wrap-container"
    v-model:visible="visible"
    :title="handleData?.[handleType]?.title ?? '角色复制'"
    centered
    :confirm-loading="loading"
    @cancel="onCancelFn"
    @ok="onSubmitFn"
  >
    <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <FRow :gutter="[24, 0]">
        <FCol :span="24">
          <FFormItem label="角色编码" name="targetRoleCode">
            <FInput v-model:value="formState.targetRoleCode" placeholder="请输入角色编码" />
          </FFormItem>
        </FCol>
        <FCol :span="24">
          <FFormItem label="角色名称" name="targetRoleName">
            <FInput v-model:value="formState.targetRoleName" placeholder="请输入角色名称" />
          </FFormItem>
        </FCol>
      </FRow>
    </FForm>
    <div></div>
  </FModal>
</template>

<script setup lang="ts">
import { message } from '@fs/smart-design'
import { ref, reactive, computed } from 'vue'
import { copyManageRole } from '@/api'
import { trimObjectStrings } from '@/utils'

const emits = defineEmits(['updateChange'])
const visible = ref<boolean>(false)
const loading = ref<boolean>(false)
const formRef = ref()
const formState = reactive<any>({
  uuids: undefined,
  conditionExpression: undefined,
})
const currentRowRecord = ref<any>()
const handleType = ref('')
const handleData = computed(() => ({
  copyRole: {
    title: '角色复制',
    msg: '角色复制成功！',
    apiUrl: copyManageRole,
    baseParams: {
      sourceRoleConfigId: currentRowRecord?.value?.id,
    },
  },
}))

const rules: Record<string, any[]> = {
  targetRoleCode: [{ required: true, message: '请输入角色编码' }],
  targetRoleName: [{ required: true, message: '请输入角色名称' }],
}

const onCancelFn = () => {
  visible.value = false
}

const onSubmitFn = async () => {
  try {
    loading.value = true
    if (!formRef.value) return
    await formRef.value.validate()
    const params: any = Object.assign({}, formState, handleData?.value?.[handleType?.value]?.baseParams)
    await handleData?.value?.[handleType?.value]?.apiUrl(trimObjectStrings(params))
    message.success(handleData?.value?.[handleType?.value]?.msg ?? '操作成功')
    onCancelFn()
    emits('updateChange')
  } finally {
    loading.value = false
  }
}

const onInitData = async () => {
  Object.entries(formState).forEach(([key, value]) => {
    formState[key] =
      handleData?.value?.[handleType?.value]?.defaultForm?.[key] || (currentRowRecord.value || {})[key] || undefined
  })
}

const onOpenFn = async (typeValue: string, data: any = {}) => {
  visible.value = true
  currentRowRecord.value = data
  handleType.value = typeValue
  await onInitData()
}

defineExpose({ onOpenFn })
</script>

<style scoped lang="scss"></style>

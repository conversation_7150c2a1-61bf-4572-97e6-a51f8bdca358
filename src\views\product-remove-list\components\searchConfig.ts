import { transformDate } from '@/utils'
import { getDepartment } from '@/api'
import { IOptions } from '@/types/pgbDataBoard'
import { ProductListParams } from '@/types/productRemoveList'
import { reactive } from 'vue'
import { i18n } from '@/init'

const handleTree = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      const data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && handleTree(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

const checkChildren = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.uuid && cur.name && Object.keys(cur).length === 2) {
      users.push(cur.uuid)
    } else if (cur.departmentChildrens && cur.departmentChildrens.length) {
      checkChildren(cur.departmentChildrens, users)
    }
    return users
  }, users)
}

const roleLists = await (async () => {
  const res = await getDepartment()
  if (res.code !== 200) throw new Error(res.msg)
  let data: any = []
  data = handleTree(res.data)
  return data
})()

const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0

export class Search {
  options: IOptions;
  [key: string]: any
  constructor(callback: any) {
    this.callback = callback
    this.options = reactive({
      demand: {
        componentName: 'FSelect',
        componentValueKey: 'demand',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('需求场景'),
          options: [
            { label: i18n.t('厂商原因导致的产品下架'), value: '厂商原因导致的产品下架' },
            { label: i18n.t('公司内部规划导致的产品下架'), value: '公司内部规划导致的产品下架' },
          ],
          allowClear: true,
          onChange: (value: any, option: any) => {
            this.callback(this.submit())
          },
        },
      },
      productStatus: {
        componentName: 'FSelect',
        componentValueKey: 'productStatus',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('产品状态'),
          options: [
            { label: i18n.t('上架'), value: '上架' },
            { label: i18n.t('下架'), value: '下架' },
            { label: i18n.t('特殊上架'), value: '特殊上架' },
          ],
          allowClear: true,
          onChange: () => {
            this.callback(this.submit())
          },
        },
      },
      uuidList: {
        componentName: 'FCascader',
        componentValueKey: 'uuidList',
        componentValue: undefined,
        componentArgsValue: {},
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          multiple: true,
          showArrow: true,
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('创建人'),
          fieldNames: { label: 'name', value: 'uuid', children: 'departmentChildrens' },
          maxTagCount: 'responsive',
          options: roleLists,
          showSearch: { filterOption },
          showCheckedStrategy: 'SHOW_CHILD',
          allowClear: true,
          onChange: (value: any, selectedOptions: any) => {
            const selectLists =
              selectedOptions.map((item: any) => {
                return item[item.length - 1]
              }) || []
            Object.assign(this.options.uuidList.componentArgsValue, { selectLists })
            this.callback(this.submit())
          },
        },
        getComponentValueFormat: (value: any, argsValue: any) => {
          const projectUuidList = checkChildren(argsValue.selectLists)
          if (projectUuidList.length) {
            return { uuidList: projectUuidList }
          } else {
            this.options.uuidList.componentValue = undefined
            return undefined
          }
        },
      },
      time: {
        componentName: 'FRangePicker',
        componentValueKey: 'time',
        componentValue: undefined,
        componentLabel: i18n.t('我司预计下架时间'),
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          pressLine: i18n.t('我司预计下架时间'),
          placeholder: [i18n.t('开始日期'), i18n.t('结束日期')],
          onChange: () => {
            this.callback(this.submit())
          },
        },
        getComponentValueFormat: (value: any) => {
          if (!value || value.length !== 2) return undefined
          return {
            startTime: transformDate(value[0], 'YYYY-MM-DD HH:mm:ss'),
            endTime: transformDate(value[1], 'YYYY-MM-DD HH:mm:ss'),
          }
        },
      },
      queryInput: {
        componentName: 'FInput',
        componentValueKey: 'queryInput',
        componentValue: undefined,
        componentLabel: i18n.t('快速搜索'),
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          pressLine: i18n.t('快速搜索'),
          placeholder: i18n.t('流程编码/产品ID/产品型号'),
          onPressEnter: () => {
            this.callback(this.submit())
          },
        },
      },
    })
    this.init()
  }

  init() {
    this.callback(this.submit())
  }

  submit() {
    const params: ProductListParams = {}
    Object.values(this.options).forEach(value => {
      ;(value.componentValue &&
        value.getComponentValueFormat &&
        Object.assign(
          params,
          value.getComponentValueFormat(value.componentValue, value?.componentArgsValue || null)
        )) ||
        (params[value.componentValueKey] = value.componentValue)
    })
    return params
  }
}

<template>
  <div class="json-data-container">
    <div class="item-box" v-for="(item, index) in list" :key="item.index">
      <FInput v-model:value="list[index].fieldKey" class="marginR8" />
      <FSelect
        v-model:value="list[index].fieldValue"
        class="marginR8"
        :options="filedDictionaryList"
        optionFilterProp="label"
        show-search
        allow-clear
      />
      <i
        class="iconfont icontubiao_xietongshanchu hover-btn color999"
        @click="deleteListFn(index)"
        v-if="list.length > (min || 0)"
      ></i>
    </div>
    <span class="cursor color4677C7 in-block" @click="addListFn" v-if="list.length < (max || Infinity)">
      <i class="icon iconfont iconxinzeng fontSize14" />
      新增
    </span>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { selectFieldDictionary } from '@/api'

interface IProps {
  value: string | undefined
  fieldKeyLabel?: string
  fieldValueLabel?: string
  min?: number
  max?: number
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:value'])
const list = ref([])
const filedDictionaryList = ref([])

const emitListFn = () => {
  const jsonObj = {}
  const orderedMap = new Map(list.value.map(item => [item.fieldKey, item.fieldValue]))
  for (const [key, value] of orderedMap) {
    jsonObj[key] = value
  }
  emits(
    'update:value',
    (Object.keys(jsonObj).length &&
      JSON.stringify(jsonObj, (key, value) => {
        return value === undefined ? '' : value
      })) ||
      ''
  )
}

watch(
  () => props.value,
  val => {
    list.value =
      (props?.value &&
        Object.entries(JSON.parse(props.value)).map(([key, value]) => ({ fieldKey: key, fieldValue: value }))) ||
      new Array(props?.min ?? 0).map(() => ({ fieldKey: '', fieldValue: '' }))
    emitListFn()
  },
  { deep: true, immediate: true }
)

watch(
  () => list,
  val => {
    emitListFn()
  },
  { deep: true }
)

const addListFn = () => {
  list.value.push({ fieldKey: '', fieldValue: '' })
}

const deleteListFn = index => {
  list.value.splice(index, 1)
}

const getFiledDictionaryListFn = async () => {
  const res = await selectFieldDictionary({ input: '' })
  filedDictionaryList.value = (res?.data ?? []).map(item => ({ label: item?.title, value: item?.field }))
}

onMounted(() => {
  requestIdleCallback(getFiledDictionaryListFn)
})
</script>

<style lang="scss" scoped>
.item-box {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  padding: 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
  &:hover {
    background: #f8f8f8;
  }
  .hover-btn {
    cursor: pointer;
    &:hover {
      color: #378eef;
    }
  }
}
</style>

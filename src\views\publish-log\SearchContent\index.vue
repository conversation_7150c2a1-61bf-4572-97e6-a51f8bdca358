<template>
  <div style="display: flex; flex-wrap: wrap">
    <template v-for="item of search.options" :key="item.componentValueKey">
      <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, nextTick, ref } from 'vue'
import { useStore } from 'vuex'
import { Search } from '@/views/message-template/components/SearchContent/search'
import { IProcessClassType } from '@/types/handle'
import { GetTagAndNode } from '@/api'
interface IProps {
  queryData: any
}
const props = defineProps<IProps>()
const emits = defineEmits(['update:queryData'])
const store = useStore()
const allUserList = computed(() => store.state.user.allUser || [])
const search = new Search()
const queryData = computed({
  get: () => props.queryData,
  set: val => emits('update:queryData', val),
})
const processTypeData = ref<IProcessClassType[]>([])

const typeOptions = ref<any>([
  { label: '飞书卡片', value: 1 },
  { label: '字典', value: 2 },
  { label: '触发器', value: 3 },
  { label: '角色', value: 4 },
  { label: '同步流程配置', value: 5 },
])
const syncEnvList = ref([
  { label: '生产', value: 1 },
  { label: '预发布', value: 2 },
  { label: '测试', value: 3 },
  { label: '中文站', value: 4 },
])
const getProcessTypes = async () => {
  try {
    const res = await GetTagAndNode()
    processTypeData.value = res.data
  } catch (error) {
    throw new Error('流程类型请求失败')
  }
}
const onChange = () =>
  (queryData.value = {
    ...search.getParams(),
    ...{
      cacheValue: search.getCacheSearch(),
    },
  })

const configList = [
  {
    componentName: 'FSelect',
    componentValueKey: 'syncType',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '配置类型'),
      placeholder: computed(() => '请选择'),
      allowClear: true,
      options: computed(() => typeOptions.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('syncType.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'syncEnv',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '同步环境'),
      placeholder: computed(() => '请选择'),
      allowClear: true,
      options: computed(() => syncEnvList.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('syncEnv.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'processConfigId',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '流程类型'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      fieldNames: { value: 'id', label: 'processName' },
      options: computed(() => processTypeData.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('processConfigId.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'createdUserId',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '发布人'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: allUserList,
      fieldNames: { value: 'uuid', label: 'feiShuName' },
      optionFilterProp: 'feiShuName',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('createdUserId.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FRangePicker',
    componentValueKey: 'time',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '发布时间'),
      valueFormat: 'YYYY-MM-DD',
      onChange,
    },
    getComponentValueFormat: (value: any) => {
      if (!value || value.length !== 2) return undefined
      return {
        startTime: value[0] + ' 00:00:00',
        endTime: value[1] + ' 23:59:59',
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions(
        'time.componentValue',
        ((data || {})?.componentValue && [data.componentValue[0], data.componentValue[1]]) || undefined
      )
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'keyword',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '快速搜索'),
      placeholder: computed(() => '配置ID/编码/配置名称'),
      allowClear: true,
      type: 'search-clear',
      onSearch: onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('keyword.componentValue', (data || {})?.componentValue || undefined)
    },
  },
]

const setSearchConfigFn = () => {
  search.initOptions(configList)
  search.clear()
  nextTick(() => {
    queryData.value = {
      ...search.getParams(),
    }
  })
}

onMounted(() => {
  setSearchConfigFn()
  getProcessTypes()
})
</script>

<style lang="scss" scoped>
:deep(.fs-input-affix-wrapper) {
  padding: 6px 8px !important;
}
</style>

<template>
  <div class="milepost-footer" v-if="props.milepost.status == 2">
    <FConfigProvider :auto-insert-space-in-button="false">
      <template v-if="props.milepost.milepostRole == 1">
        <!-- <FPopconfirm class="mr12" title="是否确认提交？" @confirm="emitOperate(EmitType.submit, props.milepost)">
          <FButton type="primary"> 提交 </FButton>
        </FPopconfirm> -->
        <FButton class="mr12 inline" type="primary" @click="emitOperate(EmitType.submit, props.milepost)">{{
          i18n.t('提交')
        }}</FButton>
        <FButton class="mr12 inline" @click="emitOperate(EmitType.reject, props.milepost)">{{
          i18n.t('驳回')
        }}</FButton>
        <FButton class="mr12 inline" @click="emitOperate(EmitType.finish, props.milepost)">{{
          i18n.t('办结')
        }}</FButton>
        <FButton class="mr12 inline" @click="emitOperate(EmitType.dispatch, props.milepost)">
          {{ i18n.t('转派') }}
        </FButton>
      </template>

      <FButton v-if="props.milepost.isRecall" class="mr12 inline" @click="emitOperate(EmitType.revoke, props.milepost)">
        {{ i18n.t('撤回') }}
      </FButton>
      <!-- 暂时性全开 -->
      <FButton class="mr12 inline" @click="emitOperate(EmitType.createProcess, props.milepost)">{{
        i18n.t('创建关联流程')
      }}</FButton>
    </FConfigProvider>
  </div>
</template>
<script setup lang="ts">
import { inject } from 'vue'
import { EmitType } from '@/views/process-detail/config'
import { useI18n } from '@/utils'
import type { IProcess } from '@/types/handle'

interface IProps {
  milepost: IProcess
}

const i18n = useI18n()
const props = defineProps<IProps>()
const handleOperate = inject('handleOperate') as (value: { type: EmitType; data: unknown }) => void
const emitOperate = (type: EmitType, data: unknown) => handleOperate({ type, data })
</script>
<style lang="scss" scoped>
.mr12 {
  margin-right: 12px;
}
.inline {
  display: inline-block !important;
}
.milepost-footer {
  margin-top: 20px;
}
</style>

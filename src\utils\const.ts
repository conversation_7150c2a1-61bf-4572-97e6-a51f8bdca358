export const isDev = process.env.NODE_ENV === 'development'
export const isProd = process.env.NODE_ENV === 'production'

export const GOTO_URL = process.env.VUE_APP_GOTO_URL
export const TRANSLATE_URL = process.env.VUE_APP_TRANSLATE_API_URL
export const i18nSign = process.env.VUE_APP_I18N_SIGN
export const POWER_ENV = process.env.VUE_APP_POWER_ENV

export const FEISHU_APPID = 'cli_a46451e6ff3c500b'
export const TICKET = 'ticket'
export const isFeishu = !!window.h5sdk
export const USER_INFO = 'userInfo'
export const USER_TOKEN = 'token'
export const HISTORY_RECORD = 'historyRecord'

export const BAESURL = process.env.VUE_APP_API_URL
export const S3URL = process.env.VUE_APP_S3_URL
export const BASESTATEURL = process.env.VUE_APP_STATE_URL
export const ERPURL = process.env.VUE_APP_ERP_URL
export const GOTO_WHITE = new Function(`return ${process.env.VUE_APP_GOTO_WHITE}`)()
export const CRMURL = process.env.VUE_APP_CRM_URL
export const MOMURL = process.env.VUE_APP_MOM_URL
export const APP_ENV = process.env.VUE_APP_ENV
export const BPM_MESSAGE_EDITOR = 'BPM_MESSAGE_EDITOR'
export const SUPERSET_URL = process.env.VUE_APP_SUPERSET_URL
export const WORKFLOW_URL = process.env.VUE_APP_WORKFLOW_URL
export const WORKFLOW_API_URL = process.env.VUE_APP_WORKFLOW_API_URL
export const NEW_IPD_CONFIG_ID = process.env.VUE_APP_PROCESS_NEW_IPD_CONFIG_ID
export const IR_CONFIG_ID = process.env.VUE_APP_PROCESS_IR_CONFIG_ID
export const SR_CONFIG_ID = process.env.VUE_APP_PROCESS_SR_CONFIG_ID
export const AR_CONFIG_ID = process.env.VUE_APP_PROCESS_AR_CONFIG_ID
export const MM_CONFIG_ID = process.env.VUE_APP_PROCESS_MM_CONFIG_ID
export const STAFFMEETING_CONFIG_ID = process.env.VUE_APP_PROCESS_STAFFMEETING_CONFIG_ID
export const PROCESS_PUBLISH_FLAG = new Function(`return ${process.env.VUE_APP_PROCESS_PUBLISH_FLAG}`)()
export const PROCESS_PUBLISH_RESET_FLAG = new Function(`return ${process.env.VUE_APP_PROCESS_PUBLISH_RESET_FLAG}`)()
export const PROCESS_PUBLISH_ENV_OPTIONS = new Function(`return ${process.env.VUE_APP_PROCESS_PUBLISH_ENV_OPTIONS}`)()

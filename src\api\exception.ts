import { request, fileRequest } from '@/utils'

export const getAbnormalOngoingProcessList = (data: any) => {
  return request.post('/api/hrAdminBoard/getAbnormalOngoingProcessList', data)
}

export function exportExceptionalProcesses(data: any): Promise<any> {
  return fileRequest.post('/api/hrAdminBoard/exportExceptionalProcesses', data, { responseType: 'blob' })
}

export function urgeExceptionalProcesses(data: any): Promise<any> {
  return fileRequest.post('/api/hrAdminBoard/urgeExceptionalProcesses', data, { responseType: 'blob' })
}

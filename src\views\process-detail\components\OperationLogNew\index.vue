<template>
  <div class="operation-container">
    <OperationLogs :instance-id="instanceId" />
  </div>
</template>

<script lang="ts" setup>
import OperationLogs from './components/OperationLogs.vue'
import { computed } from 'vue'
import { IProcess } from '@/types/handle'

interface IProps {
  processInfo: IProcess[]
}

const props = defineProps<IProps>()

const instanceId = computed(() => (props.processInfo[0] || {})?.instanceId) // 需求信息
</script>

<style lang="scss" scoped>
.operation-container {
  padding: 24px 6px 24px 24px;
}
</style>

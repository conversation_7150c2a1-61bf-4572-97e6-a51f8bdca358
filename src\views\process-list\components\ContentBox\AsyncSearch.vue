<template>
  <!-- <div class="async-search-container"> -->
  <template v-for="item of AsyncSearchConfigList" :key="item.key">
    <div class="marginB10 marginR12" v-if="item.componentType === 'ONE_LINE_INPUT'">
      <FInput
        class="width120"
        v-bind="item.attrs"
        v-model:value="AsyncSearchData[item.key]"
        :press-line="item.label"
        @press-enter="onAsyncSearch"
      >
        <template #suffix>
          <i class="cursor iconfont colord8d8d8 fontSize12" @click="onAsyncSearch">&#xe70e;</i>
        </template>
      </FInput>
    </div>

    <div class="marginB10 marginR12" v-if="['MULTIPLE_SELECT', 'SELECT'].includes(item.componentType)">
      <FSelect
        class="width120 height32"
        v-bind="item.attrs"
        :dropdown-match-select-width="true"
        v-model:value="AsyncSearchData[item.key]"
        :press-line="item.label"
        :options="item.options"
        allow-clear
        @change="onAsyncSearch"
        :get-popup-container="(triggerNode: Element) => { return triggerNode?.parentNode || null}"
      >
      </FSelect>
    </div>

    <div class="marginB10 marginR12" v-if="item.componentType === 'TREE_SELECT'">
      <FTreeSelect
        class="width120 height32"
        v-bind="item.attrs"
        v-model:value="AsyncSearchData[item.key]"
        :tree-data="item.options"
        :press-line="item.label"
        tree-node-filter-prop="label"
        show-search
        allow-clear
        @change="onAsyncSearch"
        :get-popup-container="(triggerNode: Element) => { return triggerNode?.parentNode || null}"
      >
      </FTreeSelect>
    </div>

    <div
      tabindex="-1"
      ref="rangeNumberRef"
      class="cust-range-number height32 marginR12 marginB10"
      :class="[focusStatus ? 'cust-input-number-focused' : '']"
      v-if="item.componentType === 'RANGE_NUMBER'"
    >
      <FInputNumber
        :bordered="false"
        :press-line="item.label"
        v-model:value="AsyncSearchData[item.key][0]"
        @blur="inputNumberChange($event, item)"
        @focus="
          () => {
            focusStatus = true
          }
        "
      />
      <i>-</i>
      <FInputNumber
        :bordered="false"
        :press-line="item.label"
        v-model:value="AsyncSearchData[item.key][1]"
        @blur="inputNumberChange($event, item)"
        @focus="
          () => {
            focusStatus = true
          }
        "
      />
      <i class="cust-number-clear cursor iconfont colord8d8d8 fontSize12" @click="onAsyncSearch">&#xe70e;</i>
    </div>

    <div class="marginR12 marginB10" v-if="item.componentType === 'RANGE_PICKER'">
      <FRangePicker
        class="width251 height32"
        v-model:value="AsyncSearchData[item.key]"
        :format="item.format"
        :press-line="item.label"
        allow-clear
        @change="onAsyncSearch"
        :get-popup-container="(triggerNode: Element) => { return triggerNode?.parentNode || null}"
      />
    </div>

    <div class="marginR12 marginB10" v-if="item.componentType === 'DATE_PICKER'">
      <FDatePicker
        class="width251 height32"
        v-model:value="AsyncSearchData[item.key]"
        :format="item.format"
        :press-line="item.label"
        allow-clear
        @change="onAsyncSearch"
        :get-popup-container="(triggerNode: Element) => { return triggerNode?.parentNode || null}"
      />
    </div>
  </template>
  <!-- </div> -->
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { transformDate } from '@/utils'
import { ISearchConfig } from '@/types/request'
import { getSearchConfigOptions } from '@/api/process'
import { FInputNumber } from '@fs/smart-design'
import { messageInstance as message } from '@fs/smart-design'
import dayjs from 'dayjs'

type IpForm = {
  [key: string]: any
}

type IOption = {
  value: string
  id: string
}

type ConfigList = {
  type: string
  key: string
  componentType: string
  placeholder?: string | null
  label: string
  format?: string
  options?: IOption[]
  Defaultvalue: any
  attrs: any
  formatValue?: any
  valueType: any
  [key: string]: any
}

type ConfigData = {
  [key: string]: ConfigList
}

type IDefaultValueItem = {
  value?: any
  placeholder?: string
  hasOption?: boolean
  format?: string
  attrs?: unknown
  width?: string
  formatValue?: any
  defaultValueFormat?: any
}

type propsType = {
  searchConfigList: ISearchConfig[]
  defaultSearch: IpForm
  isFold?: number
}

const props = withDefaults(defineProps<propsType>(), {
  searchConfigList: () => [],
  defaultSearch: () => ({}),
  isFold: 1,
})

const rangeNumberFormatValue = (data: any, value: any) => {
  if (!value || value.length !== 2 || value[0] > value[1]) return {}
  return {
    [`gt:${data.type}:${data.valueType}`]: (value?.[0] && value[0]) || undefined,
    [`lt:${data.type}:${data.valueType}`]: (value?.[1] && value[1]) || undefined,
  }
}

const defaultRangeNumberFormatValue = (data: any, info: any) => {
  if (
    !data[`gt:${info.field}:${info.valueType}`] ||
    !data[`lt:${info.field}:${info.valueType}`] ||
    data[`gt:${info.field}:${info.valueType}`] > data[`lt:${info.field}:${info.valueType}`]
  )
    return []
  return [Number(data[`gt:${info.field}:${info.valueType}`]), Number(data[`lt:${info.field}:${info.valueType}`])]
}

const rangePickerFormatValue = (data: any, value: any) => {
  if (!value || value.length !== 2) return {}
  return {
    [`gt:${data.type}:${data.valueType}`]: value?.[0] && transformDate(value[0], 'YYYY-MM-DD'),
    [`lt:${data.type}:${data.valueType}`]: value?.[1] && transformDate(value[1], 'YYYY-MM-DD'),
  }
}

const defaultRangePickerFormatValue = (data: any, info: any) => {
  if (!data[`gt:${info.field}:${info.valueType}`] || !data[`lt:${info.field}:${info.valueType}`]) return undefined
  return [dayjs(data[`gt:${info.field}:${info.valueType}`]), dayjs(data[`lt:${info.field}:${info.valueType}`])]
}

const datePickerFormatValue = (data: any, value: any) => {
  if (!value) return undefined
  return {
    [data.key]: transformDate(value, 'YYYY-MM-DD'),
  }
}

const defaultDatePickerFormatValue = (data: any, info: any) => {
  if (!data[`${info.searchType}:${info.field}:${info.valueType}`]) return undefined
  return dayjs(data[`${info.searchType}:${info.field}:${info.valueType}`])
}

const defaultValueList = {
  ONE_LINE_INPUT: {
    value: undefined,
    attrs: {
      placeholder: '请输入',
    },
  },
  RANGE_NUMBER: {
    value: [],
    attrs: {
      placeholder: '请输入',
    },
    defaultValueFormat: defaultRangeNumberFormatValue,
    formatValue: rangeNumberFormatValue,
  },
  SELECT: {
    value: undefined,
    attrs: {
      placeholder: '请选择',
    },
    hasOption: true,
  },
  MULTIPLE_SELECT: {
    value: undefined,
    hasOption: true,
    attrs: {
      placeholder: '请选择',
      mode: 'multiple',
      maxTagCount: 'responsive',
      class: 'cust-multiple-select',
    },
  },
  TREE_SELECT: {
    value: undefined,
    attrs: {
      placeholder: '请选择',
    },
    hasOption: true,
  },
  RANGE_PICKER: {
    value: undefined,
    attrs: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择时间',
    },
    defaultValueFormat: defaultRangePickerFormatValue,
    formatValue: rangePickerFormatValue,
  },
  DATE_PICKER: {
    value: undefined,
    attrs: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择时间',
    },
    defaultValueFormat: defaultDatePickerFormatValue,
    formatValue: datePickerFormatValue,
  },
}

const emit = defineEmits(['on-async-search'])
const AsyncSearchData = ref<IpForm>({})
const AsyncSearchConfigList = ref<ConfigData>({})
const rangeNumberRef = ref<HTMLElement[] | null>(null)
const focusStatus = ref<boolean>(false)

const getOptions = async (data: any, index: string) => {
  if (!data.url) {
    data.valueList && data.valueList.length && (AsyncSearchConfigList.value[index].options = data.valueList)
  } else {
    const res = await getSearchConfigOptions(data.url)
    if (res.code === 200) {
      AsyncSearchConfigList.value[index].options = res.data
    }
  }
}

const inputNumberChange = (value: any, item: any) => {
  focusStatus.value = false
  if (
    AsyncSearchData.value[item.key] &&
    AsyncSearchData.value[item.key].length === 2 &&
    AsyncSearchData.value[item.key][0] > AsyncSearchData.value[item.key][1]
  ) {
    message.warning(`${item.label}输入不正确，请重新输入！`)
  }
}

const onAsyncSearch = () => {
  const asyncForm: any = {}
  Object.entries(AsyncSearchConfigList.value).forEach(([key, value]) => {
    if (AsyncSearchData.value[key]) {
      if (value.formatValue) {
        Object.assign(asyncForm, value.formatValue(value, AsyncSearchData.value[key]))
      } else {
        asyncForm[key] = AsyncSearchData.value[key]
      }
    }
  })
  emit('on-async-search', asyncForm)
}

watch(
  () => props.searchConfigList,
  config => {
    let defaultValue: IDefaultValueItem = {},
      key: string,
      configDataItem: ConfigList

    AsyncSearchData.value = {}
    AsyncSearchConfigList.value = {}

    props.searchConfigList.forEach(item => {
      if (!item?.status || props?.isFold !== item?.fold) return

      key = `${item.searchType}:${item.field}:${item.valueType}`
      defaultValue = defaultValueList[item.moduleType as keyof typeof defaultValueList]
      let value = defaultValue.value
      if (
        props.defaultSearch[key] ||
        (props.defaultSearch[`gt:${item.field}:${item.valueType}`] &&
          props.defaultSearch[`lt:${item.field}:${item.valueType}`])
      ) {
        value =
          (defaultValue.defaultValueFormat && defaultValue.defaultValueFormat(props.defaultSearch, item)) ||
          props.defaultSearch[key]
      }
      AsyncSearchData.value[key] = value
      configDataItem = {
        type: item.field,
        valueType: item.valueType,
        key,
        componentType: item.moduleType,
        label: item.name,
        Defaultvalue: defaultValue.value,
        attrs: Object.assign({}, defaultValue.attrs, JSON.parse(item.propsConfig || JSON.stringify({}))),
        formatValue: defaultValue.formatValue || undefined,
      }
      AsyncSearchConfigList.value[key] = configDataItem
      if (defaultValue.hasOption) {
        AsyncSearchConfigList.value[key].options = []
        getOptions(item, key)
      }
    })
  },
  { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
.btn_div_relative {
  display: inline-block;
  position: relative;
  height: 34px;
  line-height: 1;
  :deep(.btn_span_absolute) {
    display: inline-block;
    position: absolute;
    top: -7px;
    left: 4px;
    z-index: 10;
    padding: 0 3px;
    color: #999;
    background: #fff;
    font-size: 11px;
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }
  .btn_span_absolute_left {
    left: 7px;
  }
}
:deep(.fs-select-tree-treenode) {
  height: auto;
}
.marginL12 {
  margin-left: 12px;
}
.marginB10 {
  margin-bottom: 10px;
}
:deep(.width120) {
  width: 120px;
}
:deep(.width251) {
  width: 251px;
}
.height32 {
  height: 32px;
}
// :deep(.fs-select) {
//   width: 100%;
// }
:deep(.cust-multiple-select .fs-select-selection-item) {
  line-height: 24px !important;
}
:deep(.fs-picker) {
  padding: 4px 8px 4px;
}
:deep(.fs-picker-suffix) {
  width: 16px;
  height: 16px;
}
:deep(.fs-picker-input-active) {
  background-color: #fff;
}
.cust-range-number {
  display: flex;
  align-items: center;
  width: 251px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 12px;
  color: #333;
  padding: 0 8px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  :deep(.btn_div_relative) {
    flex: 1;
  }
  &.cust-input-number-focused {
    border-color: #378eef;
    box-shadow: 0 0 0 2px #afd1f8;
    border-right-width: 1px !important;
    outline: 0;
  }
  &:hover {
    border: 1px solid #5fa4f2;
  }
  &:focus {
    border-color: #378eef;
    box-shadow: 0 0 0 2px #afd1f8;
    border-right-width: 1px !important;
    outline: 0;
  }
  :deep(.fs-input-number) {
    height: 30px;
    .fs-input-number-handler-wrap {
      display: none;
    }
  }
}
// }
</style>

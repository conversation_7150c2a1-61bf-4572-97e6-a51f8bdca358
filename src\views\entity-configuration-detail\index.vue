<template>
  <div class="entity-configuration-detail-container">
    <FSpin :spinning="pageLoading">
      <FCard :title="i18n.t('基本信息')" style="margin-bottom: 16px">
        <BaseInfo ref="baseInfoRef" :detailInfo="detailInfo" />
      </FCard>
      <FCard :title="i18n.t('字段详情')" style="margin-bottom: 64px" v-if="!!id">
        <FieldDetail :id="id" />
      </FCard>
      <div class="submit">
        <FButton type="primary" :loading="submitLoading" style="margin-right: 16px" @click="submitFn">{{
          i18n.t('确定')
        }}</FButton>
        <FButton @click="closeFn">{{ i18n.t('取消') }}</FButton>
      </div>
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseInfo from './components/BaseInfo/index.vue'
import FieldDetail from './components/FieldDetail/index.vue'
import { message } from '@fs/smart-design'
import { useI18n } from '@/utils'
import { useRouter, useRoute } from 'vue-router'
import { saveEntityData, getEntityById, updateEntityData } from '@/api'
import { IEntityPageListData } from '@/types/entity'

const i18n = useI18n()
const router = useRouter()
const route = useRoute()
const baseInfoRef = ref<any>()
const id = ref<number>(route.query.id as unknown as number)
const submitLoading = ref<boolean>(false)
const pageLoading = ref<boolean>(false)
const detailInfo = ref<IEntityPageListData>()

const submitFn = async () => {
  try {
    submitLoading.value = true
    if (!baseInfoRef.value) return
    const baseInfo = await baseInfoRef.value.submitFn()
    let res, msg
    if (id.value) {
      res = await updateEntityData({ ...baseInfo, ...{ id: Number(id.value) } })
      msg = i18n.t('编辑成功')
    } else {
      res = await saveEntityData(baseInfo)
      msg = i18n.t('新增成功')
    }
    if (res.code !== 200) throw new Error(res.msg)
    if (!id.value && res.data) {
      router.push({
        name: route.name as string,
        query: { id: res.data },
      })
      id.value = res.data
    }
    message.success(msg)
  } finally {
    submitLoading.value = false
  }
}

const closeFn = () => {
  router.back()
}

const getDetailById = async () => {
  try {
    pageLoading.value = true
    if (!id.value) return
    const res = await getEntityById(id.value)
    if (res.code !== 200) throw new Error(res.msg)
    detailInfo.value = res.data
  } finally {
    pageLoading.value = false
  }
}

onMounted(() => {
  getDetailById()
})
</script>

<style scoped lang="scss">
.entity-configuration-detail-container {
  margin: 0 auto;
  max-width: 1200px;
  margin-bottom: 64px;
  .submit {
    display: flex;
    align-items: center;
    height: 64px;
    margin: 0 auto;
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    z-index: 999;
    &::before {
      content: '';
      position: absolute;
      width: 100%;
      margin-left: -100%;
      height: 100%;
      background-color: #fff;
    }
  }
}
</style>

import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { VNode, h, computed } from 'vue'
import { i18n } from '@/init'

export const columns = computed<TableColumnsType>(() => [
  {
    title: i18n.t('任务负责人'),
    dataIndex: 'name',
    width: 152,
  },
  {
    title: i18n.t('参与项目总数'),
    dataIndex: 'projectTotal',
    width: 116,
  },
  {
    title: i18n.t('参与项目上线率'),
    dataIndex: 'onlineRate',
    customRender: ({ text, value, record }): VNode => {
      return h('span', text + '%')
    },
    width: 140,
  },
  {
    title: i18n.t('任务完成率'),
    dataIndex: 'completionRate',
    customRender: ({ text, value, record }): VNode => {
      return h('span', text + '%')
    },
    width: 140,
  },
  {
    title: i18n.t('任务提前完成率'),
    dataIndex: 'earlyCompletionRate',
    customRender: ({ text, value, record }): VNode => {
      return h('span', text + '%')
    },
    width: 146,
  },
  {
    title: i18n.t('任务按时完成率'),
    dataIndex: 'onTimeCompletionRate',
    customRender: ({ text, value, record }): VNode => {
      return h('span', text + '%')
    },
    width: 160,
  },
  {
    title: i18n.t('任务逾期完成率'),
    dataIndex: 'overdueRate',
    customRender: ({ text, value, record }): VNode => {
      return h('span', text + '%')
    },
    width: 146,
  },
  {
    title: i18n.t('按时完成'),
    dataIndex: 'onTimeCompletion',
    width: 120,
  },
  {
    title: i18n.t('提前完成量'),
    dataIndex: 'earlyCompletion',
    width: 120,
  },
  {
    title: i18n.t('逾期完成'),
    dataIndex: 'overdue',
    width: 120,
  },
  {
    title: i18n.t('进行中'),
    dataIndex: 'inProgress',
    width: 120,
  },
  {
    title: i18n.t('总任务数量'),
    dataIndex: 'total',
    width: 120,
  },
])

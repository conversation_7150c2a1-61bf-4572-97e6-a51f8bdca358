<template>
  <div>
    <template v-for="item of search.options" :key="item.componentValueKey">
      <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, nextTick, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { Search } from '@/views/message-template/components/SearchContent/search'
import { cache } from '@/utils'
import { getIpdDictionary } from '@/api'

interface IProps {
  queryData: any
}

const { currentRoute } = useRouter()
const props = defineProps<IProps>()
const emits = defineEmits(['update:queryData'])
const store = useStore()
const allUserList = computed(() => store.state.user.allUser || [])
const search = new Search()
const queryData = computed({
  get: () => props.queryData,
  set: val => emits('update:queryData', val),
})
const routerName = computed<any>(() => currentRoute.value?.name)
const dictionary = ref<any>([])
const typeOptions = ref<any>([])
const pbuOptions = ref<any>([])

const onChange = () =>
  (queryData.value = {
    ...search.getParams(),
    ...{ cacheValue: search.getCacheSearch() },
  })

const configList = [
  {
    componentName: 'FSelect',
    componentValueKey: 'type',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '产品线'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: computed(() => typeOptions.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('type.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'pbu',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => 'PBU'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: computed(() => pbuOptions.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('pbu.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'pdOwner',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '一条龙经理'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: allUserList,
      fieldNames: { value: 'uuid', label: 'feiShuName' },
      optionFilterProp: 'feiShuName',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('pdOwner.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'creator',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '录入人'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: allUserList,
      fieldNames: { value: 'uuid', label: 'feiShuName' },
      optionFilterProp: 'feiShuName',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('creator.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FRangePicker',
    componentValueKey: 'time',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '录入时间'),
      valueFormat: 'YYYY-MM-DD',
      onChange,
    },
    getComponentValueFormat: (value: any) => {
      if (!value || value.length !== 2) return undefined
      return {
        startTime: value[0] + ' 00:00:00',
        endTime: value[1] + ' 23:59:59',
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions(
        'time.componentValue',
        ((data || {})?.componentValue && [data.componentValue[0], data.componentValue[1]]) || undefined
      )
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'queryInput',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '快速检索'),
      placeholder: computed(() => '流程编号/流程标题'),
      allowClear: true,
      type: 'search-clear',
      onSearch: onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('queryInput.componentValue', (data || {})?.componentValue || undefined)
    },
  },
]

const setSearchConfigFn = () => {
  search.initOptions(configList)
  search.clear()
  const cachData = (cache.get(routerName?.value) && JSON.parse(cache.get(routerName?.value) as string)) || {}
  search.setDefaultSearch(cachData)
  nextTick(() => {
    queryData.value = {
      ...search.getParams(),
      ...{ cacheValue: cachData },
    }
  })
}

const getIpdDictionaryFn = async () => {
  const res = await getIpdDictionary()
  dictionary.value = res.data
  typeOptions.value = (dictionary.value ?? []).filter(item => item.field === 'type')
  pbuOptions.value = (dictionary.value ?? []).filter(item => item.field === 'pbu')
}

onMounted(() => {
  setSearchConfigFn()
  getIpdDictionaryFn()
})
</script>

<style lang="scss" scoped>
:deep(.fs-input-affix-wrapper) {
  padding: 6px 8px !important;
}
</style>

<template>
  <FModal :title="i18n.t('拷贝配置')" v-model:visible="visible" @ok="handleSubmit" @cancel="handleClose">
    <div>
      <FForm ref="formRef" :model="formData" :rules="formRules" layout="vertical">
        <FFormItem :label="i18n.t('来源里程碑')" name="sourceMilepostNodeId">
          <FSelect
            v-model:value="formData.sourceMilepostNodeId"
            :options="milepostOptions"
            :placeholder="i18n.t('请选择')"
          />
        </FFormItem>
        <FFormItem :label="i18n.t('目标里程碑')" name="targetMilepostNodeIds">
          <FSelect
            v-model:value="formData.targetMilepostNodeIds"
            :options="milepostOptions"
            mode="multiple"
            :placeholder="i18n.t('请选择')"
          />
        </FFormItem>
      </FForm>
    </div>
    <template #footer>
      <FConfigProvider :auto-insert-space-in-button="false">
        <FButton key="back" @click="visible = false">{{ i18n.t('取消') }}</FButton>
        <FButton key="submit" type="primary" @click="handleSubmit">{{ i18n.t('确定') }}</FButton>
      </FConfigProvider>
    </template>
  </FModal>
</template>

<script setup lang="ts">
import { ProcessNodeItem } from '@/types/processLabelModel'
import { computed, reactive, ref } from 'vue'
import { useI18n } from '@/utils'

const i18n = useI18n()

interface IProps {
  modelValue: boolean
  milepostData: ProcessNodeItem[]
}

interface IFormData {
  sourceMilepostNodeId: number
  targetMilepostNodeIds: number[]
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:modelValue', 'submit'])
const formRef = ref()
const formData = reactive<IFormData>({} as IFormData)
const formRules = reactive({
  sourceMilepostNodeId: [{ required: true, message: i18n.t('请选择来源里程碑'), trigger: 'change' }],
  targetMilepostNodeIds: [{ required: true, message: i18n.t('请选择目标里程碑'), trigger: 'change' }],
})
const milepostOptions = computed(() =>
  props.milepostData?.map(item => ({
    label: item.milepostName,
    value: item.id,
  }))
)
const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emits('update:modelValue', val),
})

// 提交
const handleSubmit = async () => {
  const $form = formRef.value
  const valid = await $form.validate()
  if (!valid) return
  emits('submit', { ...formData })
}

// 关闭
const handleClose = () => {
  visible.value = false
  const $form = formRef.value
  $form.resetFields()
}
</script>

<style scoped lang="scss">
:deep(.fs-modal-content) {
  .fs-modal-footer .fs-btn {
    width: auto !important;
    padding: 0 24px !important;
  }
}
</style>

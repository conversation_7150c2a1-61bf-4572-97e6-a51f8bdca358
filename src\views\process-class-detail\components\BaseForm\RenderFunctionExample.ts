import { h } from 'vue'
import { tip, createTipRenderer, type RenderStrategy, type RenderContext } from './renderUtils'
import ProcessTip from '../CustomTipComponents/ProcessTip.vue'
import DynamicTip from '../CustomTipComponents/DynamicTip.vue'

// 自定义渲染策略示例
const customStrategies: RenderStrategy[] = [
  // 富文本策略
  {
    test: (tipText: any) => 
      tipText && 
      typeof tipText === 'object' && 
      tipText.type === 'rich',
    render: (tipText: any, context: RenderContext) => {
      return h('div', {
        style: 'width: max-content; padding: 8px; background: #f5f5f5; border-radius: 4px;'
      }, [
        h('div', { 
          style: 'font-weight: bold; margin-bottom: 4px; color: #333;' 
        }, tipText.title),
        h('div', { 
          style: 'color: #666; font-size: 12px;',
          innerHTML: tipText.content 
        })
      ])
    }
  },

  // 列表策略
  {
    test: (tipText: any) => 
      tipText && 
      typeof tipText === 'object' && 
      tipText.type === 'list',
    render: (tipText: any, context: RenderContext) => {
      return h('div', {
        style: 'width: max-content; max-width: 300px;'
      }, [
        h('div', { style: 'font-weight: bold; margin-bottom: 4px;' }, tipText.title || '提示'),
        h('ul', { style: 'margin: 0; padding-left: 16px; font-size: 12px;' }, 
          tipText.items.map((item: string) => h('li', { key: item }, item))
        )
      ])
    }
  },

  // 状态策略
  {
    test: (tipText: any) => 
      tipText && 
      typeof tipText === 'object' && 
      tipText.type === 'status',
    render: (tipText: any, context: RenderContext) => {
      const statusColors = {
        success: '#52c41a',
        warning: '#faad14',
        error: '#ff4d4f',
        info: '#1890ff'
      }
      
      const color = statusColors[tipText.status as keyof typeof statusColors] || '#666'
      
      return h('div', {
        style: `width: max-content; display: flex; align-items: center; color: ${color};`
      }, [
        h('i', { 
          class: `iconfont icon-${tipText.status}`,
          style: 'margin-right: 4px; font-size: 14px;'
        }),
        h('span', { style: 'font-size: 12px;' }, tipText.message)
      ])
    }
  }
]

// 创建增强的渲染器
const enhancedRenderer = createTipRenderer(customStrategies)

// 使用示例配置
export const renderFunctionExamples = {
  // 1. 基础字符串（最简单）
  basicText: {
    component: 'CustomInput',
    fieldKey: 'basicText',
    label: '基础文本',
    tipText: '这是一个基础的文本提示'
  },

  // 2. 动态函数（智能响应）
  dynamicText: {
    component: 'CustomInput',
    fieldKey: 'dynamicText',
    label: '动态文本',
    tipText: tip()
      .dynamic((fieldValue, formData) => {
        if (!fieldValue) return '请输入内容'
        return `当前输入：${fieldValue}（长度：${fieldValue.length}）`
      })
      .build()
  },

  // 3. 条件渲染（智能判断）
  conditionalTip: {
    component: 'CustomSelect',
    fieldKey: 'processType',
    label: '流程类型',
    tipText: tip()
      .conditional(
        (fieldValue) => !!fieldValue,
        tip().dynamic((fieldValue) => `已选择：${fieldValue}`).build(),
        '请选择流程类型'
      )
      .build()
  },

  // 4. 自定义组件（复杂逻辑）
  customComponent: {
    component: 'CustomInput',
    fieldKey: 'customComponent',
    label: '自定义组件',
    tipText: tip()
      .component(ProcessTip, {
        baseText: '这是自定义组件提示',
        showValue: true,
        valuePrefix: '当前值：'
      })
      .build()
  },

  // 5. 富文本提示（新功能）
  richText: {
    component: 'CustomTextArea',
    fieldKey: 'richText',
    label: '富文本提示',
    tipText: {
      type: 'rich',
      title: '富文本提示标题',
      content: '这里可以包含 <strong>HTML</strong> 内容，支持<em>格式化</em>文本。'
    }
  },

  // 6. 列表提示（新功能）
  listTip: {
    component: 'CustomSelect',
    fieldKey: 'listTip',
    label: '列表提示',
    tipText: {
      type: 'list',
      title: '选择建议',
      items: [
        '选项1：适用于简单场景',
        '选项2：适用于复杂场景',
        '选项3：适用于特殊场景'
      ]
    }
  },

  // 7. 状态提示（新功能）
  statusTip: {
    component: 'CustomInput',
    fieldKey: 'statusTip',
    label: '状态提示',
    tipText: tip()
      .dynamic((fieldValue) => ({
        type: 'status',
        status: fieldValue ? 'success' : 'warning',
        message: fieldValue ? '输入有效' : '请输入内容'
      }))
      .build()
  },

  // 8. 复合条件（高级用法）
  complexConditional: {
    component: 'CustomInput',
    fieldKey: 'complexField',
    label: '复合条件',
    tipText: tip()
      .conditional(
        (fieldValue, formData) => {
          // 复杂的条件判断
          const hasValue = !!fieldValue
          const hasRelatedField = !!formData.relatedField
          return hasValue && hasRelatedField
        },
        // 满足条件时显示富文本
        {
          type: 'rich',
          title: '验证通过',
          content: '所有相关字段都已正确填写。'
        },
        // 不满足条件时显示列表提示
        {
          type: 'list',
          title: '请完成以下步骤',
          items: [
            '填写当前字段',
            '填写相关字段',
            '检查数据格式'
          ]
        }
      )
      .build()
  }
}

// 渲染函数工具类
export class TipRenderUtils {
  private renderer = enhancedRenderer

  // 渲染单个提示
  renderTip(tipText: any, context: RenderContext) {
    return this.renderer(tipText, context)
  }

  // 批量渲染提示
  renderTips(tips: Array<{ tipText: any; context: RenderContext }>) {
    return tips.map(({ tipText, context }) => this.renderTip(tipText, context))
  }

  // 条件渲染
  renderConditional(
    condition: boolean,
    trueTip: any,
    falseTip: any,
    context: RenderContext
  ) {
    const selectedTip = condition ? trueTip : falseTip
    return this.renderTip(selectedTip, context)
  }

  // 组合渲染
  renderComposed(...tips: Array<{ tipText: any; context: RenderContext }>) {
    const renderedTips = this.renderTips(tips)
    return h('div', { style: 'display: flex; gap: 8px;' }, renderedTips)
  }
}

// 导出工具实例
export const tipRenderUtils = new TipRenderUtils()

// 高阶渲染函数
export const withTipRenderer = (baseComponent: any) => {
  return (props: any) => {
    const { tipText, ...otherProps } = props
    
    if (!tipText) return h(baseComponent, otherProps)
    
    const context: RenderContext = {
      fieldKey: props.fieldKey || '',
      fieldValue: props.fieldValue,
      fieldConfig: props.fieldConfig || {},
      formData: props.formData || {}
    }

    return h('div', { style: 'position: relative;' }, [
      h(baseComponent, otherProps),
      h('div', { 
        style: 'position: absolute; top: 0; right: 0; transform: translateY(-50%);' 
      }, [
        tipRenderUtils.renderTip(tipText, context)
      ])
    ])
  }
}

// 提示渲染 Hook（Composition API 风格）
export const useTipRenderer = () => {
  return {
    renderTip: tipRenderUtils.renderTip.bind(tipRenderUtils),
    renderTips: tipRenderUtils.renderTips.bind(tipRenderUtils),
    renderConditional: tipRenderUtils.renderConditional.bind(tipRenderUtils),
    renderComposed: tipRenderUtils.renderComposed.bind(tipRenderUtils)
  }
}

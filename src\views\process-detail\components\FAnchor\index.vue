<template>
  <div class="process-anchor-wrapper">
    <FAnchor class="process-anchor">
      <FAnchorLink v-for="item in props.data" :href="`#${item.id}-${item.name}`" :title="item.name" :key="item.id" />
    </FAnchor>
  </div>
</template>
<script setup lang="ts">
interface IAnchor {
  id: number
  name: string
}

interface IProps {
  data: IAnchor[]
}

const props = defineProps<IProps>()
</script>

<style lang="scss" scoped>
.process-anchor-wrapper {
  flex: 0 0 110px;
  margin-right: 10px;
  padding-top: 349px;
  .process-anchor {
    position: sticky;
    top: 0;
  }
  :deep(.fs-anchor) {
    text-align: right;
    .fs-anchor-ink {
      left: auto;
      right: 0;
    }
    .fs-anchor-link {
      padding: 8px 16px 8px 0px;
      overflow: hidden;
    }
  }
}
</style>

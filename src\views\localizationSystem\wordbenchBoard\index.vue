<template>
  <div class="wordbench-board-container">
    <FSpin size="large" wrapper-class-name="cust-board-spin" :spinning="loading">
      <div class="title">
        <span>{{ name }}{{ i18n.t('本地化工作台') }}</span>
        <SearchCard @on-search="search" :countryList="countryList" />
      </div>
      <div class="content">
        <CardBox
          :class="[index === processList.length - 1 ? 'last-card' : '']"
          :process-info="item"
          v-for="(item, index) in processList"
          :key="item.id"
        />
      </div>
    </FSpin>
  </div>
</template>
<script setup lang="ts">
import { provide, ref, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import CardBox from './components/CardBox.vue'
import SearchCard from './components/SearchCard.vue'
import { getProcess } from '@/api/localizationSystem/wordbenchBoard'
import { selectAllLocalizationRegion } from '@/api/localizationSystem/dataBoard'
import type { TreeParams } from '@/types/localizationSystem/wordbenchBoard'
import type { LocalizationRegion } from '@/types/localizationSystem/dataBoard'
import { messageInstance as message } from '@fs/smart-design'
import { useI18n } from '@/utils'
const i18n = useI18n()

type TCountry = {
  name: string
  id: number
}

type IpForm = {
  country: number | undefined
}

const route = useRoute()
let loading = ref<boolean>(false)
let processList = ref<any[]>([])
const name = ref<string>('')
const countryList = ref<LocalizationRegion[]>([])
const searchForm = reactive<IpForm>({
  country: undefined,
})

const getAllLocalizationConfig = async () => {
  const res = await selectAllLocalizationRegion()
  if (res.code === 200) {
    console.log(res)
    countryList.value = res.data
  }
}

const search = async (searchConditions: any) => {
  if (searchConditions.country && searchConditions.country.length) {
    Object.assign(searchForm, { country: searchConditions.country[searchConditions.country.length - 1] })
    // name.value = countryList.value.find(item => item.id === searchConditions.country)?.name as string
    name.value = searchConditions.name.join('/')
    getProcessLocal()
  }
}

const getProcessLocal = async () => {
  try {
    loading.value = true
    if (!searchForm.country) {
      message.warning(i18n.t('请先选择国家'))
      return
    }
    const params: TreeParams = { localizationRegionId: searchForm.country }
    const res = await getProcess(params)
    processList.value = res.data.map((item: any) => {
      item.percent = Math.floor(((item.total - item.undoneNum) / item.total) * 100)
      return item
    })
  } finally {
    loading.value = false
  }
}

provide('getProcessLocal', getProcessLocal)

onMounted(() => {
  getAllLocalizationConfig()
})
</script>
<style lang="scss" scoped>
.wordbench-board-container {
  position: relative;
  height: calc(100% - 12px);
  margin-left: 4px;
  margin-top: 12px;
  :deep(.cust-desc-tip) {
    .fs-tooltip-inner {
      color: #333333;
    }
  }
  :deep(.cust-board-spin) {
    height: 100%;
    .fs-spin-container {
      height: 100%;
    }
  }
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 28px;
    line-height: 28px;
    margin-bottom: 24px;
    font-size: 20px;
    font-weight: 600;
    color: #333333;
  }
  .content {
    display: flex;
    height: calc(100% - 40px);
    overflow-x: scroll;
    .last-card {
      margin-right: 0;
    }
    &::-webkit-scrollbar {
      visibility: hidden;
      background: none;
    }
    &::-webkit-scrollbar-thumb {
      visibility: hidden;
    }
    &::-webkit-scrollbar-track {
      background: none;
    }
    &:hover {
      &::-webkit-scrollbar {
        visibility: visible;
      }
    }
    &:hover {
      &::-webkit-scrollbar-thumb {
        visibility: visible;
      }
    }
  }
}
</style>

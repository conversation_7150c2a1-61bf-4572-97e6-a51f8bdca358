<template>
  <FModal
    v-model:visible="value"
    :title="props.title"
    :bodyStyle="{ maxHeight: '80vh', overflow: 'scroll' }"
    centered
    :width="400"
    :z-index="1001"
  >
    <div class="notification-modal-wrapper">
      <div class="tip" :style="{ padding: props.title === ProcessModalTilte.submit ? '11px' : '11px' }">
        <i class="iconfont icontishi"></i>
        <div class="sumbit-tip" v-if="props.title === ProcessModalTilte.submit">
          <span>{{ i18n.t('是否确认提交？') }}</span>
          <p v-if="defaultMakeRole.length">
            <span>{{ i18n.t('默认抄送人：') }}</span>
            <span v-for="item in defaultMakeRole" :key="item.uuid">{{ item.feiShuName }};</span>
          </p>
        </div>
        <div v-else-if="props.title === ProcessModalTilte.finish">
          <p>{{ i18n.t('是否确认操作流程终止？') }}</p>
          <p>{{ i18n.t('流程终止后将不可恢复') }}</p>
          <p v-if="defaultMakeRole.length">
            <span>{{ i18n.t('默认抄送人：') }}</span>
            <span v-for="item in defaultMakeRole" :key="item.uuid">{{ item.feiShuName }};</span>
          </p>
        </div>
        <div v-else-if="props.title === ProcessModalTilte.lostSingle">
          <p>{{ i18n.t('是否确认操作失单？') }}</p>
          <p>{{ i18n.t('失单后流程将会终止且不可恢复') }}</p>
          <p v-if="defaultMakeRole.length">
            <span>{{ i18n.t('默认抄送人：') }}</span>
            <span v-for="item in defaultMakeRole" :key="item.uuid">{{ item.feiShuName }};</span>
          </p>
        </div>
      </div>
      <template
        v-if="[ProcessModalTilte.finish, ProcessModalTilte.lostSingle].includes(props.title) && finishDescTags.length"
      >
        <div class="header finish-desc-tag">
          <span class="title">流程终止标签：</span>
          <div class="tag-box">
            <f-tag
              v-for="(item, index) in finishDescTags"
              :key="item"
              :checked="formState.finishDescTag === item"
              @update:checked="() => (formState.finishDescTag = item)"
              size="small"
              :border="true"
              :color="labelColor[index % 5]"
              checkable
              >{{ item }}</f-tag
            >
          </div>
        </div>
      </template>
      <div class="header">
        <span class="title">{{ i18n.t('抄送人') }}</span>
        <FCheckbox v-model:checked="formState.allUsers">{{ i18n.t('抄送所有') }}</FCheckbox>
      </div>
      <FCascader
        dropdown-class-name="notification-cust-cascader"
        multiple
        show-arrow
        :placeholder="i18n.t('请输入')"
        style="width: 100%"
        :field-names="{ label: 'name', value: 'uuid', children: 'departmentChildrens' }"
        :max-tag-count="10"
        :options="personList"
        :show-search="{ filterOption }"
        show-checked-strategy="SHOW_CHILD"
        @change="onChangePerson"
        v-model:value="formState.selectUser"
        :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
        :key="key"
      ></FCascader>
      <template v-if="lostSingleData?.flag">
        <div class="header desc">
          <span class="title">{{ lostSingleData.title }}</span>
        </div>
        <FCascader
          dropdown-class-name="notification-cust-cascader"
          :placeholder="i18n.t('请输入')"
          style="width: 100%"
          :options="reasonDescList"
          :show-search="{ reasonDescFilterOption }"
          show-checked-strategy="SHOW_CHILD"
          v-model:value="formState.reasonStatus"
          :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
          :key="key"
        ></FCascader>
      </template>
      <template v-if="descData?.flag">
        <div class="header desc">
          <span class="title">{{ descData.title }}</span>
        </div>
        <FTextarea v-model:value="formState.remark" class="desc-tatea" placeholder="请输入" />
      </template>
    </div>
    <template #footer>
      <div class="notification-modal-footer">
        <FConfigProvider :auto-insert-space-in-button="false">
          <FButton key="back" @click="value = false">{{ i18n.t('取消') }}</FButton>
          <FButton :loading="loading" html-type="button" type="primary" @click="handleOk">{{ i18n.t('提交') }}</FButton>
        </FConfigProvider>
      </div>
    </template>
  </FModal>
</template>
<script lang="ts" setup>
import { reactive, computed, ref, watch, inject } from 'vue'
import type { IProcessRoleAndUser } from '@/types/request'
import { getDepartment, getMakePerson } from '@/api'
import { useI18n } from '@/utils'
import { messageInstance } from '@fs/smart-design'
const i18n = useI18n()
const personList = ref([])
interface FormState {
  allUsers: boolean
  selectUser: string[]
  remark: string
  reasonStatus: string
  finishDescTag: any
}

const ProcessModalTilte = reactive({
  submit: i18n.t('提交'),
  finish: i18n.t('流程终止'),
  lostSingle: i18n.t('失单'),
})

interface IProps {
  value: boolean
  loading: boolean | undefined
  title: string
  role: IProcessRoleAndUser[]
  isDelayStatus: boolean
  defaultRoleKey: string
  defaultRoleValue: any
}
const emit = defineEmits(['submit', 'update:value', 'update:loading'])
const props = defineProps<IProps>()
let key = ref<number>(Date.now() + Math.random())
const defaultMakeRole = ref<any[]>([])
const reasonDescList = ref([
  {
    label: '商机失单',
    value: '商机失单',
    children: [
      {
        label: '运输&清关问题',
        value: 'Z1',
        children: [
          {
            label: '客户介意支付清关费',
            value: 'Z1A',
          },
          {
            label: '客户清关难',
            value: 'Z1B',
          },
          {
            label: '客户无法清关',
            value: 'Z1C',
          },
          {
            label: '客户不接受我司报关资料',
            value: 'Z1D',
          },
          {
            label: '进口程序复杂',
            value: 'Z1E',
          },
          {
            label: '运费过高',
            value: 'Z1F',
          },
          {
            label: '无法满足客户指定运输方式',
            value: 'Z1G',
          },
          {
            label: '运输耗时太长',
            value: 'Z1H',
          },
        ],
      },
      {
        label: '产品问题',
        value: 'Z2',
        children: [
          {
            label: '产品某个属性或功能无法满足',
            value: 'Z2A',
          },
          {
            label: '产品无法提供',
            value: 'Z2B',
          },
          {
            label: '产品有起订量',
            value: 'Z2C',
          },
        ],
      },
      {
        label: '认证问题',
        value: 'Z3',
        children: [
          {
            label: '产品无3C认证',
            value: 'Z3A',
          },
          {
            label: '产品无CE认证',
            value: 'Z3B',
          },
          {
            label: '产品无UL认证',
            value: 'Z3C',
          },
          {
            label: '产品无TAA认证',
            value: 'Z3D',
          },
          {
            label: '产品无原产地证明',
            value: 'Z3E',
          },
          {
            label: '产品无其他强制性认证',
            value: 'Z3F',
          },
          {
            label: '产品无其他非强制性认证（自愿性认证）',
            value: 'Z3H',
          },
        ],
      },
      {
        label: '交期问题',
        value: 'Z4',
        children: [
          {
            label: '产品无库存，外购交期较长',
            value: 'Z4A',
          },
          {
            label: '客户需求紧急，但本地仓无库存',
            value: 'Z4B',
          },
          {
            label: '客户项目DDL提前，无法满足客户新交期',
            value: 'Z4C',
          },
        ],
      },
      {
        label: '价格问题',
        value: 'Z5',
        children: [
          {
            label: '产品价格比竞品高，客户本次不选择和FS合作',
            value: 'Z5A',
          },
          {
            label: '客户主观认为产品价格高',
            value: 'Z5B',
          },
          {
            label: 'FS产品价格无法满足客户成本预算',
            value: 'Z5C',
          },
          {
            label: '招标项目/经销商，产品折扣力度低，和前台价无差距，客户无利润',
            value: 'Z5D',
          },
          {
            label: '受市场因素影响，导致产品价格上涨',
            value: 'Z5E',
          },
          {
            label: '汇率上涨，导致价格变化',
            value: 'Z5F',
          },
        ],
      },
      {
        label: '客户方原因',
        value: 'Z6',
        children: [
          {
            label: '介意发货地是中国',
            value: 'Z6A',
          },
          {
            label: '介意中国供应商',
            value: 'Z6B',
          },
          {
            label: '客户指定合作平台，FS无法支持',
            value: 'Z6C',
          },
          {
            label: '客户拒绝沟通',
            value: 'Z6D',
          },
          {
            label: '客户失联',
            value: 'Z6E',
          },
          {
            label: '客户离职',
            value: 'Z6F',
          },
          {
            label: '客户表示不需要了',
            value: 'Z6G',
          },
          {
            label: '客户项目/工程变更',
            value: 'Z6H',
          },
          {
            label: '客户只接受和品牌产品合作',
            value: 'Z6I',
          },
          {
            label: '投标暂停',
            value: 'Z6J',
          },
          {
            label: '投标失败',
            value: 'Z6K',
          },
          {
            label: '客户只接受账期付款方式',
            value: 'Z6L',
          },
          {
            label: '客户拒绝配合我司申请提供相关文件',
            value: 'Z6M',
          },
          {
            label: '客户对供应商资质要求复杂',
            value: 'Z6N',
          },
        ],
      },
      {
        label: '服务问题',
        value: 'Z7',
        children: [
          {
            label: '无法上门安装',
            value: 'Z7A',
          },
          {
            label: '无法提供现场售后支持',
            value: 'Z7B',
          },
          {
            label: '无法提供备件支持',
            value: 'Z7C',
          },
        ],
      },
      {
        label: '其它',
        value: 'Z8',
        children: [
          {
            label: '竞争对手以套价为目的产生的商机',
            value: 'Z8A',
          },
          {
            label: 'FS内部员工测试用',
            value: 'Z8B',
          },
          {
            label: '客户地址中公司名是FS不合作对象',
            value: 'Z8C',
          },
        ],
      },
    ],
  },
])
const labelColor = ref<string[]>(['success', 'processing', 'error', 'warning', 'default'])
const processConfigInfo = inject('processConfigInfo') as Record<string, any> // 当前流程配置信息
const finishDescTags = computed(() => {
  return (processConfigInfo?.value?.finishDesc || undefined)?.split(',') || []
})
const value = computed({
  get() {
    return props.value
  },
  set(val: boolean) {
    emit('update:value', val)
  },
})

const loading = computed({
  get() {
    return props.loading
  },
  set(val: boolean) {
    emit('update:loading', val)
  },
})

const lostSingleData = computed(() => {
  if (props.title === ProcessModalTilte.lostSingle) {
    return {
      title: i18n.t('失单原因'),
      flag: true,
      key: 'reasonStatus',
    }
  }
  return { flag: false }
})

const descData = computed(() => {
  if (props.title === ProcessModalTilte.submit && props.isDelayStatus) {
    return {
      title: i18n.t('延期原因'),
      flag: true,
      key: 'delayReason',
    }
  }
  if (props.title === ProcessModalTilte.finish) {
    return {
      title: i18n.t('流程终止说明'),
      flag: true,
      key: 'finishDesc',
    }
  }
  if (props.title === ProcessModalTilte.lostSingle) {
    return {
      title: i18n.t('失单说明'),
      flag: true,
      key: 'reasonDesc',
    }
  }
  return { flag: false }
})

watch(
  () => value.value,
  val => {
    if (!personList.value.length) onGetDepartment()
    if (val) {
      getDefaultRole()
      formState.allUsers = false
      formState.selectUser = []
      formState.remark = ''
      formState.reasonStatus = ''
      formState.finishDescTag = undefined
      selectLists = []
      key.value = Date.now() + Math.random()
    }
  }
)

const getDefaultRole = async () => {
  if (!props.defaultRoleKey || !props.defaultRoleValue) return
  const res = await getMakePerson({ [props.defaultRoleKey]: props.defaultRoleValue })
  if (res.code !== 200) throw new Error(res.msg)
  defaultMakeRole.value = res?.data || []
}

const reasonDescFilterOption = (input: string, option: any) =>
  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
const formState = reactive<FormState>({
  allUsers: false,
  selectUser: [],
  remark: '',
  reasonStatus: undefined,
  finishDescTag: undefined,
})

const checkChildren = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    console.log(Object.keys(cur))
    if (cur.uuid && cur.name && Object.keys(cur).length === 2) {
      users.push(cur.uuid)
    } else if (cur.departmentChildrens && cur.departmentChildrens.length) {
      checkChildren(cur.departmentChildrens, users)
    }
    return users
  }, users)
}

let selectLists: string | any[] = []

const onChangePerson = (value: any, selectedOptions: any) => {
  selectLists =
    selectedOptions.map((item: any) => {
      return item[item.length - 1]
    }) || []
}

const handleTree = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      let data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && handleTree(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

const onGetDepartment = async () => {
  const res = await getDepartment()
  if (res.code !== 200) throw new Error(res.msg)
  personList.value = handleTree(res.data)
}
const handleOk = async () => {
  if (loading.value) return
  if (lostSingleData.value.flag && !formState.reasonStatus) {
    messageInstance.warning(lostSingleData.value.title + i18n.t('未填写！'))
    return
  }
  if (descData.value.flag && !formState.remark) {
    messageInstance.warning(descData.value.title + i18n.t('未填写！'))
    return
  }
  if (
    [ProcessModalTilte.finish, ProcessModalTilte.lostSingle].includes(props.title) &&
    finishDescTags?.value?.length &&
    !formState.finishDescTag
  ) {
    messageInstance.warning('终止标签未选择！')
    return
  }
  let allUser = Array.from(new Set([...checkChildren(selectLists), ...defaultMakeRole.value.map(item => item.uuid)]))
  if (formState.allUsers) {
    allUser = props.role.reduce((users: any, cur: any) => {
      if (cur.users && cur.users.length) {
        cur.users.forEach((item: any) => {
          item && item.uuid && !users.includes(item.uuid) && users.push(item.uuid)
        })
      }
      return users
    }, allUser)
  }
  loading.value = true
  const data: Record<string, any> = { makeUuidList: allUser }
  descData.value.flag && (data[descData.value.key as string] = formState.remark)
  lostSingleData.value.flag && (data[lostSingleData.value.key as string] = formState.reasonStatus.at(-1))
  data.finishDescTag = formState.finishDescTag
  emit('submit', data)
}
</script>
<style lang="scss" scoped>
.notification-modal-wrapper {
  :deep(.fs-select-selector) {
    min-height: 32px;
  }
  :deep(.notification-cust-cascader) {
    padding-right: 0 !important;
    .fs-cascader-menu {
      border-right-color: #eee;
      &:last-child {
        border-right: none;
      }
      .fs-cascader-menu-item {
        margin-top: 2px;
        &[aria-checked='true'] {
          background: #ebf3fd;
          border-radius: 3px;
        }
      }
      .fs-cascader-menu-item-active[aria-checked='true'] {
        background: #ebf3fd;
        border-radius: 3px;
      }
    }
  }
  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    color: #333333;
    line-height: 18px;
    font-size: 12px;
    &.desc {
      display: block;
      margin-top: 24px;
      &::before {
        display: inline-block;
        margin-right: 4px;
        color: #f36767;
        font-size: 12px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }
    }
    &.finish-desc-tag {
      display: flex;
      margin-top: 24px;
      margin-bottom: 8px;
      .tag-box {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
        :deep(.fs-tag) {
          margin-bottom: 8px;
          margin-right: 8px;
          font-size: 12px;
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
    :deep(.fs-checkbox-inner) {
      width: 14px;
      height: 14px;
    }
    :deep(.fs-checkbox-wrapper span:last-child) {
      padding-left: 8px;
      padding-right: 0;
      color: #666666;
      line-height: 18px;
      font-size: 12px;
    }
  }
  :deep(.desc-tatea) {
    height: 88px;
  }
  .tip {
    display: flex;
    margin-bottom: 24px;
    border: 1px solid #fdd2a7;
    background: #fef4e9;
    border-radius: 3px;
    i {
      height: 18px;
      line-height: 18px;
      margin-top: -1px;
      margin-right: 8px;
      color: #fa9a39;
    }
    .sumbit-tip {
      font-weight: 400;
      color: #333333;
      line-height: 18px;
      font-size: 12px;
    }
    p {
      margin-bottom: 4px;
    }
    p:first-child {
      height: 22px;
      margin-bottom: 4px;
      font-weight: 400;
      color: #333333;
      line-height: 1.2;
      font-size: 14px;
    }
    p:last-child {
      margin-bottom: 0;
      color: #999;
      font-size: 12px;
      line-height: 18px;
    }
  }
}
.notification-modal-footer {
  display: flex;
  justify-content: flex-end;
  :deep(.fs-btn) {
    min-height: 32px !important;
  }
}
</style>

import type { GetterTree } from 'vuex'
import type { IStoreState } from './index'
import { cache, USER_TOKEN, USER_INFO } from '@/utils'
import store from './index'
import { login } from '@/init'

const getToken = (state: IStoreState) => {
  let token = state.user.token
  if (!token) {
    token = cache.get(USER_TOKEN) as string
    if (!token) {
      token = login.getCookiesToken()
      token && store.commit('user/SET_USER_TOKEN', token)
    }
  }
  return token
}

const getUserInfo = (state: IStoreState) => {
  let userInfo = state.user.userInfo
  if (!userInfo) {
    const infoStr = cache.get(USER_INFO) as string
    userInfo = typeof infoStr === 'string' ? JSON.parse(infoStr) : ''
    if (userInfo) store.commit('user/SET_USER_INFO', userInfo)
  }
  return userInfo
}

const getters: GetterTree<IStoreState, IStoreState> = {
  global: (state: IStoreState) => state.global,
  isHeaderShow: (state: IStoreState) => state.global.isHeaderShow,
  isSidebarShow: (state: IStoreState) => state.global.isSidebarShow,

  userInfo: getUserInfo,
  token: getToken,

  microConfig: (state: IStoreState) => state.micro,
  language: (state: IStoreState) => state.global.language,

  historyRecord: (state: IStoreState) => state.global.historyRecord,
}

export default getters

/**
 * Cron表达式解析工具
 */

// 解析Cron表达式为人类可读的描述
export function parseCronToText(cronExpression: string): string {
  try {
    // 预处理表达式
    const processedExpression = preprocessExpression(cronExpression)
    const parts = processedExpression.split(' ')

    if (parts.length !== 6) {
      return '无效的Cron表达式'
    }

    const [seconds, minutes, hours, dayOfMonth, month, dayOfWeek] = parts

    // 构建时间部分
    let timePart = ''
    if (seconds === '0' && minutes === '0') {
      timePart = parseHours(hours)
    } else {
      timePart = `${parseSeconds(seconds)}，${parseMinutes(minutes)}，${parseHours(hours)}`
    }

    // 处理日期和星期的互斥性
    let dayPart = ''
    if (dayOfMonth === '?' && dayOfWeek !== '?') {
      // 如果日期是?，且星期不是?，则解析星期部分
      const weekDesc = parseWeeks(dayOfWeek)
      dayPart = weekDesc || '每周的每一天'
    } else if (dayOfMonth !== '?' && dayOfWeek === '?') {
      // 如果星期是?，且日期不是?，则解析日期部分
      dayPart = parseDays(dayOfMonth)
    } else if (dayOfMonth === '?' && dayOfWeek === '?') {
      // 如果两者都是?，默认为每天
      dayPart = '每天'
    } else if (dayOfMonth === '*' && dayOfWeek === '*') {
      // 如果两者都是*，表示每天
      dayPart = '每天'
    } else if (dayOfMonth === '*' && dayOfWeek !== '*') {
      // 如果日期是*，且星期不是*，则解析星期部分
      const weekDesc = parseWeeks(dayOfWeek)
      dayPart = weekDesc || '每周的每一天'
    } else if (dayOfMonth !== '*' && dayOfWeek === '*') {
      // 如果星期是*，且日期不是*，则解析日期部分
      dayPart = parseDays(dayOfMonth)
    } else {
      // 如果两者都不是*，优先使用日期部分
      dayPart = parseDays(dayOfMonth)
    }

    // 构建月份部分
    const monthPart = parseMonths(month)

    // 组合所有部分
    return `${timePart}，${dayPart}，${monthPart}`
  } catch (error) {
    console.error('解析Cron表达式出错:', error)
    return '表达式解析错误'
  }
}

// 预处理表达式
function preprocessExpression(expression: string): string {
  // 移除多余的空格
  let processed = expression.trim().replace(/\s+/g, ' ')

  // 处理范围表达式中的空格
  processed = processed.replace(/(\d+)\s*-\s*(\d+)/g, '$1-$2')

  // 处理步长表达式中的空格
  processed = processed.replace(/(\d+|\*)\s*\/\s*(\d+)/g, '$1/$2')

  return processed
}

// 解析秒部分
function parseSeconds(seconds: string): string {
  if (seconds === '*') {
    return '每秒'
  } else if (seconds === '0') {
    return '第0秒'
  } else if (seconds.includes('/')) {
    const [start, interval] = seconds.split('/')
    return `从第${start}秒开始，每${interval}秒`
  } else if (seconds.includes(',')) {
    return `第${seconds}秒`
  } else if (seconds.includes('-')) {
    const [start, end] = seconds.split('-')
    return `第${start}到第${end}秒之间的每一秒`
  }
  return `第${seconds}秒`
}

// 解析分钟部分
function parseMinutes(minutes: string): string {
  if (minutes === '*') {
    return '每分钟'
  } else if (minutes === '0') {
    return '第0分钟'
  } else if (minutes.includes('/')) {
    const [start, interval] = minutes.split('/')
    return `从第${start}分钟开始，每${interval}分钟`
  } else if (minutes.includes(',')) {
    return `第${minutes}分钟`
  } else if (minutes.includes('-')) {
    const [start, end] = minutes.split('-')
    return `第${start}到第${end}分钟之间的每一分钟`
  }
  return `第${minutes}分钟`
}

// 解析小时部分
function parseHours(hours: string): string {
  if (hours === '*') {
    return '每小时'
  } else if (hours === '0') {
    return '0点'
  } else if (hours.includes('/')) {
    const [start, interval] = hours.split('/')
    return `从${start}点开始，每${interval}小时`
  } else if (hours.includes(',')) {
    return `${hours}点`
  } else if (hours.includes('-')) {
    const [start, end] = hours.split('-')
    return `${start}点到${end}点之间的每一小时`
  }
  return `${hours}点`
}

// 解析日期部分
function parseDays(day: string): string {
  if (day === '*') {
    return '每天'
  } else if (day === '?') {
    return '' // 当日期为不指定时，返回空字符串
  } else if (day.includes('/')) {
    const [start, interval] = day.split('/')
    return `从${start}日开始，每${interval}天`
  } else if (day.includes(',')) {
    return `${day}日`
  } else if (day.includes('-')) {
    const [start, end] = day.split('-')
    return `${start}日到${end}日之间的每一天`
  } else if (day === 'L') {
    return '本月最后一天'
  } else if (day.includes('W')) {
    const dayNum = day.replace('W', '')
    return `离${dayNum}日最近的工作日`
  } else if (day.includes('#')) {
    const [weekNum, dayOfWeek] = day.split('#')
    return `本月第${weekNum}个星期${dayOfWeek}`
  }
  return `${day}日`
}

// 解析星期部分
function parseWeeks(week: string): string {
  if (week === '*') {
    return '每周的每一天'
  }

  // 处理第几个周几 (0#1 - 第一个周日)
  if (week.includes('#')) {
    const [day, nth] = week.split('#')
    const dayNum = parseInt(day)
    const nthNum = parseInt(nth)
    if (!isNaN(dayNum) && !isNaN(nthNum) && dayNum >= 0 && dayNum <= 6 && nthNum >= 1 && nthNum <= 5) {
      return `本月第${nthNum}个${getWeekDayName(dayNum)}`
    }
  }

  // 处理指定周几列表 (0,2,4 - 周日、二、四)
  if (week.includes(',')) {
    const days = week
      .split(',')
      .map(d => parseInt(d))
      .filter(d => !isNaN(d) && d >= 0 && d <= 6)
      .sort((a, b) => a - b)

    if (days.length > 0) {
      return days.map(d => getWeekDayName(d)).join('、')
    }
  }

  // 处理单个周几 (0-6)
  const dayNum = parseInt(week)
  if (!isNaN(dayNum) && dayNum >= 0 && dayNum <= 6) {
    return getWeekDayName(dayNum)
  }

  return '每周的每一天'
}

// 解析月份部分
function parseMonths(month: string): string {
  if (month === '*') {
    return '每月'
  } else if (month.includes('/')) {
    const [start, interval] = month.split('/')
    return `从${start}月开始，每${interval}个月`
  } else if (month.includes(',')) {
    return `${month}月`
  } else if (month.includes('-')) {
    const [start, end] = month.split('-')
    return `${start}月到${end}月之间的每一个月`
  }
  return `${month}月`
}

// 解析年份部分
function parseYears(year: string): string {
  if (year === '*') {
    return '，每年'
  } else if (year.includes('/')) {
    const [start, interval] = year.split('/')
    return `，从${start}年开始，每${interval}年`
  } else if (year.includes(',')) {
    return `，在${year}年`
  } else if (year.includes('-')) {
    const [start, end] = year.split('-')
    return `，在${start}年到${end}年之间的每一年`
  }
  return `，在${year}年`
}

// 获取星期几的名称
function getWeekDayName(day: number): string {
  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return weekDays[day % 7] || `星期${day}`
}

// 验证Cron表达式是否有效
export function isValidCronExpression(expression: string): boolean {
  try {
    // 预处理表达式
    const processedExpression = preprocessExpression(expression)

    // 检查表达式是否为空
    if (!processedExpression || typeof processedExpression !== 'string') {
      return false
    }

    // 检查表达式格式
    const parts = processedExpression.split(' ')
    if (parts.length !== 6) {
      return false
    }

    // 解构各部分
    const [seconds, minutes, hours, day, month, week] = parts

    // 验证秒 (0-59)
    if (!isValidPart(seconds, 0, 59)) return false

    // 验证分 (0-59)
    if (!isValidPart(minutes, 0, 59)) return false

    // 验证时 (0-23)
    if (!isValidPart(hours, 0, 23)) return false

    // 验证日 (1-31)
    if (!isValidDayOfMonth(day)) return false

    // 验证月 (1-12)
    if (!isValidMonth(month)) return false

    // 验证周 (0-6)
    if (!isValidDayOfWeek(week)) return false

    // 验证日和周的互斥关系
    if (day !== '?' && week !== '?' && day !== '*' && week !== '*') {
      return false
    }

    return true
  } catch (e) {
    return false
  }
}

// 优化验证通用部分
function isValidPart(value: string, min: number, max: number): boolean {
  // 检查特殊字符
  if (value === '*' || value === '?') return true

  // 检查步长表达式 x/y
  if (value.includes('/')) {
    const [start, interval] = value.split('/')

    // 检查间隔是否为正整数
    if (!/^\d+$/.test(interval) || parseInt(interval) <= 0) {
      return false
    }

    // 检查起始值
    if (start === '*') {
      return true
    } else {
      return isValidRange(start, min, max)
    }
  }

  // 检查列表 x,y,z
  if (value.includes(',')) {
    const values = value.split(',')
    // 检查是否有重复值
    if (new Set(values).size !== values.length) {
      return false
    }
    return values.every(v => isValidRange(v, min, max))
  }

  // 检查范围 x-y
  if (value.includes('-')) {
    const [start, end] = value.split('-')

    // 检查起始值和结束值是否为整数
    if (!/^\d+$/.test(start) || !/^\d+$/.test(end)) {
      return false
    }

    const startNum = parseInt(start)
    const endNum = parseInt(end)

    return startNum >= min && endNum <= max && startNum <= endNum
  }

  // 检查单个值
  if (!/^\d+$/.test(value)) {
    return false
  }

  const num = parseInt(value)
  return !isNaN(num) && num >= min && num <= max
}

// 验证范围
function isValidRange(value: string, min: number, max: number): boolean {
  // 检查单个值
  if (!/^\d+$/.test(value)) {
    return false
  }

  const num = parseInt(value)
  return !isNaN(num) && num >= min && num <= max
}

// 验证日期 (1-31, L, W, LW, 数字W)
function isValidDayOfMonth(value: string): boolean {
  if (value === '*' || value === '?') return true

  // 检查最后一天 L
  if (value === 'L') return true

  // 检查最接近指定日期的工作日 nW
  if (/^\d+W$/.test(value)) {
    const day = parseInt(value.replace('W', ''))
    return day >= 1 && day <= 31
  }

  // 检查本月最后一个工作日 LW
  if (value === 'LW') return true

  // 检查步长表达式 x/y
  if (value.includes('/')) {
    const [start, interval] = value.split('/')

    // 检查间隔是否为正整数
    if (!/^\d+$/.test(interval) || parseInt(interval) <= 0) {
      return false
    }

    // 检查起始值
    if (start === '*') {
      return true
    } else {
      return isValidRange(start, 1, 31)
    }
  }

  // 检查列表 x,y,z
  if (value.includes(',')) {
    return value.split(',').every(v => isValidDayOfMonth(v))
  }

  // 检查范围 x-y
  if (value.includes('-')) {
    const [start, end] = value.split('-')

    // 检查起始值和结束值是否为整数
    if (!/^\d+$/.test(start) || !/^\d+$/.test(end)) {
      return false
    }

    const startNum = parseInt(start)
    const endNum = parseInt(end)

    return startNum >= 1 && endNum <= 31 && startNum <= endNum
  }

  // 检查单个值
  if (!/^\d+$/.test(value)) {
    return false
  }

  const num = parseInt(value)
  return !isNaN(num) && num >= 1 && num <= 31
}

// 验证月份 (1-12, JAN-DEC)
function isValidMonth(value: string): boolean {
  if (value === '*') return true

  // 检查月份名称
  const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']

  // 检查步长表达式 x/y
  if (value.includes('/')) {
    const [start, interval] = value.split('/')

    // 检查间隔是否为正整数
    if (!/^\d+$/.test(interval) || parseInt(interval) <= 0) {
      return false
    }

    // 检查起始值
    if (start === '*') {
      return true
    } else if (/^\d+$/.test(start)) {
      return isValidRange(start, 1, 12)
    } else {
      return monthNames.includes(start.toUpperCase())
    }
  }

  // 检查列表 x,y,z
  if (value.includes(',')) {
    return value.split(',').every(v => {
      if (/^\d+$/.test(v)) {
        return isValidRange(v, 1, 12)
      } else {
        return monthNames.includes(v.toUpperCase())
      }
    })
  }

  // 检查范围 x-y
  if (value.includes('-')) {
    const [start, end] = value.split('-')

    // 如果是数字范围
    if (/^\d+$/.test(start) && /^\d+$/.test(end)) {
      const startNum = parseInt(start)
      const endNum = parseInt(end)
      return startNum >= 1 && endNum <= 12 && startNum <= endNum
    }

    // 如果是月份名称范围
    if (monthNames.includes(start.toUpperCase()) && monthNames.includes(end.toUpperCase())) {
      const startIdx = monthNames.indexOf(start.toUpperCase())
      const endIdx = monthNames.indexOf(end.toUpperCase())
      return startIdx <= endIdx
    }

    return false
  }

  // 检查单个值
  if (/^\d+$/.test(value)) {
    const num = parseInt(value)
    return !isNaN(num) && num >= 1 && num <= 12
  } else {
    return monthNames.includes(value.toUpperCase())
  }
}

// 优化验证星期 (0-6, SUN-SAT, L, #)
function isValidDayOfWeek(value: string): boolean {
  if (value === '*' || value === '?') return true

  // 检查星期名称
  const weekNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT']

  // 检查最后一个星期几 nL
  if (/^[0-6]L$/.test(value)) return true

  // 检查第n个星期几 n#m
  if (value.includes('#')) {
    const [dayOfWeek, nth] = value.split('#')

    // 检查星期几是否有效
    let isValidDay = false
    if (/^[0-6]$/.test(dayOfWeek)) {
      isValidDay = true
    } else if (weekNames.includes(dayOfWeek.toUpperCase())) {
      isValidDay = true
    }

    // 检查第几个是否有效 (1-5)
    const isValidNth = /^[1-5]$/.test(nth)

    return isValidDay && isValidNth
  }

  // 检查步长表达式 x/y
  if (value.includes('/')) {
    const [start, interval] = value.split('/')

    // 检查间隔是否为正整数
    if (!/^\d+$/.test(interval) || parseInt(interval) <= 0) {
      return false
    }

    // 检查起始值
    if (start === '*') {
      return true
    } else if (/^[0-6]$/.test(start)) {
      return true
    } else {
      return weekNames.includes(start.toUpperCase())
    }
  }

  // 检查列表 x,y,z
  if (value.includes(',')) {
    const values = value.split(',')
    // 检查是否有重复值
    if (new Set(values).size !== values.length) {
      return false
    }
    return values.every(v => {
      if (/^[0-6]$/.test(v)) {
        return true
      } else {
        return weekNames.includes(v.toUpperCase())
      }
    })
  }

  // 检查范围 x-y
  if (value.includes('-')) {
    const [start, end] = value.split('-')

    // 如果是数字范围
    if (/^[0-6]$/.test(start) && /^[0-6]$/.test(end)) {
      const startNum = parseInt(start)
      const endNum = parseInt(end)
      return startNum <= endNum
    }

    // 如果是星期名称范围
    if (weekNames.includes(start.toUpperCase()) && weekNames.includes(end.toUpperCase())) {
      const startIdx = weekNames.indexOf(start.toUpperCase())
      const endIdx = weekNames.indexOf(end.toUpperCase())
      return startIdx <= endIdx
    }

    return false
  }

  // 检查单个值
  if (/^[0-6]$/.test(value)) {
    return true
  } else {
    return weekNames.includes(value.toUpperCase())
  }
}

// 验证年份 (1970-2099)
function isValidYear(value: string): boolean {
  if (value === '*') return true

  // 检查步长表达式 x/y
  if (value.includes('/')) {
    const [start, interval] = value.split('/')

    // 检查间隔是否为正整数
    if (!/^\d+$/.test(interval) || parseInt(interval) <= 0) {
      return false
    }

    // 检查起始值
    if (start === '*') {
      return true
    } else {
      return isValidRange(start, 1970, 2099)
    }
  }

  // 检查列表 x,y,z
  if (value.includes(',')) {
    return value.split(',').every(v => isValidRange(v, 1970, 2099))
  }

  // 检查范围 x-y
  if (value.includes('-')) {
    const [start, end] = value.split('-')

    // 检查起始值和结束值是否为整数
    if (!/^\d+$/.test(start) || !/^\d+$/.test(end)) {
      return false
    }

    const startNum = parseInt(start)
    const endNum = parseInt(end)

    return startNum >= 1970 && endNum <= 2099 && startNum <= endNum
  }

  // 检查单个值
  if (!/^\d+$/.test(value)) {
    return false
  }

  const num = parseInt(value)
  return !isNaN(num) && num >= 1970 && num <= 2099
}

// 生成常用的Cron表达式
export const commonCronExpressions = {
  everySecond: '* * * * * ?',
  everyMinute: '0 * * * * ?',
  everyHour: '0 0 * * * ?',
  everyDay: '0 0 0 * * ?',
  everyWeek: '0 0 0 ? * MON',
  everyMonth: '0 0 0 1 * ?',
  everyYear: '0 0 0 1 1 ?',
  workingDays: '0 0 9 ? * MON-FRI',
  weekends: '0 0 10 ? * SAT,SUN',
}

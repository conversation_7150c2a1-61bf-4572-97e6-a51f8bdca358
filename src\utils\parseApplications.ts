import type { RouteRecordRaw } from 'vue-router'
import type { IApplication } from '@/types/common'

const basePath = '/:page*'
// 使用相对路径导入布局组件
const component = () => import('../layout/index.vue')

/**
 * @description 转换成路由数据
 * <AUTHOR>
 * @date 2022-07-07
 * @param {any} data:IApplication
 * @returns {any}
 */
const resolveRoutes: (data: IApplication) => RouteRecordRaw = (data: IApplication) => {
  return {
    name: data.name,
    path: data.route + basePath,
    meta: { title: data.title, name: data.name, url: data.host, baseroute: data.route },
    component,
  }
}

/**
 * @description 解析应用数据为路由数据
 * <AUTHOR>
 * @date 2022-07-07
 * @param {any} apps:IApplication[]
 * @returns {any} RouteRecordRaw[]
 */
export const parseApplications = (apps: IApplication[]) => {
  return apps.map(item => resolveRoutes(item))
}

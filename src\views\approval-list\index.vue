<template>
  <div class="process-list-new">
    <FSpin :spinning="pageLoading">
      <div class="card-content-shadow pl24 pr24 mb16">
        <FTabSet class="cust-tab-list" :tab-list="tabList" v-model:activeKey="tabValue" @change="tabChangeFn" />
        <SearchContent v-model:query-data="queryData" />
      </div>
      <ContentTable
        class="card-content-shadow pl24 pr24 pt24 mt16"
        v-model:page-data="pageData"
        :query-data="queryData"
        :list="list"
        :loading="loading"
        @on-export-process="onExportProcessFn"
        @get-processes-list-fn="getProcessesListFn"
      />
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, provide, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { IProcessClassType } from '@/types/handle'
import { cache, getUserInfo } from '@/utils'
import SearchContent from './components/SearchContent/index.vue'
import ContentTable from './components/ContentTable/index.vue'
import { message } from '@fs/smart-design'
import { getApprovalProcessType, exportNewProcessList, getApprovalProcessNew, getApprovalProcessCountNew } from '@/api'

const { currentRoute } = useRouter()
const routerName = computed<any>(() => currentRoute.value?.name)
const processTypeData = ref<IProcessClassType[]>([])
const queryData = ref<any>({})
const tabValue = ref<number>(7)
const tabList = ref<any>([
  {
    title: '全部',
    count: 0,
    status: 6,
  },
  {
    title: '进行中',
    count: 0,
    status: 7,
  },
  {
    title: '已完成',
    count: 0,
    status: 8,
  },
  {
    title: '已终止',
    count: 0,
    status: 9,
  },
  {
    title: '待我处理',
    count: 0,
    status: 1,
  },
  {
    title: '抄送我的',
    count: 0,
    status: 5,
  },
  {
    title: '我发起的',
    count: 0,
    status: 4,
  },
])
const list = ref<any>([])
const pageData = ref<any>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const loading = ref<boolean>(false)
const pageLoading = ref<boolean>(false)
const userInfo = computed(() => getUserInfo() ?? {})

const tabChangeFn = () => {
  pageData.value.pageNum = 1
  pageData.value.pageSize = 10
  getProcessesListFn()
}

const getProcessTypes = async () => {
  try {
    pageLoading.value = true
    const res = await getApprovalProcessType()
    processTypeData.value = res.data
  } catch (error) {
    throw new Error('流程类型请求失败')
  } finally {
    pageLoading.value = false
  }
}

const deleteKeysFn = data => {
  delete data.cacheValue
  delete data.init
}

const onExportProcessFn = async () => {
  if (!list.value.length) {
    message.warning('当前页面无数据，请重新选择！')
    return
  }
  const params = { ...queryData.value, type: tabValue.value }
  deleteKeysFn(params)
  const res = await exportNewProcessList(params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success('下载成功，请在飞书查看！')
}

const getProccessQueryCountFn = async () => {
  try {
    tabList.value.forEach((item: any) => {
      item.count = 0
    })
    const data = { ...queryData.value }
    deleteKeysFn(data)
    const res = await getApprovalProcessCountNew(data)
    tabList.value[1].count = res.data?.running ?? 0
    tabList.value[2].count = res.data?.completed ?? 0
    tabList.value[3].count = res.data?.cadence ?? 0
    tabList.value[4].count = res.data?.waitDeal ?? 0
    tabList.value[5].count = res.data?.make ?? 0
    tabList.value[6].count = res.data?.meStart ?? 0
  } catch (error) {
    throw new Error('流程类型请求失败')
  }
}

const getUrlSoureFn = item => {
  if (!item?.detailsUrl) return true
  if (item.detailsUrl.includes('plm') && item.detailsUrl.includes('/material/approval/detail')) return false
  return true
}

const getProcessesListFn = async () => {
  try {
    loading.value = true
    list.value = []
    const data = { ...queryData.value, type: tabValue.value }
    cache.set(
      routerName?.value,
      JSON.stringify({
        ...(data?.cacheValue ?? {}),
        pageNum: pageData.value.pageNum,
        pageSize: pageData.value.pageSize,
        type: tabValue.value,
      })
    )
    deleteKeysFn(data)
    const res = await getApprovalProcessNew({
      ...data,
      pageNum: pageData.value.pageNum,
      pageSize: pageData.value.pageSize,
      type: tabValue.value,
    })
    list.value = (res.data?.list ?? []).map(item => {
      const handleSubmitNodeInfo = {
        hasNode: false,
        nodeList: [],
        label: '--',
      }
      const handleRefuseNodeInfo = {
        hasNode: false,
        nodeList: [],
        label: '--',
      }
      if (item?.status === 0 && getUrlSoureFn(item)) {
        ;(item?.handleNodeList ?? []).forEach(item => {
          if (
            [1, 2].includes(item?.status) &&
            (item?.userList || [])?.find(userItem => userItem.uuid === userInfo.value.uuid && userItem.status === 0)
          ) {
            handleSubmitNodeInfo.hasNode = true
            handleSubmitNodeInfo.nodeList.push(item)
            handleSubmitNodeInfo.label = '提交'
            if (item?.properties?.type === 1) {
              handleSubmitNodeInfo.label = '同意'
              handleRefuseNodeInfo.hasNode = true
              handleRefuseNodeInfo.nodeList.push(item)
              handleRefuseNodeInfo.label = '拒绝'
            }
          }
        })
      }
      handleSubmitNodeInfo?.nodeList?.length > 1 && (handleSubmitNodeInfo.label = '同意')
      return {
        ...item,
        handleSubmitNodeInfo,
        handleRefuseNodeInfo,
      }
    })
    pageData.value.total = res.data?.totalCount ?? 0
  } catch (error) {
    throw new Error('流程类型请求失败')
  } finally {
    loading.value = false
  }
}

const init = () => {
  const { cacheValue, init } = queryData.value
  init && (tabValue.value = cacheValue?.type ?? 1)
  pageData.value.pageNum = (init && cacheValue?.pageNum) || 1
  pageData.value.pageSize = (init && cacheValue?.pageSize) || 10
  getProccessQueryCountFn()
  getProcessesListFn()
}

watch(
  () => queryData.value,
  data => {
    init()
  }
)

onMounted(() => {
  getProcessTypes()
})

provide('processTypeData', processTypeData)
provide('routerName', routerName)
provide('getApprovalInfoFn', init)
</script>

<style lang="scss" scoped>
.card-content-shadow {
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
:deep(.cust-tab-list) {
  .fs-badge-count {
    position: relative;
    transform: none;
  }
  #rc-tabs-0-tab-4,
  #rc-tabs-0-tab-5,
  #rc-tabs-0-tab-7,
  #rc-tabs-0-tab-8,
  #rc-tabs-0-tab-9 {
    .fs-badge-count {
      background-color: #dddddd;
      color: #666666;
      box-shadow: 0 0 0 2px #fff;
    }
  }
}
</style>

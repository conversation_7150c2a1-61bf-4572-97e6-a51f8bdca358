<template>
  <div class="process-list-container">
    <div class="bpm-header">
      <!-- 指标中心 -->
      <TargetCenter class="_last" :data="indicatorCenterInfo" />
      <!-- 消息协同 -->
      <InfoBox :data="unreadMsg" />
    </div>
    <!-- <ContentBox :tags="tags" :active-tag="activeTag" :status="processStatus" :list="processList" :pages="pages" /> -->
    <ContentBox />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, provide } from 'vue'
import { getProcessList, getIndicatorCenterInfo, getUnreadMessage } from '@/api'
import { ProcessListParams } from '@/types/processListModel'
import { BasicPageParams } from '@/types/common'
import { IIndicatorCenterInfo, ISeeMessage } from '@/types/request'

import InfoBox from './components/InfoBox.vue'
import TargetCenter from './components/TargetCenter.vue'
import ContentBox from './components/ContentBox/IndexBox.vue'

// 流程列表状态枚举 Tab
enum ProcessStatus {
  total = 'total',
  waitDeal = 'waitDeal',
  meStart = 'meStart',
  joinSoon = 'joinSoon',
  running = 'running',
  completed = 'completed',
  draftNum = 'draftNum',
}
// const processStatus = reactive({
//   [ProcessStatus.total]: { name: '全部', number: 0 },
//   [ProcessStatus.waitDeal]: { name: '待我处理', number: 0 },
//   [ProcessStatus.meStart]: { name: '我发起的', number: 0 },
//   [ProcessStatus.joinSoon]: { name: '即将参与', number: 0 },
//   [ProcessStatus.running]: { name: '进行中', number: 0 },
//   [ProcessStatus.completed]: { name: '已完成', number: 0 },
// })

// const tags = ref<unknown[]>([{}])
const activeTag = ref<ProcessStatus>(ProcessStatus.waitDeal)
const queryData = ref<ProcessListParams>({})
const pages = ref<BasicPageParams>({ pageNum: 1, pageSize: 10, total: 0 })
const processList = ref<unknown[]>([{}])
const indicatorCenterInfo = ref<IIndicatorCenterInfo>({} as IIndicatorCenterInfo)
const unreadMsg = ref<ISeeMessage[]>([])

onMounted(() => {
  // 初始化 未读 信息
  initUnreadMessage()
  // 初始化 指标中心 数据
  initIndicatorCenterInfo()
})

// 初始化未读信息
const initUnreadMessage = async () => {
  const { data = [] } = await getUnreadMessage()
  unreadMsg.value = data
}

// 初始化 指标中心 数据
const initIndicatorCenterInfo = async () => {
  const data = await getIndicatorCenterInfo()
  indicatorCenterInfo.value = data
}

// 获取流程 标签 数据
// const getProcessTags = async () => {
//   const { data = {} } = await getProccessCount()
//   Object.entries(data).forEach(([key, value]) => {
//     processStatus[key as ProcessStatus].number = value as number
//   })
// }

// 查询流程列表数据
const queryProcessList = async (data: ProcessListParams, page: BasicPageParams) => {
  const {
    data: { list, totalCount },
  } = await getProcessList(data, page)
  processList.value = list
  pages.value.total = totalCount
}

// 分页变更
const handlePageChange = (page: BasicPageParams) => {
  pages.value = page
  queryProcessList(queryData.value, pages.value)
}

// 标签变更
const handleActiveTagChange = (tag: ProcessStatus) => {
  activeTag.value = tag
  queryProcessList(queryData.value, pages.value)
}

// 依赖注入
provide('queryProcessList', queryProcessList)
provide('handlePageChange', handlePageChange)
provide('handleActiveTagChange', handleActiveTagChange)
</script>

<style lang="scss" scoped>
.bpm-header {
  display: flex;
  height: 169px;

  > * {
    flex: 1;
  }

  > ._last {
    flex: 0 0 58%;
    margin-right: 20px;
  }
}
</style>

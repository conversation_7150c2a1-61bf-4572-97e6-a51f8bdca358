<template>
  <div class="manage-role-list">
    <!-- <Breadcrumb class="none-container-padding" /> -->
    <div class="card-content-shadow pt24 pl24 pr24 mb16">
      <SearchContent v-model:query-data="queryData" />
    </div>
    <div class="card-content-shadow pt24 pl24 pr24">
      <div class="flex space-between mb16">
        <div class="fw-500 f14">角色列表</div>
        <FSpace :size="[12]" wrap class="handle-row-box">
          <FButton
            v-if="hasPublish"
            type="default"
            @click="publishVisible = true"
            class="add-button"
            :disabled="selectedRowKeys.length === 0"
          >
            <i class="iconpiliangtianjia iconfont marginR5 fontSize14"></i>批量发布
          </FButton>
          <FButton class="export-btn" type="default" @click="handleImport">
            <i class="icontubiao_daoru iconfont marginR4"></i>角色导入
          </FButton>
          <FButton type="primary" class="mr6" @click="handleRoleRef?.onOpenFn('addRole')">
            <template #icon><i class="iconfont icontubiao_tianjia1 marginR4" /></template>
            新增角色</FButton
          >
        </FSpace>
      </div>
      <FTable
        class="table-warp"
        :columns="columns"
        :loading="loading"
        :data-source="dataList"
        :row-key="(data:any) => data.roleCode"
        sticky
        :scroll="{ x: 'min-content' }"
        :pagination="{
          total: paging.total,
          current: paging.pageNum,
          pageSize: paging.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
          onChange: onPaginationChangeFn
        }"
        :row-selection="getRowSelectionConfig()"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="['roleName'].includes(column.dataIndex)">
            <span class="code-link" @click="onJumpRoleDetial(record)">{{ record[column.dataIndex] || '--' }}</span>
          </template>
          <template v-if="['status'].includes(column.dataIndex)">
            <FTag :size="18" :color="getValueFn(statusOptions, record[column.dataIndex])?.color">{{
              getValueFn(statusOptions, record[column.dataIndex])?.label
            }}</FTag>
          </template>
          <template v-if="column.key === 'publishStatus'">
            <span v-if="record.publishStatus == 1" class="enablePublish">已发布</span>
            <span v-if="record.publishStatus == 0" class="disablePublish">未发布</span>
          </template>
          <template v-if="['processConfigId'].includes(column.dataIndex)">
            <span>{{ getValueFn(processList, record[column.dataIndex], 'id')?.processName ?? '--' }}</span>
          </template>
          <template v-if="['type'].includes(column.dataIndex)">
            <span>{{ getValueFn(roleTypeList, record[column.dataIndex])?.label }}</span>
          </template>
          <template v-if="['roleDesc'].includes(column.dataIndex)">
            <MoreTextTips v-if="record[column.dataIndex]" :line-clamp="1">
              {{ record[column.dataIndex] }}
            </MoreTextTips>
          </template>
          <template v-if="['createTime', 'updateTime'].includes(column.dataIndex)">
            <span>{{
              (record[column.dataIndex] && transformDate(record[column.dataIndex], 'YYYY-MM-DD HH:mm:ss')) || '--'
            }}</span>
          </template>
          <template v-if="['handle'].includes(column.dataIndex)">
            <FSpace :size="[8]">
              <TipBtn
                has-pop
                tip-title="删除"
                pop-title="确定删除该角色吗？"
                @on-confirm-fn="onDeleteConfirmFn(record)"
                v-if="record.publishTime === null"
              >
                <i class="iconfont icontubiao_xietongshanchu hover-btn color999"></i>
              </TipBtn>
              <TipBtn tip-title="复制">
                <i
                  class="iconfont icontubiao_fuzhi hover-btn color999"
                  @click="copyRoleRef?.onOpenFn('copyRole', record)"
                ></i>
              </TipBtn>
              <TipBtn tip-title="重置" v-if="hasPublishReset && record.publishTime && record.publishStatus === 0">
                <i class="iconfont icontubiao_shuaxin2 hover-btn color999" @click="reset(record)"></i>
              </TipBtn>
            </FSpace>
          </template>
        </template>
      </FTable>
    </div>
    <ImportModal
      v-model:visible="importData.visible"
      :loading="importData.loading"
      :title="importData.title"
      tip
      template
      @download="onDownloadFn"
      @submit="onSubmitFn"
    />
    <HandleRole ref="handleRoleRef" @update-change="queryDataList" />
    <CopyRole ref="copyRoleRef" @update-change="queryDataList" />
    <PublishModal v-model:value="publishVisible" :publish-options="publishEnvOptions" @submit="allPublish" />
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, reactive, provide, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { FButton, FTag, message } from '@fs/smart-design'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import SearchContent from './components/SearchContent/index.vue'
import MoreTextTips from '@/components/MoreTextTips/index'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import ImportModal from './components/ImportModal/index.vue'
import HandleRole from './components/HandleRole/index.vue'
import CopyRole from './components/CopyRole/index.vue'
import { manageRoleList, deleteManageRole, importManageRole, GetTagAndNode, rollbackRole, synRole } from '@/api'
import { cache, transformDate, download } from '@/utils'
import { getValueFn } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'
import PublishModal from '@/components/PublishModal/index.vue'
import usePublish from '@/hooks/usePublish'

const { hasEnvPublish, hasPublish, hasPublishReset, publishEnvOptions, publishVisible } = usePublish()
const { currentRoute, push } = useRouter()
const routerName = computed<any>(() => currentRoute.value?.name)
const loading = ref(false)
const paging = reactive<any>({ pageNum: 1, pageSize: 10, total: 0 })
const dataList = ref<any[]>([])
const columns = ref(
  [
    { title: '角色名称', dataIndex: 'roleName', key: 'roleName', width: 132, fixed: 'left' },
    { title: '角色编码', dataIndex: 'roleCode', key: 'roleCode', width: 132 },
    { title: '所属流程', dataIndex: 'processConfigId', key: 'processConfigId', width: 132 },
    { title: '角色类型', dataIndex: 'type', key: 'type', width: 132 },
    hasEnvPublish.value && { title: '发布状态', dataIndex: 'publishStatus', key: 'publishStatus', width: 132 },
    { title: '是否启用', dataIndex: 'status', key: 'status', width: 132 },
    { title: '备注说明', dataIndex: 'roleDesc', key: 'roleDesc', width: 260 },
    { title: '创建人', dataIndex: 'createBy', key: 'createBy', width: 132 },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 168 },
    { title: '修改人', dataIndex: 'updateBy', key: 'updateBy', width: 132 },
    { title: '修改时间', dataIndex: 'updateTime', key: 'updateTime', width: 168 },
    { title: '操作', dataIndex: 'handle', key: 'handle', width: 110, fixed: 'right' },
  ].filter(Boolean)
)
const queryData = ref<any>({})
const statusOptions = ref([
  { label: '否', value: 0, color: 'error' },
  { label: '是', value: 1, color: 'success' },
])
const roleTypeList = ref<any[]>([
  { value: 1, label: '铁六角' },
  { value: 2, label: '轮询' },
  { value: 3, label: '表达式' },
  { value: 4, label: '抄送' },
  { value: 5, label: '接口类型' },
  { value: 6, label: '抄送包含创建人' },
])
const synTypeList = ref<any[]>([
  { value: '1', label: '同步工时' },
  { value: '2', label: '同步mom' },
  { value: '3', label: '同步关联流程' },
  { value: '4', label: '同步SAP' },
])
const processList = ref<any>([])
const importData = reactive<any>({
  visible: false,
  loading: false,
  title: '角色导入',
})
const handleRoleRef = ref()
const copyRoleRef = ref()
const getRowSelectionConfig = () => {
  return {
    selectedRowKeys: selectedRowKeys.value,
    onChange: onSelectChange,
    getCheckboxProps: record => ({
      disabled: record.publishStatus === 1,
    }),
  }
}
// 表格勾选配置项
const selectedRowKeys = ref<string[]>([])

// 表格勾选
function onSelectChange(keys: string[], _selectedRows: object[]) {
  selectedRowKeys.value = keys
}
const reset = record => {
  rollbackRole({
    type: 1,
    codeList: [record.roleCode],
  }).then(res => {
    if (res.code == 200) {
      paging.pageNum = 1
      message.success('重置成功')
      queryDataList()
    }
  })
}
const allPublish = (publishData: any) => {
  synRole({
    type: publishData?.type || 1,
    codeList: selectedRowKeys.value,
  }).then(res => {
    if (res.code == 200) {
      paging.pageNum = 1
      message.success('批量发布成功')
      queryDataList()
    }
  })
}
const onDownloadFn = () => {
  download(
    'https://pvt-doc.whgxwl.com/default/13665200b24745938d0fdf97c56ee51f?AWSAccessKeyId=AKIAZ36WLN7CVAY2ALUW&Expires=1840443902&Signature=Fki3neW3hAXtVP3W%2BhU%2B9agS%2Fxw%3D',
    '角色导入模板.xls'
  )
}

const onSubmitFn = async (data: any) => {
  importData.loading = true
  try {
    const formData = new FormData()
    formData.set('file', data.files[0].originFileObj)
    const res = await importManageRole(formData)
    if (res.code !== 200) throw new Error(res.msg)
    message.success(res?.msg || '导入成功')
    queryDataList()
    importData.visible = false
  } finally {
    importData.loading = false
  }
}

const onDeleteConfirmFn = async (record: any) => {
  const res = await deleteManageRole({ id: record.id })
  if (res.code !== 200) throw new Error(res.msg)
  paging.pageNum = 1
  message.success('删除成功')
  queryDataList()
}

const onJumpRoleDetial = (record: any) => {
  push({ name: 'manageRoleDetail', params: { id: record.id } })
}

const handleImport = () => {
  importData.visible = true
}

// 查询列表
const queryDataList = async () => {
  try {
    loading.value = true
    const data = { ...queryData.value }
    cache.set(
      routerName?.value,
      JSON.stringify({
        ...(data?.cacheValue ?? {}),
        pageNum: paging.pageNum,
        pageSize: paging.pageSize,
      })
    )
    delete data.cacheValue
    const res = await manageRoleList({ ...data, currPage: paging.pageNum, pageSize: paging.pageSize })
    dataList.value = res?.data?.list || []
    paging.total = res?.data?.totalCount || 0
  } finally {
    loading.value = false
    selectedRowKeys.value = []
  }
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  paging.pageNum = current
  paging.pageSize = pageSize
  queryDataList()
}

// 查询列表
const onGetSearchData = (data: any) => {
  queryData.value = data
  paging.pageNum = data?.cacheValue?.pageNum || 1
  paging.pageSize = data?.cacheValue?.pageSize || 10
  queryDataList()
}

const getProcessListFn = async () => {
  const res = await GetTagAndNode()
  if (res.code !== 200) throw new Error(res.msg)
  processList.value = res.data || []
}

watch(
  () => queryData.value,
  val => {
    onGetSearchData(val)
  },
  { deep: true }
)

provide('statusOptions', statusOptions)
provide('roleTypeList', roleTypeList)
provide('synTypeList', synTypeList)
provide('processList', processList)
onMounted(() => {
  !processList.value?.length && requestIdleCallback(getProcessListFn)
})
</script>
<style scoped lang="scss">
.manage-role-list {
  .none-container-padding {
    // margin-top: -20px;
  }
  .card-content-shadow {
    background: #ffffff;
    // box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .code-link {
    cursor: pointer;
    color: #378eef;
  }
  .mr6 {
    margin-right: 6px;
  }
  .mr4 {
    margin-right: 4px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .empty-content {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .count-info-content {
    line-height: 18px;
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
}
.enablePublish {
  display: inline-block;
  width: 44px;
  height: 18px;
  text-align: center;
  background: #eafaf2;
  border-radius: 3px;
  font-size: 12px;
  color: #2fcc83;
  line-height: 18px;
}
.disablePublish {
  display: inline-block;
  width: 44px;
  height: 18px;
  text-align: center;
  background: #eeeeee;
  border-radius: 3px;
  font-size: 12px;
  color: #999999;
  line-height: 18px;
}
</style>

<template>
  <div class="dict-tree-node">
    <div class="node-content" :style="{ paddingLeft: `${level * 20}px` }">
      <div class="node-left">
        <div class="drag-handle">
          <i class="icon iconfont icontubiao_tuodong1 lh22"></i>
        </div>

        <div class="node-box">
          <div class="item-box name-box">
            <FInput v-model:value="node.originalName" placeholder="请输入字典名称" />
          </div>
          <div class="item-box value-box">
            <FInput v-model:value="node.value" placeholder="请输入字典值" />
          </div>
        </div>
      </div>

      <!-- 操作按钮 - 支持插槽自定义 -->
      <div class="node-actions" @click.stop>
        <!-- 自定义操作按钮插槽 -->
        <slot name="actions" :node="node" :level="level" :index="index" :parentNodeChildren="parentNodeChildren">
        </slot>
      </div>
    </div>

    <!-- 子节点 -->
    <div class="node-children" v-if="hasChildren">
      <draggable
        v-model="nodeChildren"
        :group="getDragGroup()"
        item-key="id"
        handle=".drag-handle"
        :animation="200"
        ghost-class="ghost-item"
        chosen-class="chosen-item"
      >
        <template #item="{ element, index }">
          <DictTreeNode :parent-node-children="nodeChildren" :node="element" :level="level + 1" :index="index">
            <!-- 传递操作按钮插槽 -->
            <template #actions="slotProps">
              <slot name="actions" v-bind="slotProps" />
            </template>
          </DictTreeNode>
        </template>
      </draggable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRefs, withDefaults } from 'vue'
import draggable from 'vuedraggable'

// 定义组件属性
const props = withDefaults(
  defineProps<{
    parentNodeChildren: any
    node: any
    level?: number
    index?: number
  }>(),
  {
    level: 0,
    index: 0,
  }
)

const emit = defineEmits([])
const { node } = toRefs(props)
const nodeChildren = computed({
  get: () => {
    return node.value.dict_tree_children || []
  },
  set: value => {
    node.value.dict_tree_children = value
  },
})
const hasChildren = computed(() => {
  return node.value.dict_tree_children && node.value.dict_tree_children.length > 0
})

// 获取拖拽组
const getDragGroup = () => {
  return `dict-tree-level-${props.level + 1}-parent-${(node.value as any).dict_tree_id}`
}
</script>

<style scoped lang="scss">
.dict-tree-node {
  .node-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }
    &:hover {
      background: #f8f8f8;
    }
  }

  .drag-handle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-right: 8px;

    .icon {
      cursor: move;
      color: #999;
      &:hover {
        color: #666;
      }
    }
  }

  .node-left {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .node-box {
      display: flex;
      flex-wrap: nowrap;
      flex: 1;
      .item-box {
        flex: 1;
        margin-right: 8px;
        &:last-child {
          margin-right: 0;
        }
      }
      .value-box {
        flex: 1 1 282px;
        max-width: 282px;
      }
    }
  }
  .node-actions {
    margin-left: 12px;
    width: 44px;
  }
}

.ghost-item {
  opacity: 0.5;
  background-color: #f0f0f0;
}

.chosen-item {
  background-color: #e6f7ff;
  box-shadow: 0px 4px 12px 1px rgba(88, 98, 110, 0.14);
}

.ghost-item {
  opacity: 0.5;
}
</style>

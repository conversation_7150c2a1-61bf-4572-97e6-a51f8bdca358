<template>
  <div class="table-box-container">
    <div class="header">
      <span class="title">{{ i18n.t('需求列表') }}</span>
      <div class="table-handle">
        <FButton class="export-btn marginR12" @click="emits('onExportProcess')">
          <i class="icontubiao_xiazai iconfont"></i>{{ i18n.t('下载') }}
        </FButton>
        <FButton type="primary" class="add-btn marginR12" @click="onAddProcessFn">
          <i class="icontubiao_tianjia1 iconfont"></i>{{ i18n.t('新增') }}
        </FButton>
        <component :is="columnsConfig.Operation"></component>
      </div>
    </div>
    <FTable
      :data-source="list"
      :loading="loading"
      :columns="columnsConfig.tableColumns"
      :row-key="(data:any) => data.id"
      :row-selection="rowSelection"
      table-layout="fixed"
      sticky
      :scroll="{ x: '100%' }"
      :pagination="{
        total: page.total,
        current: page.currPage,
        pageSize: page.pageSize,
        showTotal: (total: number) => `${i18n.t('共')}${total}${i18n.t('条')}`,
        showQuickJumper: true,
        showSizeChanger: true,
      }"
      @change="handleTableChange"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'processInstanceCode'">
          <div class="code-box">
            <span class="code-link marginR4" @click="onJumpDemandDetial(record)">{{ text || '--' }}</span>
            <TipBtn :tip-title="i18n.t('复制编号及详情页链接')">
              <i class="iconfont icontubiao_fuzhi copy-text" :data-clipboard-text="copyTest" @click="copy(record)" />
            </TipBtn>
          </div>
          <MoreTextTips>
            <div>{{ record?.topicName || '--' }}</div>
          </MoreTextTips>
        </template>
        <template v-if="column.dataIndex === 'node'">
          <div>{{ text || '--' }}</div>
          <div class="color999">{{ record?.nodeSuperviser || '--' }}</div>
          <div class="color999">
            <span>{{ i18n.t('耗时：') }}</span>
            <span>{{ (record?.realityDuration && record?.realityDuration + '天') || '--' }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'isUrgent'">
          <span :class="['level-tag', getLevelFn(record).class]">{{ getLevelFn(record).text }}</span>
        </template>
        <template
          v-if="['scene', 'what', 'why', 'how', 'when', 'sumUp', 'acceptanceResults'].includes(column.dataIndex)"
        >
          <MoreTextTips>
            <span>{{ text }}</span>
          </MoreTextTips>
        </template>
        <template v-if="['competitor'].includes(column.dataIndex) && text">
          <MoreTextBtn>
            <span>{{ text }}</span>
          </MoreTextBtn>
        </template>
        <template v-if="column.dataIndex === 'zlfile' && text && text?.length">
          <div class="file-box" v-for="item in text || []" :key="item.url">
            <img class="icon-pic mr4" :src="getPicFn(item)" />
            <span class="text-name" :title="item.name" @click="onPreviewFn(item)">{{ item.name }}</span>
            <i class="iconfont icontubiao_xiazai ml4" @click="downLoadFile(item)"></i>
          </div>
        </template>
        <template v-if="column.dataIndex === 'customersIndustry'">
          <div>
            <span>{{ i18n.t('行业') }}：</span>
            <span>{{ record?.customersIndustry }}</span>
          </div>
          <div>
            <span>{{ i18n.t('区域') }}：</span>
            <span>{{ record?.countriesName }}</span>
          </div>
          <div>
            <span>{{ i18n.t('等级') }}：</span>
            <span>{{ record?.customerslevel }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'relevanceList' && text && text?.length">
          <MoreTextTips :lineClamp="3">
            <div class="relevance-box" v-for="item in text || []" :key="item.id">
              <span class="code-link" @click="onJumpDemandDetial(item, 'instanceId')">{{
                item?.relevanceNumber || '--'
              }}</span>
              <div>
                <span>{{ i18n.t('当前节点') }}：</span>
                <span>{{ item.currentStage }}</span>
              </div>
              <div>
                <span>{{ i18n.t('当前负责人') }}：</span>
                <span>{{ item.nodeOwner }}</span>
              </div>
            </div>
          </MoreTextTips>
        </template>
        <template v-if="column.dataIndex === 'creatorName'">
          <div>{{ record?.creatorName || '--' }}</div>
          <div>{{ record?.createdTime || '--' }}</div>
        </template>
        <template v-if="column.dataIndex === 'handle'">
          <FRow :gutter="[12, 0]">
            <FCol :span="6">
              <TipBtn :tip-title="i18n.t('编辑')">
                <i class="iconfont icontubiao_xietongbianji cursor color4677C7" @click="onJumpDemandDetial(record)" />
              </TipBtn>
            </FCol>
            <FCol :span="6">
              <TipBtn :tip-title="i18n.t('消息')">
                <i class="iconfont icontubiao_xiaoxi cursor color4677C7" @click="goMessageInfo(record)" />
              </TipBtn>
            </FCol>
          </FRow>
        </template>
      </template>
    </FTable>
    <!-- 消息协同 -->
    <FDrawer
      class="custom-class"
      :title="i18n.t('消息协同')"
      :width="380"
      :mask-closable="true"
      v-model:visible="messageFlag"
      placement="right"
    >
      <Message
        :process-no="processInfo.processInstanceCode"
        :instance-id="(processInfo.instanceId as unknown as number)"
        :process-name="processInfo.topicName"
        :params-wrapper="paramsWrapper"
      >
        <template #header>
          <div class="message-header-box">
            <p class="title">
              <i class="icon" /><span>{{ `${processInfo.topicName}：${processInfo.processInstanceCode}` }}</span>
            </p>
          </div>
        </template>
      </Message>
      <!-- <ChatRoom
        :process-no="processInfo.processInstanceCode"
        :instance-id="processInfo.instanceId"
        :process-name="processInfo.topicName"
        :params-wrapper="paramsWrapper"
      /> -->
    </FDrawer>
  </div>
</template>

<script setup lang="ts">
import { BasicPageParams } from '@/types/processBoard'
import { PageDemandRes } from '@/types/processManagement'
import { columns } from './tableConfig'
import { computed, ref, inject, Ref, watch, reactive } from 'vue'
import { useI18n, BAESURL, S3URL } from '@/utils'
import { useRoute, useRouter } from 'vue-router'
import Clipboard from 'clipboard'
import { message } from '@fs/smart-design'
import TipBtn from '../TipBtn/index.vue'
import MoreTextBtn from '../MoreTextBtn/index.vue'
import MoreTextTips from '@/components/MoreTextTips/index'
import pdf from '@/assets/images/pdf.png'
import chm from '@/assets/images/chm.png'
import divPic from '@/assets/images/div.png'
import other from '@/assets/images/other.png'
import png from '@/assets/images/png.png'
import ppt from '@/assets/images/ppt.png'
import excel from '@/assets/images/excel.png'
import word from '@/assets/images/word.png'
import txt from '@/assets/images/txt.png'
import Message from '@/components/Message/index.vue'
import ChatRoom from '@/components/ChatRoom/index.vue'
import { tableColumnKeys, useTableColumn } from '@/components/TableOperation/index'

const i18n = useI18n()
const route = useRoute()
const router = useRouter()

type propsType = {
  list: PageDemandRes[]
  page: BasicPageParams
  loading: boolean
  tabValue: any
}
const props = withDefaults(defineProps<propsType>(), {
  list: () => [],
  page: () => ({}),
  loading: false,
  tabValue: 0,
})

const emits = defineEmits(['onPageChange', 'onExportProcess', 'update:page'])
const { id, processDefineKey } = route.params as { id: string; processDefineKey: string }
const page = computed<BasicPageParams>({
  get: () => props.page,
  set: val => emits('update:page', val),
})
const selectedKeys = ref<string[]>([])
const rowSelection = ref({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[]) => {
    selectedKeys.value = selectedRowKeys
  },
})
const copyTest = ref('')
const messageFlag = ref<boolean>(false)
const processInfo = ref<any>({
  processType: '',
  topicName: '',
  processInstanceCode: '',
  instanceId: '',
})
const paramsWrapper = <T>(
  data: T
): T & { processType: string; processInstanceCode: string; instanceTopicName: string } => {
  return {
    ...data,
    processType: processInfo.value.processType,
    instanceTopicName: processInfo.value.topicName,
    processInstanceCode: processInfo.value.processInstanceCode,
  }
}
const processConfig = inject<Ref>('processConfig') // 流程类型
const columnsConfig = reactive<any>({})
const cacheKey = computed(() => {
  if (route.params?.demandType) return route.params?.demandType
  return 'noCache'
})

const copy = (data: any) => {
  copyTest.value = `${data.topicName} ${data.processInstanceCode} ${BAESURL}/bpm-manage/process/detail/${data.id}`
  const clipboard = new Clipboard('.copy-text')
  clipboard.on('success', e => {
    message.success(i18n.t('复制成功'))
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    message.warning(i18n.t('复制失败'))
    clipboard.destroy()
  })
}

const goMessageInfo = (record: any) => {
  processInfo.value = {
    processType: processConfig?.value?.processName || '',
    topicName: record.topicName || '',
    processInstanceCode: record.processInstanceCode || '',
    instanceId: record?.id || '',
  }
  messageFlag.value = true
}

const getPicFn = (item: any) => {
  const type = item.name.substring(item.name.lastIndexOf('.') + 1).toLowerCase()
  if (['pdf'].includes(type)) {
    return pdf
  } else if (['chm'].includes(type)) {
    return chm
  } else if (['png', 'jpg'].includes(type)) {
    return png
  } else if (['ppt'].includes(type)) {
    return ppt
  } else if (['txt'].includes(type)) {
    return txt
  } else if (['div'].includes(type)) {
    return divPic
  } else if (['xlsx', 'xlsm', 'xlsb', 'xls'].includes(type)) {
    return excel
  } else if (['doc', 'docm', 'docx', 'dot'].includes(type)) {
    return word
  }
  return other
}

const onPreviewFn = (item: any) => {
  const httpUrl = `${S3URL}/api/feishu/fsDownload/${item.file_token}?url=${item.url}&type=1`
  window.open(httpUrl, '_blank')
}

const downLoadFile = (item: any) => {
  const httpUrl = `${S3URL}/api/feishu/fsDownload/${item.file_token}?url=${item.url}`
  window.open(httpUrl, '_blank')
}

// 需求详情跳转
const onJumpDemandDetial = (record: any, recordKey: any = 'id') => {
  const options = { name: 'ProcessDetail', params: { id: record[recordKey] } }
  router.push(options)
}

const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  page.value.currPage = pagination.current
  page.value.pageSize = pagination.pageSize
  const order = (sorter?.order && sorter.order.slice(0, sorter.order.length - 3)) || undefined
  emits('onPageChange', (order && { sort: `${sorter?.field}:${order}` }) || null)
}

const onAddProcessFn = () => {
  const params = { id, processDefineKey }
  router.push({
    name: 'DemandAdd',
    params,
    query: {
      localName: route.name as string,
    },
  })
}

const getLevelFn = (data: any = {}) => {
  switch (data?.level) {
    case '0':
      return {
        class: 'warning',
        text: i18n.t('P1-不重要但紧急'),
      }
    case '1':
      return {
        class: 'danger',
        text: i18n.t('P0-重要且紧急'),
      }
    case '2':
      return {
        class: 'success',
        text: i18n.t('P2-重要不紧急'),
      }
    case '3':
      return {
        class: 'default',
        text: i18n.t('P3-不重要不紧急'),
      }
    default:
      return {
        class: '',
        text: '--',
      }
  }
}

watch(
  () => props.tabValue,
  tabValue => {
    Object.assign(columnsConfig, useTableColumn(columns.value, `processManagementTable_${cacheKey.value}_${tabValue}`))
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
.table-box-container {
  background: #ffffff;
  box-shadow: 0px 2px 5px 0px rgba(176, 177, 178, 0.4);
  border-radius: 4px;
  padding: 24px 24px 0 24px;
  margin-top: 12px;
  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      line-height: 22px;
    }
    .btn {
      font-size: 12px;
      font-weight: 400;
      color: #378eef;
      line-height: 18px;
      cursor: pointer;
    }
    .table-handle {
      display: flex;
      align-items: center;
    }
  }
  :deep(.fs-table-cell) {
    &:empty {
      &::after {
        content: '--';
      }
    }
  }
  :deep(.fs-table-cell-fix-left-last:empty::after) {
    position: relative;
    box-shadow: none;
  }
  .code-box {
    display: flex;
    align-items: center;
    .level-tag {
      display: inline-block;
      padding: 0 3px;
      height: 18px;
      line-height: 18px;
      background: #fdecec;
      border-radius: 3px;
      border: 1px solid #f68d8d;
      color: #f04141;
    }
    .copy-text {
      color: #d8d8d8;
      cursor: pointer;
    }
  }
  .code-link {
    cursor: pointer;
    color: #378eef;
  }
  .file-box {
    display: flex;
    height: 32px;
    align-items: center;
    &:hover {
      background-color: #f1f4f8;
      border-radius: 2px;
      .icontubiao_xiazai {
        display: inline-block;
      }
    }
    .icon-pic {
      width: 16px;
    }
    .text-name {
      max-width: 78%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }
    .icontubiao_xiazai {
      display: none;
      flex: 1;
      text-align: right;
      font-size: 16px;
      color: #999;
      cursor: pointer;
    }
  }
  .level-tag {
    display: inline-block;
    padding: 0 3px;
    height: 18px;
    line-height: 18px;
    border-radius: 3px;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.default {
      border: 1px solid #2fcc83;
      color: #2fcc83;
      background: #eafaf2;
    }

    &.success {
      border: 1px solid #87bbf5;
      color: #378eef;
      background: #ebf3fd;
    }

    &.warning {
      border: 1px solid #fa8f23;
      color: #fa8f23;
      background: #fef4e9;
    }

    &.danger {
      border: 1px solid #f68d8d;
      color: #f04141;
      background: #fdecec;
    }
  }
  :deep(.column-operated-icon) {
    margin-top: -4px;
  }
  :deep(.fs-table-tbody) {
    tr > td {
      background: #fff;
    }
  }
  :deep(.fs-table-container) {
    &::after {
      box-shadow: none;
    }
  }
  :deep(.fs-table-content) {
    padding-bottom: 4px;
  }
  .fei-su-pagination {
    padding-top: 11px;
  }
}
.message-header-box {
  height: 40px;
  margin: 0px 20px 0;
  border-bottom: 1px solid #eeeeee;
  display: flex;
  align-items: center;
  .title {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;
    i {
      display: inline-block;
      width: 14px;
      height: 12px;
      background: url('~@/assets/images/forwarder_detial_title01.png') repeat no-repeat;
      background-size: 100% 100%;
      margin-right: 5px;
      flex-shrink: 0;
    }
    span {
      flex: 1;
      font-size: 12px;
      color: #999999;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>

<template>
  <div class="search-content-container">
    <div class="content-box flex" :class="[showMoreFlag && 'show-more']">
      <div class="left-box" ref="searchcontentBox">
        <template v-for="item of search.options" :key="item.componentValueKey">
          <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue" />
        </template>
      </div>
      <div class="right-box flex">
        <div class="more-box flex ml4" v-show="showMoreBtn" @click="showMoreFlag = !showMoreFlag">
          <span>
            {{ (showMoreFlag && i18n.t('收起')) || i18n.t('展开') }}
          </span>
          <i class="iconfont iconjiantouxia" :class="[(showMoreFlag && 'iconjiantoushang') || 'iconjiantouxia']"></i>
        </div>
        <div class="more-box flex ml8" v-show="search.clearFlag" @click="onClearSearchDataFn">
          <span>
            {{ i18n.t('清空全部') }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@/views/message-template/components/SearchContent/search'
import { onMounted, computed, ref, onBeforeUnmount, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { useI18n } from '@/utils'
import { getDepartment, getSearchConfigById } from '@/api'
import { getDictionary } from '@/api/processManagement'
import { BasicPageParams } from '@/types/processBoard'

type propsType = {
  tabValue: number
  pageData: BasicPageParams
}
const props = defineProps<propsType>()
const store = useStore()
const i18n = useI18n()
const route = useRoute()
const emits = defineEmits(['onGetSearchData', 'update:tabValue', 'update:pageData'])
const { id } = route.params as { id: string }
const showMoreFlag = ref<boolean>(false)
const showMoreBtn = ref<boolean>(false)
const searchcontentBox = ref<HTMLElement>()
const allUserList = computed(() => store.state.user.allUser || [])
const dictionaryList = ref([])
const searchObj = ref({})
const search = new Search()
const roleLists = ref([])
const cacheKey = computed(() => {
  if (route.params?.demandType) return route.params?.demandType
  return 'noCache'
})
const pageData = computed({
  get: () => props.pageData,
  set: val => emits('update:pageData', val),
})
const tabValue = computed({
  get: () => props.tabValue,
  set: val => emits('update:tabValue', val),
})
const setSearchConfig = async (processConfigId: number) => {
  const res = await getSearchConfigById(processConfigId)
  if (res.code === 200 && res?.data.length) {
    const searchConfigList = res.data
      .filter(item => item.moduleType === 'SELECT')
      .map(item => {
        return {
          componentName: 'FSelect',
          componentValueKey: `${item.searchType}:${item.field}:${item.valueType}`,
          componentAttrs: {
            class: 'width120 marginR12 marginB24',
            pressLine: computed(() => item.name),
            placeholder: computed(() => i18n.t('请选择')),
            showSearch: true,
            optionFilterProp: 'label',
            allowClear: true,
            options: computed(() => item.valueList),
            onChange: (value: any, option: any) => {
              onGetSearchDataFn()
            },
          },
          setComponentValueFormat: (data: any) => {
            search.setOptions(
              `${item.searchType}:${item.field}:${item.valueType}.componentValue`,
              (data || {})?.componentValue || undefined
            )
          },
          getComponentValueFormat: (value: any, argsValue: any) => {
            return {
              searchObj: Object.assign(searchObj.value, {
                [`${item.searchType}:${item.field}:${item.valueType}`]: value,
              }),
            }
          },
        }
      })
    search.addOptions(searchConfigList)
  }
}

const handleTree = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      const data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && handleTree(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

const checkChildren = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.uuid && cur.name && Object.keys(cur).length === 2) {
      users.push(cur.uuid)
    } else if (cur.departmentChildrens && cur.departmentChildrens.length) {
      checkChildren(cur.departmentChildrens, users)
    }
    return users
  }, users)
}
const configList = [
  {
    componentName: 'FSelect',
    componentValueKey: 'source',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('需求类型')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['source'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('source.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'demand',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('需求来源')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['xq_demand'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('demand.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'level',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('需求等级')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['isUrgent'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('level.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'processInstanceCode',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('需求编号')),
      placeholder: computed(() => i18n.t('需求编号')),
      allowClear: true,
      type: 'search-clear',
      onSearch: () => {
        onGetSearchDataFn()
      },
      onClear: () => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('processInstanceCode.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'queryInput',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => i18n.t('快速搜索')),
      placeholder: computed(() => i18n.t('关联产品/需求主题')),
      allowClear: true,
      type: 'search-clear',
      onSearch: () => {
        onGetSearchDataFn()
      },
      onClear: () => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('queryInput.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'cycleRating',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('周期评级')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['xq_zqpj'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('cycleRating.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'instanceStatus',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('需求状态')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => [
        { label: i18n.t('进行中'), value: 0 },
        { label: i18n.t('已完成'), value: 1 },
        { label: i18n.t('已办结'), value: 2 },
      ]),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('instanceStatus.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'productLine',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('产品线')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['type'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('productLine.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'pbu',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('PBU')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['pbu'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('pbu.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FCascader',
    componentValueKey: 'projectUuidList',
    componentArgsValue: {},
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      multiple: true,
      showArrow: true,
      placeholder: i18n.t('请选择'),
      pressLine: i18n.t('参与人员'),
      fieldNames: { label: 'name', value: 'uuid', children: 'departmentChildrens' },
      maxTagCount: 'responsive',
      optionFilterProp: 'name',
      showSearch: true,
      showCheckedStrategy: 'SHOW_CHILD',
      allowClear: true,
      options: computed(() => roleLists?.value),
      onChange: (value: any, selectedOptions: any) => {
        const selectLists =
          selectedOptions.map((item: any) => {
            return item[item.length - 1]
          }) || []
        search.setOptions(
          'projectUuidList.componentArgsValue',
          Object.assign(search.getOptions('projectUuidList.componentArgsValue'), { selectLists })
        )
        onGetSearchDataFn()
      },
    },
    getComponentValueFormat: (value: any, argsValue: any) => {
      const projectUuidList = checkChildren(argsValue?.selectLists ?? [])
      if (projectUuidList.length) {
        return { projectUuidList: projectUuidList }
      } else {
        search.setOptions('projectUuidList.componentValue', undefined)
        return undefined
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('projectUuidList.componentValue', (data || {})?.componentValue || undefined)
      search.setOptions('projectUuidList.componentArgsValue', (data || {})?.componentArgsValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'creatorUuid',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('创建人')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: allUserList,
      optionFilterProp: 'name',
      fieldNames: { label: 'name', value: 'uuid' },
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('creatorUuid.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'accept',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('是否承接需求')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['xq_sfcj'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('accept.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'customersLevel',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('客户等级')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['customers_level'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('customersLevel.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'customersIndustry',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('客户行业')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['customers_industry'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('customersIndustry.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'countriesName',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('客户区域')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['countries_name'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('countriesName.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'adopt',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => i18n.t('是否验收通过')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      allowClear: true,
      options: computed(() => dictionaryList.value.filter((item: any) => ['xq_sfjs'].includes(item.field))),
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('adopt.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FRangePicker',
    componentValueKey: 'time',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => i18n.t('创建时间')),
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onChange: (value: any, option: any) => {
        onGetSearchDataFn()
      },
    },
    getComponentValueFormat: (value: any) => {
      if (!value || value.length !== 2) return undefined
      return {
        startTime: value[0],
        endTime: value[1],
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions(
        'time.componentValue',
        ((data || {})?.componentValue && [data.componentValue[0], data.componentValue[1]]) || undefined
      )
    },
  },
]
search.initOptions(configList)

const handleResize = () => {
  const { clientHeight = 0 } = searchcontentBox.value || {}
  if (clientHeight > 57) {
    showMoreBtn.value = true
  } else {
    showMoreFlag.value = false
    showMoreBtn.value = false
  }
}

const getRoleLists = async () => {
  const res = await getDepartment()
  if (res.code !== 200) throw new Error(res.msg)
  let data: any = []
  data = handleTree(res.data)
  roleLists.value = data
}

const getDictionaryFn = async () => {
  const res = await getDictionary()
  if (res.code !== 200) throw new Error(res.msg)
  dictionaryList.value = res?.data || []
}

const onGetSearchDataFn = (type: any = undefined) => {
  const params = { ...search.getParams(), ...{ status: tabValue.value || undefined } }
  emits('onGetSearchData', { params, cacheValue: search.getCacheSearch(), type })
  searchObj.value = {}
}

const onClearSearchDataFn = () => {
  search.clear()
  onGetSearchDataFn()
}

const initData = async () => {
  showMoreFlag.value = false
  search.clear()
  const cachData = store.getters['local/getLocalProcessManagementSearchData'] || {}
  tabValue.value = (cachData?.[cacheKey.value as string] ?? {})?.status ?? 0
  pageData.value.currPage = (cachData?.[cacheKey.value as string] ?? {})?.currPage ?? 1
  pageData.value.pageSize = (cachData?.[cacheKey.value as string] ?? {})?.pageSize ?? 10
  search.setDefaultSearch(cachData?.[cacheKey.value as string] ?? {})
}

watch(
  () => route.params,
  () => {
    nextTick(async () => {
      await initData()
      onGetSearchDataFn('init')
    })
  },
  { deep: true, immediate: true }
)

onMounted(async () => {
  handleResize()
  window.addEventListener('resize', handleResize)
  requestIdleCallback(getDictionaryFn)
  requestIdleCallback(getRoleLists)
  id && (await setSearchConfig(Number(id)))
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})

defineExpose({ onGetSearchDataFn })
</script>

<style lang="scss" scoped>
.search-content-container {
  .content-box {
    align-items: flex-start;
    padding-top: 6px;
    max-height: 56px;
    overflow: hidden;
    &.show-more {
      max-height: 100%;
    }
    .left-box {
      display: flex;
      flex-wrap: wrap;
    }
    .right-box {
      height: 30px;
      align-items: center;
      color: #378eef;
      cursor: pointer;
      .more-box {
        align-items: center;
      }
      span {
        white-space: nowrap;
      }
    }
    :deep(.tip-color) {
      border-color: #378eef;
    }
  }
}
</style>

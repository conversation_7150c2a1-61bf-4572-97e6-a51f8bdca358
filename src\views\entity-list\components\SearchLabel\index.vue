<template>
  <div class="search-label-container">
    <span class="label-title">{{ i18n.t('标签') }}：</span>
    <div class="label-box">
      <span class="label-item" v-for="(item, index) in formState" v-show="item.isSearchValue" :key="item.fieldCode">
        <span>【{{ item.fieldName }}】</span>
        <span>{{ type[item.type as keyof typeof type] }}</span>
        <span>【{{ item.getComponentValueLabelFormat && item.getComponentValueLabelFormat(item.values) }}】</span>
        <i class="iconfont icontubiao_shanchu11" @click="onDeteleFn(index)"></i>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { FieldList } from '@/types/entity'
import { useI18n } from '@/utils'

type propsType = {
  formState: FieldList[]
}

const i18n = useI18n()
const emits = defineEmits(['update:formState', 'getDataFn'])
const props = defineProps<propsType>()
const type = {
  eq: '等值',
  like: '模糊',
  range: '数字范围',
  timeRange: '时间范围',
}
const formState = computed({
  get: () => props.formState,
  set: val => emits('update:formState', val),
})

const onDeteleFn = (index: number) => {
  formState.value.splice(index, 1)
  emits('getDataFn', formState.value)
}
</script>

<style scoped lang="scss">
.search-label-container {
  display: flex;
  margin-top: 4px;
  .label-title {
    font-size: 12px;
    color: #999999;
    line-height: 24px;
    margin-top: 12px;
    white-space: nowrap;
  }
  .label-box {
    display: flex;
    flex-wrap: wrap;
    .label-item {
      display: flex;
      height: 24px;
      line-height: 24px;
      padding: 0 4px;
      margin-top: 12px;
      margin-left: 6px;
      background: #f8f8f8;
      border-radius: 3px;
      color: #333333;
      .iconfont {
        margin-left: 3px;
        cursor: pointer;
      }
    }
  }
}
</style>

<template>
  <div class="fs-table-column-set">
    <div class="fs-table-column-set-head">
      <span class="title">{{ i18n.t('展示列') }}</span>
      <div class="reset" @click="onReset">{{ i18n.t('重置') }}</div>
    </div>
    <div class="fs-table-column-set-body">
      <p class="title">{{ i18n.t('固定在左侧') }}</p>
      <div ref="dragLeftRef" class="fs-table-drag-content">
        <div v-for="(item, index) in leftList" :key="getKey(item, index)" class="fs-table-drag-item">
          <div class="side-left">
            <FIcon type="icon-tuodong" class="handle" />
            <FCheckbox
              v-model:checked="item.checked"
              class="checkbox"
              :disabled="getcheckboxDisabled(item)"
              @change="dataUpdate"
            />
            <span class="text" :title="(item.title as string || item.name as string)">
              {{ item.title || item.name }}
            </span>
          </div>
        </div>
      </div>
      <p class="title">{{ i18n.t('不固定') }}</p>
      <div ref="dragRef" class="fs-table-drag-content">
        <div v-for="(item, index) in list" :key="getKey(item, index)" class="fs-table-drag-item">
          <div class="side-left">
            <FIcon type="icon-tuodong" class="handle" />
            <FCheckbox
              v-model:checked="item.checked"
              class="checkbox"
              :disabled="getcheckboxDisabled(item)"
              @change="dataUpdate"
            />
            <span class="text" :title="(item.title as string || item.name as string)">
              {{ item.title || item.name }}
            </span>
          </div>
          <div class="side-right">
            <FIcon type="icon-jiantoushang5" class="handle" @click="onLeft(index)" />
            <FIcon type="icon-jiantouxia5" class="handle" @click="onRight(index)" />
          </div>
        </div>
      </div>
      <p class="title">{{ i18n.t('固定在右侧') }}</p>
      <div ref="dragRightRef" class="fs-table-drag-content">
        <div v-for="(item, index) in rightList" :key="getKey(item, index)" class="fs-table-drag-item">
          <div class="side-left">
            <FIcon type="icon-tuodong" class="handle" />
            <FCheckbox
              v-model:checked="item.checked"
              class="checkbox"
              :disabled="getcheckboxDisabled(item)"
              @change="dataUpdate"
            />
            <span class="text" :title="(item.title as string || item.name as string)">
              {{ item.title || item.name }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Sortable from 'sortablejs'
import { ref, watch, onMounted } from 'vue'
import { useI18n } from '@/utils'
import type { FColumnGroup, FColumnType } from './index'

interface IProps {
  columns: any[]
}

const props = defineProps<IProps>()
const emit = defineEmits(['change', 'reset'])
const i18n = useI18n()

const dragLeftRef = ref<HTMLElement>()
const dragRef = ref<HTMLElement>()
const dragRightRef = ref<HTMLElement>()

const leftList = ref<FColumnType[]>([])
const list = ref<FColumnType[]>([])
const rightList = ref<FColumnType[]>([])

watch(
  () => props.columns,
  newVal => {
    const columnGroup: FColumnGroup = newVal.reduce(
      (total: FColumnGroup, current) => {
        if (current.checked !== false) current.checked = true
        if (!current.fixed) {
          total.list.push(current)
        } else if (current.fixed === 'right') {
          total.rightList.push(current)
        } else if (current.fixed === true || current.fixed === 'left') {
          total.leftList.push(current)
        }
        return total
      },
      { leftList: [], list: [], rightList: [] }
    )
    leftList.value = columnGroup.leftList
    list.value = columnGroup.list
    rightList.value = columnGroup.rightList
  },
  { deep: true, immediate: true }
)

const getKey = (item: FColumnType, index: number) => {
  return '_' + item.checked + item.title + item.name + item.key + item.dataIndex + index
}
const getcheckboxDisabled = (item: FColumnType) => {
  const len = props.columns.filter(item => item.checked).length
  return len <= 1 && item.checked
}

const dataUpdate = () => {
  leftList.value = leftList.value.map(item => {
    item.fixed = 'left'
    return item
  })
  list.value = list.value.map(item => {
    item.fixed = false
    return item
  })
  rightList.value = rightList.value.map(item => {
    item.fixed = 'right'
    return item
  })
  const dataList = [...leftList.value, ...list.value, ...rightList.value]
  emit('change', dataList)
}

let tempMoveObj: FColumnType
const ininSortable = () => {
  new Sortable(dragLeftRef.value as HTMLElement, {
    animation: 150,
    group: 'tableColumn',
    handle: '.handle',
    onAdd(e) {
      const newIdx = e.newIndex as number
      leftList.value.splice(newIdx, 0, tempMoveObj)
    },
    onRemove(e) {
      const oldIdx = e.oldIndex as number
      leftList.value.splice(oldIdx, 1)
    },
    onUpdate(e) {
      const oldIdx = e.oldIndex as number
      leftList.value.splice(oldIdx, 1)
      const newIdx = e.newIndex as number
      leftList.value.splice(newIdx, 0, tempMoveObj)
    },
    onStart(e) {
      const oldIdx = e.oldIndex as number
      tempMoveObj = leftList.value[oldIdx]
    },
    onEnd() {
      tempMoveObj = null as any
      dataUpdate()
    },
  })
  new Sortable(dragRef.value as HTMLElement, {
    animation: 150,
    group: 'tableColumn',
    handle: '.handle',
    onAdd(e) {
      const newIdx = e.newIndex as number
      list.value.splice(newIdx, 0, tempMoveObj)
    },
    onRemove(e) {
      const oldIdx = e.oldIndex as number
      list.value.splice(oldIdx, 1)
    },
    onUpdate(e) {
      const oldIdx = e.oldIndex as number
      list.value.splice(oldIdx, 1)
      const newIdx = e.newIndex as number
      list.value.splice(newIdx, 0, tempMoveObj)
    },
    onStart(e) {
      const oldIdx = e.oldIndex as number
      tempMoveObj = list.value[oldIdx]
    },
    onEnd() {
      tempMoveObj = null as any
      dataUpdate()
    },
  })
  new Sortable(dragRightRef.value as HTMLElement, {
    animation: 150,
    handle: '.handle',
    group: 'tableColumn',
    onAdd(e) {
      const newIdx = e.newIndex as number
      rightList.value.splice(newIdx, 0, tempMoveObj)
    },
    onRemove(e) {
      const oldIdx = e.oldIndex as number
      rightList.value.splice(oldIdx, 1)
    },
    onUpdate(e) {
      const oldIdx = e.oldIndex as number
      rightList.value.splice(oldIdx, 1)
      const newIdx = e.newIndex as number
      rightList.value.splice(newIdx, 0, tempMoveObj)
    },
    onStart(e) {
      const idx = e.oldIndex as number
      tempMoveObj = rightList.value[idx]
    },
    onEnd() {
      tempMoveObj = null as any
      dataUpdate()
    },
  })
}

const onReset = () => {
  emit('reset')
}
const onLeft = (index: number) => {
  const item = list.value[index]
  leftList.value.push(item)
  list.value.splice(index, 1)
  dataUpdate()
}
const onRight = (index: number) => {
  const item = list.value[index]
  rightList.value.push(item)
  list.value.splice(index, 1)
  dataUpdate()
}

onMounted(() => {
  ininSortable()
})
</script>

<style scoped lang="scss">
.fs-table-column-set {
  width: 198px;
  max-height: 336px;
  padding: 16px 0px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .fs-table-column-set-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    flex-shrink: 0;
    padding: 0px 16px;

    .title {
      font-size: 14px;
      font-weight: 500;
    }

    .reset {
      font-size: 12px;
      font-weight: 400;
      color: var(--fs-blue-base);
      line-height: 22px;
      cursor: pointer;
    }
  }

  .fs-table-column-set-body {
    flex-shrink: 1;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0px 4px;

    .title {
      padding: 3px 12px;
      font-size: 12px;
      font-weight: 400;
      color: var(--fs-grey-7);
      line-height: 18px;
      margin: 0;
    }

    .fs-table-drag-content {
      min-height: 12px;

      > .fs-table-drag-item {
        height: 32px;
        line-height: 32px;
        padding: 0px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .side-left {
          flex: 1;
          display: flex;
          align-items: center;
          width: 0;

          .handle {
            font-size: 16px;
            margin-right: 8px;
            color: var(--fs-grey-7);
            cursor: pointer;
          }

          .checkbox {
            :deep(.fs-checkbox-wrapper-checked) {
              margin-right: 8px;
            }
          }

          .text {
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .side-right {
          flex-shrink: 0;
          display: none;
          align-items: center;

          .handle {
            font-size: 16px;
            margin-right: 8px;
            color: var(--fs-blue-base);
            cursor: pointer;
          }

          .handle:last-child {
            margin-right: 0;
          }
        }

        &:hover {
          .side-right {
            display: flex;
          }
        }
      }

      .fs-table-drag-item:hover {
        background-color: var(--fs-buleGrey-base-2);
      }
    }
  }
}
</style>

<style>
.bpm-custom-column-operated-popover .fs-popover-inner {
  padding: 0;
}

.bpm-custom-operated-icon {
  font-size: 16px;
  padding: 2px;
  border-radius: 3px;
}

.bpm-custom-operated-icon:hover {
  background-color: var(--fs-buleGrey-base-2);
}

.bpm-custom-operated-icon:active {
  background-color: var(--fs-buleGrey-base-3);
}
</style>

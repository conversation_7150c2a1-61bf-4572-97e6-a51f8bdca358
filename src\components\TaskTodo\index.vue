<template>
  <div>
    <FModal v-model:visible="value" :title="i18n.t('审核')" :footer="null" :width="960" footer-hide>
      <div class="card-datasource">
        <img src="@/assets/images/mail.png" alt="" style="width: 20px; margin-right: 5px" /> {{ i18n.t('来自') }}
        <span style="margin-left: 3px; color: #666666">{{ taskRecord.superviser }}&nbsp;</span>
        <pre
          class="remarks pre-warp"
          v-html="taskRecord.contentData ? JSON.parse(taskRecord.contentData as string).taskDesc : '--'"
        />
      </div>
      <div class="card-manager" style="border-bottom: none">
        <div>
          <img class="avatar" :src="defaultfImg" />
          <span class="card-manager-name color333"> @{{ taskRecord.superviser }} </span>
          <span class="color999">{{ i18n.t('任务标题') }}:</span>
          <span class="card-manager-name color333">{{ taskRecord.taskName }}</span>
          <span class="color999">
            <span class="iconfont color999 fontSize14" style="margin-left: 20px; margin-right: 2px"> &#xe629; </span>
            <span>{{ i18n.t('目标完成时间') }}: {{ transformDate(taskRecord.forcastTime) }}</span>
          </span>
        </div>
      </div>
      <div class="task-item-remark marginT10">
        <p class="fontSize12">{{ i18n.t('处理说明') }}：</p>
        <MyViewer :html="taskRecord.contentData ? JSON.parse(taskRecord.contentData as string).handleTaskDesc : ''" />

        <!-- 处理表单 -->
        <FormRender
          v-if="props.taskRecord.formKey"
          :id="(props.taskRecord.formKey as number)"
          type="view"
          :data="TFFormData(props.taskRecord)"
        />
      </div>
      <div class="showFile marginT10" v-if="fileList && fileList.length > 0">
        <div class="fileList">
          <div class="content-box">
            <div class="checkbox-item" v-for="(item, index) in fileList" :key="index">
              <div class="txt">
                <p class="txt-top">
                  <span class="iconfont fontSize12 marginR5" style="color: #fdb824">&#xe655;</span>
                  <a>{{ item.fileName }}</a>
                  <span class="size">({{ (item.fileSize / 1024).toFixed(0) }}KB)</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <FForm layout="vertical" class="marginT20">
        <FFormItem :label="i18n.t('审核说明')" name="contentData" class="fs-textarea" style="width: 100%">
          <FTextarea v-model:value="formState.verifyDesc" :placeholder="i18n.t('请输入')" />
        </FFormItem>
      </FForm>
      <div class="footer-button">
        <FButton style="margin-right: 20px" class="grey-btn" @click="onRjecct">{{ i18n.t('驳回') }}</FButton>
        <FButton type="primary" @click="onCheck">{{ i18n.t('通过') }}</FButton>
      </div>
    </FModal>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive, computed, markRaw } from 'vue'
import MyViewer from '@/components/Editor/MyViewer.vue' // 富文本解析
import FormRender from '@/components/FormRender/index.vue' // 表单渲染
import { checkMission, rejectMission } from '@/api/handle'
import { transformDate, useI18n } from '@/utils'
import defaultfImg from '@/assets/images/head.png'
import { messageInstance as message } from '@fs/smart-design'
import { ITask } from '@/types/handle'

interface IProps {
  value: boolean
  taskRecord: ITask
  paramsWrapper: (data: unknown) => unknown
}

interface FormState {
  verifyDesc: string
}
const i18n = useI18n()
const emit = defineEmits(['submit', 'update:value', 'fatherMethod'])
const props = defineProps<IProps>()
const value = computed({
  get: () => props.value,
  set: (val: boolean) => emit('update:value', val),
})
const fileList = ref<any>([])
const formState = reactive<FormState>({ verifyDesc: '' })
const dialogVisible = ref(false)

watch(
  () => props.value,
  () => {
    dialogVisible.value = props.value
    fileList.value = props.taskRecord.attachmentData ? JSON.parse(props.taskRecord.attachmentData) : []
  }
)

const handleClose = () => {
  formState.verifyDesc = ''
  emit('update:value', false)
}

//审核
const onCheck = () => {
  const data = {
    id: props.taskRecord.id,
    contentData: { verifyDesc: formState.verifyDesc },
  }
  checkMission(props.paramsWrapper(data)).then(res => {
    if (res.code == 200) {
      message.success(i18n.t('保存成功'))
      handleClose()
      emit('fatherMethod')
    } else {
      message.error(res.msg)
    }
  })
}
//驳回
const onRjecct = () => {
  const data = {
    id: props.taskRecord.id,
    contentData: { verifyDesc: formState.verifyDesc },
  }
  rejectMission(data).then(res => {
    if (res.code == 200) {
      message.success(i18n.t('保存成功'))
      handleClose()
      emit('fatherMethod')
    } else {
      message.error(res.msg)
    }
  })
}

const TFFormData = (taskData: ITask) => {
  const {
    creator,
    createdTime,
    contentData,
    formData,
    forcastTime,
    taskCompletedTime,
    taskStartTime,
    updatedUser,
    updatedTime,
    ...data
  } = taskData
  return markRaw({ envData: data, envDefaultFormData: formData })
}
</script>
<style lang="scss" scoped>
.card-datasource {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 10px;
  align-items: center;
  color: #999;
  border-bottom: 1px dashed #e0e0e0;
  font-size: 12px;
  .remarks {
    flex: 0 0 100%;
    margin: 10px 5px 0 5px;
  }
}
.card-manager {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 14px;
  padding-bottom: 10px;
  font-size: 12px;

  .file-btn-placeholder {
    width: 66px;
    flex-shrink: 0px;
    height: 20px;
    pointer-events: none;
  }

  > div {
    display: flex;
    align-items: center;
    line-height: 20px;
    > img {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 5px;
    }

    > span {
      display: flex;
      height: 20px;
      align-items: center;
    }
  }
  .card-manager-name {
    margin-right: 20px;
  }
}

.footer-button {
  margin-top: 20px;
  text-align: right;
  .grey-btn {
    background: #f8f8f8;
  }
}

.showFile {
  background: #f8f8f8;
  border-radius: 3px;
  padding: 20px;
  box-sizing: border-box;
}
.content-box {
  display: flex;
  flex-wrap: wrap;
  .checkbox-item {
    width: 33.3333%;
    margin-bottom: 20px;

    :deep(.ivu-checkbox-wrapper) {
      display: flex;
      align-items: center;
    }
  }
  .txt {
    margin-left: 10px;
    width: calc(100% - 26px);
    .txt-top {
      height: 12px;
      font-size: 12px;
      line-height: 12px;
      display: flex;
      margin-bottom: 5px;
      & > .icon-file {
        margin-right: 5px;
        width: 11px;
        height: 11px;
        flex-shrink: 0;
      }
      & > .file-name {
        font-size: 12px;
        color: #378eef;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;
        color: #378eef;
        display: inline-block;
      }
      & > .size {
        color: #bbbbbb;
        flex-shrink: 0;
        width: auto;
      }
    }
    .txt-bottom {
      height: 12px;
      font-size: 12px;
      color: #666666;
      line-height: 12px;
      .time {
        margin-left: 10px;
      }
    }
  }
}
</style>

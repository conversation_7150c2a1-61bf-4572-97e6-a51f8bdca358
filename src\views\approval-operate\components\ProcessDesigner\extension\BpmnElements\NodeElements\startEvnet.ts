import { GraphModel, HtmlNode, HtmlNodeModel, NodeConfig } from '@logicflow/core'
import { genBpmnId } from '@logicflow/extension/es/bpmn-elements/utils'

class StartEvnetModel extends HtmlNodeModel {
  static extendKey = 'EndEvnetModel'
  constructor(data: NodeConfig, graphModel: GraphModel) {
    if (!data.id) data.id = `Event_${genBpmnId()}`
    if (!data.text) data.text = '开始'
    if (typeof data.text === 'string') data.text = { value: data.text, x: data.x, y: data.y + 1 }

    super(data, graphModel)
    this.themeColor = '#333'
  }

  initNodeData(data: any) {
    super.initNodeData(data)
    this.width = 60
    this.height = 38
    // this.text.editable = false
  }

  getTextStyle() {
    const style = super.getTextStyle()
    style.fontSize = 14
    style.fill = '#fff'
    return style
  }
}

class StartEvnetView extends HtmlNode {
  setHtml(rootEl: HTMLElement): void {
    const $container = document.createElement('div')
    $container.style.borderRadius = '23px'
    $container.style.backgroundColor = this.props.model.themeColor
    $container.style.width = this.props.model.width + 'px'
    $container.style.height = this.props.model.height + 'px'
    $container.style.lineHeight = this.props.model.height + 'px'
    rootEl.appendChild($container)
  }
}

export default {
  type: 'bpmn:startEvent',
  view: StartEvnetView,
  model: StartEvnetModel,
}

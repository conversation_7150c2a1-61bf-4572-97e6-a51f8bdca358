<template>
  <FModal
    width="720px"
    wrapClassName="bom-modal-wrap-container"
    v-model:visible="visible"
    :title="handleData?.[handleType]?.title ?? '创建角色'"
    centered
    :confirm-loading="loading"
    @cancel="onCancelFn"
    @ok="onSubmitFn"
  >
    <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <FRow :gutter="[24, 0]">
        <FCol :span="12">
          <FFormItem label="角色编码" name="roleCode">
            <FInput v-model:value="formState.roleCode" placeholder="请输入" />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem label="角色名称" name="roleName">
            <FInput v-model:value="formState.roleName" placeholder="请输入" />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem label="启用状态" name="status">
            <FSelect
              v-model:value="formState.status"
              placeholder="请选择"
              :options="statusOptions"
              optionFilterProp="label"
              show-search
            />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem label="角色类型" name="type">
            <FSelect
              v-model:value="formState.type"
              placeholder="请选择"
              :options="roleTypeList"
              optionFilterProp="label"
              show-search
              @change="onChangeFiledFn"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12" v-if="formState.type === 5">
          <FFormItem label="外部同步类型" name="isSyn">
            <FSelect
              v-model:value="formState.isSyn"
              placeholder="请选择"
              :options="synTypeList"
              optionFilterProp="label"
              show-search
            />
          </FFormItem>
        </FCol>
        <FCol :span="12" v-if="formState.type === 5">
          <FFormItem label="接口地址" name="url">
            <FInput v-model:value="formState.url" placeholder="请输入接口地址" />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem label="所属流程" name="processConfigId">
            <FSelect
              v-model:value="formState.processConfigId"
              placeholder="请选择"
              :options="processList"
              :fieldNames="{ label: 'processName', value: 'id' }"
              optionFilterProp="processName"
              allow-clear
              show-search
            />
          </FFormItem>
        </FCol>
        <FCol :span="24">
          <FFormItem label="角色说明" name="roleDesc">
            <FTextarea v-model:value="formState.roleDesc" style="min-height: 88px" placeholder="请输入" />
          </FFormItem>
        </FCol>
      </FRow>
    </FForm>
    <div></div>
  </FModal>
</template>

<script setup lang="ts">
import { message } from '@fs/smart-design'
import { ref, reactive, computed, inject, Ref } from 'vue'
import { manageAddRole, manageUpdateRole } from '@/api'
import { trimObjectStrings } from '@/utils'

const emits = defineEmits(['updateChange'])
const visible = ref<boolean>(false)
const statusOptions = inject<Ref<any>>('statusOptions') as any
const roleTypeList = inject<Ref<any>>('roleTypeList') as any
const synTypeList = inject<Ref<any>>('synTypeList') as any
const processList = inject<Ref<any>>('processList') as any
const loading = ref<boolean>(false)
const formRef = ref()
const formState = reactive<any>({
  roleCode: undefined,
  roleDesc: undefined,
  roleName: undefined,
  status: undefined,
  processConfigId: undefined,
  url: undefined,
  type: undefined,
  isSyn: undefined,
})
const currentRowRecord = ref<any>()
const handleType = ref('')
const handleData = computed(() => ({
  addRole: {
    title: '创建角色',
    msg: '创建角色成功！',
    apiUrl: manageAddRole,
  },
  updateRole: {
    title: '编辑角色',
    msg: '编辑角色成功！',
    apiUrl: manageUpdateRole,
  },
}))

const rules: Record<string, any[]> = {
  roleName: [{ required: true, message: '请输入角色名称' }],
  roleCode: [{ required: true, message: '请输入角色编码' }],
  type: [{ required: true, message: '请选择角色类型' }],
  status: [{ required: true, message: '请选择启用状态' }],
  isSyn: [{ required: true, message: '请选择外部同步类型' }],
  url: [{ required: true, message: '请输入接口地址' }],
}

const onChangeFiledFn = value => {
  formState.isSyn = undefined
  formState.url = undefined
}

const onCancelFn = () => {
  visible.value = false
}

const onSubmitFn = async () => {
  try {
    loading.value = true
    if (!formRef.value) return
    await formRef.value.validate()
    const params: any = Object.assign({}, formState, handleData?.value?.[handleType?.value]?.baseParams)
    await handleData?.value?.[handleType?.value]?.apiUrl(trimObjectStrings(params))
    message.success(handleData?.value?.[handleType?.value]?.msg ?? '操作成功')
    onCancelFn()
    emits('updateChange')
  } finally {
    loading.value = false
  }
}

const onInitData = async () => {
  Object.entries(formState).forEach(([key, value]) => {
    formState[key] =
      handleData?.value?.[handleType?.value]?.defaultForm?.[key] || (currentRowRecord.value || {})[key] || undefined
  })
}

const onOpenFn = async (typeValue: string, data: any = {}) => {
  visible.value = true
  currentRowRecord.value = data
  handleType.value = typeValue
  await onInitData()
}

defineExpose({ onOpenFn })
</script>

<style scoped lang="scss">
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
</style>

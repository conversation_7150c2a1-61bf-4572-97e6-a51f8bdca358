<template>
  <div v-if="componentConfig?.isDetail">
    <FileList
      v-if="fileList && fileList?.length"
      :modelValue="fileList.map(item => ({ url: item.url, fileName: item.name }))"
      class="blue"
    />
    <div v-else>--</div>
  </div>
  <FUpload
    v-else
    v-model:file-list="fileList"
    v-bind="$attrs"
    :disabled="componentConfig?.componentAttrs?.disabled"
    :before-upload="beforeUpload"
    @remove="onRemoveFn"
    :key="updateKey"
  >
    <FButton size="small" v-if="!componentConfig?.isDetail">
      <template #icon><FIcon type="icon-a-shangchuan2" /></template>
      {{ i18n.t('上传附件') }}
    </FButton>
  </FUpload>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useI18n } from '@/utils'
import { upload } from '@/api'
import FileList from '@/views/process-operate/components/CustomComponents/BusinessComponent/FileList/index.vue'
import { message } from '@fs/smart-design'

interface IProps {
  value: any[] | undefined
  componentConfig?: any
}

const i18n = useI18n()
const props = defineProps<IProps>()
const emits = defineEmits(['update:value'])
const fileList = computed<any[]>({
  get: () => (props?.value ?? []).map(item => ({ name: item.name, url: item.url, size: item.size, ...item })),
  set: val =>
    emits(
      'update:value',
      (val || [])?.map(item => ({ name: item.name, url: item.url, size: item.size, ...item }))
    ),
})
const updateKey = ref<number>(Date.now() + Math.random())

const onRemoveFn = async (file: any) => {
  !props?.componentConfig?.componentAttrs?.disabled &&
    (fileList.value = fileList.value.filter(item => item?.url !== file.url))
}

const beforeUpload = async (file: any) => {
  // const formData = new FormData()
  // formData.append('file', file)
  // formData.append('isOpen', 'false')
  // formData.append('expire', '0')

  // 手动上传
  // const url = await upload(formData)
  // fileList.value = [...(fileList.value || []), { name: file.name, url: url, size: file.size }]

  // return false
  let key = Date.now() + '' + Math.random()
  file.status = 'uploading'
  file.key = key
  uploadItem(file, key)
  return false
}

// 检测上传进度
const onHandleUploadProgress = (progressEvent: any, key: string) => {
  const index = fileList.value.findIndex((item: any) => item.key === key)
  index !== -1 &&
    (fileList.value[index].percent = Math.round((progressEvent.loaded / progressEvent.total) * 10000) / 100.0)
}
const uploadItem = (file: any, key: string) => {
  let data = new FormData()
  data.append('file', file)
  data.append('isOpen', 'false')
  data.append('expire', '0')
  upload(data, onHandleUploadProgress, key).then((res: any) => {
    const index = fileList.value.findIndex((item: any) => item.key === key)
    if (res && index !== -1) {
      fileList.value[index].url = res
      fileList.value[index].status = 'success'
      console.log('fileList.value :>> ', fileList.value, index)
    } else if (index !== -1) {
      fileList.value[index].status = 'error'
      message.error(i18n.t('上传失败，请移除！'))
    }
    fileList.value = [...fileList.value]
    updateKey.value = Date.now() + Math.random()
  })
}
</script>

<template>
  <FLayoutContent class="layout-main">
    <HistoryRecord />
    <Container>
      <RouterView />
    </Container>
  </FLayoutContent>
</template>

<script lang="ts" setup>
import Container from '@/components/Container/index.vue'
import HistoryRecord from '@/components/HistoryRecord/index.vue'
</script>

<style lang="scss" scoped>
.layout-main {
  margin: 0;
  padding: 0;
  height: calc(100% - 56px);
  overflow: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }
}
</style>

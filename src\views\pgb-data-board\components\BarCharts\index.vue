<template>
  <div class="bar-charts-container">
    <FSpin size="large" :spinning="chartsLoading">
      <div class="title">{{ i18n.t('流程统计') }}</div>
      <BaseEchart :option="barOptions" height="426px" />
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import BaseEchart from '@/components/BaseEchart/index.vue'
import type { EChartsOption } from '@/components/BaseEchart/config'
import { useI18n } from '@/utils'
const i18n = useI18n()

type propsType = {
  chartsLoading: boolean
  barOptions: EChartsOption
}
const props = defineProps<propsType>()
</script>

<style lang="scss" scoped>
.bar-charts-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 512px;
  // margin-top: 24px;
  padding: 20px 24px 18px 24px;
  background-color: #fff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
  .title {
    margin-bottom: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 24px;
  }
}
</style>

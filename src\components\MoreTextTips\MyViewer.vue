<template>
  <div class="ql-container ql-snow color333">
    <div class="ql-editor" v-html="porps.html" @click="handleElementClick"></div>
  </div>
</template>
<script lang="ts" setup>
const porps = defineProps({ html: { type: String, default: '' } })
const handleElementClick = (e: any) => {
  if (e.target.nodeName === 'IMG') {
    window.open(e.target.currentSrc, '_blank')
  }
}
</script>

<style lang="scss" scoped>
.ql-container.ql-snow {
  position: relative;
  border: none !important;
  .ql-editor {
    display: inline;
    padding: 0;
    word-break: break-all;
  }
  :deep(.ql-video) {
    width: 100%;
    height: 434px;
  }
  :deep(.fs-image) {
    position: absolute;
  }

  :deep(img) {
    max-width: 200px;
    max-height: 120px;
  }

  p {
    margin: 0;
  }
}
</style>

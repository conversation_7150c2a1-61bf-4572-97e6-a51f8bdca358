<template>
  <div class="handle-sort-container">
    <i class="iconfont icontubiao_paixu color999 cursor" @click="openPopver"></i>
    <FModal
      v-model:visible="visible"
      class="handle-sort-pop"
      :title="i18n.t('分类管理')"
      :width="400"
      @cancel="onCancel"
      destroyOnClose
      :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
    >
      <FTable
        class="cust-table"
        :loading="loading"
        :row-key="(record:any) => record.id"
        :columns="columns"
        :data-source="list"
        :customRow="customRow"
        :scroll="{ x: true, y: 300 }"
        :rowClassName="(record: any, index: number) => ('table-striped')"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'icon'">
            <i class="iconfont icontubiao_tuodong1 color999 cursor"></i>
          </template>
          <template v-if="column.key === 'sort'">
            <span v-if="!record.edit">{{ index + 1 }}</span>
            <FInputNumber
              class="input-sort"
              v-else
              v-model:value="record.sort"
              :min="0"
              :precision="0"
              :placeholder="i18n.t('请输入')"
            />
          </template>
        </template>
        <template #emptyText>
          <NoData />
        </template>
      </FTable>
      <template #footer>
        <div class="footer">
          <FButton class="close-btn" @click="onCancel" :loading="loading">{{ i18n.t('取消') }}</FButton>
          <FButton type="primary" class="submit-btn" @click="onSave" :loading="loading">{{ i18n.t('确认') }}</FButton>
        </div>
      </template>
    </FModal>
  </div>
</template>
<script lang="ts" setup>
import { ref, inject, computed } from 'vue'
import { updateLabelSort } from '@/api/localizationSystem/wordbenchBoard'
import { messageInstance as message } from '@fs/smart-design'
import { useI18n } from '@/utils'
const i18n = useI18n()

const getProcessLocals: any = inject('getProcessLocal')

type propsType = {
  sortList: any[]
}

const props = withDefaults(defineProps<propsType>(), {
  sortList: () => [],
})

const columns = computed(() => [
  {
    title: '',
    key: 'icon',
    width: 50,
  },
  {
    title: i18n.t('排序'),
    dataIndex: 'sort',
    key: 'sort',
    width: 100,
  },
  {
    title: i18n.t('分类名称'),
    dataIndex: 'name',
    width: 216,
  },
])

const list = ref<any[]>([])
let visible = ref<boolean>(false)
let loading = ref<boolean>(false)

const openPopver = () => {
  if (!props.sortList.length) {
    message.warning(i18n.t('请先添加标签'))
  } else {
    visible.value = true
    loading.value = false
    props.sortList.length &&
      (list.value = JSON.parse(JSON.stringify(props.sortList)).sort((max: any, min: any) => {
        return max.sort - min.sort
      }))
  }
}

const onCancel = () => {
  visible.value = false
  loading.value = false
}

const onSave = async () => {
  try {
    if (loading.value) return
    let params = list.value.map((item, index) => ({
      id: item.id,
      sort: index + 1,
    }))
    await updateLabelSort(params)
    message.success(i18n.t('提交成功'))
    onCancel()
    getProcessLocals()
  } finally {
    loading.value = false
  }
}

let dragItem: any
let targItem: any
const customRow = (record: any) => {
  return {
    draggable: true,
    ondrag(e: DragEvent) {
      dragItem = record
    },
    ondrop(e: DragEvent) {
      targItem = record
    },
    ondragend(e: DragEvent) {
      if (dragItem !== targItem) {
        const dragItemIndex = list.value.findIndex((item: any) => item.id === dragItem.id)
        const targItemIndex = list.value.findIndex((item: any) => item.id === targItem.id)

        ;[list.value[dragItemIndex], list.value[targItemIndex]] = [list.value[targItemIndex], list.value[dragItemIndex]]
      }
    },
    ondragover(e: DragEvent) {
      return false
    },
  }
}
</script>
<style lang="scss" scoped>
.handle-sort-container {
  .iconfont {
    font-weight: 400;
  }
  .input-sort {
    width: 90%;
  }
  :deep(.cust-table) {
    tr:nth-child(2n) {
      td {
        background-color: transparent !important;
      }
    }
  }
  .handle-sort-pop {
    .footer {
      display: flex;
      justify-content: flex-end;
      button {
        width: 64px;
        height: 24px;
        border-radius: 3px;
      }
      .close-btn {
        border: 1px solid #cccccc;
        background: #ffffff;
        color: #333333;
      }
      .submit-btn {
        background: #378eef;
      }
    }
  }
}
</style>

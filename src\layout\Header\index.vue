<template>
  <FLayoutHeader class="layout-header-component">
    <div class="layout-header-left">
      <MapNavList :data="props.menus" @on-menu-change="handleMenuChange" />
    </div>
    <div class="layout-header-center"></div>
    <div class="layout-header-right">
      <!-- 外链 -->
      <MapNav />
      <!-- 国际化 -->
      <I18n />
      <div class="layout-header-split" />
      <!-- 用户信息 -->
      <UserInfo />
    </div>
  </FLayoutHeader>
</template>

<script setup lang="ts">
import { IMicroMenu } from '@fs/hooks'

import MapNavList from './components/MapNavList/index.vue'
import UserInfo from './components/UserInfo/index.vue'
import I18n from './components/I18n/index.vue'
import MapNav from './components/MapNav/index.vue'

interface IProps {
  menus: IMicroMenu[]
}

const props = defineProps<IProps>()
const emits = defineEmits(['on-menu-change'])

const handleMenuChange = (menu: IMicroMenu) => {
  emits('on-menu-change', menu)
}
</script>

<style lang="scss" scoped>
.layout-header-component {
  height: 56px;
  padding: 0px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: transparent;

  .layout-header-left {
    display: flex;
    align-items: center;
  }

  .layout-header-right {
    display: flex;
    align-items: center;
  }

  .layout-header-split {
    width: 1px;
    height: 24px;
    margin: 0 16px;
    background-color: #b6c4d3;
  }
}
</style>

<template>
  <div class="search-box-container">
    <div class="left">
      <SearchList :searchConfigLists="searchConfigLists" />
    </div>
    <div class="right">
      <FButton class="export-btn" type="primary" @click="$emit('on-export-process')">
        <i class="icontubiao_xiazai iconfont"></i>{{ i18n.t('下载') }}
      </FButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchList from './components/SearchList/index.vue'
import { Search } from './searchConfig'
import { ISearchDataConfig } from '@/types/pgbDataBoard'
import { reactive } from 'vue'
import { useI18n } from '@/utils'
const i18n = useI18n()
const emits = defineEmits(['onGetSearchData'])
const searchConfigLists = reactive<ISearchDataConfig>(new Search(emits))
</script>

<style lang="scss" scoped>
.search-box-container {
  display: flex;
  justify-content: space-between;
  color: #999;
  .right {
    .export-btn {
      width: 80px;
      margin-left: 12px;
      .icontubiao_xiazai {
        margin-right: 4px;
      }
    }
  }
}
</style>

<template>
  <div class="board-task-container">
    <div v-if="!nodeLst.length" class="no-data">
      <img src="./images/noData.png" alt="" />
      <span>{{ i18n.t('暂无数据') }}</span>
    </div>
    <BaseCharts v-else :options="options" />
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import BaseCharts from './BaseCharts.vue'
import { useI18n } from '@/utils'
const i18n = useI18n()

type TNode = {
  name: string
  value: number
}

type propsType = {
  nodeLst: TNode[]
}

const props = withDefaults(defineProps<propsType>(), {
  nodeLst: () => [],
})

const options = reactive<any>({
  color: ['#378EEF', '#2FCC83', '#8391A0', '#FA8F23', '#F04141'],
  tooltip: {
    trigger: 'item',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#666',
        type: 'solid',
      },
    },
    backgroundColor: '#000000B3',
    borderColor: 'transparent',
    formatter: function (params: any) {
      return `<div style="color: #fff">${params.name}</div>
      <div style="color: #fff"><span style="display: inline-block; margin-right: 24px"><i style="display: inline-block; width: 8px; height: 8px;margin-right:4px;background-color: #F04141FF;border-radius:50%"></i>${i18n.t(
        '任务占比'
      )}</span><span>${params.percent}%</span></div>
      <div style="color: #fff"><span style="display: inline-block; margin-right: 24px"><i style="display: inline-block; width: 8px; height: 8px;margin-right:4px;background-color: #F04141FF;border-radius:50%"></i>${i18n.t(
        '任务完成数'
      )}</span><span>${params.value}个</span></div>`
    },
  },
  legend: {
    icon: 'circle',
    itemWidth: 8,
    itemHeight: 8,
    padding: 0,
    bottom: 0,
    left: 'center',
    selectedMode: false,
    textStyle: {
      height: 9,
      fontSize: 12,
      color: '#666666FF',
      rich: {},
    },
  },
  series: {
    type: 'pie',
    radius: ['35%', '55%'],
    center: ['50%', '50%'],
    startAngle: 180,
    label: {
      show: true,
      formatter(param: any) {
        return param.value + i18n.t('个')
      },
    },
    data: [],
  },
})

watch(
  () => props.nodeLst,
  newVal => {
    if (newVal.length) {
      options.series.data = newVal
    } else {
      options.series.data = []
    }
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.board-task-container {
  width: 100%;
  height: 100%;
  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    img {
      height: 160px;
    }
    span {
      color: #999;
    }
  }
}
</style>

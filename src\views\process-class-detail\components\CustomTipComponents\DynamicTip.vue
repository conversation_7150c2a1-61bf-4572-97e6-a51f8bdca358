<template>
  <div style="width: max-content;">
    <FPopover 
      :title="popoverTitle"
      placement="topRight" 
      trigger="hover"
      :destroy-tooltip-on-hide="true"
      :get-popup-container="getPopupContainer"
    >
      <template #content>
        <div style="max-width: 300px;">
          <!-- 动态内容区域 -->
          <div v-if="relatedProcesses.length" class="mb-2">
            <div class="font-weight-bold mb-1">关联流程：</div>
            <div v-for="process in relatedProcesses" :key="process.id" class="mb-1">
              <FTag size="small" :color="process.status === 'active' ? 'success' : 'default'">
                {{ process.name }}
              </FTag>
            </div>
          </div>
          
          <!-- 验证状态 -->
          <div v-if="validationStatus" class="mb-2">
            <div class="font-weight-bold mb-1">验证状态：</div>
            <FTag size="small" :color="validationStatus.type">
              {{ validationStatus.message }}
            </FTag>
          </div>
          
          <!-- 建议操作 -->
          <div v-if="suggestions.length" class="mb-2">
            <div class="font-weight-bold mb-1">建议：</div>
            <ul style="margin: 0; padding-left: 16px;">
              <li v-for="suggestion in suggestions" :key="suggestion">
                {{ suggestion }}
              </li>
            </ul>
          </div>
          
          <!-- 自定义内容插槽 -->
          <slot name="content" :fieldValue="fieldValue" :formData="formData" />
        </div>
      </template>
      
      <span style="margin-left: 4px; cursor: pointer;" :class="tipClass">
        <i :class="iconClass" style="margin-right: 2px; font-size: 14px;" />
        <span style="font-size: 12px;">{{ displayText }}</span>
      </span>
    </FPopover>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue'

interface Props {
  fieldKey?: string
  fieldValue?: any
  fieldConfig?: any
  formData?: any
  // 自定义属性
  title?: string
  text?: string
  type?: 'info' | 'warning' | 'error' | 'success'
  showRelatedProcesses?: boolean
  showValidation?: boolean
  customSuggestions?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  title: '详细信息',
  text: '查看详情',
  type: 'info',
  showRelatedProcesses: false,
  showValidation: false,
  customSuggestions: () => []
})

// 响应式数据
const relatedProcesses = ref<any[]>([])
const validationStatus = ref<any>(null)

// 计算属性
const popoverTitle = computed(() => {
  return `${props.title} - ${props.fieldConfig?.label || props.fieldKey}`
})

const displayText = computed(() => {
  return props.text
})

const tipClass = computed(() => {
  const baseClass = 'custom-dynamic-tip'
  const typeClass = {
    info: 'tip-info',
    warning: 'tip-warning', 
    error: 'tip-error',
    success: 'tip-success'
  }
  return `${baseClass} ${typeClass[props.type]}`
})

const iconClass = computed(() => {
  const iconMap = {
    info: 'icontubiao_tishi_mian',
    warning: 'icontubiao_jinggao',
    error: 'icontubiao_cuowu',
    success: 'icontubiao_chenggong'
  }
  return `iconfont ${iconMap[props.type]}`
})

const suggestions = computed(() => {
  const baseSuggestions = [...props.customSuggestions]
  
  // 根据字段值和配置动态生成建议
  if (props.fieldConfig?.required && (!props.fieldValue || props.fieldValue === '')) {
    baseSuggestions.unshift('此字段为必填项，请填写相关内容')
  }
  
  if (props.fieldKey === 'processDefineKey' && props.fieldValue) {
    baseSuggestions.push('选择流程后将自动加载相关配置')
  }
  
  return baseSuggestions
})

// 监听字段值变化，动态更新相关数据
watch(() => props.fieldValue, async (newValue) => {
  if (props.showRelatedProcesses && newValue) {
    // 模拟获取关联流程数据
    await loadRelatedProcesses(newValue)
  }
  
  if (props.showValidation) {
    validateField(newValue)
  }
}, { immediate: true })

// 加载关联流程数据
const loadRelatedProcesses = async (processId: any) => {
  try {
    // 这里应该调用实际的API
    // const response = await getRelatedProcesses(processId)
    // relatedProcesses.value = response.data
    
    // 模拟数据
    relatedProcesses.value = [
      { id: 1, name: '审批流程A', status: 'active' },
      { id: 2, name: '审批流程B', status: 'inactive' }
    ]
  } catch (error) {
    console.error('加载关联流程失败:', error)
    relatedProcesses.value = []
  }
}

// 验证字段
const validateField = (value: any) => {
  if (!value) {
    validationStatus.value = {
      type: 'warning',
      message: '字段值为空'
    }
  } else if (props.fieldConfig?.required) {
    validationStatus.value = {
      type: 'success', 
      message: '验证通过'
    }
  } else {
    validationStatus.value = null
  }
}

// 获取弹出容器
const getPopupContainer = (trigger: HTMLElement) => {
  return trigger.parentElement || document.body
}
</script>

<style scoped>
.custom-dynamic-tip {
  cursor: pointer;
  transition: all 0.3s ease;
}

.tip-info {
  color: #1890ff;
}

.tip-warning {
  color: #faad14;
}

.tip-error {
  color: #ff4d4f;
}

.tip-success {
  color: #52c41a;
}

.custom-dynamic-tip:hover {
  opacity: 0.8;
}

.font-weight-bold {
  font-weight: bold;
}

.mb-1 {
  margin-bottom: 4px;
}

.mb-2 {
  margin-bottom: 8px;
}
</style>

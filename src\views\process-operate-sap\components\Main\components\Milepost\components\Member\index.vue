<template>
  <div class="meber-wrapper">
    <span class="meber" @click="flag && (selectFlag = !selectFlag)">
      <img class="meber-img" :src="props.src" />
      {{ props.name }}
    </span>
    <div class="meber-select" v-if="selectFlag && flag">
      <div class="meber-select-input-wrapper">
        <input
          class="meber-select-input"
          type="text"
          :value="currUserName"
          @input="handleChange"
          :placeholder="props.name"
        />
      </div>
      <div class="meber-select-item-wrapper">
        <div
          :class="['meber-select-item', { active: item.value === props.id }]"
          :key="item.value"
          v-for="item in userData"
          @click="handleClick(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref, inject } from 'vue'
import { useStore } from 'vuex'
import { FModal } from '@fs/smart-design'
import { useI18n } from '@/utils'
import { IUser } from '@/types/handle'
import { EmitType } from '@/views/process-detail/config'

type UserType = {
  label: string
  value: string
}

interface IProps {
  id?: string
  name?: string
  src?: string
  flag?: boolean
}

const i18n = useI18n()
const store = useStore()
const props = defineProps<IProps>()
const operate = inject('operate') as (key: EmitType, data: any) => void

const selectFlag = ref<boolean>(false)
const currUserId = ref<string>()
const currUserName = ref<string>()
const userData = computed<UserType[]>(() =>
  (store.state.user.allUser || [])
    .map((item: IUser) => ({ label: item.name, value: item.uuid }))
    .filter(
      (item: UserType) => !currUserName.value || item.label.toLowerCase().includes(currUserName.value.toLowerCase())
    )
)

watch(
  () => props.id,
  () => {
    currUserId.value = props.id
  },
  { immediate: true }
)

const handleClick = (item: UserType) => {
  FModal.confirm({
    title: i18n.t('是否确认更换节点负责人？'),
    content: i18n.t('该节点负责人将会变更为 ${name} ，请谨慎操作！', { name: item.label }),
    okText: i18n.t('确认'),
    cancelText: i18n.t('取消'),
    onOk: () => {
      currUserId.value = item.value
      currUserName.value = item.label
      operate(EmitType.dispatch, { type: 'switch', data: { id: item.value, name: item.label } })
    },
  })
}

const handleChange = (e: any) => {
  currUserName.value = e.target.value
}
</script>

<style scoped lang="scss">
.meber-wrapper {
  position: relative;
  z-index: 9;
}
.meber {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 12px;

  > .meber-img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
}

.meber-select {
  position: absolute;
  top: 32px;
  left: 0;
  width: 315px;
  height: 228px;
  background: #ffffff;
  box-shadow: 0px 4px 12px 1px rgba(88, 98, 110, 0.14);
  border-radius: 3px;

  .meber-select-input-wrapper {
    position: relative;
    margin: 16px 16px 0 16px;
    .meber-select-input {
      width: 283px;
      height: 32px;
      line-height: 32px;
      padding: 0 8px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      background: #ffffff;
      border-radius: 3px;
      border: 1px solid #378eef;
      box-sizing: border-box;
      outline: none;

      &:focus {
        border: 1px solid #378eef;
        box-shadow: 0px 0px 0px 2px #afd1f8;
      }
    }

    &::before {
      position: absolute;
      top: calc(16px - 8px);
      right: 8px;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      font-size: 16px;
      font-family: 'bpm-iconfont' !important;
      color: #bbb;
      content: '\e79a';
    }

    &::placeholder {
      color: #bbb;
      font-size: 12px;
    }
  }

  .meber-select-item-wrapper {
    margin: 8px 4px 4px 4px;
    height: 168px;
    overflow: hidden;
    overflow-y: auto;

    .meber-select-item {
      padding: 0 12px;
      margin-bottom: 2px;
      height: 32px;
      line-height: 32px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      background: #ffffff;
      border-radius: 3px;
      box-sizing: border-box;
      cursor: pointer;

      &:hover {
        background-color: #f1f4f8;
      }

      &.active {
        color: #378eef;
        background-color: #ebf3fd;
      }
    }
  }
}
</style>

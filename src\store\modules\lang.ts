import { Commit, ActionContext } from 'vuex'
type LangType = 'zn-CN' | 'en-US'
interface ILangState {
  locale: string
  locales: object
}

const locale = 'en-US'
const locales = { 'en-US': 'English', 'zh-CN': '中文' }

/**
 * 根据据浏览器的语言，来选择系统支持的语言
 */
function getBrowserLanguage() {
  let lang = navigator.language
  lang = lang.substring(0, 2)
  // 所有的英语国家，默认成美式英语
  if (lang === 'en') {
    return 'en-US'
  }
  // 所有的中文国家，默认成汉语简体
  if (lang === 'zh') {
    return 'zh-CN'
  }
  return locale
}

const state = {
  locale: localStorage.getItem('locale') || getBrowserLanguage(),
  locales: locales,
}

const getters = {
  locale: (state: ILangState) => state.locale,
  locales: (state: ILangState) => state.locales,
}

const mutations = {
  SET_LOCALE(state: ILangState, locale: LangType) {
    state.locale = locale
    localStorage.setItem('locale', locale)
  },
}

const actions = {
  setLocale({ commit }: ActionContext<ILangState, any>, locale: LangType) {
    commit('SET_LOCALE', locale)
  },
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}

<template>
  <FModal
    v-model:visible="visible"
    :width="720"
    :title="props.title"
    :confirm-loading="loading"
    :centered="true"
    @cancel="onCancel"
    @ok="onOk"
  >
    <PromptText style="margin-bottom: 24px" v-if="tip">
      <div>1、支持导入格式为xlsx的文件(手动修改文件后缀无效)；</div>
      <div>2、模版中的表头不可更改，不可删除；</div>
      <div>3、数据记录不要超过1000条；</div>
      <div>当有一条数据错误时,会跳过错误数据,正常导入其他数据</div>
    </PromptText>

    <div>
      <div class="upload-label">
        <div class="label-left required">{{ props.title }}</div>
        <div v-if="template" class="label-right" @click="emits('download')">
          <i class="iconfont icontubiao_xiazai"></i>
          下载模板
        </div>
      </div>
      <FUploadDragger v-model:file-list="fileList" accept=".xls" :max-count="1" :before-upload="() => false">
        <p class="upload-drag-icon"><span class="icon icontubiao_shangchuan_mian upload-icon"></span></p>
        <p class="ant-upload-text">单击或拖动文件到此区域</p>
        <p class="ant-upload-hint colorBBB">支持上传xlsx格式文件，文件大小2G</p>
      </FUploadDragger>
    </div>
  </FModal>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { message } from '@fs/smart-design'

import PromptText from '@/components/PromptText/index.vue'

interface IProps {
  title: string
  visible: boolean
  loading: boolean
  template?: boolean
  tip?: boolean
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:visible', 'download', 'submit'])

const visible = computed({
  get() {
    return props.visible
  },
  set(value: boolean) {
    emits('update:visible', value)
  },
})

const fileList = ref<File[]>([])

const onOk = async () => {
  if (!fileList?.value?.length) {
    message.warning('请先上传文件！')
    return
  }
  emits('submit', { files: fileList.value })
}

const onCancel = () => {
  fileList.value = []
  visible.value = false
}

watch(
  () => visible.value,
  val => {
    if (val) {
      fileList.value = []
    }
  }
)
</script>

<style scoped lang="scss">
.upload-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 18px;
  height: 18px;
  margin-bottom: 8px;

  .label-left {
    font-size: 12px;
    font-weight: 400;
    color: #333333;
  }

  .label-right {
    font-size: 12px;
    font-weight: 400;
    color: #378eef;
    cursor: pointer;
  }
}

.upload-drag-icon {
  margin: 0;
  padding: 0;
  height: 48px;

  .upload-icon {
    color: #d8d8d8;
    font-size: 36px;
  }
}

.required::before {
  display: inline-block;
  margin-right: 4px;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}
</style>

import { request, BASESTATEURL } from '@/utils'
import type {
  Res,
  TRes,
  LocalOrderNumParams,
  LocalOrderNumIndo,
  RegisterAndUVInfo,
  Datum,
  LocalizationConfig,
  RetentiondataParams,
  RetentiondataInfo,
  LocalizationRegion,
  IAllLocalizationCountry,
  IAllLocalizationCountryData,
} from '@/types/localizationSystem/dataBoard'

// 获取统计token
export const getStatisticsToken = (): Promise<Res<{ token: string }>> => {
  return request.post('/api/workbench/getStatisticsToken')
}
// 获取
export const getLocalOrderNum = (params: LocalOrderNumParams): Promise<Res<LocalOrderNumIndo>> => {
  return request.post(BASESTATEURL + '/api/orderBoard/localOrderNum', params)
}
// 查询注册数和uv数
export const getRegisterAndUV = (params: LocalOrderNumParams): Promise<TRes<RegisterAndUVInfo>> => {
  return request.post('/api/localization/getRegisterAndUV', params)
}
// 查询流程任务完成数
export const getProcessTaskNum = (params: LocalOrderNumParams): Promise<TRes<Datum[]>> => {
  return request.post('/api/localization/getProcessTaskNum', params)
}

// 查找所有本地化配置
export const selectAllLocalizationConfig = (): Promise<TRes<LocalizationConfig[]>> => {
  return request.get('/api/localization/selectAllLocalizationConfig')
}
// 查询流资数据
export const getRetentiondata = (params: RetentiondataParams): Promise<Res<RetentiondataInfo>> => {
  return request.post(`/api/localization/getRetentiondata?lang=${params.lang}`, params.localizationDataQry)
}

// 查找所有本地化配置
export const selectAllLocalizationRegion = (): Promise<TRes<LocalizationRegion[]>> => {
  return request.get('/api/localization/selectAllLocalizationRegion')
}

// 查找所有本地化配置
export const selectAllLocalizationCountry = (
  params: IAllLocalizationCountry
): Promise<TRes<IAllLocalizationCountryData[]>> => {
  return request.get('/api/localization/selectAllLocalizationCountry', { params })
}

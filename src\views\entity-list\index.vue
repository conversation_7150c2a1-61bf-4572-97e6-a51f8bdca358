<template>
  <div class="entity-list-container">
    <FCard>
      <div class="header">
        <SearchContent :fieldList="fieldList" v-model:selectData="selectData" @getDetailById="getDetailById" />
        <FButton type="primary" class="add-btn" @click="onImportFn">
          <i class="icontubiao_xiazai iconfont"></i>{{ i18n.t('下载') }}
        </FButton>
      </div>
      <FTable
        :data-source="list"
        :loading="tableLoading"
        :columns="columns"
        table-layout="fixed"
        :pagination="false"
        :scroll="{ x: '100%' }"
        @resizeColumn="handleResizeColumn"
      >
      </FTable>
      <div class="fei-su-pagination" style="padding-bottom: 0">
        <FPagination
          v-model:current="pageData.pageNum"
          v-model:pageSize="pageData.pageSize"
          :total="pageData.total"
          @change="onChangeFn"
          show-size-changer
          show-quick-jumper
          :show-total="() => `${i18n.t('共')} ${pageData.total} ${i18n.t('条')}`"
        />
      </div>
    </FCard>
  </div>
</template>

<script setup lang="ts">
import SearchContent from './components/SearchContent/index.vue'
import { searchDatamodel, getFieldConfig, exportDatamodel } from '@/api'
import { BasicPageParams } from '@/types/productRemoveList'
import { FieldList } from '@/types/entity'
import { onMounted, ref, reactive, h } from 'vue'
import { useRoute } from 'vue-router'
import { message } from '@fs/smart-design'
import { useI18n, transformDate } from '@/utils'

const i18n = useI18n()
const route = useRoute()
const tableLoading = ref<boolean>(false)
const pageData = reactive<BasicPageParams>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const columns = ref<any>()
const list = ref<any>()
const fieldList = ref<FieldList[]>([])
const selectData = ref<any[]>([])

const onImportFn = async () => {
  if (!route.params?.id) return
  if (!list.value.length) {
    message.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const params = {
    entityId: route.params?.id,
    type: 2,
    searchList: selectData.value.map((item: any) => ({
      fieldCode: item.fieldCode,
      type: item.type,
      values: (item.getComponentValueFormat && item.getComponentValueFormat(item.values)) || [],
    })),
  }
  const res = await exportDatamodel(params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('下载成功，请在飞书查看！'))
}

const onChangeFn = (current: number, pageSize: number) => {
  pageData.pageNum = current
  pageData.pageSize = pageSize
  getDetailById()
}

const handleResizeColumn = (width: any, column: any) => {
  column.width = width
}

const createColumnsFn = (list: FieldList[]) => {
  columns.value = (list || [])
    .filter(item => item.isShow === 0)
    .sort((min, max) => {
      return (min?.sort || 0) - (max?.sort || 0)
    })
    .map((item: FieldList) => {
      if (['datePickerCode', 'rangePickerCode'].includes(item.dataType as string)) {
        return {
          title: item.fieldName,
          dataIndex: item.fieldCode,
          minWidth: 60,
          width: 160,
          customRender: (record: any) => {
            return h('span', transformDate(record.text, 'YYYY-MM-DD HH:mm:ss'))
          },
          resizable: true,
        }
      } else {
        return {
          title: item.fieldName,
          dataIndex: item.fieldCode,
          minWidth: 60,
          width: 120,
          resizable: true,
        }
      }
    })
}

const getFieldList = async () => {
  try {
    tableLoading.value = true
    const res = await getFieldConfig(route.params?.id as unknown as number)
    if (res.code !== 200) throw new Error(res.msg)
    fieldList.value = res?.data || []
    createColumnsFn(fieldList.value)
  } finally {
    tableLoading.value = false
  }
}

const getDetailById = async () => {
  try {
    if (!route.params?.id) return
    tableLoading.value = true
    const params = {
      entityId: route.params?.id,
      type: 1,
      searchList: selectData.value.map((item: any) => ({
        fieldCode: item.fieldCode,
        type: item.type,
        values: (item.getComponentValueFormat && item.getComponentValueFormat(item.values)) || [],
      })),
      currPage: pageData.pageNum,
      pageSize: pageData.pageSize,
    }
    const res = await searchDatamodel(params)
    if (res.code !== 200) throw new Error(res.msg)
    list.value = res?.data?.list || []
    pageData.total = res?.data?.totalCount || 0
  } finally {
    tableLoading.value = false
  }
}

onMounted(() => {
  getDetailById()
  getFieldList()
})
</script>

<style scoped lang="scss">
.entity-list-container {
  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
  }
}
</style>

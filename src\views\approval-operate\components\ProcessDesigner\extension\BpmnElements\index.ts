import LogicFlow from '@logicflow/core'
import StartEvent from './NodeElements/startEvnet'
import EndEvent from './NodeElements/endEvnet'
import UserTask from './NodeElements/userTask'
import ChilrenTask from './NodeElements/childrenTask'
import ChildProcess from './NodeElements/childProcess'
import ChildrenTaskEdge from './NodeElements/chilrenTaskEdge'
import SequenceEdge from './NodeElements/sequenceEdge'
import Approval from './NodeElements/approval'

export default class BpmnElement {
  static pluginName = 'BpmnElement'
  constructor({ lf }: { lf: LogicFlow }) {
    lf.register(StartEvent)
    lf.register(EndEvent)
    lf.register(UserTask)
    lf.register(ChilrenTask)
    lf.register(ChildProcess)
    lf.register(ChildrenTaskEdge)
    lf.register(SequenceEdge)

    // 审批节点
    lf.register(Approval)
  }
}

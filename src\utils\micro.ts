/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Power } from '@fs/hooks'
import store from '@/store'
import { power } from '@/init'
import { debounce } from '.'

/**
 * @description: 获取 micro app 实例
 * <AUTHOR>
 * @date 2022-11-21
 * @returns any
 */
export const getMicroAppInstance = () => {
  return window.microApp
}

/**
 * @description: 获取基础路由
 * <AUTHOR>
 * @date 2022-11-21
 * @returns string
 */
export const BaseRoute = window.__MICRO_APP_BASE_ROUTE__ || '/bpm-manage'

/**
 * @description: 是否是微前端环境
 * <AUTHOR>
 * @date 2022-11-21
 * @returns boolean
 */
export const isMicroApp = window.__MICRO_APP_ENVIRONMENT__ ?? false

/**
 * @description 获取主应用下发的全局数据
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {void} void
 */
export const getMicroGlobalData = () => {
  if (isMicroApp) {
    return window.microApp.getGlobalData()
  } else {
    throw new Error('非微前端环境')
  }
}

/**
 * @description 获取主应用下发的数据
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {void} void
 */
export const getMicroAppData = () => {
  if (isMicroApp) {
    return window.microApp.getData()
  } else {
    throw new Error('非微前端环境')
  }
}

/**
 * @description 设置主应用的头部是否显示
 * <AUTHOR>
 * @date 2022-07-15
 * @param {any} microGlobalData:ecord<string, any>
 * @param {any} isShow:boolean
 * @returns {void} void
 */
export const setMainAppHeaderShow = (microGlobalData: Record<string, any>, isShow: boolean) => {
  microGlobalData?.methods?.setIsHeaderShow(isShow)
}

/**
 * @description 设置主应用的侧边栏是否显示
 * <AUTHOR>
 * @date 2022-07-15
 * @param {any} microGlobalData:ecord<string, any>
 * @param {any} isShow:boolean
 * @returns {void} void
 */
export const setMainAppSidebarShow = (microGlobalData: Record<string, any>, isShow: boolean) => {
  microGlobalData?.methods?.setIsSidebarShow(isShow)
}

/**
 * @description 设置主应用的头部和侧边栏是否显示
 * <AUTHOR>
 * @date 2022-07-15
 * @param {any} microGlobalData: Record<string, any>
 * @param {any} isHeaderShow:boolean
 * @param {any} isSidebarShow?:boolean
 * @returns {void} void
 */
export const setMainAppLayoutShow = (isHeaderShow: boolean, isSidebarShow?: boolean) => {
  const microGlobalData = getMicroGlobalData()

  isSidebarShow = isSidebarShow ?? isHeaderShow
  setMainAppHeaderShow(microGlobalData, isHeaderShow)
  setMainAppSidebarShow(microGlobalData, isSidebarShow)
}

/**
 * @description 获取 token
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {any} any
 */
export const getToken = () => {
  let token = ''
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    token = micorGlobalData?.methods?.getToken()
  } else {
    token = store.getters.token
  }

  if (token) return token
  else return void 0
}

/**
 * @description 获取用户信息
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {any} any
 */
export const getUserInfo = () => {
  let userInfo
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    userInfo = micorGlobalData?.methods?.getUserInfo() ?? {}
  } else {
    userInfo = store.getters.userInfo
  }

  userInfo?.uuid && (userInfo.adminId = userInfo.uuid)
  userInfo?.userName && (userInfo.feiShuName = userInfo.userName)

  return userInfo
}

/**
 * @description 获取语言类型
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {string} string
 */
export const getLanguage = () => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    return micorGlobalData?.methods?.getLanguage() || navigator.language
  } else {
    return store.getters.language
  }
}

/**
 * @description 更新token
 * @returns Promise<any>
 */
export const updateToken = async () => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    return micorGlobalData?.methods?.updateToken?.()
  } else {
    return store.dispatch('user/checkToken')
  }
}

/**
 * @description 获取权限信息
 * @date 2023-10-07
 * @returns {any} any
 */
export const getPower = () => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    return micorGlobalData?.methods?.getPower() as Power
  } else {
    return power
  }
}

/**
 * @description 适配器 - 设置布局显示
 * @param isHeaderShow 是否显示头部
 * @param isSidebarShow 是否显示侧边栏
 */
export const setLayoutShow = (isHeaderShow: boolean, isSidebarShow?: boolean) => {
  isSidebarShow = isSidebarShow ?? isHeaderShow

  if (isMicroApp) {
    setMainAppLayoutShow(isHeaderShow, isSidebarShow)
  } else {
    store.commit('global/SET_IS_HEADER_SHOW', isHeaderShow)
    store.commit('global/SET_IS_SIDEBAR_SHOW', isSidebarShow)
  }
}

/**
 * @description: 登出
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {any} any
 */
export const logout = debounce(() => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    return micorGlobalData?.methods?.logout()
  } else {
    return store.dispatch('user/logout')
  }
}, 200)

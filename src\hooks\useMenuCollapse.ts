import { ref, type Ref } from 'vue'
interface IActions {
  setCollapse: (val: boolean) => void
  toggleCollapse: () => void
}

function useMenuCollapse(val?: boolean): [collapse: Ref<boolean>, actions: IActions] {
  const collapse = ref<boolean>(val ?? false)

  const setCollapse = (val: boolean) => {
    collapse.value = val
  }
  const toggleCollapse = () => {
    collapse.value = !collapse.value
  }

  const actions: IActions = {
    setCollapse,
    toggleCollapse,
  }
  return [collapse, actions]
}

export default useMenuCollapse

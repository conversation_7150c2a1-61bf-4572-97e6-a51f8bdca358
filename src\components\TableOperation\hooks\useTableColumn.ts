import { reactive, computed, h } from 'vue'
import { cloneColumns } from '..'
import { ColumnOperationTitle } from '../components'
import useColumnLocalStore from './useColumnLocalStore'

/**
 * 验证columns数据是否缺失key
 */
const validateColumns = columns => {
  for (let i = 0; i < columns.length; i++) {
    const column = columns[i]
    if (!column.key) {
      throw new Error('使用useTableColumn传入columns的参数必须要存在key！')
    }
  }
}

const formatUseColumns = (columns = []) => {
  const startDragHideColumns = []
  const operationColumns = []

  let startDragHideFlag = false
  for (let i = 0; i < columns.length; i++) {
    const column = columns[i]

    if (column.dragHide && !startDragHideFlag) {
      startDragHideColumns.push(column)
    } else {
      startDragHideFlag = true
      operationColumns.push(column)
    }
  }
  return {
    startDragHideColumns,
    operationColumns,
  }
}

/** 扩展table列操作 */
export default function useTableColumn<T extends { key: string }>(
  columns: T[],
  key,
  types: ('column' | 'size')[] = ['column']
) {
  const { startDragHideColumns, operationColumns } = formatUseColumns(columns)

  validateColumns(operationColumns)

  /**
   * 默认的传入的列
   */
  const defaultColumn = cloneColumns([...operationColumns])
  const { getColumns, setColunms } = useColumnLocalStore(operationColumns, key)

  /**
   * 当前操作的列的实际内容
   */
  const setColumnTitleProps = reactive({
    columns: getColumns(),
    onChange: columns => {
      setColumnTitleProps.columns = columns
      setColunms(columns)
    },
    onReset: () => {
      setColumnTitleProps.columns = cloneColumns(defaultColumn)
      setColunms([])
    },
  })

  /**
   * 从操作数据中映射，tabe中需要显示的列
   */
  const tableViewColumns = computed(() => {
    return setColumnTitleProps.columns.filter(item => item.checked !== false)
  })

  /**
   * 设置列移动虚拟dom处理
   */
  let setColumnTitle = null
  if (types.includes('column')) {
    setColumnTitle = ColumnOperationTitle(setColumnTitleProps)
  }

  /**
   * 操作列组件封装
   */
  // const operation = computed(() => {
  //   const operations = [setColumnTitle].filter(Boolean)
  //   if (!operations.length) return []
  //   const width = 20 * operations.length
  //   return [
  //     {
  //       title: h('span', operations),
  //       width: width,
  //       maxWidth: width,
  //       minWidth: width,
  //       customHeaderCell: () => ({ style: { padding: '0px' } }),
  //       fixed: tableViewColumns.value[tableViewColumns.value.length - 1].fixed
  //     }
  //   ]
  // })

  /**
   * 操作列组件封装
   */
  const Operation = () => {
    const operations = [setColumnTitle].filter(Boolean)
    if (!operations.length) return []
    const width = 20 * operations.length
    return h('span', operations)
  }

  const startHideColumns = computed(() => {
    return startDragHideColumns.map(column => {
      column.fixed = tableViewColumns.value[0]?.fixed
      return column
    })
  })

  /*
   * table中需要显示的列进行映射
   */
  const tableColumns = computed(() => {
    return [
      ...startHideColumns.value,
      ...tableViewColumns.value,
      // ...operation.value
    ]
  })

  return {
    tableColumns,
    Operation,
  }
}

import type { IMicroMenu } from '@fs/hooks'
import type { IApplication, IMicroAppConfig } from '@/types/request'

export interface IMicroState {
  [key: string]: unknown
  project: IMicroAppConfig | null
  apps: IApplication[] | null
  menus: IMicroMenu[] | null
}

const state: IMicroState = {
  project: null,
  apps: null,
  menus: null,
}

const mutations = {
  SET_MICRO_PROJECT: (state: IMicroState, data: IMicroAppConfig) => {
    state.project = data
  },
  SET_MICRO_APPS: (state: IMicroState, data: IApplication[]) => {
    state.apps = data
  },
  SET_MICRO_MENUS: (state: IMicroState, data: IMicroMenu[]) => {
    state.menus = data
  },
  CLEAR_MICRO_STATE: (state: IMicroState) => {
    state.project = null
    state.apps = null
    state.menus = null
  },
}

const actions = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}

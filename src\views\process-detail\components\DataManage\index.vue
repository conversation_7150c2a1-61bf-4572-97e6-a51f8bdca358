<template>
  <div class="process-detail-data-manage">
    <div :class="['left', (siderExpandedFlag && 'hidden-left') || 'expanded-left']">
      <div class="title">
        <span>{{ i18n.t('资料目录') }}</span>
        <TipBtn :tip-title="i18n.t('收起菜单')" v-if="!siderExpandedFlag">
          <i class="iconfont iconshouqi1 cursor hover-btn" @click="siderExpandedFn" />
        </TipBtn>
      </div>
      <div class="expanded-box" @click="expandedFn">
        <i :class="['iconfont', 'cursor', (expandedFlag && 'icontubiao_tianjia21') || 'icontubiao_shanjian1']" />
        <span>{{ (expandedFlag && i18n.t('一键展开')) || i18n.t('一键收起') }}</span>
      </div>
      <FSpin wrapper-class-name="tree-spin-container" :spinning="treeLoading">
        <FTree
          v-if="treeData.length"
          class="data-tree"
          v-model:selectedKeys="selectedKeys"
          :show-line="{ showLeafIcon: false }"
          :default-expand-all="expandedFlag"
          :key="treeKey"
          show-icon
          :field-names="{ children: 'children', title: 'label', key: 'value' }"
          :tree-data="treeData"
          @select="selectTreeFn"
        >
          <template #icon><FIcon type="icon-jilu" /></template>
          <template #switcherIcon="{ expanded }">
            <FIcon :type="(expanded && 'icon-jiantouxia1') || 'icon-jiantouyou2'" />
          </template>
        </FTree>
      </FSpin>
    </div>
    <div class="line"></div>
    <div class="right">
      <div class="search-box">
        <TipBtn :tip-title="i18n.t('展开菜单')" v-if="siderExpandedFlag">
          <i v-if="siderExpandedFlag" class="iconfont iconzhankai1 cursor hover-btn" @click="siderExpandedFn" />
        </TipBtn>
        <FInput
          v-model:value="documentSearch.input"
          class="search-input width120"
          :press-line="i18n.t('资料名称')"
          allow-clear
          :placeholder="i18n.t('请输入')"
          type="search-clear"
          @search="onSearch"
          @clear="onSearch"
        />
        <div class="right-btn">
          <TipBtn
            :hasPop="!!state.checkedList.length"
            :tip-title="(state.checkedList.length && i18n.t('删除')) || i18n.t('请选择数据')"
            :pop-title="i18n.t('确定执行删除操作吗？')"
            @onConfirmFn="onDeleteFn"
          >
            <FButton class="marginR12">
              <i class="iconfont icontubiao_xietongshanchu marginR4"></i>
              <span>{{ i18n.t('删除') }}</span>
            </FButton>
          </TipBtn>
          <TipBtn :tip-title="(state.checkedList.length && i18n.t('批量下载')) || i18n.t('请选择数据')">
            <FButton type="primary" @click="() => state.checkedList.length && onHandleExportFn()">
              <i class="iconfont icontubiao_xiazai marginR4"></i>
              <span class="inline-block mr8">{{ i18n.t('批量下载') }}</span>
            </FButton>
          </TipBtn>
        </div>
      </div>
      <FSpin wrapper-class-name="list-spin-container" :spinning="ListLoading">
        <div class="card-box">
          <template v-if="list?.length">
            <FCheckboxGroup v-model:value="state.checkedList" @change="checkedGroupChange">
              <div class="card-item" v-for="item in list" :key="item">
                <div class="header-box">
                  <div class="hearder-title">
                    <img
                      @click="() => item?.docFile?.length && downloadFn(item?.docFile)"
                      :src="require(`./images/${getPicFn(item?.docFile ?? [])}.svg`)"
                    />
                    <FTooltip :get-popup-container="target" :overlay-style="{ maxWidth: 'none' }">
                      <span class="name-box" @click="downloadFn(item?.docFile)">
                        <span class="name">{{
                          (item?.docFile[0]?.fileName ?? item?.docFile[0]?.text ?? i18n.t('文件需点击查看')).split(
                            '.'
                          )[0]
                        }}</span>
                        <span class="format">{{
                          (item?.docFile[0]?.fileName ?? item?.docFile[0]?.text ?? '').split('.')[1]
                        }}</span>
                      </span>
                      <template #title>
                        <div v-for="(file, index) in item?.docFile ?? []" :key="file?.url ?? index" class="name-box">
                          <span class="name cursor" @click="onPreviewFn(file)">
                            <span style="color: #fff">{{
                              file?.fileName ?? file?.text ?? i18n.t('文件需点击查看')
                            }}</span>
                            <!-- <i :class="['iconfont', 'cursor', onGetIconFn(file)]" @click="onPreviewFn(file)"></i> -->
                          </span>
                        </div>
                      </template>
                    </FTooltip>
                  </div>
                  <FCheckbox
                    :style="{ visibility: (state.checkedList.includes(item.id) && 'visible') || 'hidden' }"
                    v-model:value="item.id"
                  />
                </div>
                <div class="tag-box">
                  <TipBtn :tip-title="item?.docTypeName ?? ''">
                    <FTag :border="false" color="success">
                      {{ item?.docTypeName ?? '' }}
                    </FTag>
                  </TipBtn>
                </div>
                <div class="role-box">
                  <i class="iconfont icontubiao_renyuan2 marginR4" />
                  <span>{{ item.createBy }}</span>
                </div>
                <div class="footer">
                  <span class="time">{{
                    (item?.updateTime && transformDate(item.updateTime, 'YYYY-MM-DD HH:mm:ss')) || '--'
                  }}</span>
                  <div class="btn-box">
                    <TipBtn :tip-title="i18n.t('详情')">
                      <i class="iconfont icongengduo21 cursor hover-btn" @click="documentDetailRef?.open(item)"></i>
                    </TipBtn>
                  </div>
                </div>
              </div>
            </FCheckboxGroup>
            <div class="footer-box" v-if="page.total">
              <FCheckbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate" @change="checkAllFn">
                <span class="file-ellipsis fontS12"> {{ i18n.t('全选') }} </span>
              </FCheckbox>
              <FPagination
                v-model:current="page.currPage"
                @change="onPaginationChangeFn"
                v-model:pageSize="page.pageSize"
                :total="page.total"
                show-size-changer
                show-quick-jumper
                :show-total="() => `${i18n.t('共')} ${page.total} ${i18n.t('条')}`"
              />
            </div>
          </template>
          <FEmpty style="width: 100%" v-else />
        </div>
      </FSpin>
    </div>
    <DocumentDetail ref="documentDetailRef" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, computed } from 'vue'
import { useI18n, transformDate, download, batchDownload, S3URL } from '@/utils'
import { BasicPageParams } from '@/types/processBoard'
import type { IProcess } from '@/types/handle'
import { message } from '@fs/smart-design'
import DocumentDetail from './components/DocumentDetail.vue'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import { getDocumentType, getDocumentList, PageDocumentListParams, batchDeleteDocument } from '@/api'

interface IProps {
  processInfo: IProcess[]
  other: unknown
}
const i18n = useI18n()
const porps = defineProps<IProps>()
const target = ref<any>(() => document.querySelector('#container'))
const page = reactive<BasicPageParams>({
  currPage: 1,
  pageSize: 10,
  total: 0,
})
const documentDetailRef = ref()
const selectedKeys = ref<string[]>([])
const treeKey = ref<number>(Date.now() + Math.random())
const expandedFlag = ref(false)
const siderExpandedFlag = ref(false)
const treeData = ref([])
let _expandKeys_ = []
const filterTree = (val, tree, newArr = []) => {
  if (!(tree.length && val)) {
    return tree
  }
  for (let item of tree) {
    if (item.label.toLowerCase().indexOf(val.toLowerCase()) > -1) {
      newArr.push(item)
      if (item.children) _expandKeys_.push(item.key)
      continue
    }
    if (item.children && item.children.length) {
      _expandKeys_.push(item.key)
      let subArr = filterTree(val, item.children)
      if (subArr && subArr.length) {
        let node = { ...item, children: subArr }
        newArr.push(node)
      }
    }
  }
  return newArr
}
const list = ref([])
const treeLoading = ref<boolean>(false)
const ListLoading = ref<boolean>(false)
const documentSearch = ref<PageDocumentListParams>({
  input: undefined,
  docAssortId: undefined, // 一级字典
  docTypeId: undefined, // 二级字典
})
const instanceId = computed(() => porps.processInfo[0]?.instanceId)
const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
})

const checkedGroupChange = (e: any) => {
  state.indeterminate = !!e.length && e.length < list.value.length
  state.checkAll = e.length === list.value.length
}

const checkAllFn = (e: any) => {
  Object.assign(state, {
    checkedList: e.target.checked ? list.value.map(item => item.id) : [],
    indeterminate: false,
  })
}

const expandedFn = () => {
  expandedFlag.value = !expandedFlag.value
  treeKey.value = Date.now() + Math.random()
}

const siderExpandedFn = () => {
  siderExpandedFlag.value = !siderExpandedFlag.value
}

const onDeleteFn = async () => {
  const res = await batchDeleteDocument(state.checkedList)
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('删除成功'))
  getDocumentTypeFn()
  getDocumentListFn()
}

const onGetIconFn = file => {
  if (file.link) return 'icontubiao_yanjing'
  const type = (file?.fileName ?? '').substring((file?.fileName ?? '').lastIndexOf('.') + 1).toLowerCase()
  if (['png', 'jpg', 'pdf', 'txt'].includes(type)) {
    return 'icontubiao_yanjing'
  } else {
    return 'icontubiao_xiazai'
  }
}

const getPicFn = (list: any) => {
  const type =
    (list?.length === 1 && list[0]?.link && 'link') ||
    (list[0]?.fileName ?? '').substring((list[0]?.fileName ?? '').lastIndexOf('.') + 1).toLowerCase() ||
    'other'
  if (['pdf'].includes(type)) {
    return 'pdf'
  } else if (['png', 'jpg'].includes(type)) {
    return 'png'
  } else if (['ppt'].includes(type)) {
    return 'ppt'
  } else if (['txt'].includes(type)) {
    return 'txt'
  } else if (['div'].includes(type)) {
    return 'div'
  } else if (['xlsx', 'xlsm', 'xlsb', 'xls'].includes(type)) {
    return 'excel'
  } else if (['doc', 'docm', 'docx', 'dot'].includes(type)) {
    return 'word'
  } else if (['link'].includes(type)) {
    return 'link'
  }
  return 'other'
}

const onPreviewFn = file => {
  const type = (file?.fileName ?? '').substring((file?.fileName ?? '').lastIndexOf('.') + 1).toLowerCase()
  if (['png', 'jpg', 'pdf', 'txt'].includes(type)) {
    const httpUrl = `${S3URL}/api/s3/getFileByUrl?url=${encodeURIComponent(file.url)}&filename=${encodeURIComponent(
      file.fileName
    )}&type=1`
    window.open(httpUrl, '_blank')
  } else if (type) {
    download(encodeURIComponent(file.url), encodeURIComponent(file.fileName))
  }
  file.link && window.open(file.link, '_blank')
}

const downloadFn = list => {
  const fileList = [],
    linkList = []
  list.forEach(item => {
    item.url && fileList.push(item)
    item.link && linkList.push(item)
  })
  fileList?.length === 1 && download(fileList[0].url, fileList[0].fileName)
  fileList?.length > 1 && batchDownload(fileList.map(item => ({ url: item.url, fileName: item.fileName })))
  !fileList.length && linkList?.length && window.open(linkList[0].link, '_blank')
}

const onHandleExportFn = () => {
  const fileList = [],
    linkList = []
  list.value.forEach(item => {
    if (state.checkedList.includes(item.id)) {
      ;(item?.docFile ?? []).forEach(fileItem => {
        fileItem.url && fileList.push(fileItem)
        fileItem.link && linkList.push(fileItem)
      })
    }
  })
  fileList?.length === 1 && download(fileList[0].url, fileList[0].fileName)
  fileList?.length > 1 && batchDownload(fileList.map(item => ({ url: item.url, fileName: item.fileName })))
  !fileList.length && linkList?.length && window.open(linkList[0].link, '_blank')
  Object.assign(state, {
    indeterminate: false,
    checkAll: false,
    checkedList: [],
  })
}

const selectTreeFn = (value, data) => {
  documentSearch.value.input = undefined
  documentSearch.value.docAssortId = data?.node?.parent?.key || data?.node?.key || undefined
  documentSearch.value.docTypeId = (data?.node?.parent?.key && data?.node?.key) || undefined
  getDocumentListFn()
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  page.currPage = current
  page.pageSize = pageSize
  getDocumentListFn()
}

const getDocumentTypeFn = async () => {
  try {
    treeLoading.value = true
    const res = await getDocumentType({ instanceId: instanceId.value })
    if (res.code !== 200) throw new Error(res.msg)
    const data = res?.data ?? []
    treeData.value = [{ label: i18n.t('全部分类'), value: 0, children: data }]
  } finally {
    treeLoading.value = false
  }
}

const getDocumentListFn = async (data = null) => {
  try {
    ListLoading.value = true
    const params = Object.assign(
      {
        correlateInstanceId: instanceId.value,
      },
      documentSearch.value,
      page,
      data
    )
    delete params.total
    const res = await getDocumentList(params)
    if (res.code !== 200) throw new Error(res.msg)
    list.value = res?.data?.list ?? []
    page.total = res?.data?.totalCount ?? 0
    Object.assign(state, {
      indeterminate: false,
      checkAll: false,
      checkedList: [],
    })
  } finally {
    ListLoading.value = false
  }
}

const onSearch = () => {
  getDocumentListFn()
}
onMounted(() => {
  getDocumentTypeFn()
  getDocumentListFn()
})
</script>

<style lang="scss" scoped>
.name {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #333333;
  .iconfont {
    display: inline-block;
    margin-left: 12px;
    cursor: pointer;
    color: #378eef;
  }
}
.process-detail-data-manage {
  display: flex;
  flex-wrap: nowrap;
  height: 656px;
  padding: 0 0 24px 24px;
  .left {
    height: 100%;
    overflow: hidden;
    transition: all 0.3s;
    &.hidden-left {
      width: 0;
    }
    &.expanded-left {
      width: 260px;
      min-width: 130px;
    }
    .title {
      display: flex;
      justify-content: space-between;
      padding: 16px 0;
      padding-right: 16px;
      font-size: 14px;
      color: #333333;
      border-bottom: 1px solid #eeeeee;
      span {
        white-space: nowrap;
        font-weight: bold;
      }
    }
    .expanded-box {
      display: flex;
      align-items: center;
      margin: 12px 0;
      padding-left: 8px;
      color: #999;
      font-size: 12px;
      cursor: pointer;
      i {
        margin-right: 4px;
      }
    }
    :deep(.tree-spin-container) {
      height: calc(100% - 104px);
      .fs-spin-container {
        height: 100%;
      }
      .data-tree {
        height: 100%;
        overflow: scroll;
        padding-right: 4px;
        margin-right: 6px;
        .fs-tree-switcher-leaf-line {
          &::after {
            display: none;
          }
        }
        .fs-tree-node-selected {
          .fs-tree-title {
            color: #378eef !important;
          }
        }
        .fs-tree-title {
          font-size: 12px !important;
        }
      }
    }
  }
  @media (max-width: 1510px) {
    .left {
      &.expanded-left {
        width: 250px;
      }
    }
  }
  @media (max-width: 1410px) {
    .left {
      &.expanded-left {
        width: 230px;
      }
    }
  }
  @media (max-width: 1310px) {
    .left {
      &.expanded-left {
        width: 210px;
      }
    }
  }
  @media (max-width: 1200px) {
    .left {
      &.expanded-left {
        width: 200px;
      }
    }
  }
  .line {
    width: 1px;
    height: 100%;
    background-color: #eeeeee;
  }
  .right {
    flex: 1;
    margin-top: 16px;
    margin-left: 22px;
    .search-box {
      display: flex;
      align-items: center;
      margin-right: 22px;
      i {
        margin-right: 8px;
      }
      .right-btn {
        display: flex;
        margin-left: auto;
      }
    }
    :deep(.list-spin-container) {
      height: calc(100% - 39px);
      .fs-spin-container {
        height: 100%;
      }
      .card-box {
        height: 100%;
        margin-top: 8px;
        .fs-checkbox-group {
          display: flex;
          flex-wrap: wrap;
          max-height: calc(100% - 54px);
          overflow-y: scroll;
          .card-item {
            flex: 1 1 282px;
            margin-right: 16px;
            margin-top: 16px;
            background: #ffffff;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid #eeeeee;
            transition: all 0.3s;
            padding: 16px;
            &:hover {
              border: 1px solid #87bbf5;
              background: #fbfdff;
            }
            &:hover .fs-checkbox-wrapper {
              visibility: visible !important;
            }
            .header-box {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .hearder-title {
                display: flex;
                align-items: center;
                flex-wrap: nowrap;
                max-width: 80%;
                img {
                  width: 18px;
                  height: 18px;
                  margin-right: 4px;
                }
                .name-box {
                  display: flex;
                  align-items: center;
                  max-width: 89%;
                  overflow: hidden;
                  font-size: 14px;
                  color: #333333;
                  font-weight: bold;
                  &:hover {
                    color: #378eef;
                  }
                  &:hover .name {
                    color: #378eef;
                  }
                  .name {
                    display: inline-block;
                    width: 100%;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    cursor: pointer;
                  }
                  .format:not(:empty) {
                    &::before {
                      content: '.';
                    }
                  }
                }
              }
              .fs-checkbox-wrapper {
                margin-right: 0;
              }
            }
            .tag-box {
              display: flex;
              flex-wrap: nowrap;
              margin-top: 8px;
              .tip-btn-container {
                flex: 1;
                max-width: min-content;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                .fs-tag {
                  flex: 1;
                  width: 100%;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
              }
            }
            .role-box {
              display: flex;
              align-items: center;
              margin-bottom: 2px;
              color: #bbbbbb;
            }
            .footer {
              display: flex;
              align-items: center;
              justify-content: space-between;
              line-height: 1;
              color: #bbbbbb;
              .btn-box {
                display: flex;
                .iconfont {
                  padding: 2px;
                }
              }
            }
          }
          @media (min-width: 1740px) {
            .card-item {
              flex: 1 1 calc(20% - 16px);
              max-width: calc(20% - 16px);
            }
          }
          @media (max-width: 1740px) {
            .card-item {
              flex: 1 1 calc(25% - 16px);
              max-width: calc(25% - 16px);
            }
          }
          @media (max-width: 1510px) {
            .card-item {
              flex: 1 1 calc(33.33% - 16px);
              max-width: calc(33.33% - 16px);
            }
          }
          @media (max-width: 1200px) {
            .card-item {
              flex: 1 1 calc(50% - 16px);
              max-width: calc(50% - 16px);
            }
          }
        }
        .footer-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: calc(100% - 22px);
          margin: 0;
          margin-right: 22px;
          margin-top: 16px;
        }
      }
    }
  }
  :deep(.fs-tree-switcher) {
    padding: 0;
  }
  .hover-btn {
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      color: #333333;
      background: #f1f4f8;
    }
  }
}
</style>

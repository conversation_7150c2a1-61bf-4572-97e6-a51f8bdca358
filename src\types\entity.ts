export interface IRes<T = null> {
  code: number // 状态码
  msg?: string // 消息
  data: T // 数据
  traceId: string
}

export interface IEntityClassData {
  children: IEntityClassData[]
  ext: string
  field: string
  id: number
  label: string
  name: string
  parentId: number
  processConfigId: number
  status: number
  value: string
}

export interface FieldList {
  dataType?: string
  entityId?: number
  fieldCode?: string
  fieldName?: string
  id?: number
  isNull?: number
  isPrimaryKey?: number
  isSystem?: number
  isUpdate?: number
  quoteEntityId?: number
  sort?: number
  status?: number
  [key: string]: any
}

export interface BasicPageParams {
  currPage?: number
  pageSize?: number
  totalCount?: number
  totalPage?: number
}

export interface IEntityPageListParams extends BasicPageParams {
  code?: string
  createdTime?: string
  createdUserName?: string
  createdUuid?: string
  currPage?: number
  describe?: string
  entityClass?: string
  entityClassValue?: string
  entityType?: number
  fieldList?: FieldList[]
  id?: number
  masterEntityId?: number
  masterEntityName?: string
  name?: string
  pageSize?: number
  queryInput?: string
  startTime?: string
  status?: number
}

export interface IEntityPageListData {
  code: string
  createdTime: string
  createdUserName: string
  createdUuid: string
  currPage: number
  describe: string
  entityClass: string
  entityClassValue: string
  entityType: number
  fieldList: FieldList[]
  id: number
  masterEntityId: number
  masterEntityName: string
  name: string
  pageSize: number
  queryInput: string
  startTime: string
  status: number
}

export interface IEntityPageList extends BasicPageParams {
  list: IEntityPageListData[]
}

export interface IEntityFormState {
  name?: string
  code?: string
  entityClass?: string
  status?: number
  entityType?: number
  masterEntityId?: number
  id?: number
  describe?: string
  esMaxResultWindow?: number
  masterEntityName?: string
}

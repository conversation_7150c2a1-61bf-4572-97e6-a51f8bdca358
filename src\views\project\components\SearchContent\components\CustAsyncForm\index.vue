<template>
  <FTooltip
    v-if="foldList?.length"
    open
    overlay-class-name="cust-search-tip-box"
    :overlay-style="{ padding: '0', maxWidth: 'none', zIndex: 10 }"
    trigger="click"
    placement="bottom"
    color="#fff"
    @visible-change="(visible:boolean) => {visibleStasus = visible}"
    :get-popup-container="target"
  >
    <template #title>
      <div class="async-search-container">
        <component
          v-for="item of search.options"
          :key="item.componentValueKey"
          :is="item.componentName"
          v-bind="item.componentAttrs"
          v-model:value="item.componentValue"
        />
      </div>
    </template>
    <span class="cust-search-title">
      更多筛选<i :class="['iconfont', visibleStasus ? 'iconjiantoushang' : 'iconjiantouxia']" />
    </span>
  </FTooltip>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, markRaw, defineAsyncComponent, nextTick } from 'vue'
import { Search } from '@/views/message-template/components/SearchContent/search'
import { getSearchConfigOptions } from '@/api/process'

interface IProps {
  value: any
  foldList: any
  queryData: any
  foldSearchObjCacheData: any
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:value', 'change'])
const search = new Search()
const value = computed({
  get: () => props?.value ?? {},
  set: val => emits('update:value', val),
})
const visibleStasus = ref<boolean>(false)
const target = ref(() => document.querySelector('#container'))

const onChange = () => {
  value.value = search.getParams()
  emits('change', value.value, search.getCacheSearch())
}
const CustRangeNumber = markRaw(defineAsyncComponent(() => import('../CustRangeNumber/index.vue')))
const searchConfigFoldList = ref<any[]>([])
const foldListOptions = ref<any[]>([])

const getOptions = async (data: any, key: string) => {
  if (!data.url) {
    data.valueList &&
      data.valueList.length &&
      search.setOptions(
        (data?.moduleType === 'TREE_SELECT' && `${key}.componentAttrs.treeData`) || `${key}.componentAttrs.options`,
        data.valueList
      )
  } else {
    const res = await getSearchConfigOptions(data.url)
    if (res.code === 200) {
      search.setOptions(
        (data?.moduleType === 'TREE_SELECT' && `${key}.componentAttrs.treeData`) || `${key}.componentAttrs.options`,
        res.data
      )
    }
  }
}

const setSearchConfigFn = async () => {
  searchConfigFoldList.value = []
  foldListOptions.value = []
  props?.foldList.forEach(item => {
    const key = `${item.searchType}:${item.field}:${item.valueType}`
    const custAttr = JSON.parse(item.propsConfig || JSON.stringify({}))

    if (item?.moduleType === 'ONE_LINE_INPUT') {
      const data = {
        componentName: 'FInput',
        componentValueKey: 'queryInput',
        componentAttrs: Object.assign(
          {},
          {
            class: 'width240 marginR12 marginB16',
            pressLine: item.name,
            placeholder: '请输入',
            allowClear: true,
            type: 'search-clear',
            onSearch: onChange,
            onClear: onChange,
          },
          custAttr
        ),
        setComponentValueFormat: async (data: any) => {
          search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
        },
      }

      searchConfigFoldList.value.push(data)
    }

    if (item?.moduleType === 'RANGE_NUMBER') {
      const data = {
        componentName: CustRangeNumber,
        componentValueKey: key,
        componentAttrs: Object.assign(
          {},
          {
            class: 'width240 marginR12 marginB16',
            pressLine: item.name,
            config: { ...item },
            onChange,
          },
          custAttr
        ),
        setComponentValueFormat: async (data: any) => {
          search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
        },
        getComponentValueFormat: (value: any) => {
          if (!value || value.length !== 2) return undefined
          const data = {}
          const config = search.getOptions(`${key}.componentAttrs.config`)
          data[`gt:${config.field}:${config.valueType}`] = value?.[0] ?? undefined
          data[`lt:${config.field}:${config.valueType}`] = value?.[1] ?? undefined
          return data
        },
      }

      searchConfigFoldList.value.push(data)
    }

    if (['SELECT', 'MULTIPLE_SELECT'].includes(item?.moduleType)) {
      const optionConfig = {
        item: { ...item },
        key,
      }
      const data = {
        componentName: 'FSelect',
        componentValueKey: key,
        componentAttrs: Object.assign(
          {},
          {
            class: 'width240 marginR12 marginB16',
            pressLine: item.name,
            placeholder: '请选择',
            showSearch: true,
            allowClear: true,
            options: [],
            optionFilterProp: 'label',
            onChange,
            onClear: onChange,
          },
          item.moduleType === 'MULTIPLE_SELECT' ? { mode: 'multiple', maxTagCount: 'responsive' } : {},
          custAttr
        ),
        setComponentValueFormat: async (data: any) => {
          search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
        },
      }

      searchConfigFoldList.value.push(data)
      foldListOptions.value.push(optionConfig)
    }

    if (['TREE_SELECT'].includes(item?.moduleType)) {
      const optionConfig = {
        item: { ...item },
        key,
      }
      const data = {
        componentName: 'FTreeSelect',
        componentValueKey: key,
        componentAttrs: Object.assign(
          {},
          {
            class: 'width240 marginR12 marginB16',
            pressLine: item.name,
            placeholder: '请选择',
            dropdownMatchSelectWidth: false,
            showSearch: true,
            allowClear: true,
            treeData: [],
            treeNodeFilterProp: 'label',
            maxTagCount: 'responsive',
            onChange,
            onClear: onChange,
          },
          custAttr
        ),
        setComponentValueFormat: async (data: any) => {
          search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
        },
      }

      searchConfigFoldList.value.push(data)
      foldListOptions.value.push(optionConfig)
    }

    if (item?.moduleType === 'DATE_PICKER') {
      const data = {
        componentName: 'FDatePicker',
        componentValueKey: key,
        componentAttrs: Object.assign(
          {},
          {
            class: 'width240 marginR12 marginB16',
            pressLine: item.name,
            valueFormat: 'YYYY-MM-DD',
            placeholder: '请选择时间',
            config: { ...item },
            onChange,
            onClear: onChange,
          },
          custAttr
        ),
        setComponentValueFormat: async (data: any) => {
          search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
        },
      }

      searchConfigFoldList.value.push(data)
    }

    if (item?.moduleType === 'RANGE_PICKER') {
      const data = {
        componentName: 'FRangePicker',
        componentValueKey: key,
        componentAttrs: Object.assign(
          {},
          {
            class: 'width240 marginR12 marginB16',
            pressLine: item.name,
            valueFormat: 'YYYY-MM-DD',
            placeholder: ['请选择时间', '请选择时间'],
            config: { ...item },
            onChange,
            onClear: onChange,
          },
          custAttr
        ),
        setComponentValueFormat: async (data: any) => {
          search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
        },
        getComponentValueFormat: (value: any) => {
          if (!value || value.length !== 2) return undefined
          const data = {}
          const config = search.getOptions(`${key}.componentAttrs.config`)
          data[`gt:${config.field}:${config.valueType}`] = value?.[0] ?? undefined
          data[`lt:${config.field}:${config.valueType}`] = value?.[1] ?? undefined
          return data
        },
      }

      searchConfigFoldList.value.push(data)
    }
  })
  search.initOptions(searchConfigFoldList?.value ?? [])
  search.setDefaultSearch(props?.foldSearchObjCacheData?.componentArgsValue ?? {})
  foldListOptions?.value?.forEach(options => getOptions(options.item, options.key))
}

onMounted(() => {
  setSearchConfigFn()
})
</script>

<style lang="scss" scoped>
.cust-search-title {
  height: 32px;
  line-height: 32px;
  margin-bottom: 24px;
  font-size: 12px;
  color: #378eef;
  cursor: pointer;
  i {
    margin-left: 4px;
    font-size: 12px;
  }
}
.async-search-container {
  width: 516px;
  display: flex;
  flex-wrap: wrap;
  padding: 16px 0 0 12px;
}
:deep(.fs-tree-select) {
  margin-bottom: 0;
}
</style>

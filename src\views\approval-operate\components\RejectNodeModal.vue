<template>
  <div class="reject-node-container">
    <FModal
      width="720px"
      v-model:visible="visible"
      :title="i18n.t('驳回')"
      :confirmLoading="confirmLoading"
      :getContainer="target"
      centered
      @ok="submitFn"
    >
      <FForm ref="formRef" :model="formState" layout="vertical">
        <FRow :gutter="[24, 0]">
          <FCol :span="12" v-if="(handleNode?.properties?.operation || []).includes(4)">
            <FFormItem
              :label="i18n.t('驳回至节点')"
              name="targetMilepostId"
              :rules="[{ required: targetMilepostIdRequired, message: i18n.t('请选择加签人员') }]"
            >
              <FSelect
                :placeholder="i18n.t('请选择节点')"
                v-model:value="formState.targetMilepostId"
                :options="beforeNodeList"
                option-filter-prop="topicName"
                :field-names="{ label: 'topicName', value: 'id' }"
                allow-clear
                show-search
              />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('审批说明')" name="msg">
              <FTextarea v-model:value="formState.msg" style="min-height: 88px" :placeholder="i18n.t('请输入')" />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem
              label="上传文件"
              name="attachment"
              :progress="{ strokeWidth: 2, showInfo: false }"
              style="width: 100%"
            >
              <FUploadDragger v-model:file-list="formState.files" action="" :before-upload="handleUpload">
                <p class="upload-drag-icon">
                  <span class="iconfont icontubiao_shangchuan_mian upload-icon"></span>
                </p>
                <p class="ant-upload-text">{{ i18n.t('单击或拖动文件到此区域') }}</p>
                <p class="ant-upload-hint">
                  {{
                    i18n.t(
                      '支持上传PDF, JPG, JPEG, PNG, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, ZIP, RAR格式文件，文件大小2G'
                    )
                  }}
                </p>
              </FUploadDragger>
            </FFormItem>
          </FCol>
        </FRow>
      </FForm>
      <div></div>
    </FModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, Ref, computed } from 'vue'
import { useI18n } from '@/utils'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { message } from '@fs/smart-design'
import { rejectApprovalNode, RefuseNodeParams, HandleNodeList, upload } from '@/api'
import { fileLimit } from '@/views/approval-operate/utils'

const i18n = useI18n()
const emits = defineEmits(['update:visible'])
const confirmLoading = ref<boolean>(false)
const handleNode = inject<Ref<HandleNodeList>>('handleNode') as Ref<HandleNodeList> // 当前里程碑信息
const beforeNodeList = ref<HandleNodeList[]>()
const getApprovalInfoFn = inject('getApprovalInfoFn') as () => void // 更新当前里程碑信息
const visible = ref<boolean>(false)
const formRef = ref<FormInstance>()
const formState = reactive<RefuseNodeParams>({
  msg: undefined,
  files: [],
  targetMilepostId: undefined,
})
const targetMilepostIdRequired = computed(() => (handleNode?.value?.properties?.operation || []).includes(4))
const target = ref(() => document.querySelector('#container'))

const handleUpload = (file: any) => {
  if (!fileLimit(file)) {
    file.status = 'error'
    return false
  }
  let key = Date.now() + '' + Math.random()
  file.status = 'uploading'
  file.key = key
  uploadItem(file, key)
  return false
}
// 检测上传进度
const onHandleUploadProgress = (progressEvent: any, key: string) => {
  const index = formState.files.findIndex((item: any) => item.key === key)
  index !== -1 &&
    (formState.files[index].percent = Math.round((progressEvent.loaded / progressEvent.total) * 10000) / 100.0)
}
const uploadItem = (file: any, key: string) => {
  let data = new FormData()
  data.append('file', file)
  data.append('isOpen', 'false')
  data.append('expire', '0')
  upload(data, onHandleUploadProgress, key).then((res: any) => {
    const index = formState.files.findIndex((item: any) => item.key === key)
    if (res && index !== -1) {
      formState.files[index].fileUrl = res
      formState.files[index].status = 'success'
    } else if (index !== -1) {
      formState.files[index].status = 'error'
      message.error(i18n.t('上传失败，请移除！'))
    }
  })
}

const submitFn = async () => {
  try {
    confirmLoading.value = true
    if (!formRef.value) {
      return
    }
    const flag = formState.files.some((item: any) => item.status !== 'success')
    if (flag) {
      message.warning(i18n.t('请检查文件是否上传完成或上传失败'))
      return
    }
    await formRef.value.validate()
    const params = {
      files: (formState?.files ?? []).map(item => ({ fileName: item.name, size: item.size, url: item.fileUrl })),
      instanceId: handleNode?.value?.instanceId,
      msg: formState?.msg || undefined,
      sourceMilepostId: handleNode?.value?.id,
      targetMilepostId:
        formState?.targetMilepostId ||
        beforeNodeList.value.find(item => item.nextMilepostId == handleNode.value.id)?.id,
    }
    const res = await rejectApprovalNode(params)
    if (res.code !== 200) throw new Error(res.msg)
    message.success(res.msg || i18n.t('驳回成功！'))
    getApprovalInfoFn()
    visible.value = false
  } finally {
    confirmLoading.value = false
  }
}

const open = data => {
  formRef.value?.resetFields()
  formState.msg = undefined
  formState.files = []
  formState.targetMilepostId = undefined
  beforeNodeList.value = data
  visible.value = true
}

defineExpose({ open })
</script>
<style scoped lang="scss">
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
:deep(.fs-modal-footer) {
  padding: 12px 24px !important;
}
.upload-drag-icon {
  margin: 0;
  padding: 0;
  height: 48px;
  .upload-icon {
    color: #d8d8d8;
    font-size: 36px;
  }
}
</style>

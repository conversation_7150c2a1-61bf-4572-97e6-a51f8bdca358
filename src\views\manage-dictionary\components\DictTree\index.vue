<template>
  <div class="dict-tree-container">
    <template v-for="(element, index) in treeData" :key="index">
      <DictTreeNode
        :node="element"
        :level="0"
        :index="index"
        :sibling-count="treeData.length"
        :accordion="accordion"
        :lazy-load="lazyLoad"
        :selected-node-id="selectedNodeId"
        :drag-api="dragApi"
        @toggle="handleToggle"
        @lazy-load="emit('lazy-load', $event)"
        @node-select="(node, selected) => emit('node-select', node, selected)"
      >
        <!-- 传递操作按钮插槽 -->
        <template #actions="slotProps">
          <slot name="actions" v-bind="slotProps" />
        </template>
      </DictTreeNode>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, withDefaults } from 'vue'
import DictTreeNode from './DictTreeNode.vue'
import { deepClone } from '@/utils'

// 定义组件属性
const props = withDefaults(
  defineProps<{
    data: any[]
    accordion?: boolean
    lazyLoad?: ((params: any) => Promise<any>) | null
    selectedNodeId?: string
    dragApi?: ((params: any) => Promise<any>) | null
  }>(),
  {
    accordion: false,
    lazyLoad: null,
    selectedNodeId: '',
    dragApi: null,
  }
)

// 定义事件
const emit = defineEmits(['node-toggle', 'lazy-load', 'node-select'])

// 响应式数据
const treeData = ref<any[]>([])

// 初始化数据 - 直接使用传入的组件前缀字段数据
const initTreeData = () => {
  treeData.value = deepClone(props.data)
}

// 处理节点展开/折叠
const handleToggle = (nodeId: string, isAccordion = false) => {
  const toggleNode = (nodes: any[], level = 0) => {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i]
      if (node.dict_tree_id === nodeId) {
        node.dict_tree_expanded = !node.dict_tree_expanded

        // 手风琴模式：如果展开当前节点，关闭同级其他节点
        if (isAccordion && node.dict_tree_expanded) {
          nodes.forEach(sibling => {
            if (sibling.dict_tree_id !== nodeId) {
              sibling.dict_tree_expanded = false
            }
          })
        }

        emit('node-toggle', node)
        return true
      }
      if (node.dict_tree_children && toggleNode(node.dict_tree_children, level + 1)) {
        return true
      }
    }
    return false
  }

  toggleNode(treeData.value)
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    initTreeData()
  },
  { immediate: true, deep: true }
)

// 暴露方法 - 直接操作组件前缀字段
defineExpose({
  getTreeData: () => treeData.value,
  expandAll: () => {
    const expandNode = (nodes: any[]) => {
      nodes.forEach(node => {
        node.dict_tree_expanded = true
        if (node.dict_tree_children && node.dict_tree_children.length > 0) {
          expandNode(node.dict_tree_children)
        }
      })
    }
    expandNode(treeData.value)
  },
  collapseAll: () => {
    const collapseNode = (nodes: any[]) => {
      nodes.forEach(node => {
        node.dict_tree_expanded = false
        if (node.dict_tree_children && node.dict_tree_children.length > 0) {
          collapseNode(node.dict_tree_children)
        }
      })
    }
    collapseNode(treeData.value)
  },
  expandNode: (nodeId: string) => {
    const findAndExpand = (nodes: any[]): boolean => {
      for (const node of nodes) {
        if (node.dict_tree_id === nodeId) {
          node.dict_tree_expanded = true
          return true
        }
        if (node.dict_tree_children && findAndExpand(node.dict_tree_children)) {
          return true
        }
      }
      return false
    }
    findAndExpand(treeData.value)
  },
  collapseNode: (nodeId: string) => {
    const findAndCollapse = (nodes: any[]): boolean => {
      for (const node of nodes) {
        if (node.dict_tree_id === nodeId) {
          node.dict_tree_expanded = false
          return true
        }
        if (node.dict_tree_children && findAndCollapse(node.dict_tree_children)) {
          return true
        }
      }
      return false
    }
    findAndCollapse(treeData.value)
  },
})
</script>

<style scoped lang="scss">
.dict-tree {
  width: 100%;

  .dict-tree-container {
    background-color: #fff;
  }
}

.ghost-item {
  opacity: 0.5;
  background-color: #f0f0f0;
}

.chosen-item {
  background-color: #e6f7ff;
}
</style>

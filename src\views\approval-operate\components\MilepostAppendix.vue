<template>
  <div class="milepost-appendix" v-if="files && files.length">
    <div class="milepost-appendix-item" v-for="file in files" :key="file.url">
      <p>
        {{ i18n.t('文件名') }}：<a @click.stop="download(file.url, file.name)" :title="file.name">{{ file.name }}</a>
      </p>
      <p>{{ i18n.t('文件大小') }}：{{ (file.size / 1024 / 1024).toFixed(2) }} MB</p>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useI18n, download } from '@/utils'

interface IProps {
  formData: any
}

const i18n = useI18n()
const props = defineProps<IProps>()
const files = computed(() => props.formData?.file ?? [])
</script>
<style lang="scss" scoped>
.milepost-appendix {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 20px;
  box-sizing: border-box;

  .milepost-appendix-item {
    flex: 0 0 calc(25% - 10px);
    display: flex;
    padding: 12px 6px;
    margin: 0 10px 10px 0;
    justify-content: space-between;
    flex-direction: column;
    box-sizing: border-box;
    border: 1px solid #e8e8e8;
    overflow: hidden;

    > p {
      margin: 0;
      padding: 0;
      font-size: 12px;
      color: #666;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>

<template>
  <div class="history-record">
    <icon class="iconfont iconshanjian1" />
    <div
      :class="['history-record-item', { active: actionHistoryRecord?.fullPath === item.fullPath }]"
      v-for="item in historyRecord"
      :key="item.fullPath"
      @click="handleActionClick(item)"
    >
      <span>{{ item.title }}</span>
      <icon class="iconfont icontubiao_shanchu11" @click.stop="handleRemoveClick(item)" />
    </div>
    <FDropdown :trigger="['click']">
      <icon class="iconfont iconshanchu21" v-show="historyRecord.length" />
      <template #overlay>
        <FMenu @click="handleClearClick">
          <FMenuItem key="closeAll">关闭所有</FMenuItem>
          <FMenuItem key="closeOther">关闭其他</FMenuItem>
        </FMenu>
      </template>
    </FDropdown>
  </div>
</template>

<script setup lang="ts">
import { IHistoryRecord } from '@/types/common'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

const store = useStore()
const route = useRoute()
const router = useRouter()
const actionHistoryRecord = ref<IHistoryRecord>()
const historyRecord = computed<IHistoryRecord[]>(() => store.getters.historyRecord)

watch(
  () => route.fullPath,
  () => {
    const record: IHistoryRecord = {
      fullPath: route.fullPath,
      title: (route.meta.title || route.name || '未命名') as string,
    }
    actionHistoryRecord.value = record
    store.commit('global/ADD_HISTORY_RECORD', record)
  },
  { immediate: true }
)

const handleClearClick = ({ key }) => {
  if (key === 'closeAll') store.commit('global/CLEAR_HISTORY_RECORD')
  else if (key === 'closeOther') store.commit('global/CLEAR_OTHER_HISTORY_RECORD', actionHistoryRecord.value)
}

const handleActionClick = (item: IHistoryRecord) => {
  router.push(item.fullPath)
}

const handleRemoveClick = (item: IHistoryRecord) => {
  store.commit('global/REMOVE_HISTORY_RECORD', item)
}
</script>

<style scoped lang="scss">
.history-record {
  display: flex;
  align-items: center;
  height: 38px;
  margin: 8px 24px 24px 24px;
  padding: 0 12px;
  border: 1px solid #ffffff;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.4);

  .iconshanjian1 {
    margin-right: 8px;
    color: #b6c4d3;
    font-size: 20px;
  }

  .iconshanchu21 {
    width: 20px;
    height: 20px;
    line-height: 20px;
    color: #a0b1c5;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;

    &:hover {
      color: #333;
      background-color: #d6e6ff;
    }
  }

  .history-record-item {
    position: relative;
    display: flex;
    align-items: center;
    height: 26px;
    margin-right: 16px;
    padding: 4px 8px;
    color: #8391a0;
    font-size: 12px;
    border-radius: 2px;
    box-sizing: border-box;
    cursor: pointer;

    &:hover {
      color: #333;
    }

    &.active {
      color: #333;
      background: #d6e6ff;
    }

    &::after {
      position: absolute;
      top: 5px;
      right: -8px;
      content: ' ';
      width: 1px;
      height: 16px;
      background-color: #b6c4d3;
    }

    .icontubiao_shanchu11 {
      margin-left: 8px;
      color: #8391a0;
      font-size: 16px;

      &:hover {
        color: #333;
      }
    }
  }
}
</style>

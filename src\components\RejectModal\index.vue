<template>
  <FModal v-model:visible="value" :title="i18n.t('驳回')" :width="400" :z-index="1001">
    <div class="reject-modal-wrapper">
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <div class="finish-desc-tag" v-if="rejectDescTags.length">
          <span class="title">流程驳回标签：</span>
          <div class="tag-box">
            <f-tag
              v-for="(item, index) in rejectDescTags"
              :key="item"
              :checked="formState.rejectDesc === item"
              @update:checked="() => (formState.rejectDesc = item)"
              size="small"
              :border="true"
              :color="labelColor[index % 5]"
              checkable
              >{{ item }}</f-tag
            >
          </div>
        </div>
        <FFormItem :label="i18n.t('请选择驳回节点')" name="targetMilepostId">
          <FSelect
            :placeholder="i18n.t('请选择驳回节点')"
            v-model:value="formState.targetMilepostId"
            :field-names="{ label: 'topicName', value: 'id' }"
            :options="rejectList"
            allow-clear
            show-search
          />
        </FFormItem>
        <FFormItem :label="i18n.t('备注说明：')" name="msg">
          <FTextarea v-model:value="formState.msg" :rows="4" :placeholder="i18n.t('请输入')" />
        </FFormItem>
      </FForm>
    </div>
    <template #footer>
      <FButton key="back" @click="value = false">{{ i18n.t('取消') }}</FButton>
      <FButton key="submit" type="primary" @click="handleOk(formRef)">{{ i18n.t('确定') }}</FButton>
    </template>
  </FModal>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, watch, inject } from 'vue'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { message } from '@fs/smart-design'
import { useI18n } from '@/utils'
interface FormState {
  targetMilepostId: number | string
  msg: string
  rejectDesc?: string | undefined
}
const emit = defineEmits(['submit', 'update:value'])
const i18n = useI18n()
const props = defineProps({
  value: {
    type: Boolean,
    default: false,
  },
  rejectList: {
    type: Object,
    default: () => ({}),
  },
})
watch(
  () => props.rejectList,
  newValue => {
    formState.targetMilepostId = newValue?.[newValue.length - 1]?.id
  }
)

const value = computed({
  get() {
    return props.value
  },
  set(val: boolean) {
    emit('update:value', val)
  },
})

watch(
  () => props.value,
  val => {
    if (val) {
      formState.msg = ''
      formState.rejectDesc = undefined
    }
  }
)

const rules: Record<string, Rule[]> = {
  targetMilepostId: [{ required: true, message: i18n.t('请选择驳回节点'), trigger: 'change' }],
  msg: [{ required: true, message: i18n.t('请填写备注说明'), trigger: 'blur' }],
}
const formRef = ref<FormInstance>()
const formState = reactive<FormState>({
  targetMilepostId: '',
  msg: '',
  rejectDesc: undefined,
})
const labelColor = ref<string[]>(['success', 'processing', 'error', 'warning', 'default'])
const processConfigInfo = inject('processConfigInfo') as Record<string, any> // 当前流程配置信息
const rejectDescTags = computed(() => {
  return (processConfigInfo?.value?.rejectDesc || undefined)?.split(',') || []
})

const handleOk = async (formRef: FormInstance | undefined) => {
  if (!formRef) return
  await formRef.validate()
  if (rejectDescTags.value.length && !formState.rejectDesc) {
    message.warning('请选择驳回标签！')
    return
  }
  emit('submit', formState)
}
</script>
<style lang="scss">
.reject-modal-wrapper {
  .fs-form-item {
    margin-bottom: 22px !important;
  }
  div.fs-form-item-control-input-content,
  .fs-form-item-control-input-content textarea.fs-input {
    height: auto !important;
  }

  .fs-modal-footer .fs-btn {
    display: inline-block;
  }
}
</style>
<style lang="scss" scoped>
.finish-desc-tag {
  margin-bottom: 14px;
  .title {
    display: inline-block;
    margin-bottom: 8px;
    color: #333333;
    line-height: 18px;
    font-size: 12px;
    &::before {
      display: inline-block;
      margin-right: 4px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }
  }
  .tag-box {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    :deep(.fs-tag) {
      margin-bottom: 8px;
      margin-right: 8px;
      font-size: 12px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
:deep(.fs-checkbox-inner) {
  width: 14px;
  height: 14px;
}
:deep(.fs-checkbox-wrapper span:last-child) {
  padding-left: 8px;
  padding-right: 0;
  color: #666666;
  line-height: 18px;
  font-size: 12px;
}
</style>

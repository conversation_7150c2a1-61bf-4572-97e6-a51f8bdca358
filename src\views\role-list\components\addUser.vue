<template>
  <div class="create-creative" ref="createCreative">
    <FModal
      v-model:visible="dialogVisible"
      :title="title"
      :width="400"
      @cancel="close"
      :get-container="() => createCreative"
    >
      <div>
        <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
          <FFormItem :label="i18n.t('人员')" name="uuids">
            <FSelect
              v-model:value="formState.uuids"
              mode="multiple"
              style="width: 100%"
              :placeholder="i18n.t('请选择添加人员')"
              :options="userAll"
              :filter-option="filterOption"
              :disabled="edit"
            ></FSelect>
          </FFormItem>
          <FFormItem v-if="roleType && roleType === 3" :label="i18n.t('表达式')" name="conditionExpression">
            <FTextarea v-model:value="formState.conditionExpression" :rows="4" :placeholder="i18n.t('请输入')" />
          </FFormItem>
        </FForm>
      </div>
      <template #footer>
        <FConfigProvider :auto-insert-space-in-button="false">
          <FButton key="back" @click="close">{{ i18n.t('取消') }}</FButton>
          <FButton key="submit" type="primary" @click="handleOk()">{{ i18n.t('确定') }}</FButton>
        </FConfigProvider>
      </template>
    </FModal>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch, unref } from 'vue'
import { getAllUsers } from '@/api'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { useI18n } from '@/utils'

const i18n = useI18n()

onMounted(() => {
  getUserData()
})

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  show: { type: Boolean },
  edit: { type: Boolean },
  roleType: { type: Number },
  defaultRole: {
    type: Array,
    default: () => [],
  },
  defaultText: {
    type: String,
    default: undefined,
  },
})

type IFormState = {
  uuids: any[]
  conditionExpression: string | undefined
}

const emit = defineEmits(['submit', 'popupClose'])
const formRef = ref<FormInstance>()
const formState = ref<IFormState>({
  uuids: [],
  conditionExpression: undefined,
})
const rules: Record<string, Rule[]> = {
  uuids: [{ required: true, message: i18n.t('请选择需要添加的人员') }],
  conditionExpression: [{ required: false, message: i18n.t('请输入表达式') }],
}
const dialogVisible = ref(false)
const createCreative = ref<HTMLElement>()
//获取全部人员
const userAll = ref<any[]>()
const getUserData = async () => {
  const res = await getAllUsers()
  res.data.forEach((item: any) => {
    item.value = item.uuid
    item.label = item.feiShuName
  })
  userAll.value = res.data
}

const close = () => {
  formState.value.uuids = []
  formState.value.conditionExpression = undefined
  formRef.value?.resetFields()
  emit('popupClose')
}

const handleOk = async () => {
  const $form = formRef.value as FormInstance
  await $form.validate()
  emit('submit', JSON.parse(JSON.stringify(unref(formState.value))))
  close()
}

const filterOption = (input: string, option: Record<string, string>) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

watch([() => props.show, () => props.defaultRole, () => props.defaultText], newValue => {
  dialogVisible.value = newValue[0]
  formState.value.uuids = newValue[1]
  formState.value.conditionExpression = newValue[2] || undefined
})
</script>
<style lang="scss" scoped>
.create-creative {
  :deep(.fs-form-item-control-input-content) {
    height: auto !important;
    .textarea.fs-input {
      height: auto !important;
    }
  }
}
</style>

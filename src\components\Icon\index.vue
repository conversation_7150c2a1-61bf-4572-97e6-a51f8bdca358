<template>
  <i :class="['iconfont', props.icon]" :style="style" />
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface IProps {
  icon: string
  size?: string | number
  color?: string
}

const props = defineProps<IProps>()
const style = computed(() => ({ fontSize: handleSize(props.size), color: props.color || '' }))
const handleSize = (size?: number | string) => {
  if (!size) return '14px'
  if (typeof size === 'number' || !Number.isNaN(+size)) return `${size}px`
  return size
}
</script>

<style scoped lang="scss">
.iconfont {
  transition: color 0.5s;
}
</style>

<template>
  <div style="display: flex; flex-wrap: wrap">
    <template v-for="item of search.options" :key="item.componentValueKey">
      <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, nextTick, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { Search } from '@/views/message-template/components/SearchContent/search'
import { cache } from '@/utils'
import { getModifyProcessDictionary } from '@/api/processBoard'

interface IProps {
  queryData: any
}

const { currentRoute } = useRouter()
const props = defineProps<IProps>()
const emits = defineEmits(['update:queryData'])
const store = useStore()
const allUserList = computed(() => store.state.user.allUser || [])
const search = new Search()
const queryData = computed({
  get: () => props.queryData,
  set: val => emits('update:queryData', val),
})
const routerName = computed<any>(() => currentRoute.value?.name)
const dictionary = ref<any>([])
const typeOptions = ref<any>([])
const xqTypeOptions = ref<any>([])
const firstClassOptions = ref<any>([])

const onChange = () =>
  (queryData.value = {
    ...search.getParams(),
    ...{
      cacheValue: search.getCacheSearch(),
    },
  })

const configList = [
  {
    componentName: 'FSelect',
    componentValueKey: 'typeList',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '产品线'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      mode: 'multiple',
      maxTagCount: 'responsive',
      options: computed(() => typeOptions.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('typeList.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'firstClassList',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '项目类型'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      mode: 'multiple',
      maxTagCount: 'responsive',
      options: computed(() => firstClassOptions.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('firstClassList.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'xqTypeList',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '需求类型'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      mode: 'multiple',
      maxTagCount: 'responsive',
      options: computed(() => xqTypeOptions.value || []),
      optionFilterProp: 'label',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('xqTypeList.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'userList',
    componentAttrs: {
      class: 'width120 marginR12 marginB24',
      pressLine: computed(() => '项目处理人'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      mode: 'multiple',
      maxTagCount: 'responsive',
      options: allUserList,
      fieldNames: { value: 'uuid', label: 'feiShuName' },
      optionFilterProp: 'feiShuName',
      onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('userList.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FRangePicker',
    componentValueKey: 'time',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '录入时间'),
      valueFormat: 'YYYY-MM-DD',
      onChange,
    },
    getComponentValueFormat: (value: any) => {
      if (!value || value.length !== 2) return undefined
      return {
        startTime: value[0] + ' 00:00:00',
        endTime: value[1] + ' 23:59:59',
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions(
        'time.componentValue',
        ((data || {})?.componentValue && [data.componentValue[0], data.componentValue[1]]) || undefined
      )
    },
  },
]

const setSearchConfigFn = () => {
  search.initOptions(configList)
  search.clear()
  const cachData = (cache.get(routerName?.value) && JSON.parse(cache.get(routerName?.value) as string)) || {}
  search.setDefaultSearch(cachData)
  nextTick(() => {
    queryData.value = {
      ...search.getParams(),
      ...{
        cacheValue: cachData,
      },
    }
  })
}

const getModifyProcessDictionaryFn = async () => {
  const res = await getModifyProcessDictionary()
  dictionary.value = res.data
  typeOptions.value = (dictionary.value ?? []).filter(item => item.field === 'type')
  xqTypeOptions.value = (dictionary.value ?? []).filter(item => item.field === 'xq_type')
  firstClassOptions.value = (dictionary.value ?? []).filter(item => item.field === 'first_class')
}

onMounted(() => {
  setSearchConfigFn()
  getModifyProcessDictionaryFn()
})
</script>

<style lang="scss" scoped>
:deep(.fs-input-affix-wrapper) {
  padding: 6px 8px !important;
}
</style>

import { getUserInfo } from '@/utils'
import type { triggerListSearch } from '@/types/excutionListModel'
import type { ProcessListSearch, ILocalProjectUuidData } from '@/types/processListModel'

type IProcessListSearchInfo = {
  [key: string]: ProcessListSearch | undefined
}

type ILocalProjectUuidDataInfo = {
  [key: string]: ILocalProjectUuidData | undefined
}

type IBoardItem = {
  componentValue?: unknown
  componentArgsValue?: Record<string, unknown>
  [key: string]: unknown
}

type IProcessBoardSearchInfo = {
  [key: string]: IBoardItem
}

type IAutomationSearchInfo = {
  [key: string]: triggerListSearch
}

type ILocalUserData = {
  [key: string]: unknown
}

interface LocalStore {
  localCountryId?: number[]
  localSearchData?: IProcessListSearchInfo
  localProjectUuidData?: ILocalProjectUuidDataInfo
  localBoardSearchData?: IProcessBoardSearchInfo
  localProcessManagementSearchData?: IProcessBoardSearchInfo
  localUserData?: ILocalUserData
  localAutomationSearchData?: IAutomationSearchInfo
}

// 获取本地存储数据的辅助函数
const getStorageItem = (key: string, storage: Storage = sessionStorage) => {
  try {
    const item = storage.getItem(key)
    return item ? JSON.parse(item) : undefined
  } catch (error) {
    console.warn(`Failed to parse storage item ${key}:`, error)
    return undefined
  }
}

// 设置本地存储数据的辅助函数
const setStorageItem = (key: string, value: unknown, storage: Storage = sessionStorage) => {
  try {
    storage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Failed to set storage item ${key}:`, error)
  }
}

const state: LocalStore = {
  localCountryId: getStorageItem('LOCAL_COUNTRY_ID'),
  localSearchData: getStorageItem('LOCAL_SEARCH_DATA'),
  localProjectUuidData: getStorageItem('LOCAL_PROJECT_UUID_DATA'),
  localBoardSearchData: getStorageItem('LOCAL_BOARD_SEARCH_DATA'),
  localProcessManagementSearchData: getStorageItem('LOCAL_PROCESS_MANAGEMENT_DATA'),
  localUserData: undefined,
  localAutomationSearchData: getStorageItem('LOCAL_AUTOMATION_SEARCH_DATA'),
}
const mutations = {
  SET_LOCALCOUNTRYID: (state: LocalStore, localCountryId: number[]) => {
    state.localCountryId = localCountryId
    setStorageItem('LOCAL_COUNTRY_ID', localCountryId)
  },
  SET_LOCAL_SEARCH_DATA: (state: LocalStore, localSearchData: ProcessListSearch) => {
    const info = Object.assign({}, state.localSearchData, localSearchData)
    state.localSearchData = info
    setStorageItem('LOCAL_SEARCH_DATA', info)
  },
  SET_LOCAL_PROJECT_UUID_DATA: (state: LocalStore, localProjectUuidData: ILocalProjectUuidData) => {
    const info = Object.assign({}, state.localProjectUuidData, localProjectUuidData)
    state.localProjectUuidData = info
    setStorageItem('LOCAL_PROJECT_UUID_DATA', info)
  },
  SET_LOCAL_BOARD_SEARCH_DATA: (state: LocalStore, localBoardSearchData: IProcessBoardSearchInfo) => {
    state.localBoardSearchData = localBoardSearchData
    setStorageItem('LOCAL_BOARD_SEARCH_DATA', localBoardSearchData)
  },
  SET_LOCAL_PROCESS_MANAGEMENT_SEARCH_DATA: (
    state: LocalStore,
    localProcessManagementSearchData: IProcessBoardSearchInfo
  ) => {
    const info = { ...state.localProcessManagementSearchData, ...localProcessManagementSearchData }
    state.localProcessManagementSearchData = info
    setStorageItem('LOCAL_PROCESS_MANAGEMENT_DATA', info)
  },
  SET_LOCAL_USER_DATA: (state: LocalStore, localUserData: ILocalUserData) => {
    const userInfo = getUserInfo()
    const info = {
      feiShuName: userInfo.feiShuName || '--',
      ...state.localUserData,
      ...localUserData,
    }
    state.localUserData = info
    setStorageItem(`LOCAL_USER_DATA_${userInfo.adminId}`, info, localStorage)
  },
  SET_LOCAL_AUTOMATION_SEARCH_DATA: (state: LocalStore, localAutomationSearchData: IAutomationSearchInfo) => {
    state.localAutomationSearchData = localAutomationSearchData
    setStorageItem('LOCAL_AUTOMATION_SEARCH_DATA', localAutomationSearchData)
  },
}

const handleGetLocalUserData = (state: LocalStore) => {
  if (state.localUserData) return state.localUserData
  const userInfo = getUserInfo()
  return getStorageItem(`LOCAL_USER_DATA_${userInfo.adminId}`, localStorage)
}

const getters = {
  getLocalCountryId: (state: LocalStore) => state.localCountryId,
  getLocalSearchData: (state: LocalStore) => state.localSearchData,
  getLocalProjectUuidData: (state: LocalStore) => state.localProjectUuidData,
  getLocalBoardUuidData: (state: LocalStore) => state.localBoardSearchData,
  getLocalProcessManagementSearchData: (state: LocalStore) => state.localProcessManagementSearchData,
  getLocalUserData: handleGetLocalUserData,
  getLocalAutomationData: (state: LocalStore) => state.localAutomationSearchData,
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
}

import { h, type VNode, type Component } from 'vue'
import MoreTextTips from '@/components/MoreTextTips/index'

// 渲染上下文类型
export interface RenderContext {
  fieldKey: string
  fieldValue: any
  fieldConfig: any
  formData: any
}

// 提示类型定义
export type TipText = 
  | string 
  | ((fieldValue: any, formData: any, fieldConfig: any) => string)
  | { component: Component | string; props?: Record<string, any> }

// 渲染策略接口
export interface RenderStrategy {
  test: (tipText: any) => boolean
  render: (tipText: any, context: RenderContext) => VNode
}

// 基础渲染器工厂
export class TipRenderFactory {
  private strategies: RenderStrategy[] = []

  // 注册渲染策略
  register(strategy: RenderStrategy) {
    this.strategies.push(strategy)
    return this
  }

  // 渲染提示
  render(tipText: TipText, context: RenderContext): VNode {
    // 找到匹配的策略
    const strategy = this.strategies.find(s => s.test(tipText))
    
    if (strategy) {
      return this.wrapWithContainer(strategy.render(tipText, context))
    }
    
    // 兜底渲染
    return this.wrapWithContainer(this.renderFallback(tipText))
  }

  // 包装容器
  private wrapWithContainer(content: VNode): VNode {
    return h('div', { style: 'width: max-content;' }, [content])
  }

  // 兜底渲染
  private renderFallback(tipText: any): VNode {
    return this.createTextTip(String(tipText || ''))
  }

  // 创建文本提示
  createTextTip(text: string, config: any = {}): VNode {
    const defaultConfig = {
      lineClamp: 1,
      bgColor: '#000',
      textColor: '#fff',
      ...config
    }
    
    return h(MoreTextTips, defaultConfig, {
      default: () => this.createTipContent(text)
    })
  }

  // 创建提示内容
  createTipContent(text: string): VNode {
    return h('span', { style: 'margin-left: 4px' }, [
      h('i', { 
        class: 'iconfont icontubiao_tishi_mian',
        style: 'margin-right: 2px; font-size: 14px'
      }),
      h('span', text)
    ])
  }

  // 创建组件提示
  createComponentTip(component: Component | string, props: any = {}, context: RenderContext): VNode {
    return h(component, {
      ...props,
      fieldKey: context.fieldKey,
      fieldValue: context.fieldValue,
      fieldConfig: context.fieldConfig,
      formData: context.formData
    })
  }
}

// 预定义的渲染策略
export const builtinStrategies = {
  // 字符串策略
  string: {
    test: (tipText: any) => typeof tipText === 'string',
    render: (tipText: string, context: RenderContext, factory: TipRenderFactory) => 
      factory.createTextTip(tipText)
  },

  // 函数策略
  function: {
    test: (tipText: any) => typeof tipText === 'function',
    render: (tipText: Function, context: RenderContext, factory: TipRenderFactory) => {
      const text = tipText(context.fieldValue, context.formData, context.fieldConfig)
      return factory.createTextTip(text)
    }
  },

  // 组件策略
  component: {
    test: (tipText: any) => 
      tipText && 
      typeof tipText === 'object' && 
      'component' in tipText && 
      tipText.component,
    render: (tipText: any, context: RenderContext, factory: TipRenderFactory) => 
      factory.createComponentTip(tipText.component, tipText.props, context)
  },

  // 条件策略
  conditional: {
    test: (tipText: any) => 
      tipText && 
      typeof tipText === 'object' && 
      'condition' in tipText,
    render: (tipText: any, context: RenderContext, factory: TipRenderFactory) => {
      const shouldShow = tipText.condition(context.fieldValue, context.formData, context.fieldConfig)
      const selectedTip = shouldShow ? tipText.trueTip : tipText.falseTip
      
      if (!selectedTip) return factory.createTextTip('')
      
      return factory.render(selectedTip, context)
    }
  }
}

// 创建默认渲染器
export const createDefaultRenderer = (): TipRenderFactory => {
  const factory = new TipRenderFactory()

  // 注册内置策略
  factory
    .register({
      test: builtinStrategies.string.test,
      render: (tipText, context) => builtinStrategies.string.render(tipText, context, factory)
    })
    .register({
      test: builtinStrategies.function.test,
      render: (tipText, context) => builtinStrategies.function.render(tipText, context, factory)
    })
    .register({
      test: builtinStrategies.component.test,
      render: (tipText, context) => builtinStrategies.component.render(tipText, context, factory)
    })
    .register({
      test: builtinStrategies.conditional.test,
      render: (tipText, context) => builtinStrategies.conditional.render(tipText, context, factory)
    })

  return factory
}

// 高阶渲染函数
export const createTipRenderer = (customStrategies: RenderStrategy[] = []) => {
  const renderer = createDefaultRenderer()
  
  // 注册自定义策略
  customStrategies.forEach(strategy => renderer.register(strategy))
  
  return (tipText: TipText, context: RenderContext) => renderer.render(tipText, context)
}

// 渲染函数组合器
export const composeTipRenderers = (...renderers: Array<(tipText: any, context: RenderContext) => VNode | null>) => {
  return (tipText: TipText, context: RenderContext): VNode => {
    for (const renderer of renderers) {
      const result = renderer(tipText, context)
      if (result) return result
    }
    
    // 兜底
    return h('div', { style: 'width: max-content;' }, [
      h('span', { style: 'color: #999; font-size: 12px;' }, '无效提示')
    ])
  }
}

// 提示配置构建器
export class TipConfigBuilder {
  private config: any = {}

  static create() {
    return new TipConfigBuilder()
  }

  // 文本提示
  text(text: string, options?: { bgColor?: string; textColor?: string }) {
    this.config = text
    return this
  }

  // 动态文本
  dynamic(fn: (fieldValue: any, formData: any, fieldConfig: any) => string) {
    this.config = fn
    return this
  }

  // 组件提示
  component(component: Component | string, props?: Record<string, any>) {
    this.config = { component, props }
    return this
  }

  // 条件提示
  conditional(
    condition: (fieldValue: any, formData: any, fieldConfig: any) => boolean,
    trueTip: TipText,
    falseTip?: TipText
  ) {
    this.config = { condition, trueTip, falseTip }
    return this
  }

  // 构建配置
  build(): TipText {
    return this.config
  }
}

// 便捷的构建器函数
export const tip = TipConfigBuilder.create

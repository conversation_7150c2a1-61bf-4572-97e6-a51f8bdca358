import { h, type VNode, type Component } from 'vue'
import MoreTextTips from '@/components/MoreTextTips/index'

// 简化的提示类型定义
export type TipText =
  | string
  | ((fieldValue: any, formData: any, fieldConfig: any) => string | VNode)
  | { component: Component | string; props?: Record<string, any> }

// 简化的渲染函数 - 只做核心逻辑
export const renderTip = (
  tipText: TipText,
  fieldValue: any,
  formData: any,
  fieldConfig: any
): VNode => {
  // 字符串 - 渲染文本提示
  if (typeof tipText === 'string') {
    return h('div', { style: 'width: max-content;' }, [
      h(MoreTextTips, {
        lineClamp: 1,
        bgColor: '#000',
        textColor: '#fff'
      }, {
        default: () => h('span', { style: 'margin-left: 4px' }, [
          h('i', {
            class: 'iconfont icontubiao_tishi_mian',
            style: 'margin-right: 2px; font-size: 14px'
          }),
          h('span', tipText)
        ])
      })
    ])
  }

  // 函数 - 执行函数，用户自己决定返回什么
  if (typeof tipText === 'function') {
    const result = tipText(fieldValue, formData, fieldConfig)

    // 如果用户返回的是 VNode，直接使用
    if (result && typeof result === 'object' && result.type) {
      return h('div', { style: 'width: max-content;' }, [result])
    }

    // 如果用户返回的是字符串，渲染为文本提示
    if (typeof result === 'string') {
      return renderTip(result, fieldValue, formData, fieldConfig)
    }

    // 其他情况返回空
    return h('div', { style: 'display: none;' })
  }

  // 对象 - 渲染自定义组件
  if (tipText && typeof tipText === 'object' && tipText.component) {
    return h('div', { style: 'width: max-content;' }, [
      h(tipText.component as any, {
        ...(tipText.props || {}),
        fieldValue,
        formData,
        fieldConfig
      })
    ])
  }

  // 兜底 - 空渲染
  return h('div', { style: 'display: none;' })
}

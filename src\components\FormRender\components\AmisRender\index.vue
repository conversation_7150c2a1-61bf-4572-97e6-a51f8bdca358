<template>
  <div class="amis-render">
    <Render
      ref="renderRef"
      :schema="props.schema"
      :props="markRaw(props.globalData)"
      :config="{ ...AmisRenderConfig, fetcher: fetcher }"
    />
  </div>
</template>

<script setup lang="ts">
import { markRaw, ref } from 'vue'
import { Render } from 'fs-speed'
import { AmisRenderConfig } from '../..'

interface IProps {
  schema: unknown
  globalData: Record<string, unknown>
  fetcher: any
}

const props = defineProps<IProps>()
const renderRef = ref()

defineExpose({
  getScoped: () => renderRef.value?.getAmisInstance?.(),
})
</script>

<style scoped lang="scss">
.amis-render {
  width: 100%;
}
</style>

import store from '@/store'
import { createRouter, createWebHistory, type RouteLocationNormalizedLoaded } from 'vue-router'
import {
  BaseRoute,
  feishuOAuth,
  getMicroAppInstance,
  getPower,
  getToken,
  getUserInfo,
  isFeishu,
  isMicroApp,
  logout,
  setLayoutShow,
  updatePower,
} from '@/utils'

import { routes, viewRouter } from './route-record'
import type { IViewPower } from '@fs/hooks'

const router = createRouter({
  history: createWebHistory(BaseRoute),
  routes,
})

// 处理布局显示 - 默认情况下，自身的布局是 隐藏 的
const hanldeLayoutShow = (to: RouteLocationNormalizedLoaded) => {
  const { hideLayout } = to?.meta ?? {}
  const isSap = (to?.query?.env as string)?.toUpperCase() == 'SAP'
  const flag = isSap ? false : !hideLayout // 是否显示头部
  setLayoutShow(flag)
}

const checkRoute = async (to: RouteLocationNormalizedLoaded) => {
  if (['/403', '/404'].includes(to.path)) return true

  const token = getToken()
  const userInfo = getUserInfo()
  const { code } = to.query

  //  erp 项目跳转到 bpm 时携带的 token
  const extraToken = window.location.search.match(/token=([^&]*)/)?.[1]

  if (isFeishu) {
    if (!token && code) {
      const redirect = (to.query.redirect || '/') as string
      await store.dispatch('user/loginByCode', code)
      return { path: redirect, replace: true, query: {} }
    }
    // 飞书客户端内的环境, 且没有 token，进行 code 获取
    if (!token) {
      feishuOAuth()
      return false
    }
  } else if (extraToken && extraToken !== token) {
    // 带 token 跳转到 bpm 时并且和当前 token 不同的时候，需要重置当前 token 和 用户信息
    await store.dispatch('user/resetToken', extraToken)
    return { ...to, replace: true }
  }
  // 是否要更新权限
  const power = getPower()
  if (token && userInfo && !power.power) {
    await updatePower()
    // const viewPower = power.viewPower ?? []
    // const viewPowerMap = viewPower.reduce(
    //   (pre: Map<string, IViewPower>, cur: IViewPower) => pre.set(cur.pageUri, cur),
    //   new Map()
    // )
    // viewRouter.forEach(item => {
    //   if (viewPowerMap.has(item.path)) {
    //     if (!router.hasRoute(item.name as string)) router.addRoute('MainLayout', item)
    //     else console.warn(`[router beforeEach] 路由 ${item.name as string} 已存在`)
    //   }
    // })
    // router.addRoute({ path: '/:pathMatch(.*)', redirect: '/404' })
    // console.log('router', router)
    return { ...to, replace: true }
  }

  // token 存在，用户信息不存在，获取用户信息
  if (token && !userInfo) {
    await store.dispatch('user/getUserInfo')
    return { ...to, replace: true }
  }

  // 用户信息和 token 都不存在，跳转到 sso 登陆
  if (!token && !userInfo) {
    await logout()
    return false
  }

  return true
}

router.beforeEach(async to => {
  hanldeLayoutShow(to)
  if (isMicroApp) return true
  return checkRoute(to)
})

router.onError(err => {
  if (err.name === 'NavigationFailure' && err.type === 4) {
    router.push({ name: '/404' })
  } else {
    console.error('router error: ', err)
  }
})

// 微前端缓解下，处理路由跳转
if (isMicroApp) {
  const microApp = getMicroAppInstance()
  microApp.addDataListener((data: { path: string; language: string }) => {
    // 当基座下发跳转指令时进行跳转
    if (data.path) router.push(data.path)
  })
}

export default router

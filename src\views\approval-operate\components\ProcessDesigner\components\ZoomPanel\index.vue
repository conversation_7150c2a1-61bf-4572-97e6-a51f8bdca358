<template>
  <div class="zoom-panel">
    <FButton class="lf-zoom-out mr8" size="small" @click="zoomOut">
      <template #icon><MinusOutlined style="font-size: 16px; color: #999; opacity: 0.6" /></template>
    </FButton>
    <span class="lf-zoom-value mr8">{{ (zoom * 100).toFixed(0) }}%</span>
    <FButton class="lf-zoom-in mr8" size="small" @click="zoomIn">
      <template #icon><FIcon type="icon-tianjia2" /></template>
    </FButton>
    <FButton class="lf-zoom-reset icon-tubiao_shuaxin2" size="small" @click="resetZoom">
      <template #icon><FIcon type="icon-tubiao_shuaxin2" /></template>
    </FButton>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MinusOutlined } from '@ant-design/icons-vue'
import Zoom from '@/views/process-designer/extension/Zoom'

interface IProps {
  instance: Zoom
}

const props = defineProps<IProps>()
const zoom = ref(1)

const zoomOut = () => {
  zoom.value -= 0.1
  props.instance.setZoom(zoom.value)
}

const zoomIn = () => {
  zoom.value += 0.1
  props.instance.setZoom(zoom.value)
}

const resetZoom = () => {
  zoom.value = 1
  props.instance.setZoom(zoom.value)
}
</script>

<style scoped lang="scss">
.zoom-panel {
  :deep(
      button.fs-btn:not(
          .fs-btn-dashed,
          .fs-btn-text,
          .fs-btn-link,
          .fs-btn-circle,
          .fs-btn-loading,
          .fs-input-search-button
        )
    ) {
    min-width: auto !important;
  }

  :deep(.fs-action-icon) {
    color: #999;
    font-size: 16px;
  }
}
</style>

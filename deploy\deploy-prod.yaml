kind: Service
apiVersion: v1
metadata:
  labels:
    app: {APP_NAME}
  name: {APP_NAME}
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: {APP_NAME}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {APP_NAME}
  labels:
    app: {APP_NAME}
spec:
  replicas: 2
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: {APP_NAME}
  template:
    metadata:
      labels:
        app: {APP_NAME}
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                    - {APP_NAME}
              topologyKey: kubernetes.io/hostname
      containers:
        - name: {APP_NAME}
          image: {IMAGE_URL}:{IMAGE_TAG}
          imagePullPolicy: IfNotPresent
          readinessProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 15
            periodSeconds: 20
          livenessProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 15
            periodSeconds: 20
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh","-c","sleep 25"]
          resources:
            requests:
              cpu: 500m
              memory: 200Mi
            limits:
              cpu: 500m
              memory: 200Mi
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: harbor-secret
<template>
  <div class="message-list-container">
    <!-- 面包屑 -->
    <!-- <Breadcrumb :data="[i18n.t('首页'), i18n.t('消息列表')]" /> -->
    <div class="search-box">
      <SearchList :search-config-lists="searchConfigLists" />
      <FButton class="export-btn" type="primary" @click="onImportMessage">
        <i class="icontubiao_xiazai iconfont"></i>{{ i18n.t('下载') }}
      </FButton>
    </div>
    <div class="content-box">
      <TableBox :loading="tableLoading" :list="list" />
      <div class="fei-su-pagination">
        <FPagination
          v-model:current="pageData.pageNum"
          v-model:pageSize="pageData.pageSize"
          :total="pageData.total"
          @change="onChangeFn"
          show-size-changer
          show-quick-jumper
          :show-total="() => `${i18n.t('共')} ${pageData.total} ${i18n.t('条')}`"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/views/pgb-data-board/components/Breadcrumb/index.vue'
import SearchList from '@/views/pgb-data-board/components/SearchBox/components/SearchList/index.vue'
import TableBox from './components/TableBox/index.vue'
import { ref, reactive } from 'vue'
import { pageMessage, importMessage } from '@/api/message'
import { IPageMessage, IMessageListItem, BasicPageParams } from '@/types/request'
import { ISearchDataConfig } from '@/types/pgbDataBoard'
import { messageInstance } from '@fs/smart-design'

import { useI18n, deepClone } from '@/utils'
import { Search } from './components/searchConfig'
const i18n = useI18n()

const tableLoading = ref<boolean>(false)
const pageData = reactive<BasicPageParams>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const searchData = ref<IPageMessage>({})
const list = ref<IMessageListItem[]>([])

const onGetSearchData = (data: any) => {
  searchData.value = data
  pageData.pageNum = 1
  getProcessList()
}

const onImportMessage = async () => {
  if (!pageData.total) {
    messageInstance.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const params = deepClone(searchData.value) // 拷贝一份参数
  const res = await importMessage(params)
  if (res.code !== 200) throw new Error(res.msg)
  messageInstance.success(i18n.t('下载成功，请在飞书查看！'))
}

const onChangeFn = (current: number, pageSize: number) => {
  pageData.pageNum = current
  pageData.pageSize = pageSize
  getProcessList()
}

const getProcessList = async () => {
  try {
    tableLoading.value = true
    const params = deepClone(Object.assign({}, pageData, searchData.value)) // 拷贝一份参数
    delete params.total
    const res = await pageMessage(params)
    list.value = res?.data?.list || []
    pageData.total = res?.data?.totalCount || 0
  } finally {
    tableLoading.value = false
  }
}

const searchConfigLists = reactive<ISearchDataConfig>(new Search(onGetSearchData))
</script>

<style scoped lang="scss">
.message-list-container {
  .search-box {
    display: flex;
    justify-content: space-between;
    padding: 22px 24px 10px 24px;
    // margin-top: 24px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
  .content-box {
    margin-top: 16px;
    padding: 24px 24px 0;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
}
</style>

<template>
  <div class="apprival-tab-container">
    <div class="nav">
      <div class="box" v-for="(item, index) in tabList" :key="index" @click="onNavChange(item.status)">
        <span :class="['nav-item', { 'nav-active': search.type === item.status }]">
          <span>{{ item.title }}</span>
        </span>
        <span v-if="item?.count" class="count">{{ item.count }}</span>
      </div>
    </div>
    <div class="table-content">
      <template v-if="approvalTabData?.length">
        <div class="list-item" v-for="item in approvalTabData" :key="item.id">
          <div class="item-box">
            <div class="left">
              <div class="title" @click="goApprovalDetailFn(item)">{{ item?.topicName }}</div>
              <div class="info">
                <MoreTextTips :get-popup-container-element="target" :line-clamp="1">
                  <span class="num no-wrap">{{ item?.processInstanceCode }}</span>
                  <span class="line"></span>
                  <span class="tip no-wrap">{{ item?.processType }}</span>
                  <span class="line"></span>
                  <span class="name no-wrap">
                    <i class="iconfont icontubiao_renyuan2"></i>
                    {{ item?.creatorName }}
                  </span>
                  <template v-for="outlineItme in item?.outlineList ?? []" :key="outlineItme?.field">
                    <span class="line"></span>
                    <span class="name no-wrap">
                      <span>{{ outlineItme?.label }}：</span>
                      <span>{{ outlineItme?.value }}</span>
                    </span>
                  </template>
                </MoreTextTips>
              </div>
            </div>
            <div class="right">
              <div class="handle">
                <span
                  class="color378EEF"
                  v-if="getUrlSoureFn(item) && search.type === 1"
                  @click="handleApprovalSubmitFn(item)"
                  >{{ (item?.handleNode?.properties?.type === 1 && i18n.t('同意')) || i18n.t('提交') }}</span
                >
                <span
                  class="color999999"
                  v-if="getUrlSoureFn(item) && search.type === 1 && item?.handleNode?.properties?.type === 1"
                  @click="handleApprovalRefuseFn(item)"
                  >{{ i18n.t('拒绝') }}</span
                >
              </div>
              <div class="time no-wrap">
                {{ (item?.createdTime && transformDate(item.createdTime, 'YYYY-MM-DD HH:mm:ss')) || '--' }}
              </div>
            </div>
          </div>
          <div class="table-line"></div>
        </div>
        <FPagination
          v-if="page.total > 10"
          class="cust-apprival-tab-pagination"
          v-model:current="page.pageNum"
          @change="onPaginationChangeFn"
          v-model:pageSize="page.pageSize"
          :total="page.total"
          show-size-changer
          show-quick-jumper
          :show-total="() => `${i18n.t('共')} ${page.total} ${i18n.t('条')}`"
        />
      </template>
      <FEmpty v-else />
    </div>
    <SubmitNodeModal ref="submitNodeModalRef" />
    <RefuseNodeModal ref="refuseNodeModalRef" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, provide } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n, transformDate } from '@/utils'
import {
  BasicPageParams,
  PageApprovalProcessRes,
  PageApprovalProcessParams,
  PageApprovalProcessCountRes,
  ApprovalInfoRes,
} from '@/api'
import MoreTextTips from '@/components/MoreTextTips/index'
import SubmitNodeModal from '@/views/approval-operate/components/SubmitNodeModal.vue'
import RefuseNodeModal from '@/views/approval-operate/components/RefuseNodeModal.vue'

interface IProps {
  search: PageApprovalProcessParams
  page: BasicPageParams
  approvalTabData: PageApprovalProcessRes[]
  approvalTabDataCount: PageApprovalProcessCountRes
}
const props = defineProps<IProps>()
const emits = defineEmits(['getDataFn', 'update:page', 'update:search'])
const i18n = useI18n()
const router = useRouter()
const target = ref<any>(() => document.querySelector('#container'))
const page = computed<BasicPageParams>({
  get: () => props.page,
  set: val => emits('update:page', val),
})
const search = computed<PageApprovalProcessParams>({
  get: () => props.search,
  set: val => emits('update:search', val),
})
const submitNodeModalRef = ref()
const refuseNodeModalRef = ref()
const approvalTabData = computed<PageApprovalProcessRes[]>(() =>
  (props?.approvalTabData ?? []).map(item => {
    item.handleNode = (item?.handleNodeList?.length && item.handleNodeList[0]) || {}
    return item
  })
)
const handleNode = ref<ApprovalInfoRes>()
const tabList = computed<any>(() => [
  {
    title: '待我审批',
    count: props?.approvalTabDataCount?.waitDeal ?? 0,
    status: 1,
  },
  {
    title: '待我阅读',
    count: props?.approvalTabDataCount?.unread ?? 0,
    status: 2,
  },
  {
    title: '抄送我的',
    count: 0,
    status: 5,
  },
  {
    title: '我已处理',
    count: 0,
    status: 3,
  },
  {
    title: '我发起的',
    count: 0,
    status: 4,
  },
])

const getUrlSoureFn = item => {
  if (!item?.detailsUrl) return true
  if (item.detailsUrl.includes('plm') && item.detailsUrl.includes('/material/approval/detail')) return false
  return true
}

const handleApprovalSubmitFn = item => {
  handleNode.value = item.handleNode
  submitNodeModalRef?.value?.open()
}

const handleApprovalRefuseFn = item => {
  handleNode.value = item.handleNode
  refuseNodeModalRef?.value?.open()
}

const onNavChange = val => {
  search.value.type = val
  page.value.pageNum = 1
  page.value.pageSize = 10
  emits('getDataFn')
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  page.value.pageNum = current
  page.value.pageSize = pageSize
  emits('getDataFn')
}

const goApprovalDetailFn = (item: PageApprovalProcessRes) => {
  if (item?.detailsUrl) {
    window.open(item?.detailsUrl)
  } else {
    const params = { id: item.id }
    router.push({ name: 'ApprovalDetail', params })
  }
}

provide('handleNode', handleNode)
</script>
<style scoped lang="scss">
.line {
  display: inline-block;
  height: 14px;
  width: 1px;
  margin: 0 8px;
  background: #eeeeee;
  color: #999999;
  vertical-align: sub;
}
.name {
  vertical-align: middle;
  color: #999999;
  .iconfont {
    margin-right: 4px;
    font-size: 14px;
  }
}
.apprival-tab-container {
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
  overflow: hidden;
  .no-wrap {
    white-space: nowrap;
  }
  .nav {
    padding: 0px 24px;
    background-color: #fff;
    display: flex;
    height: 54px;
    line-height: 54px;
    border-bottom: 1px solid #eee;
    width: 100%;
    .box {
      margin-right: 24px;
    }
    .nav-item {
      color: #333;
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
      display: inline-block;
      height: 100%;
      position: relative;
      transition: none 0s ease 0s;
    }

    .nav-active {
      font-weight: 500;
      &::after {
        content: '';
        width: 32px;
        height: 2px;
        background: #378eef;
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    .count {
      height: 20px;
      padding: 1px 5px;
      margin-left: 4px;
      background-color: #f04141;
      color: #ffffff;
      border-radius: 9px;
      font-size: 12px;
      vertical-align: bottom;
    }
  }
  .table-content {
    height: calc(100vh - 392px);
    min-height: 470px;
    overflow: auto;
    padding: 8px 16px 16px;
    margin-bottom: 0;
    font-size: 12px;
    .list-item {
      list-style-type: none;
      .item-box {
        display: flex;
        justify-content: space-between;
        padding: 8px 12px;
        margin: 8px 0;
        &:hover {
          background: #f1f4f8;
          border-radius: 3px;
          .left {
            .title {
              color: #378eef;
            }
          }
        }
        .left {
          .title {
            display: inline-block;
            margin-bottom: 8px;
            cursor: pointer;
            color: #333333;
            white-space: nowrap;
          }
          .info {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            color: #999999;
            .line {
              display: inline-block;
              height: 14px;
              width: 1px;
              margin: 0 8px;
              background: #eeeeee;
              vertical-align: sub;
            }
            .name {
              vertical-align: middle;
              .iconfont {
                margin-right: 4px;
                font-size: 14px;
              }
            }
          }
        }
        .right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .handle {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 8px;
            &:empty {
              &::after {
                content: '--';
              }
            }
            span {
              display: inline-block;
              margin-left: 12px;
              cursor: pointer;
            }
          }
          .time {
            margin-left: 12px;
            color: #bbbbbb;
          }
        }
      }
      .table-line {
        height: 1px;
        width: 100%;
        background-color: #eeeeee;
      }
      .color378EEF {
        color: #378eef;
      }
      .color999999 {
        color: #999999;
      }
    }
  }
  .cust-apprival-tab-pagination {
    float: right;
    margin: 0;
    margin-top: 16px;
  }
}
</style>

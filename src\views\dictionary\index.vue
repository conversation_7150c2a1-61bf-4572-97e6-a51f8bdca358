<template>
  <div class="dictionary-container">
    <div class="tab-box">
      <span
        :class="['tab-item', activeName === item.to ? 'active' : '']"
        v-for="item in dictionaryList"
        :key="item.to"
        @click="onHandleDictionary(item)"
      >
        {{ item.name }}
        <i class="line"></i>
      </span>
    </div>
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from '@/utils'

const i18n = useI18n()

const route = useRoute()
const router = useRouter()

const dictionaryList = computed<any[]>(() => [
  {
    to: 'DictionaryHome',
    name: i18n.t('字典管理'),
  },
  {
    to: 'DictionarySearch',
    name: i18n.t('搜索项字典管理'),
  },
])
const activeName = ref<string>(route.name as string)

const onHandleDictionary = (item: any) => {
  activeName.value = item.to
  router.push({
    name: item.to,
  })
}
</script>
<style scoped lang="scss">
.dictionary-container {
  .tab-box {
    display: flex;
    padding-bottom: 8px;
    margin-bottom: 16px;
    .tab-item {
      margin-right: 48px;
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      font-size: 14px;
      font-weight: 400;
      color: #333;
      &.active {
        font-size: 16px;
        font-weight: 600;
        position: relative;
        .line {
          position: absolute;
          width: 32px;
          height: 2px;
          margin: 0;
          background: #378eef;
          bottom: -8px;
          left: 50%;
          -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
        }
      }
    }
  }
}
</style>

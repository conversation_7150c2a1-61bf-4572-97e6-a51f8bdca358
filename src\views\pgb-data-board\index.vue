<template>
  <div class="pgb-data-board-container">
    <!-- 面包屑 -->
    <!-- <Breadcrumb :data="[i18n.t('首页'), i18n.t('PBG看板')]" /> -->
    <BarCharts :barOptions="options" :chartsLoading="chartsLoading" />
    <div class="content-box">
      <SearchBox @onGetSearchData="onGetSearchData" @onExportProcess="onExportProcess" />
      <TableBox :page="pageData" :loading="tableLoading" :list="list" @onPageChange="onPageChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from './components/Breadcrumb/index.vue'
import BarCharts from './components/BarCharts/index.vue'
import SearchBox from './components/SearchBox/index.vue'
import TableBox from './components/TableBox/index.vue'
import { onMounted, ref, reactive } from 'vue'
import type { EChartsOption } from '@/components/BaseEchart/config'
import { getPgbProcessList, getPgbExport, getPgbCount } from '@/api/pgbDataBoard'
import { ProcessListParams, ProcessItem, BasicPageParams } from '@/types/pgbDataBoard'
import { messageInstance } from '@fs/smart-design'
import { barOptions } from './components/BarCharts/barOptions'
import { useI18n } from '@/utils'
const i18n = useI18n()

const options = reactive<EChartsOption>(barOptions)
const chartsLoading = ref<boolean>(false)
const tableLoading = ref<boolean>(false)
const pageData = reactive<BasicPageParams>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const searchData = ref<ProcessListParams>({})
const list = ref<ProcessItem[]>([])

const onGetSearchData = (data: any) => {
  searchData.value = data
  getProcessList()
}

const onExportProcess = async () => {
  if (!list.value.length) {
    messageInstance.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const res = await getPgbExport(searchData.value)
  if (res.code !== 200) throw new Error(res.msg)
  messageInstance.success(i18n.t('下载成功，请在飞书查看！'))
}

const onPageChange = (data: ProcessListParams) => {
  Object.assign(pageData, data) // 修改为新pageData值
  getProcessList()
}

const getProcessList = async () => {
  try {
    tableLoading.value = true
    const params = Object.assign({}, pageData, searchData.value) // 拷贝一份参数
    delete params.total
    const res = await getPgbProcessList(params)
    list.value = (res?.data?.list || []).map(item =>
      Object.assign(item, {
        reviewMsg_status: false,
        evolve_status: false,
        kp_status: false,
        reviewMsg: item.reviewMsg || '',
        evolve: item.evolve || '',
        kp: item.kp || '',
      })
    )
    pageData.total = res?.data?.totalCount || 0
  } finally {
    tableLoading.value = false
  }
}

const getData = async () => {
  try {
    chartsLoading.value = true
    const res = await getPgbCount()
    if (res.code !== 200) throw new Error(res.msg)
    options.xAxis = Object.assign({}, options.xAxis, {
      data: [i18n.t('PBG项目管理流程'), i18n.t('1-N流程'), i18n.t('IPD流程')],
    })
    options.series = [
      {
        barWidth: 40,
        barGap: '40%',
        type: 'bar',
        name: i18n.t('进行中数量'),
        data: [res?.data?.pbgInprogress || 0, res?.data?.oneNInprogress || 0, res?.data?.ipdInprogress || 0],
      },
      {
        barWidth: 40,
        barGap: '40%',
        type: 'bar',
        name: i18n.t('已完成数量'),
        data: [res?.data?.pbgComplete || 0, res?.data?.oneNComplete || 0, res?.data?.ipdComplete || 0],
      },
    ]
  } finally {
    chartsLoading.value = false
  }
}

onMounted(() => {
  getData()
})
</script>

<style scoped lang="scss">
.pgb-data-board-container {
  .content-box {
    margin-top: 16px;
    padding: 24px 24px 0;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
}
</style>

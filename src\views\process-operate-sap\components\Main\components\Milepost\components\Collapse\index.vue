<template>
  <FCollapse class="milepost-base-info" v-model:active-key="activeKey">
    <template #expandIcon="props">
      <div class="icon-box">
        <DownOutlined v-show="props.isActive" />
        <RightOutlined v-show="!props.isActive" />
      </div>
    </template>
    <FCollapsePanel :header="props.header" key="1">
      <template v-for="name in Object.keys($slots)" :key="name" #[name]="slotProps">
        <slot :name="name" v-bind="slotProps || {}"></slot>
      </template>
    </FCollapsePanel>
  </FCollapse>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DownOutlined, RightOutlined } from '@ant-design/icons-vue'

interface IProps {
  header: string
}
const props = defineProps<IProps>()
const activeKey = ref(1) // 当前展开的面板
</script>

<style scoped lang="scss">
.mt16 {
  margin-top: 16px;
}
.milepost-base-info {
  border: none;
  :deep(.fs-collapse-item) {
    border: none;
    .fs-collapse-header {
      align-items: center !important;
      padding: 0;
      color: #333 !important;
      font-size: 14px;
      font-weight: 500;
      border: none;
      background-color: white;
    }

    .fs-collapse-content {
      border: none;
      box-shadow: none !important;
      background-color: white !important;

      .fs-collapse-content-box {
        padding: 16px 0 !important;
        background-color: white !important;
        box-shadow: none !important;
      }
    }

    .fs-collapse-arrow {
      margin-right: 8px;
    }
  }
  .icon-box {
    margin-right: 8px;
    :deep(svg) {
      width: 12px;
      height: 12px;
    }
  }
}
</style>

<template>
  <FCard :title="i18n.t('参与角色')" :head-style="COMPONENT_CRAD_BODY_STYLE" :body-style="COMPONENT_CRAD_BODY_STYLE">
    <div class="partake-role">
      <div class="partake-role-item">
        <p class="partake-role-label" :style="`flex: 0 0 ${roleNameLength * 14 + 10}px`">{{ i18n.t('拉群方式') }}:</p>
        <div class="partake-role-content">
          <FButton type="primary" :disabled="fsGroupId" :loading="fsGroupButtonLoading" @click="handleFSGroupClick">
            {{ i18n.t('自动拉群') }}
          </FButton>
        </div>
      </div>
      <div class="partake-role-item" v-for="item in processRoleInfo" :key="item.id">
        <p class="partake-role-label" :style="`flex: 0 0 ${roleNameLength * 14 + 10}px`">{{ item.roleName }}:</p>
        <div class="partake-role-content">
          <!-- :disabled="!item.id || !power" -->
          <FSelect
            :mode="!!item.id ? '' : 'multiple'"
            :value="TFValue(item)"
            :options="userData"
            :filter-option="filterOption"
            :field-names="{ label: 'name', value: 'uuid' }"
            style="width: 300px"
            show-search
            :disabled="!item.id"
            :placeholder="i18n.t('请选择')"
            @change="(data: string) => handleUserChange(data, item)"
          />
        </div>
        <div v-if="!!item.id && item.users && item.users.length > 1" class="partake-role-item-operate">
          <FTooltip>
            <template #title>
              <p class="m0">{{ i18n.t('角色成员异常') }}：</p>
              <p style="margin: 0; line-height: 2" v-for="user in item.users" :key="user.uuid">{{ user.name }}</p>
            </template>
            <span style="color: red">{{ i18n.t('角色成员异常') }}<InfoCircleOutlined style="color: red" /></span>
          </FTooltip>
        </div>
        <div v-if="!item.id" class="partake-role-item-operate">
          <Icon class="pointer" icon="iconxinzeng" @click="() => handleAddOtherClick()" />
        </div>
        <div v-if="item.isCustom" class="partake-role-item-operate">
          <Icon class="pointer" icon="iconshanchu2" @click="() => handleDeleteRole(item)" />
        </div>
      </div>
    </div>
    <div class="partake-role-operate">
      <FButton @click="handleCreateRoleClick"><Icon icon="iconxinzeng" /> {{ i18n.t('新增角色') }}</FButton>
    </div>

    <RoleInfo v-model="roleModalFlag" :user-list="userData" @create="handleRoleCreate" />
    <OtherRoleModal v-model="otherRoleModalFlag" :user-list="userData" @add="handleOtherAdd" />
  </FCard>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import { COMPONENT_CRAD_BODY_STYLE, EmitType } from '../../../../config'
import {
  addProcessOtherUser,
  createFSGroup,
  createProcessRoleAndUser,
  deleteProcessRoleAndUser,
  getFSGroupId,
  updateProcessRoleAndUser,
} from '@/api'
import { messageInstance as message, FModal } from '@fs/smart-design'
import { InfoCircleOutlined } from '@ant-design/icons-vue'

import Icon from '@/components/Icon/index.vue'
import RoleInfo from './components/RoleInfoModal/index.vue'
import OtherRoleModal from './components/OtherRoleModal/index.vue'

import type { IProcess, IUser } from '@/types/handle'
import type { IProcessRoleAndUser } from '@/types/request'
import { debounce, useI18n } from '@/utils'

type UserType = IUser & { name: string }

interface IProps {
  processInfo: IProcess[]
  other: { processRoleInfo: IProcessRoleAndUser[] }
}

const i18n = useI18n()
const store = useStore()
const props = defineProps<IProps>()

const handleOperate = inject('handleOperate') as (value: { type: keyof typeof EmitType }) => void

const fsGroupId = ref<string>()
const fsGroupButtonLoading = ref(false)
const roleNameLength = ref(0)
const roleModalFlag = ref(false)
const otherRoleModalFlag = ref(false)
const instanceId = computed(() => props.processInfo[0]?.instanceId)
const processRoleInfo = computed(() => {
  props?.other?.processRoleInfo?.forEach(item => {
    item.roleName.length > roleNameLength.value ? (roleNameLength.value = item.roleName.length) : roleNameLength.value
  })
  return props?.other?.processRoleInfo || []
})
const userData = computed<UserType[]>(() => store.state.user.allUser || [])
const userMap = computed<Map<string, UserType>>(() => {
  const map = new Map()
  userData.value.forEach(item => {
    map.set(item.uuid, item)
  })
  return map
})

onMounted(async () => {
  const res = await getFSGroupId(instanceId.value)
  if (res.data) fsGroupId.value = res.data
})

const handleDeleteRole = (role: IProcessRoleAndUser) => {
  FModal.confirm({
    title: i18n.t('是否确认删除当前角色？'),
    okText: i18n.t('确认'),
    cancelText: i18n.t('取消'),
    onOk: async () => {
      await deleteProcessRoleAndUser(role.id)
      handleOperate({ type: EmitType.updateRole })
      message.success(i18n.t('删除成功'))
    },
  })
}

const handleUserChange = (uuid: string, role: IProcessRoleAndUser) => {
  const { users, ...data } = role
  data.uuid = uuid
  data.name = userMap.value.get(uuid)?.name as string
  FModal.confirm({
    title: i18n.t('是否确认修改当前角色的成员？'),
    okText: i18n.t('确认'),
    cancelText: i18n.t('取消'),
    onOk: async () => {
      await updateProcessRoleAndUser(data)
      handleOperate({ type: EmitType.updateRole })
      message.success(i18n.t('修改成功'))
    },
  })
}

const handleRoleCreate = async ({ roleName, userId }: any) => {
  await createProcessRoleAndUser({
    instanceId: instanceId.value,
    roleName,
    uuid: userId,
    name: userMap.value.get(userId)?.name as string,
  })
  handleOperate({ type: EmitType.updateRole })
  message.success(i18n.t('新增成功'))
}

const handleOtherAdd = async ({ userIds }: { userIds: string[] }) => {
  await addProcessOtherUser(instanceId.value, userIds)
  handleOperate({ type: EmitType.updateRole })
  message.success(i18n.t('添加成功'))
}

const handleAddOtherClick = () => {
  otherRoleModalFlag.value = true
}

const handleCreateRoleClick = () => {
  roleModalFlag.value = true
}

const handleFSGroupClick = debounce(async () => {
  fsGroupButtonLoading.value = true
  try {
    const res = await createFSGroup(instanceId.value)
    fsGroupId.value = res.data
    message.success(i18n.t('拉群成功'))
  } catch (error) {
    // message.error('拉群失败')
  } finally {
    fsGroupButtonLoading.value = false
  }
})

const TFValue = (data: IProcessRoleAndUser) => (data.id ? data.uuid : (data.users || []).map(item => item.uuid))
const filterOption = (input: string, option: UserType) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
</script>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}

.partake-role {
  padding-top: 20px;
  .partake-role-item {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    > .partake-role-label {
      // flex: 0 0 220px;
      margin: 0;
      height: 32px;
      line-height: 32px;
    }

    > .partake-role-item-operate {
      padding: 0 20px;
      height: 32px;
      line-height: 32px;
    }
  }
}

.partake-role-operate {
  margin-bottom: 20px;
}
</style>

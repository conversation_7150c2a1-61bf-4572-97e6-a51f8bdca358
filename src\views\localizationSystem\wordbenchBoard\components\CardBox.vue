<template>
  <div class="card-container">
    <CardTitle :title-info="titleInfo" />
    <main
      ref="mainScorllRef"
      :class="['main-box', showMainScroll ? 'showMainScroll' : '']"
      :style="{ 'max-height': processInfo.display ? 'calc(100% - 76px)' : 'calc(100% - 26px)' }"
    >
      <FSpin class="cust-main-loading" size="large" fix :spinning="loading">
        <CardList
          :class="[cardListInfo.length === 1 ? 'all-height' : '']"
          v-for="(item, index) in cardListInfo"
          :list-info="item"
          :default-show="!index && !!item.list.length"
          :key="key + item.id"
        />
      </FSpin>
    </main>
    <AddCard v-if="processInfo.display" :add-info="addInfo" />
  </div>
</template>
<script setup lang="ts">
import { ref, watch, computed, onMounted, onBeforeUnmount, unref } from 'vue'
import CardTitle from './CardTitle.vue'
import CardList from './CardList.vue'
import AddCard from './AddCard.vue'
import { getProcessList } from '@/api/localizationSystem/wordbenchBoard'
import type { ListParams } from '@/types/localizationSystem/wordbenchBoard'

const props = defineProps({
  processInfo: { type: Object, default: () => ({}) },
})

let cardListInfo = ref<any[]>([])
let loading = ref<boolean>(false)
let showMainScroll = ref<boolean>(false)
let mainTop1 = ref<number>(0)
let mainTop2 = ref<number>(0)
let timer = ref<number>()
let key = ref<string>(Date.now + '' + Math.random())
const mainScorllRef = ref<HTMLElement | null>(null)

const getProcessLists = async (val: any, index: number) => {
  try {
    if (!val) return
    loading.value = true
    const params: ListParams = {
      labelCode: val.labelCode,
      pageNum: 1,
      pageSize: 10,
    }

    const res = await getProcessList(params)
    cardListInfo.value[index].list = res.data.list || []
    cardListInfo.value[index].totalCount = res.data.totalCount
    if (res.data.totalCount > 1 * 10) {
      cardListInfo.value[index].pageNum = 2
    } else {
      cardListInfo.value[index].noData = true
    }
    key.value = Date.now + '' + Math.random()
  } finally {
    loading.value = false
  }
}

const scrollChange = () => {
  clearTimeout(timer.value)
  timer.value = setTimeout(isScrollEnd, 1000)
  mainTop1.value = mainScorllRef.value?.scrollTop || 0
  showMainScroll.value = true
}

const isScrollEnd = () => {
  mainTop2.value = mainScorllRef.value?.scrollTop || 0
  if (mainTop2.value === mainTop1.value) {
    showMainScroll.value = false
  }
}

watch(
  () => props.processInfo,
  val => {
    const data = JSON.parse(JSON.stringify(unref(val)))
    cardListInfo.value = (data?.localizationLabels || []).map((item: any, index: number) => {
      item.list = []
      item.pageNum = 1
      item.pageSize = 10
      item.totalCount = item.number || 0
      item.addProcessConfigId = data.processConfigId
      item.addProcessDefinekey = data.processDefinekey
      return item
    })
    if (val.localizationLabels.length) {
      getProcessLists(val.localizationLabels[0], 0)
    }
  },
  { deep: true, immediate: true }
)
const titleInfo = computed(() => {
  let sortList = (props.processInfo?.localizationLabels || [])
    .filter((item: any) => !!item.sort)
    .map((item: any, index: number) => ({ id: item.id, sort: item.sort || index, name: item.labelName }))
  let addSortList = (props.processInfo?.localizationLabels || [])
    .filter((item: any) => !item.sort)
    .map((item: any, index: number) => ({
      id: item.id,
      sort: item.sort || index + sortList.length,
      name: item.labelName,
    }))
  return {
    name: props.processInfo?.processConfigName || '',
    total: props.processInfo?.total || 0,
    undoneNum: props.processInfo?.undoneNum || 0,
    percent: props.processInfo?.percent || 0,
    sortList: [...sortList, ...addSortList],
  }
})

const addInfo = computed(() => ({
  processConfigId: props.processInfo?.processConfigId || '',
  localizationRegionId: props.processInfo?.localizationRegionId || 0,
}))

onMounted(() => {
  mainScorllRef.value?.addEventListener('scroll', scrollChange)
})
onBeforeUnmount(() => {
  clearTimeout(timer.value)
  timer.value = undefined
  mainScorllRef.value?.removeEventListener('scroll', scrollChange)
  showMainScroll.value = false
})
</script>
<style lang="scss" scoped>
.card-container {
  flex: 1;
  height: calc(100% - 50px);
  min-width: 280px;
  max-width: 304px;
  margin-right: 24px;
  .main-box {
    position: relative;
    width: calc(100% + 12px);
    max-height: calc(100% - 76px);
    overflow-y: scroll;
    &::-webkit-scrollbar {
      visibility: hidden;
      background: none;
    }
    &::-webkit-scrollbar-thumb {
      visibility: hidden;
    }
    &::-webkit-scrollbar-track {
      background: none;
    }
    &.showMainScroll:hover {
      &::-webkit-scrollbar {
        visibility: visible;
      }
    }
    &.showMainScroll:hover {
      &::-webkit-scrollbar-thumb {
        visibility: visible;
      }
    }
    :deep(.all-height) {
      .cust-scroll {
        max-height: calc(100% - 345px);
      }
    }
    .card-list-container:last-child {
      overflow: hidden;
      border-radius: 0px 0px 12px 12px;
    }
    .cust-main-loading {
      width: calc(100% - 6px);
    }
  }
}
</style>

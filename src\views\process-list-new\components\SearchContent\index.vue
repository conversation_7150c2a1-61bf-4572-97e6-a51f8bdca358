<template>
  <div class="search-content-container">
    <template v-for="item of search.options" :key="item.componentValueKey">
      <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, inject, watch, markRaw, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'
import { Search } from '@/views/message-template/components/SearchContent/search'
import { cache } from '@/utils'
import { getSearchConfigById, getSearchConfigOptions } from '@/api/process'

interface IProps {
  queryData: any
  hasExportTemplate: boolean
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:queryData', 'update:hasExportTemplate'])
const store = useStore()
const allUserList = computed(() => store.state.user.allUser || [])
const search = new Search()
const queryData = computed({
  get: () => props.queryData,
  set: val => emits('update:queryData', val),
})
const hasExportTemplate = computed({
  get: () => props.hasExportTemplate,
  set: val => emits('update:hasExportTemplate', val),
})
const processTypeData = inject('processTypeData') as any
const routerName = inject('routerName') as any
const currProcessTypeId = inject('currProcessTypeId') as any
const processConfigIdList = inject('processConfigIdList') as any
const isUrgentOptions = ref<any>([
  { value: '0', label: '一般' },
  { value: '1', label: '加急' },
])

const onChange = () =>
  (queryData.value = {
    ...search.getParams(),
    ...{ cacheValue: search.getCacheSearch() },
  })
const CustSelect = markRaw(defineAsyncComponent(() => import('./components/CustSelect/index.vue')))
const CustRangeNumber = markRaw(defineAsyncComponent(() => import('./components/CustRangeNumber/index.vue')))
const CustAsyncForm = markRaw(defineAsyncComponent(() => import('./components/CustAsyncForm/index.vue')))
const configList = [
  {
    componentName: CustSelect,
    componentValueKey: 'processConfigId',
    componentAttrs: {
      style: computed(() => (currProcessTypeId.value == 'all' || processConfigIdList.value ? '' : 'display: none;')),
      class: 'width160 marginR12 marginB24',
      pressLine: computed(() => '流程类型'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: computed(() => processTypeData.value || []),
      optionFilterProp: 'processName',
      fieldNames: { value: 'id', label: 'processName' },
      onChange: (value, option) => {
        // hasExportTemplate.value = option?.templateIsEnabled
        search.setOptions('nodeId.componentValue', undefined)
        const selectProcess = processTypeData.value.find((item: any) => item.id === value)
        search.setOptions('nodeId.componentAttrs.options', selectProcess?.nodes || [])
        getsearchConfig(value)
        onChange()
      },
      onClear: () => {
        // hasExportTemplate.value = false
        search.setOptions('nodeId.componentValue', undefined)
        search.setOptions('nodeId.componentAttrs.options', [])
        getsearchConfig(undefined)
        onChange()
      },
    },
    setComponentValueFormat: async (data: any) => {
      search.setOptions('processConfigId.componentValue', (data || {})?.componentValue || undefined)
      const selectProcess = processTypeData.value.find((item: any) => item.id === (data || {})?.componentValue)
      search.setOptions('nodeId.componentAttrs.options', selectProcess?.nodes || [])
      // hasExportTemplate.value = selectProcess?.templateIsEnabled
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'nodeId',
    componentAttrs: {
      class: 'width160 marginR12 marginB24',
      pressLine: computed(() => '当前阶段'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: [],
      optionFilterProp: 'milepostName',
      fieldNames: { value: 'nodeId', label: 'milepostName' },
      onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('nodeId.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'isUrgent',
    componentAttrs: {
      class: 'width160 marginR12 marginB24',
      pressLine: computed(() => '紧急程度'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: computed(() => isUrgentOptions.value || []),
      optionFilterProp: 'label',
      onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('isUrgent.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'createUser',
    componentAttrs: {
      class: 'width160 marginR12 marginB24',
      pressLine: computed(() => '录入人'),
      placeholder: computed(() => '请选择'),
      showSearch: true,
      allowClear: true,
      options: allUserList,
      fieldNames: { value: 'uuid', label: 'feiShuName' },
      optionFilterProp: 'feiShuName',
      onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('createUser.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FRangePicker',
    componentValueKey: 'time',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '录入时间'),
      valueFormat: 'YYYY-MM-DD',
      onChange,
    },
    getComponentValueFormat: (value: any) => {
      if (!value || value.length !== 2) return undefined
      return {
        startTime: value[0] + ' 00:00:00',
        endTime: value[1] + ' 23:59:59',
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions(
        'time.componentValue',
        ((data || {})?.componentValue && [data.componentValue[0], data.componentValue[1]]) || undefined
      )
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'queryInput',
    componentAttrs: {
      class: 'width240 marginR12 marginB24',
      pressLine: computed(() => '快速检索'),
      placeholder: computed(() => '流程编号/需求描述'),
      allowClear: true,
      type: 'search-clear',
      onSearch: onChange,
      onClear: onChange,
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('queryInput.componentValue', (data || {})?.componentValue || undefined)
    },
  },
]
const systemHiddenFields = ref([])
const searchConfigList = ref<any[]>([])
const searchConfigNoFoldList = ref<any[]>([])
const foldList = ref<any[]>([])
const noFoldList = ref<any[]>([])
const noFoldListOptions = ref<any[]>([])
const cachData = ref<any>({})
const currentCachData = ref<any>({})

const getOptions = async (data: any, key: string) => {
  if (!data.url) {
    data.valueList &&
      data.valueList.length &&
      search.setOptions(
        (data?.moduleType === 'TREE_SELECT' && `${key}.componentAttrs.treeData`) || `${key}.componentAttrs.options`,
        data.valueList
      )
  } else {
    const res = await getSearchConfigOptions(data.url)
    if (res.code === 200) {
      search.setOptions(
        (data?.moduleType === 'TREE_SELECT' && `${key}.componentAttrs.treeData`) || `${key}.componentAttrs.options`,
        res.data
      )
    }
  }
}

const getsearchConfig = async (processConfigId: number) => {
  systemHiddenFields.value = []
  searchConfigList.value = []
  foldList.value = []
  noFoldList.value = []
  queryData.value.searchObj = {}
  search.initOptions(configList)
  if (!processConfigId) return
  const res = await getSearchConfigById(processConfigId)
  if (res.code === 200 && res?.data.length) {
    searchConfigList.value = res?.data ?? []
    res.data.forEach(item => {
      if (item?.moduleType === 'SYSTEM_FIELDS' && item?.field.startsWith('systemFields:') && !item.status) {
        systemHiddenFields.value.push(item?.field?.split(':')[1])
      }
      if (item?.moduleType !== 'SYSTEM_FIELDS' && item?.status && item?.fold) {
        foldList.value.push(item)
      }
      if (item?.moduleType !== 'SYSTEM_FIELDS' && item?.status && !item?.fold) {
        noFoldList.value.push(item)
      }
    })

    noFoldList.value.forEach(item => {
      const key = `${item.searchType}:${item.field}:${item.valueType}`
      const custAttr = JSON.parse(item.propsConfig || JSON.stringify({}))

      if (item?.moduleType === 'ONE_LINE_INPUT') {
        const data = {
          componentName: 'FInput',
          componentValueKey: 'queryInput',
          componentAttrs: Object.assign(
            {},
            {
              class: 'width160 marginR12 marginB24',
              pressLine: item.name,
              placeholder: '请输入',
              allowClear: true,
              type: 'search-clear',
              onSearch: onChange,
              onClear: onChange,
            },
            custAttr
          ),
          setComponentValueFormat: async (data: any) => {
            search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
          },
          getComponentValueFormat: (value: any) => {
            if (!value) return undefined
            const data = queryData?.value?.searchObj ?? {}
            data[key] = value
            return { searchObj: data }
          },
        }

        searchConfigNoFoldList.value.push(data)
      }

      if (item?.moduleType === 'RANGE_NUMBER') {
        const data = {
          componentName: CustRangeNumber,
          componentValueKey: key,
          componentAttrs: Object.assign(
            {},
            {
              class: 'width160 marginR12 marginB24',
              pressLine: item.name,
              config: { ...item },
              onChange,
            },
            custAttr
          ),
          setComponentValueFormat: async (data: any) => {
            search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
          },
          getComponentValueFormat: (value: any) => {
            if (!value || value.length !== 2) return undefined
            const data = queryData?.value?.searchObj ?? {}
            const config = search.getOptions(`${key}.componentAttrs.config`)
            data[`gt:${config.field}:${config.valueType}`] = value?.[0] ?? undefined
            data[`lt:${config.field}:${config.valueType}`] = value?.[1] ?? undefined
            return { searchObj: data }
          },
        }

        searchConfigNoFoldList.value.push(data)
      }

      if (['SELECT', 'MULTIPLE_SELECT'].includes(item?.moduleType)) {
        const optionConfig = {
          item: { ...item },
          key,
        }
        const data = {
          componentName: 'FSelect',
          componentValueKey: key,
          componentAttrs: Object.assign(
            {},
            {
              class: 'width160 marginR12 marginB24',
              pressLine: item.name,
              placeholder: '请选择',
              showSearch: true,
              allowClear: true,
              options: [],
              optionFilterProp: 'label',
              onChange,
              onClear: onChange,
            },
            item.moduleType === 'MULTIPLE_SELECT' ? { mode: 'multiple', maxTagCount: 'responsive' } : {},
            custAttr
          ),
          setComponentValueFormat: async (data: any) => {
            search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
          },
          getComponentValueFormat: (value: any) => {
            if (!value) return undefined
            const data = queryData?.value?.searchObj ?? {}
            data[key] = value
            return { searchObj: data }
          },
        }

        searchConfigNoFoldList.value.push(data)
        noFoldListOptions.value.push(optionConfig)
      }

      if (['TREE_SELECT'].includes(item?.moduleType)) {
        const optionConfig = {
          item: { ...item },
          key,
        }
        const data = {
          componentName: 'FTreeSelect',
          componentValueKey: key,
          componentAttrs: Object.assign(
            {},
            {
              class: 'width160 marginR12 marginB24',
              pressLine: item.name,
              placeholder: '请选择',
              dropdownMatchSelectWidth: false,
              showSearch: true,
              allowClear: true,
              treeData: [],
              treeNodeFilterProp: 'label',
              maxTagCount: 'responsive',
              onChange,
              onClear: onChange,
            },
            custAttr
          ),
          setComponentValueFormat: async (data: any) => {
            search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
          },
          getComponentValueFormat: (value: any) => {
            if (!value) return undefined
            const data = queryData?.value?.searchObj ?? {}
            data[key] = value
            return { searchObj: data }
          },
        }

        searchConfigNoFoldList.value.push(data)
        noFoldListOptions.value.push(optionConfig)
      }

      if (item?.moduleType === 'DATE_PICKER') {
        const data = {
          componentName: 'FDatePicker',
          componentValueKey: key,
          componentAttrs: Object.assign(
            {},
            {
              class: 'width160 marginR12 marginB24',
              pressLine: item.name,
              valueFormat: 'YYYY-MM-DD',
              placeholder: '请选择时间',
              config: { ...item },
              onChange,
              onClear: onChange,
            },
            custAttr
          ),
          setComponentValueFormat: async (data: any) => {
            search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
          },
          getComponentValueFormat: (value: any) => {
            if (!value) return undefined
            const data = queryData?.value?.searchObj ?? {}
            data[key] = value
            return { searchObj: data }
          },
        }

        searchConfigNoFoldList.value.push(data)
      }

      if (item?.moduleType === 'RANGE_PICKER') {
        const data = {
          componentName: 'FRangePicker',
          componentValueKey: key,
          componentAttrs: Object.assign(
            {},
            {
              class: 'width240 marginR12 marginB24',
              pressLine: item.name,
              valueFormat: 'YYYY-MM-DD',
              placeholder: ['请选择时间', '请选择时间'],
              config: { ...item },
              onChange,
              onClear: onChange,
            },
            custAttr
          ),
          setComponentValueFormat: async (data: any) => {
            search.setOptions(`${key}.componentValue`, (data || {})?.componentValue || undefined)
            search.setOptions(`${key}.componentArgsValue`, (data || {})?.componentArgsValue || undefined)
          },
          getComponentValueFormat: (value: any) => {
            if (!value || value.length !== 2) return undefined
            const data = queryData?.value?.searchObj ?? {}
            const config = search.getOptions(`${key}.componentAttrs.config`)
            data[`gt:${config.field}:${config.valueType}`] = value?.[0] ?? undefined
            data[`lt:${config.field}:${config.valueType}`] = value?.[1] ?? undefined
            return { searchObj: data }
          },
        }

        searchConfigNoFoldList.value.push(data)
      }
    })
    search.addOptions(searchConfigNoFoldList.value)
    search.addOptions([
      {
        componentName: CustAsyncForm,
        componentValueKey: 'foldSearchObj',
        componentAttrs: {
          foldList: foldList.value,
          queryData: queryData?.value,
          foldSearchObjCacheData: currentCachData.value?.foldSearchObj ?? {},
          onChange: (value, cacheData) => {
            search.setOptions(`foldSearchObj.componentArgsValue`, cacheData || undefined)
            onChange()
          },
        },
        setComponentValueFormat: async (data: any) => {
          search.setOptions(`foldSearchObj.componentValue`, (data || {})?.componentValue || undefined)
          search.setOptions(`foldSearchObj.componentArgsValue`, (data || {})?.componentArgsValue || undefined)
        },
        getComponentValueFormat: (value: any) => {
          if (!value) return undefined
          const data = queryData?.value?.searchObj ?? {}

          return { searchObj: Object.assign(data, value) }
        },
      },
    ])

    noFoldListOptions.value.forEach(options => getOptions(options.item, options.key))
  }
}

const setSearchConfigFn = async () => {
  await nextTick()
  search.initOptions(configList)
  search.clear()
  cachData.value = (cache.get(routerName?.value) && JSON.parse(cache.get(routerName?.value) as string)) || {}
  currentCachData.value = cachData.value?.[currProcessTypeId?.value] || {}
  !currentCachData.value?.processConfigId &&
    !processConfigIdList.value &&
    (currentCachData.value['processConfigId'] = { componentValue: +currProcessTypeId.value })

  await getsearchConfig(currentCachData.value?.processConfigId?.componentValue || undefined)
  search.setDefaultSearch(currentCachData.value)
  nextTick(() => {
    queryData.value = {
      ...search.getParams(),
      ...{ cacheValue: currentCachData.value, init: true },
    }
  })
}

watch(
  () => currProcessTypeId.value,
  () => {
    currProcessTypeId.value && setSearchConfigFn()
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.search-content-container {
  display: flex;
  flex-wrap: wrap;
}
:deep(.fs-tree-select) {
  margin-bottom: 0;
}
:deep(.fs-input-affix-wrapper) {
  padding: 6px 8px !important;
}
</style>

import { computed, ref } from 'vue'
import { PROCESS_PUBLISH_FLAG, PROCESS_PUBLISH_RESET_FLAG, PROCESS_PUBLISH_ENV_OPTIONS } from '@/utils/const'
import { getPower } from '@/utils'

export default function useCache() {
  const { fnPowerMap } = getPower()
  const hasEnvPublish = computed(() => PROCESS_PUBLISH_FLAG)
  const hasPublish = computed(() => PROCESS_PUBLISH_FLAG && fnPowerMap.has('BPM.Function.ProcessConfig'))
  const hasPublishReset = computed(() => PROCESS_PUBLISH_RESET_FLAG && fnPowerMap.has('BPM.Function.ProcessConfig'))
  const publishEnvOptions = computed(
    () => (fnPowerMap.has('BPM.Function.ProcessConfig') && PROCESS_PUBLISH_ENV_OPTIONS) || []
  )
  const publishVisible = ref(false)

  return {
    hasPublish,
    hasPublishReset,
    publishEnvOptions,
    publishVisible,
    hasEnvPublish,
  }
}

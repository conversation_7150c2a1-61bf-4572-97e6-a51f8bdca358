import { transformDate } from '@/utils'
import { getEntityClass } from '@/api'
import { IOptions } from '@/types/pgbDataBoard'
import { ProductListParams } from '@/types/productRemoveList'
import { reactive } from 'vue'
import { i18n } from '@/init'
import store from '@/store'

const entityClassLists = await (async () => {
  const res = await getEntityClass()
  let data: any = []
  if (res.code !== 200) throw new Error(res.msg)
  data = res?.data || []
  return data
})()

export class Search {
  options: IOptions;
  [key: string]: any
  constructor(callback: any) {
    this.callback = callback
    this.options = reactive({
      entityClass: {
        componentName: 'FSelect',
        componentValueKey: 'entityClass',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('实体分类'),
          options: entityClassLists,
          allowClear: true,
          onChange: (value: any, option: any) => {
            this.callback(this.submit())
          },
        },
      },
      status: {
        componentName: 'FSelect',
        componentValueKey: 'status',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('全部'),
          pressLine: i18n.t('启用状态'),
          options: [
            { label: i18n.t('启用'), value: 0 },
            { label: i18n.t('停用'), value: 1 },
          ],
          allowClear: true,
          onChange: () => {
            this.callback(this.submit())
          },
        },
      },
      entityType: {
        componentName: 'FSelect',
        componentValueKey: 'entityType',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('全部'),
          pressLine: i18n.t('实体类型'),
          options: [
            { label: i18n.t('主实体'), value: 0 },
            { label: i18n.t('明细实体'), value: 1 },
          ],
          allowClear: true,
          onChange: () => {
            this.callback(this.submit())
          },
        },
      },
      createdUuid: {
        componentName: 'FSelect',
        componentValueKey: 'createdUuid',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('创建人'),
          options: store.state.user.allUser || [],
          showSearch: true,
          fieldNames: { label: 'feiShuName', value: 'uuid' },
          optionFilterProp: 'feiShuName',
          allowClear: true,
          onChange: () => {
            this.callback(this.submit())
          },
        },
      },
      time: {
        componentName: 'FRangePicker',
        componentValueKey: 'time',
        componentValue: undefined,
        componentLabel: i18n.t('创建时间'),
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          pressLine: i18n.t('创建时间'),
          placeholder: [i18n.t('开始日期'), i18n.t('结束日期')],
          onChange: () => {
            this.callback(this.submit())
          },
        },
        getComponentValueFormat: (value: any) => {
          if (!value || value.length !== 2) return undefined
          return {
            startTime: transformDate(value[0], 'YYYY-MM-DD HH:mm:ss'),
            endTime: transformDate(value[1], 'YYYY-MM-DD HH:mm:ss'),
          }
        },
      },
      queryInput: {
        componentName: 'FInput',
        componentValueKey: 'queryInput',
        componentValue: undefined,
        componentLabel: i18n.t('快速搜索'),
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          pressLine: i18n.t('快速搜索'),
          placeholder: i18n.t('实体名称/实体编码/数据库表名/备注说明'),
          onPressEnter: () => {
            this.callback(this.submit())
          },
        },
      },
    })
    this.init()
  }

  init() {
    this.callback(this.submit())
  }

  submit() {
    const params: ProductListParams = {}
    Object.values(this.options).forEach(value => {
      ;(value.componentValue &&
        value.getComponentValueFormat &&
        Object.assign(
          params,
          value.getComponentValueFormat(value.componentValue, value?.componentArgsValue || null)
        )) ||
        (params[value.componentValueKey] = value.componentValue)
    })
    return params
  }
}

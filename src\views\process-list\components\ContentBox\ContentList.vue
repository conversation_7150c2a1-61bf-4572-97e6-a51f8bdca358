<template>
  <div class="ct-box positionR">
    <template v-if="tab !== 8">
      <!-- 筛选条件 -->
      <SearchHead
        :tab="props.tab"
        @add-new-demand="isNewdemandModal = true"
        @search="onSearch"
        @on-export-process="onExportProcess"
      />

      <!-- 列表 -->
      <CustomTable :type="props.tab" :data-source="processes" @update-table="getProcessesList" :loading="loading" />
      <div class="fei-su-pagination">
        <span class="fontSize12">{{ i18n.t('共') }} {{ paginate.total }} {{ i18n.t('条') }}</span>
        <FPagination
          v-model:current="paginate.pageNum"
          v-model:pageSize="paginate.pageSize"
          :total="paginate.total"
          @change="showSizeChange"
          show-size-changer
          show-quick-jumper
        />
      </div>
    </template>

    <FEmpty v-else style="margin-top: 16px" :image="imgEmpty" :description="i18n.t('请在对应站点处理！')" />
    <!-- 新增弹框 -->
    <CreateProcessModal v-model="isNewdemandModal" :is-detail="false" />
  </div>
</template>
<script setup lang="ts">
import SearchHead from './SearchHead.vue'
import CreateProcessModal from '@/components/CreateProcessModal/index.vue'
import CustomTable from './CustomTable.vue'
import { BasicPageParams } from '@/types/common'
import { ref, onMounted, watch, computed } from 'vue'
import {
  getProcessList,
  getProccessQueryCount,
  getDraftProcessInfo,
  exportProcess,
  getOtherProccessQueryCount,
  getRelatedModifyInstances,
  templateExportProcess,
} from '@/api'
import { messageInstance as message } from '@fs/smart-design'
import { useStore } from 'vuex'
import { useI18n } from '@/utils'
import { useRoute } from 'vue-router'
import imgEmpty from '@/assets/images/empty.png'

const route = useRoute()
const cacheKey = computed(() => {
  if (route.name === 'ProcessList') return 'all'
  if (route.params?.type) return route.params?.type
  return 'noCache'
})

const i18n = useI18n()
const emit = defineEmits(['on-remind'])
const props = defineProps({
  tab: { type: Number, default: 0 },
  tableLoading: { type: Boolean, default: false },
})
const store = useStore()
const processes = ref<any>([])
const searchData = ref<Record<string, unknown>>({})
const paginate = ref<BasicPageParams>({ pageNum: 1, pageSize: 10, total: 0 })
const loading = ref<boolean>(false)
const isNewdemandModal = ref<boolean>(false)
const baseConfig = {
  production: {
    url: 'https://cn-bpm.fs.com',
  },
  compliance: {
    url: 'https://bpm.fs.com',
  },
}

const mode = process.env.VUE_APP_ENV || 'localhost'

watch(
  () => props.tab,
  () => onTabChange(),
  { deep: true }
)
onMounted(() => {
  const cache = store.getters['local/getLocalSearchData']
  if (cache !== undefined && cache !== null) {
    const localSearchData = cache[cacheKey.value as string] ?? {}
    for (const key in paginate.value) {
      if (localSearchData[key]) {
        paginate.value[key as keyof BasicPageParams] = localSearchData[key]
      }
    }
  }
  // getProcessesList()
})

const showSizeChange = (current: number, pageSize: number) => {
  paginate.value.pageSize = pageSize
  getProcessesList()
}

const onTabChange = () => {
  paginate.value.pageNum = 1
  if (props.tab === 7) {
    getDraftList()
  } else if (props.tab === 8) {
    getOhterList()
  } else {
    getProcessesList()
  }
}

const onSearch = (val = {}) => {
  searchData.value = val
  if (props.tab !== 7) getProcessesList()
  else getDraftList()
}

const getOhterList = async () => {
  loading.value = true
  processes.value = []
  window.open(`${baseConfig[mode].url}/bpm-manage/process/list`)
  const query = searchData.value
  const params = { type: 2, ...query }

  const promiseList = [getProccessQueryCount(params), getOtherProccessQueryCount(params, baseConfig[mode].url)]
  const [resCount, otherResCount] = await Promise.all(promiseList)
  const cacheValue = {
    ...params,
    ...{ pageNum: paginate.value.pageNum, pageSize: paginate.value.pageSize },
  }
  store.commit('local/SET_LOCAL_SEARCH_DATA', { [cacheKey.value as string]: cacheValue })
  processes.value = []
  paginate.value.total = 0
  emit('on-remind', {
    ...resCount.data,
    ...((otherResCount?.data && { otherJoinSoon: otherResCount.data.waitDeal }) || {}),
  })
  loading.value = false
}

const getDraftList = async () => {
  loading.value = true
  processes.value = []

  const query = searchData.value
  const params = { type: props.tab, ...query }

  const promiseList = [getDraftProcessInfo(params, paginate.value), getProccessQueryCount(params)]
  baseConfig[mode] && promiseList.push(getOtherProccessQueryCount(params, baseConfig[mode].url))
  const [resProcessList, resCount, otherResCount] = await Promise.all(promiseList)
  const cacheValue = {
    ...params,
    ...{ pageNum: paginate.value.pageNum, pageSize: paginate.value.pageSize },
  }
  store.commit('local/SET_LOCAL_SEARCH_DATA', { [cacheKey.value as string]: cacheValue })
  processes.value = resProcessList.data?.list ?? []
  paginate.value.total = resProcessList.data?.totalCount ?? 0
  emit('on-remind', {
    ...resCount.data,
    ...((otherResCount?.data && { otherJoinSoon: otherResCount.data.waitDeal }) || {}),
  })
  loading.value = false
}

// 获取流程modify关联流程
const getRelatedModifyInstancesFn = async (instanceIds: any[]) => {
  if (!instanceIds?.length) return
  const { data = {} } = await getRelatedModifyInstances({ instanceIds })
  processes.value.forEach(item => {
    if (data?.[item?.id]) {
      item.relatedModifyInstances = (data?.[item.id] ?? []).map(item => ({
        ...item,
        instanceId: item.id,
      }))
    }
  })
}

const getProcessesList = async () => {
  loading.value = true
  processes.value = []

  const query = searchData.value
  const params = { type: props.tab, ...query }

  const promiseList = [getProcessList(params, paginate.value), getProccessQueryCount(params)]
  baseConfig[mode] && promiseList.push(getOtherProccessQueryCount(params, baseConfig[mode].url))
  const [resProcessList, resCount, otherResCount] = await Promise.all(promiseList)
  processes.value = resProcessList.data?.list ?? []
  paginate.value.total = resProcessList.data?.totalCount ?? 0
  const cacheValue = {
    ...params,
    ...{ pageNum: paginate.value.pageNum, pageSize: paginate.value.pageSize },
  }
  store.commit('local/SET_LOCAL_SEARCH_DATA', { [cacheKey.value as string]: cacheValue })
  emit('on-remind', {
    ...resCount.data,
    ...((otherResCount?.data && { otherJoinSoon: otherResCount.data.waitDeal }) || {}),
  })
  getRelatedModifyInstancesFn((processes.value || []).map(item => item.id))
  loading.value = false
}

const exportConfig = {
  common: exportProcess,
  exportTemplate: templateExportProcess,
}
const onExportProcess = async (exportType = 'common') => {
  if (!processes.value.length) {
    message.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const query = searchData.value
  const params = { type: props.tab, ...query }
  const res = await exportConfig[exportType](params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('下载成功，请在飞书查看！'))
}
</script>
<style lang="scss" scoped>
.ct-box {
  padding: 0px 20px;
}
.page-content {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>

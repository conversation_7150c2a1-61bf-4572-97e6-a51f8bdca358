// import { App } from 'vue'
// import router from '@/router'
// import * as Sentry from '@sentry/vue'
// import { POWER_ENV, USER_INFO, cache } from '@/utils'
// import { Operation } from '@sentry/vue/types/types'

// const EXTRA_KEY = 'BPM_PROCESS_EXTRA'
// const dsn = 'https://<EMAIL>/16'

// export default function initSentry(app: App) {
//   const isTest = POWER_ENV === 'test'
//   const rate = isTest ? 1.0 : 0.2
//   const user = JSON.parse((cache.get(USER_INFO) as string) || 'null')

//   if (isTest) return // 测试环境不上报

//   const transport = Sentry.makeMultiplexedTransport(Sentry.makeFetchTransport, (args) => {
//     const event = args.getEvent()
//     if (event && event.extra && EXTRA_KEY in event.extra && Array.isArray(event.extra[EXTRA_KEY])) {
//       return event.extra[EXTRA_KEY]
//     }
//     return []
//   })

//   const beforeSend = (event: any) => {
//     if (event?.exception?.values?.[0]?.stacktrace?.frames) {
//       const frames = event.exception.values[0].stacktrace.frames
//       const routeTo = frames
//         .filter((frame: Record<string, any>) => frame.module_metadata && frame.module_metadata.dsn)
//         .map((v: Record<string, unknown>) => v.module_metadata)
//         .slice(-1)

//       if (routeTo.length) event.extra = { ...event.extra, [EXTRA_KEY]: routeTo }
//     }

//     // 过滤预期中的错误
//     if (event?.exception?.values?.[0]) {
//       const exception = event.exception.values[0]
//       const isAmisI18n = exception.value.includes('plugins/help/js/i18n/keynav')
//       const isResizeObserver = exception.value.includes(
//         'ResizeObserver loop completed with undelivered notifications',
//       )
//       const isUserOut = exception.value.includes('登录信息已失效')
//       const isUserOut2 = exception.value.includes('获取权限异常')
//       const isEditor = exception.value.includes(`Failed to execute 'setStart' on 'Range'`)
//       const isEditor2 = exception.value.includes(`Failed to execute 'insertBefore' on 'Node'`)
//       const isAmisMobx = exception.value.includes(`[mobx-state-tree]`)

//       if (
//         isAmisI18n ||
//         isResizeObserver ||
//         isUserOut ||
//         isUserOut2 ||
//         isEditor ||
//         isEditor2 ||
//         isAmisMobx
//       )
//         return null
//     }

//     return event
//   }

//   const options = {
//     app,
//     dsn,
//     // debug: true,
//     // release: __SENTRY_RELEASE__,
//     environment: POWER_ENV,
//     transport,
//     initialScope: {} as any,
//     integrations: [
//       Sentry.breadcrumbsIntegration({ console: false }), // 面包屑记录
//       Sentry.browserTracingIntegration({ router }), // 性能监控
//       Sentry.httpContextIntegration(), // 请求监控
//       Sentry.replayIntegration({ maskAllText: false, blockAllMedia: false }), // 重播
//       Sentry.vueIntegration({ app, attachProps: true }),
//       Sentry.moduleMetadataIntegration(), // 模块元数据信息
//     ],
//     sendDefaultPii: true, // httpContextIntegration：是否收集 cookie 和 header
//     sampleRate: rate,
//     tracesSampleRate: rate,
//     replaysSessionSampleRate: rate,
//     replaysOnErrorSampleRate: 1.0,

//     beforeSend,

//     tracingOptions: {
//       trackComponents: true, // 组件跟踪
//       hooks: ['activate', 'create', 'destroy', 'mount', 'update', 'unmount'] as Operation[], // 钩子跟踪
//     },
//   }

//   if (user)
//     options.initialScope.user = {
//       id: user.id,
//       username: user.userName,
//       email: user.email,
//       dept: user.deptName,
//     }

//   Sentry.init(options)
// }

export default null

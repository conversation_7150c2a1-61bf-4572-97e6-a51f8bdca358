import { request } from '@/utils'
import { IRes, IFormSchemaData } from '@/types/request/index'

export const getFormSchema = async (id: string | number) => {
  const data = await request.get<IRes<IFormSchemaData>>('/api/bpmDefine/getPageDesignById?id=' + id)
  return data as unknown as IRes<IFormSchemaData>
}

export const getProcessModelList = async () => {
  return await request.post('/api/bpmDefine/getProcessModel', {
    currPage: 1,
    pageSize: 9999,
    processName: '',
  })
}

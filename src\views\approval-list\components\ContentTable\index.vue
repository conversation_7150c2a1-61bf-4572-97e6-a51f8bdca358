<template>
  <div class="process-list-new-content-table-container">
    <div class="flex space-between mb16">
      <div class="fw-500 f14">流程列表</div>
      <FSpace :size="[12]" wrap class="handle-row-box">
        <!-- <FButton  type="primary" @click="$emit('on-export-process')">
          <template #icon><i class="iconfont icontubiao_xiazai marginR4" /></template>
          批量导出
        </FButton> -->
      </FSpace>
    </div>
    <FTable
      class="table-warp"
      :columns="columnsConfig.tableColumns"
      :loading="loading"
      :data-source="list"
      :row-key="(data:any) => data.id"
      :row-selection="rowSelection"
      :sticky="{ offsetHeader: 0 }"
      :scroll="{ x: 'min-content' }"
      :pagination="{
          total: pageData.total,
          current: pageData.pageNum,
          pageSize: pageData.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
          onChange: onPaginationChangeFn
        }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'creatorInfo'">
          <div class="label-box">
            <span class="label">[录入时间]</span>
            <span>{{
              (record?.createdTime && transformDate(record?.createdTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--'
            }}</span>
          </div>
          <div class="label-box">
            <span class="label">[录入人]</span>
            <span>{{ record?.creatorName ?? '--' }}</span>
          </div>
          <span class="code-link mt2" @click="logModalRef?.onOpenFn(record?.id)">查看日志</span>
        </template>

        <template v-if="column.dataIndex === 'processInfo'">
          <div class="label-box">
            <span class="label">[流程编号]</span>
            <span :class="[record?.id ? 'code-link' : '']" @click="goApprovalDetailFn(record)">{{
              record?.processInstanceCode ?? '--'
            }}</span>
          </div>
          <div class="label-box">
            <span class="label">[流程标题]</span>
            <MoreTextTips :line-clamp="1">
              <span>{{ record?.topicName ?? '--' }}</span>
            </MoreTextTips>
          </div>
          <div class="label-box">
            <span class="label">[流程类型]</span>
            <MoreTextTips :line-clamp="1">
              <span>{{ record?.processType ?? '--' }}</span>
            </MoreTextTips>
          </div>
        </template>

        <template v-if="column.dataIndex === 'outlineList'">
          <MoreTextTips :line-clamp="3" v-if="record?.outlineList?.length">
            <div class="label-box" v-for="(item, index) in record?.outlineList ?? []" :key="index">
              <span class="label">[{{ item?.label ?? item?.field ?? '--' }}]</span>
              <span>{{ item?.value ?? '--' }}</span>
            </div>
          </MoreTextTips>
        </template>

        <template v-if="column.dataIndex === 'currentMilepost'">
          <MoreTextTips
            class="milepost-box"
            :line-clamp="2"
            :get-popup-container-element="getPopupContainerElement"
            v-if="record?.handleNodeList?.length"
          >
            <div class="mb2" v-for="(item, index) in record?.handleNodeList ?? []" :key="index">
              <span>{{ item?.topicName ?? '--' }}</span
              >/
              <span>{{ item?.userList?.[0]?.userName ?? '--' }}</span>
              <FTooltip placement="top" color="#fff" v-if="item?.userList?.length > 1">
                <template #title>
                  <div class="c333" v-for="(user, userIndex) in item.userList.slice(1)" :key="userIndex">
                    {{ user.userName }}
                  </div>
                </template>
                <FTag class="ml4" size="small" :border="false" color="processing">+{{ item.userList.length - 1 }}</FTag>
              </FTooltip>
            </div>
          </MoreTextTips>
          <div
            v-if="![1, 2].includes(record?.status)"
            class="blue cursor mt2"
            @click="workFlowDrawerRef?.onOpenFn(record.id)"
          >
            流程进度
          </div>
        </template>

        <template v-if="column.dataIndex === 'processStaus'">
          <div class="flex">
            <FTag
              size="small"
              :border="false"
              :color="
                (record?.status === 0 && 'warning') ||
                (record?.status === 1 && 'success') ||
                (record?.status === 2 && 'error') ||
                'default'
              "
            >
              {{
                (record?.status === 0 && '进行中') ||
                (record?.status === 1 && '已完成') ||
                (record?.status === 2 && '已终止') ||
                '--'
              }}
            </FTag>
          </div>
        </template>

        <template v-if="column.dataIndex === 'handleKey'">
          <FSpace :size="[8]">
            <TipBtn tip-title="详情">
              <i class="iconfont icontubiao_chakanxiangqing hover-btn color999" @click="goApprovalDetailFn(record)"></i>
            </TipBtn>
            <template v-if="record?.handleSubmitNodeInfo.hasNode">
              <FDropdown v-if="record.handleSubmitNodeInfo.nodeList.length > 1" :overlay-style="{ minWidth: 'auto' }">
                <TipBtn :tip-title="record?.handleSubmitNodeInfo.label">
                  <i class="iconfont icontubiao_chenggong hover-btn color999"></i>
                </TipBtn>
                <template #overlay>
                  <FMenu>
                    <FMenuItem
                      v-for="(item, index) in record.handleSubmitNodeInfo.nodeList"
                      :key="index"
                      @click="handleApprovalSubmitFn(item)"
                      >{{ item?.topicName }}</FMenuItem
                    >
                  </FMenu>
                </template>
              </FDropdown>
              <TipBtn v-else :tip-title="record?.handleSubmitNodeInfo.label">
                <i
                  class="iconfont icontubiao_chenggong hover-btn color999"
                  @click="handleApprovalSubmitFn(record.handleSubmitNodeInfo.nodeList[0])"
                ></i>
              </TipBtn>
            </template>

            <template v-if="record?.handleRefuseNodeInfo.hasNode">
              <FDropdown v-if="record?.handleRefuseNodeInfo.nodeList.length > 1" :overlay-style="{ minWidth: 'auto' }">
                <TipBtn :tip-title="record?.handleRefuseNodeInfo.label">
                  <i class="iconfont icontubiao_shanchu2 hover-btn color999"></i>
                </TipBtn>
                <template #overlay>
                  <FMenu>
                    <FMenuItem
                      v-for="(item, index) in record.handleRefuseNodeInfo.nodeList"
                      :key="index"
                      @click="handleApprovalRefuseFn(item)"
                      >{{ item?.topicName }}</FMenuItem
                    >
                  </FMenu>
                </template>
              </FDropdown>
              <TipBtn v-else :tip-title="record?.handleRefuseNodeInfo.label">
                <i
                  class="iconfont icontubiao_shanchu2 hover-btn color999"
                  @click="handleApprovalRefuseFn(record.handleRefuseNodeInfo.nodeList[0])"
                ></i>
              </TipBtn>
            </template>
          </FSpace>
        </template>
      </template>
    </FTable>
    <!-- 新增弹框 -->
    <LogTableModal ref="logModalRef" />
    <SubmitNodeModal ref="submitNodeModalRef" />
    <RefuseNodeModal ref="refuseNodeModalRef" />
    <WorkFlowDrawer ref="workFlowDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, provide } from 'vue'
import { useRouter } from 'vue-router'
import { useTableColumn } from '@/components/TableOperation/index'
import MoreTextTips from '@/components/MoreTextTips/index'
import LogTableModal from '../LogTableModal/index.vue'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import SubmitNodeModal from '@/views/approval-operate/components/SubmitNodeModal.vue'
import RefuseNodeModal from '@/views/approval-operate/components/RefuseNodeModal.vue'
import WorkFlowDrawer from '../WorkFlowDrawer/index.vue'
import { transformDate } from '@/utils'
import { PageApprovalProcessRes, ApprovalInfoRes } from '@/api'

interface IProps {
  queryData: any
  pageData: any
  list: any
  loading: boolean
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:pageData', 'on-export-process', 'get-processes-list-fn'])
const router = useRouter()
const pageData = computed({
  get: () => props.pageData,
  set: val => emits('update:pageData', val),
})

const selectedKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[]) => (selectedKeys.value = selectedRowKeys),
}))
const logModalRef = ref()
const columnsConfig = reactive(
  useTableColumn(
    [
      { title: '流程信息', dataIndex: 'processInfo', key: 'processInfo', width: 260 },
      { title: '概要信息', dataIndex: 'outlineList', key: 'outlineList', width: 220 },
      { title: '当前阶段', dataIndex: 'currentMilepost', key: 'currentMilepost', width: 280 },
      { title: '流程状态', dataIndex: 'processStaus', key: 'processStaus', width: 130 },
      { title: '录入时间/操作日志', dataIndex: 'creatorInfo', key: 'creatorInfo', width: 260 },
      { title: '操作', dataIndex: 'handleKey', key: 'handleKey', width: 120, fixed: 'right' },
    ],
    'approvalProcessListTable'
  )
)
const getPopupContainerElement = ref(
  () => document.querySelector('.process-list-new-content-table-container') as HTMLElement
)
const submitNodeModalRef = ref()
const refuseNodeModalRef = ref()
const workFlowDrawerRef = ref()
const handleNode = ref<ApprovalInfoRes>()

const handleApprovalSubmitFn = item => {
  handleNode.value = item
  submitNodeModalRef?.value?.open()
}

const handleApprovalRefuseFn = item => {
  handleNode.value = item
  refuseNodeModalRef?.value?.open()
}

const goApprovalDetailFn = (item: PageApprovalProcessRes) => {
  if (item?.detailsUrl) {
    window.open(item?.detailsUrl)
  } else {
    const params = { id: item.id }
    router.push({ name: 'ApprovalDetail', params })
  }
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  pageData.value.pageNum = current
  pageData.value.pageSize = pageSize
  emits('get-processes-list-fn')
}

provide('handleNode', handleNode)
</script>

<style lang="scss" scoped>
.process-list-new-content-table-container {
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .code-link {
    display: inline-block;
    cursor: pointer;
    color: #378eef;
  }
  .mt2 {
    margin-top: 2px;
  }
  .label-box {
    display: flex;
    color: #333;
    margin-bottom: 2px;
    .label {
      margin-right: 4px;
      color: #999;
      white-space: nowrap;
    }
  }
  .info-type {
    display: inline-block;
    height: 18px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 18px;
    padding: 0px 4px;
    margin-right: 4px;
    cursor: pointer;
  }
  .info-type0 {
    color: #2fcc83;
    background: #eafaf2;
  }
  .info-type1 {
    color: #378eef;
    background: #ebf3fd;
  }
  .info-type2 {
    color: #ffab00;
    background: #fef7e7;
  }
  .info-type3 {
    color: #a697fe;
    background: rgba(166, 151, 254, 0.2);
  }
  .content-box {
    padding: 8px;
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
  .relevance-list {
    .label {
      margin-right: 4px;
      color: #999;
      white-space: nowrap;
    }
    .warp-content {
      word-break: break-all;
      white-space: normal;
      cursor: pointer;
      color: #378eef;
      &::after {
        content: ' / ';
      }
      &:last-child::after {
        content: '';
      }
    }
  }
  :deep(.fs-tooltip) {
    max-width: none;
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
}
</style>

<template>
  <div class="search-list-container">
    <template v-for="item of searchConfigLists.options" :key="item.componentValueKey">
      <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue">
        <template #suffix>
          <i class="cursor iconfont colord8d8d8 fontSize12" @click="item.componentAttrs.onPressEnter">&#xe70e;</i>
        </template>
      </component>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ISearchDataConfig } from '@/types/pgbDataBoard'

type propsType = {
  searchConfigLists: ISearchDataConfig
}
const props = defineProps<propsType>()
</script>

<style lang="scss" scoped>
.search-list-container {
  display: flex;
  flex-wrap: wrap;
  color: #999;
  .marginB12 {
    margin-bottom: 12px;
  }
  .marginR12 {
    margin-right: 12px;
  }
  :deep(.fs-select-selection-item) {
    color: #333;
  }
  :deep(.fs-select-selection-placeholder) {
    color: #333;
  }
  :deep(.fs-select-selection-placeholder) {
    color: #666;
  }
}
</style>

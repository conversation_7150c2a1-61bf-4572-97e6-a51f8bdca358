<template>
  <div class="approval-info-container">
    <FCard class="border-radius-box form-card-box marginB16" :title="i18n.t('表单信息')">
      <BaseInfo v-bind="applyFormInfo" />
    </FCard>
    <FCard class="border-radius-box" :title="i18n.t('审批记录')">
      <LogTable />
    </FCard>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from '@/utils'
import { ApplyFormInfoRes } from '@/api'
import BaseInfo from './BaseInfo.vue'
import LogTable from './LogTable.vue'

interface IProps {
  applyFormInfo: any
}
const i18n = useI18n()
const props = defineProps<IProps>()
</script>

<style scoped lang="scss">
.approval-info-container {
  .border-radius-box {
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.04);
    border-radius: 4px;
  }
  .form-card-box {
    :deep(.fs-card-body) {
      padding: 9px 7px 0px 7px !important;
    }
  }
}
</style>

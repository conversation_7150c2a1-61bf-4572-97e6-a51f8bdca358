<template>
  <FPopover trigger="hover" overlay-class-name="popover-select-notes" :get-popup-container="getPopupContainer">
    <template #content>
      <div class="file-box">
        <FCheckboxGroup v-model:value="checkedGroup" @change="checkedGroupChange" class="marginB10">
          <p class="fontS12" style="margin-bottom: 5px">{{ i18n.t('附件') }}</p>
          <div v-for="file in plainOptions" :key="file.resourseKey" style="margin-bottom: 5px">
            <FCheckbox v-model:value="file.resourseKey">
              <a class="file-ellipsis fontS12" @click.prevent="download(file)">
                <span class="iconfont fontS12 file-icon">&#xe655;</span>
                {{ file.fileName }}
              </a>
            </FCheckbox>
          </div>
        </FCheckboxGroup>
        <div class="download-batch">
          <FCheckbox v-model:checked="isCheckAll" @change="checkAll" class="marginR20 fontS12">
            <span class="file-ellipsis fontS12" style="margin-left: 6px"> {{ i18n.t('全选') }} </span>
          </FCheckbox>
          <a @click="batchDownload"><span class="iconfont fontS12 marginR6">&#xe63f;</span>{{ i18n.t('批量下载') }}</a>
        </div>
      </div>
    </template>
    <slot name="icon">
      <a> <span class="iconfont fontS12">&#xe656;</span> {{ i18n.t('附件') }} </a>
    </slot>
  </FPopover>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { messageInstance as message } from '@fs/smart-design'
import { useI18n } from '@/utils'

const i18n = useI18n()

const getPopupContainer = (trigger: HTMLElement) => trigger.parentElement

const props = defineProps({
  data: {
    type: [Object, Array],
    default: () => [],
  },
})

const emit = defineEmits(['download', 'batchDownload'])
const plainOptions = ref<any>(props.data)
const checkedGroup = ref<any>([])
const isCheckAll = ref(false)
const checkedGroupChange = (e: any) => {
  isCheckAll.value = e.length === plainOptions.value.length
}

const checkAll = (e: any) => {
  if (isCheckAll.value) {
    checkedGroup.value = plainOptions.value.map((item: any) => {
      return item.resourseKey
    })
  } else {
    checkedGroup.value = []
  }
}

const download = (file: any) => {
  emit('download', file.resourseKey, file.fileName)
}

const batchDownload = () => {
  if (checkedGroup.value.length > 0) {
    const checkList: any[] = []
    plainOptions.value.forEach((item: any) => {
      checkedGroup.value.forEach((items: string) => {
        if (items === item.resourseKey) {
          const list: Record<string, string> = {}
          list.url = item.resourseKey
          list.fileName = item.fileName
          checkList.push(list)
        }
      })
    })
    emit('batchDownload', checkList)
    checkedGroup.value = []
    isCheckAll.value = false
  } else {
    message.error('请勾选文件')
  }
}
</script>
<style lang="scss" scoped>
.file-box {
  min-width: 200px;
  font-size: 12px;
  .file-icon {
    color: #fdb926 !important;
  }
  .file-ellipsis {
    vertical-align: -5px;
    display: inline-block;
    max-width: 239px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
    color: #378eef;
    & > .iconfont {
      margin-left: 6px;
    }
  }
}
.download-batch {
  padding-top: 10px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
}
.marginR6 {
  margin-right: 6px;
}
.fontS12 {
  font-size: 12px;
}
</style>

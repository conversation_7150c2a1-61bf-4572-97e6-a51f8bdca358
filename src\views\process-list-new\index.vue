<template>
  <div class="process-list-new" :key="currProcessTypeId">
    <FSpin :spinning="pageLoading">
      <!-- <Breadcrumb class="none-container-padding" :list="breadcrumbList" /> -->
      <div class="card-content-shadow pl24 pr24 mb16">
        <FTabSet class="cust-tab-list" :tab-list="tabList" v-model:activeKey="tabValue" @change="tabChangeFn" />
        <SearchContent v-model:query-data="queryData" v-model:has-export-template="hasExportTemplate" />
      </div>
      <ContentTable
        class="card-content-shadow pl24 pr24 pt24 mt16"
        v-model:page-data="pageData"
        :query-data="queryData"
        :list="list"
        :loading="loading"
        :curr-process-type-data="currProcessTypeData"
        :curr-process-type-id="currProcessTypeId"
        :has-export-template="hasExportTemplate"
        @on-export-process="onExportProcessFn"
        @get-processes-list-fn="getProcessesListFn"
      />
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, provide, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { IProcessClassType } from '@/types/handle'
import { cache } from '@/utils'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import SearchContent from './components/SearchContent/index.vue'
import ContentTable from './components/ContentTable/index.vue'
import { message } from '@fs/smart-design'
import {
  GetTagAndNode,
  exportNewProcessList,
  getNewProcessList,
  getProccessQueryCount,
  templateExportProcess,
} from '@/api'

const route = useRoute()
const currProcessTypeId = ref()
const routerName = ref()
const processTypeData = ref<IProcessClassType[]>([])
const currProcessTypeData = computed<any>(
  () => processTypeData.value.find(item => item.id == +currProcessTypeId.value) ?? {}
)
const processConfigIdList = computed<any>(() => {
  if (currProcessTypeId.value != 'all' && !currProcessTypeData.value.id)
    return processTypeData.value.map(item => item.id)
  return undefined
})
const breadcrumbList = computed(() =>
  [
    { title: '流程管理' },
    { title: '流程列表（新）' },
    currProcessTypeData?.value?.processName && { title: currProcessTypeData?.value?.processName ?? '' },
  ].filter(Boolean)
)
const queryData = ref<any>({})
const tabValue = ref<number>(1)
const tabList = ref<any>([
  {
    title: '全部',
    count: 0,
    status: 0,
  },
  {
    title: '待我处理',
    count: 0,
    status: 1,
  },
  {
    title: '已完成',
    count: 0,
    status: 5,
  },
  {
    title: '已终止',
    count: 0,
    status: 6,
  },
  // {
  //   title: '草稿',
  //   count: 0,
  //   status: 7,
  // },
])
const list = ref<any>([])
const pageData = ref<any>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const loading = ref<boolean>(false)
const pageLoading = ref<boolean>(false)
const hasExportTemplate = ref<boolean>(false)

const tabChangeFn = () => {
  pageData.value.pageNum = 1
  pageData.value.pageSize = 10
  getProcessesListFn()
}

const getProcessTypes = async () => {
  try {
    pageLoading.value = true
    const res = await GetTagAndNode()
    processTypeData.value = res.data
  } catch (error) {
    throw new Error('流程类型请求失败')
  } finally {
    pageLoading.value = false
  }
}

const handleProcessType = async () => {
  await nextTick()
  if (processConfigIdList.value) {
    processTypeData.value = processTypeData.value.filter(item => item?.groupCode == currProcessTypeId.value)
  }
}

const deleteKeysFn = data => {
  delete data.cacheValue
  delete data.init
}

const exportConfig = {
  common: exportNewProcessList,
  exportTemplate: templateExportProcess,
}

const onExportProcessFn = async (exportType = 'common') => {
  if (!list.value.length) {
    message.warning('当前页面无数据，请重新选择！')
    return
  }
  const params = { ...queryData.value, type: tabValue.value }
  deleteKeysFn(params)
  const res = await exportConfig[exportType](params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success('下载成功，请在飞书查看！')
}

const getProccessQueryCountFn = async () => {
  try {
    tabList.value.forEach((item: any) => {
      item.count = 0
    })
    const data = {
      ...queryData.value,
      processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
    }
    deleteKeysFn(data)
    const res = await getProccessQueryCount(data)
    // tabList.value[0].count = res.data?.total ?? 0
    tabList.value[1].count = res.data?.waitDeal ?? 0
    // tabList.value[2].count = res.data?.completed ?? 0
    // tabList.value[3].count = res.data?.completion ?? 0
    // tabList.value[4].count = res.data?.draftNum ?? 0
  } catch (error) {
    throw new Error('流程类型请求失败')
  }
}

const getProcessesListFn = async () => {
  try {
    loading.value = true
    list.value = []
    const data = {
      ...queryData.value,
      type: tabValue.value,
      processConfigIdList: (!queryData.value?.processConfigId && processConfigIdList.value) || undefined,
    }
    const cachData = (cache.get(routerName?.value) && JSON.parse(cache.get(routerName?.value) as string)) || {}
    cache.set(
      routerName?.value,
      JSON.stringify(
        Object.assign(cachData, {
          [currProcessTypeId.value]: {
            ...(data?.cacheValue ?? {}),
            pageNum: pageData.value.pageNum,
            pageSize: pageData.value.pageSize,
            type: tabValue.value,
          },
        })
      )
    )
    deleteKeysFn(data)
    const res = await getNewProcessList(data, pageData.value)
    list.value = res.data?.list ?? []
    pageData.value.total = res.data?.totalCount ?? 0
  } catch (error) {
    throw new Error('流程类型请求失败')
  } finally {
    loading.value = false
  }
}

watch(
  () => queryData.value,
  data => {
    if (!data?.init) {
      pageData.value.pageNum = 1
      pageData.value.pageSize = 10
    }
    getProccessQueryCountFn()
    getProcessesListFn()
  }
)

watch(
  () => route.params.type,
  async type => {
    await getProcessTypes()
    handleProcessType()
    currProcessTypeId.value = type
    routerName.value = route.name
    const cachData = (cache.get(routerName?.value) && JSON.parse(cache.get(routerName?.value) as string)) || {}
    const currentCachData = cachData?.[currProcessTypeId?.value] || {}
    tabValue.value = currentCachData?.type ?? 1
    pageData.value.pageNum = currentCachData?.pageNum ?? 1
    pageData.value.pageSize = currentCachData?.pageSize ?? 10
  },
  { immediate: true }
)

provide('processTypeData', processTypeData)
provide('routerName', routerName)
provide('currProcessTypeId', currProcessTypeId)
provide('currProcessTypeData', currProcessTypeData)
provide('processConfigIdList', processConfigIdList)
</script>

<style lang="scss" scoped>
.none-container-padding {
  margin-top: -20px;
  // margin-left: -20px;
  // width: calc(100% + 40px);
}
.card-content-shadow {
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
.cust-tab-list {
  :deep(.fs-badge-count) {
    position: relative;
    transform: none;
  }
}
</style>

import { request, WORKFLOW_URL } from '@/utils'
import type {
  ProcessClassifyResponse,
  GetProcessParams,
  ProcessNodeParams,
  ProcessNodeResponse,
  ManagerConfigResponse,
  ManagerConfigParams,
  UpdataManagerConfigParams,
  ConfigListParams,
  ConfigListResponse,
  UpdateStatusParams,
  UpdateStatusResponse,
  DelManagerResponse,
  DelManagerParams,
  IFormAll,
} from '@/types/processLabelModel'
import { IRes } from '@/types/request'

// 流程类型
export function GetProcess(data: GetProcessParams): Promise<ProcessClassifyResponse> {
  return request.post('/api/bpmDefine/getProcess', data)
}

// 查询里程碑
export function GetProcessNode(params: ProcessNodeParams): Promise<ProcessNodeResponse> {
  return request.get('/api/bpmDefine/getNode', {
    params,
  })
}
// 流程标签添加数据
export function CreateProcessLabel(data: ManagerConfigParams): Promise<ManagerConfigResponse> {
  return request.post('/api/managerConfig/addManagerConfig', data)
}
// 修改流程标签
export function UpdateProcessLabel(data: UpdataManagerConfigParams): Promise<ManagerConfigResponse> {
  return request.post('/api/managerConfig/updManagerConfig', data)
}
// 4. 查询列表
export function GetConfigList(data: ConfigListParams): Promise<ConfigListResponse> {
  return request.post('/api/managerConfig/getConfigList', data)
}
// 删除
export function DelManager(params: DelManagerParams): Promise<DelManagerResponse> {
  return request.get('/api/managerConfig/delManagerConfig', {
    params,
  })
}
// 禁用/解禁负责人时效配置
export function UpdateStatusManager(params: UpdateStatusParams): Promise<UpdateStatusResponse> {
  return request.get('/api/managerConfig/banManagerConfig', {
    params,
  })
}

export interface ICopyConfigParams {
  processConfigId: number
  sourceMilepostNodeId: number
  targetMilepostNodeIds: number[]
}
// 拷贝
export const copyConfig = async (params: ICopyConfigParams) => {
  const res = await request.post<IRes>('/api/managerConfig/copyConfig', params)
  return res as unknown as IRes
}

// 获取表单
export function getFormAll(): Promise<IFormAll> {
  return request.get('/api/bpmDefine/getFormAll')
}

// 执行表达式
export function execute(data: any): Promise<any> {
  return request.post('/api/devopss/execute', data)
}

export const getByIdExportTemplateConfig = async (id: string) => {
  const res = await request.get<IRes>(`/api/exportTemplateConfig/getByConfigId/${id}`)
  return res as unknown as IRes
}

export const updateByIdExportTemplateConfig = async (params: any) => {
  const res = await request.post<IRes>(`/api/exportTemplateConfig/update`, params)
  return res as unknown as IRes
}

export const updateByIdExportTemplateFieldSort = async (params: any) => {
  const res = await request.post<IRes>(`/api/exportTemplateField/sort`, params)
  return res as unknown as IRes
}

export const getByIdExportTemplateField = async (id: string) => {
  const res = await request.get<IRes>(`/api/exportTemplateField/getByTemplateId/${id}`)
  return res as unknown as IRes
}

export const saveExportTemplateField = async (params: any) => {
  const res = await request.post<IRes>(`/api/exportTemplateField/save`, params)
  return res as unknown as IRes
}

export const updateExportTemplateField = async (params: any) => {
  const res = await request.post<IRes>(`/api/exportTemplateField/update`, params)
  return res as unknown as IRes
}

export const getSysField = async () => {
  const res = await request.get<IRes>(`/api/exportTemplateConfig/getSysField`)
  return res as unknown as IRes
}

export const getNodeField = async (processKey, nodeId) => {
  const res = await request.get<IRes>(`${WORKFLOW_URL}/api/pageDesign/getNodeField/${processKey}/${nodeId}`)
  return res as unknown as IRes
}

export const deleteNodeField = async (params: any, id: any) => {
  const res = await request.post<IRes>(`/api/exportTemplateField/delete/${id}`, params)
  return res as unknown as IRes
}

export const getNextSortOrder = async (id: any) => {
  const res = await request.get<IRes>(`/api/exportTemplateField/nextSortOrder/${id}`)
  return res as unknown as IRes
}

export const previewTemplateField = async (params: any) => {
  const res = await request.post<IRes>(`/api/file/previewTemplate`, params)
  return res as unknown as IRes
}

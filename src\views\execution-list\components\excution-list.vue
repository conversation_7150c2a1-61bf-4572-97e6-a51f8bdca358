<template>
  <div class="excution-list-container shadow-radius">
    <div class="content-box">
      <FTable
        :data-source="dataList"
        :columns="columns"
        :pagination="false"
        :scroll="{ x: '100%' }"
        class="table-container"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'sourceProcessConfigId'">
            <div class="cursor">
              <div class="cursor">{{ record.sourceTopicName }}</div>
              <div class="blue-link cursor" @click="jumpDetail(record, 'source')">{{ record.sourceNumber }}</div>
              <div class="cursor">{{ record.sourceProcessConfigName }}</div>
            </div>
          </template>
          <template v-if="column.key === 'targetProcessConfigId'">
            <div class="cursor">
              <div class="cursor">{{ record.targetTopicName }}</div>
              <div class="blue-link cursor" @click="jumpDetail(record, 'target')">{{ record.targetNumber }}</div>
              <div class="cursor">{{ record.targetProcessConfigName }}</div>
            </div>
          </template>
          <template v-if="column.key === 'triggerName'">{{ record.triggerName }}</template>
          <template v-if="column.key === 'triggerType'">{{ transformTrigger(record.triggerType) }}</template>
          <template v-if="column.key === 'status'">
            <span v-if="record.status == 2" class="wait">未执行</span>
            <span v-if="record.status == 1" class="enable">{{ i18n.t('成功') }}</span>
            <span v-if="record.status == 0" class="disable">{{ i18n.t('失败') }}</span>
          </template>
          <template v-if="column.key === 'retryStatus'">
            <span v-if="record.retryStatus == 1" class="disable">{{ i18n.t('失败') }}</span>
            <span v-if="record.retryStatus == 2" class="enable">{{ i18n.t('重试成功') }}</span>
          </template>
          <template v-if="column.key === 'remarks'">
            <FPopover
              trigger="hover"
              placement="bottomLeft"
              :overlay-style="{ width: '500px', maxHeight: '500px', overflow: 'auto' }"
            >
              <template #content>
                <span class="remark-popover">
                  {{ record.remarks }}
                </span>
              </template>
              <span class="content-remark">
                {{ record.remarks ? record.remarks : '--' }}
              </span>
            </FPopover>
          </template>
          <template v-if="column.key === 'createdTime'">
            {{ dayjs(record.createdTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <FSpace :size="[8]" wrap>
              <TipBtn
                v-if="record?.retryStatus === 1"
                has-pop
                :tip-title="i18n.t('重试')"
                :pop-title="i18n.t('确定重新发起触发器事件吗？')"
                :pop-content="i18n.t('将重新发起触发器事件，请谨慎操作！')"
                @onConfirmFn="onCallApiWithRetriesConfirmFn(record)"
              >
                <i class="iconfont icontubiao_chenggong hover-btn"></i>
              </TipBtn>
            </FSpace>
          </template>
        </template>
        <template #emptyText>
          <img :src="noImgUrl" />
          <p class="colorBBB fontSize12 fs-tip">{{ i18n.t('暂无流程类型~') }}</p>
        </template>
      </FTable>
      <div class="fei-su-pagination">
        <FPagination
          v-model:current="paging.pageNum"
          @change="onChange"
          v-model:page-size="paging.pageSize"
          :total="paging.total"
          show-size-changer
          :show-total="() => `共 ${paging.total} 页`"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import noImgUrl from '@/assets/images/no-data.png'
import dayjs from 'dayjs'
import { IPage, LogData } from '@/types/excutionListModel'
import { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { message } from '@fs/smart-design'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import { queryExcutionList, callApiWithRetriesTrigger } from '@/api/automotion'
import { useRouter } from 'vue-router'
import { transformTrigger } from '@/utils/filter'
import { useI18n, jumpToDemand } from '@/utils'
const i18n = useI18n()
const dataList = ref<LogData[]>([])
const paging = ref<IPage>({ pageNum: 1, pageSize: 10, total: 0 })
const columns = computed<TableColumnsType>(() => [
  { title: i18n.t('触发流程'), dataIndex: 'sourceProcessConfigId', key: 'sourceProcessConfigId', width: 160 },
  { title: i18n.t('执行流程'), dataIndex: 'targetProcessConfigId', key: 'targetProcessConfigId', width: 160 },
  { title: i18n.t('触发器名称'), dataIndex: 'triggerName', key: 'triggerName', width: 160 },
  { title: i18n.t('触发器类型'), dataIndex: 'triggerType', key: 'triggerType', width: 160 },
  { title: i18n.t('状态'), dataIndex: 'status', key: 'status', width: 130 },
  { title: i18n.t('重试状态'), dataIndex: 'retryStatus', key: 'retryStatus', width: 130 },
  { title: i18n.t('提示信息'), dataIndex: 'remarks', key: 'remarks', width: 400 },
  { title: i18n.t('创建时间'), dataIndex: 'createdTime', key: 'createdTime', width: 180 },
  { title: i18n.t('操作'), dataIndex: 'handle', key: 'handle', fixed: 'right', width: 80 },
])
const router = useRouter()
interface IProps {
  searchParams: object
}
const props = defineProps<IProps>()
onMounted(() => {
  fetchData()
})
watch(
  () => props.searchParams,
  () => {
    fetchData()
  }
)

const onCallApiWithRetriesConfirmFn = async (record: LogData) => {
  // 重试触发器
  const res = await callApiWithRetriesTrigger({ id: record.id })
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('重发成功'))
  fetchData()
}

const fetchData = async () => {
  const parmas = {
    pageNum: paging.value.pageNum,
    pageSize: paging.value.pageSize,
    ...props.searchParams,
  }
  const res = await queryExcutionList(parmas)
  if (res.code == 200) {
    dataList.value = res.data.list
    paging.value.total = res.data.totalCount
  }
}
const onChange = (current: number, pageSize: number) => {
  paging.value.pageNum = current
  paging.value.pageSize = pageSize
  fetchData()
}
const jumpDetail = (record: LogData, distance: string) => {
  // 跳转到流程详情
  if (distance == 'source') {
    jumpToDemand(record.sourceInstanceId, record.sourceProcessConfigId, true, router)
  } else if (distance == 'target') {
    jumpToDemand(record.targetInstanceId, record.targetProcessConfigId, true, router)
  }
}
</script>

<style lang="scss" scoped>
.excution-list-container {
  background-color: #fff;
  .content-box {
    padding: 24px 24px 0;
    .blue-link {
      color: #378eef;
    }
    .content-remark {
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .enable {
      display: inline-block;
      width: 30px;
      height: 18px;
      font-size: 12px;
      color: #2fcc83;
      border: 1px solid #2fcc83;
      background-color: #eafaf2;
      text-align: center;
      border-radius: 2px;
    }
    .disable {
      display: inline-block;
      width: 30px;
      height: 18px;
      color: #f04141;
      border: 1px solid #f04141;
      background-color: #fdecec;
      text-align: center;
      border-radius: 2px;
    }
    .wait {
      display: inline-block;
      height: 18px;
      padding: 0 2px;
      color: #333333;
      border: 1px solid #a6b0ba;
      background-color: #eeeeee;
      text-align: center;
      border-radius: 2px;
    }
  }
}
.table-container {
  :deep(.fs-table) {
    .fs-table-tbody > tr.fs-table-row:hover > td,
    .fs-table-tbody > tr > td.fs-table-cell-row-hover {
      background-color: #f1f4f8 !important;
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
}
:deep(.fs-table-tbody) {
  .fs-table-cell:empty::before {
    content: '--';
  }
}
.shadow-radius {
  background: #ffffff;
  box-shadow: 0 2px 8px 0 rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
.fei-su-pagination {
  display: flex;
  justify-content: flex-end;
  padding: 14px 0 24px;
  > span {
    line-height: 32px;
    color: #bbb;
    margin-right: 10px;
  }
}
</style>

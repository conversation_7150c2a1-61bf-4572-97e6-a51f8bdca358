<template>
  <div class="content-box">
    <Tap :list="tapList" :disable="disable" @change="tapChange" />
    <Content :tab="tapKey" @on-remind="getReminds" v-model:table-loading="disable" />
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onBeforeMount } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from '@/utils'
import Tap from './TapBox.vue'
import Content from './ContentList.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const cacheKey = computed(() => {
  if (route.name === 'ProcessList') return 'all'
  if (route.params?.type) return route.params?.type
  return 'noCache'
})

const i18n = useI18n()
const store = useStore()
const tapKey = ref<number>(1)
const disable = ref<boolean>(false)

const baseConfig = {
  production: {
    title: i18n.t('(cn)待我处理'),
    url: 'https://cn-bpm.fs.com',
  },
  compliance: {
    title: i18n.t('(en)待我处理'),
    url: 'https://bpm.fs.com',
  },
}

const mode = process.env.VUE_APP_ENV || 'localhost'
const taps = computed(() => {
  if (baseConfig[mode]) {
    return [
      { label: i18n.t('全部'), value: 0, id: 0 },
      { label: i18n.t('待我处理'), value: 0, id: 1 },
      { label: baseConfig[mode].title, value: 0, id: 8 },
      { label: i18n.t('我发起的'), value: 0, id: 2 },
      { label: i18n.t('即将参与'), value: 0, id: 3 },
      { label: i18n.t('进行中'), value: 0, id: 4 },
      { label: i18n.t('已完成'), value: 0, id: 5 },
      { label: i18n.t('流程终止'), value: 0, id: 6 },
      { label: i18n.t('草稿'), value: 0, id: 7 },
    ]
  } else {
    return [
      { label: i18n.t('全部'), value: 0, id: 0 },
      { label: i18n.t('待我处理'), value: 0, id: 1 },
      { label: i18n.t('我发起的'), value: 0, id: 2 },
      { label: i18n.t('即将参与'), value: 0, id: 3 },
      { label: i18n.t('进行中'), value: 0, id: 4 },
      { label: i18n.t('已完成'), value: 0, id: 5 },
      { label: i18n.t('流程终止'), value: 0, id: 6 },
      { label: i18n.t('草稿'), value: 0, id: 7 },
    ]
  }
})
const reminds = ref({
  completed: 0,
  joinSoon: 0,
  meStart: 0,
  running: 0,
  total: 0,
  waitDeal: 0,
  completion: 0,
  draftNum: 0,
  otherJoinSoon: 0,
})

const tapList = computed(() => {
  const list = JSON.parse(JSON.stringify(taps.value))
  if (baseConfig[mode]) {
    list[0].value = reminds.value.total
    list[1].value = reminds.value.waitDeal
    list[2].value = reminds.value.otherJoinSoon
    list[3].value = reminds.value.meStart
    list[4].value = reminds.value.joinSoon
    list[5].value = reminds.value.running
    list[6].value = reminds.value.completed
    list[7].value = reminds.value.completion
    list[8].value = reminds.value.draftNum
  } else {
    list[0].value = reminds.value.total
    list[1].value = reminds.value.waitDeal
    list[2].value = reminds.value.meStart
    list[3].value = reminds.value.joinSoon
    list[4].value = reminds.value.running
    list[5].value = reminds.value.completed
    list[6].value = reminds.value.completion
    list[7].value = reminds.value.draftNum
  }
  return list
})

onBeforeMount(() => {
  const cache = store.getters['local/getLocalSearchData']
  if (cache !== undefined && cache !== null) {
    const localSearchData = cache[cacheKey.value as string] ?? {}
    ;(localSearchData.type || localSearchData.type === 0) && (tapKey.value = localSearchData.type)
  }
})

const tapChange = (data: number) => {
  tapKey.value = data
}

const getReminds = (val: any) => {
  reminds.value = val || {}
}
</script>
<style lang="scss" scoped>
.content-box {
  background: #ffffff;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  margin-top: 20px;
}
</style>

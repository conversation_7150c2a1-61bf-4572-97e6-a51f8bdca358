<template>
  <div class="count-card-container marginB16">
    <div
      class="card-box"
      :style="{ backgroundImage: `url(${item.backgroundImg})` }"
      v-for="item in cardList"
      :key="item.title"
    >
      <div class="left">
        <span class="title">{{ item.title }}</span>
        <div class="data-box">
          <span class="num">{{ item.num }}</span>
          <span class="text" v-html="item.text"></span>
        </div>
      </div>
      <img :src="item.img" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from '@/utils'
import { FlowChartIndicatorsRes, PageApprovalProcessCountRes } from '@/api'
import wait from '../../images/wait.png'
import wait_icon from '../../images/wait_icon.png'
import processed from '../../images/processed.png'
import processed_icon from '../../images/processed_icon.png'
import start from '../../images/start.png'
import start_icon from '../../images/start_icon.png'

interface IProps {
  countCardData?: FlowChartIndicatorsRes
  approvalTabDataCount: PageApprovalProcessCountRes
}

const props = defineProps<IProps>()
const i18n = useI18n()
const cardList = computed(() => [
  {
    title: i18n.t('待我处理'),
    num: props?.approvalTabDataCount?.waitDeal ?? 0,
    text:
      i18n.t('待我阅读') +
      `<span style="color: #333;"> ${props?.approvalTabDataCount?.unread ?? 0} </span>` +
      i18n.t('个'),
    backgroundImg: wait,
    img: wait_icon,
  },
  {
    title: i18n.t('本月我已处理'),
    num: props?.countCardData?.processedMonth ?? 0,
    text:
      i18n.t('全部处理') +
      `<span style="color: #333;"> ${props?.countCardData?.processedAll ?? 0} </span>` +
      i18n.t('个'),
    backgroundImg: processed,
    img: processed_icon,
  },
  {
    title: i18n.t('本月我发起的'),
    num: props?.countCardData?.meStartMonth ?? 0,
    text:
      i18n.t('全部发起') +
      `<span style="color: #333;"> ${props?.countCardData?.meStartAll ?? 0} </span>` +
      i18n.t('个'),
    backgroundImg: start,
    img: start_icon,
  },
])
</script>

<style scoped lang="scss">
.count-card-container {
  display: flex;
  flex-wrap: nowrap;
  padding: 24px;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
  .card-box {
    display: flex;
    width: 377px;
    height: 130px;
    align-items: center;
    flex-wrap: nowrap;
    justify-content: space-between;
    flex: 1;
    padding: 2.6% 3%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-size: 12px;
    & + .card-box {
      margin-left: 16px;
    }
    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      height: 100%;
      .title {
        font-size: 16px;
        white-space: nowrap;
        line-height: 1;
        font-weight: 700;
      }
      .data-box {
        display: flex;
        flex-wrap: nowrap;
        align-items: baseline;
        .num {
          margin-top: 16px;
          margin-right: 12px;
          font-size: 32px;
          color: #333333;
          white-space: nowrap;
          line-height: 1;
        }
        .text {
          color: #999999;
          white-space: nowrap;
        }
      }
    }
    img {
      width: 26%;
      min-width: 48px;
    }
  }
  @media screen and (max-width: 1440px) {
    .card-box {
      .data-box {
        flex-direction: column;
        .num {
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>

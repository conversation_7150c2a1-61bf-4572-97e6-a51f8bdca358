import { request } from '@/utils'
import { IRes, IResDataList } from '@/types/request'

export const manageRoleList = async (data: any) => {
  const res = await request.post<IRes<IResDataList<any>>>('/api/roleConcern/getRoleByCondition', data)
  return res as unknown as IRes<IResDataList<any>>
}

export const manageAddRole = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/roleConcern/addRole', data)
  return res as unknown as IRes<any>
}

export const manageUpdateRole = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/roleConcern/updateRole', data)
  return res as unknown as IRes<any>
}

export const deleteManageRole = async (params: any) => {
  const res = await request.get<IRes<any>>('/api/roleConcern/deleteaRole', { params })
  return res as unknown as IRes<any>
}

export const importManageRole = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/roleConcern/importRole', data)
  return res as unknown as IRes<any>
}

export const importManageRoleUser = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/roleConcern/importRoleStuff', data)
  return res as unknown as IRes<any>
}

export const getManageRole = async (params: any) => {
  const res = await request.get<IRes<any>>('/api/roleConcern/getRoleDetailById', { params })
  return res as unknown as IRes<any>
}

export const getManageRoleUserList = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/roleConcern/getRoleStuffByConfigId', data)
  return res as unknown as IRes<any>
}

export const addManageRoleUser = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/roleConcern/addBatchRoleStuff', data)
  return res as unknown as IRes<any>
}

export const updateManageRoleUser = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/roleConcern/updateRoleStuff', data)
  return res as unknown as IRes<any>
}

export const deleteManageRoleUser = async (params: any) => {
  const res = await request.get<IRes<any>>('/api/roleConcern/deleteaRoleStuff', { params })
  return res as unknown as IRes<any>
}

export const copyManageRole = async (data: any) => {
  const res = await request.post<IRes<any>>('/api/roleConcern/copyRole', data)
  return res as unknown as IRes<any>
}

export const rollbackRole = async (data: any) => {
  const res = await request.post(`/api/processSyn/rollbackRole`, data)
  return res as unknown as IRes<any>
}

export const synRole = async (data: any) => {
  const res = await request.post(`/api/processSyn/synRole`, data)
  return res as unknown as IRes<any>
}

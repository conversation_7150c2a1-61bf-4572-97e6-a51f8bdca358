<template>
  <div ref="chartRef" :style="{ width: props.width, height: props.height }"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, shallowRef, markRaw, watch } from 'vue'
import Echarts, { type EChartsOption } from './config'

interface Props {
  option: EChartsOption
  width?: string
  height: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
})

const chartRef = shallowRef()
const chartInstance = shallowRef<Echarts.ECharts>()

const init = () => {
  if (!chartRef.value) return
  chartInstance.value = markRaw(Echarts.init(chartRef.value))
  setOptions()
}

const handleResize = () => {
  chartInstance.value?.resize({ animation: { duration: 300 } })
}

const setOptions = () => {
  if (chartInstance.value) {
    chartInstance.value?.setOption(props.option)
    handleResize()
  }
}

defineExpose({
  getInstance: () => chartInstance.value,
  handleResize,
})

onMounted(() => {
  init()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  chartInstance.value?.dispose()
  window.removeEventListener('resize', handleResize)
})

watch(
  props,
  () => {
    setOptions()
  },
  {
    deep: true,
  }
)
</script>

<template>
  <div class="process" :data-id="(process.prefix as string)">
    <div
      class="process-icon"
      :style="{ backgroundColor: BG_COLOR[process.prefix as keyof typeof ProcessType] || '#F4FAFE' }"
    >
      <img :src="PROCESS_ICON[process.prefix as keyof typeof ProcessType] || DEFAULT" alt="" />
    </div>
    <div class="process-content">
      <p class="process-title">
        {{ process.processName }}
        <FTooltip class="__collect" :title="tooltip">
          <Icon :icon="icon" @click.stop="emits('collect', process)" />
        </FTooltip>
      </p>
      <p v-if="!process.remark || process.remark.length <= 30" class="process-desc">{{ process.remark }}</p>
      <FTooltip
        v-else
        :title="h('p', { style: { margin: 0, padding: '6px 8px', color: '#333' } }, process.remark)"
        color="white"
      >
        <p class="process-desc">{{ process.remark }}</p>
      </FTooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { IClassData } from '@/types/class'

import Icon from '@/components/Icon/index.vue'

import DSTE from '../../assets/images/process-icon/DSTE.png'
import IPD from '../../assets/images/process-icon/IPD.png'
import MTL from '../../assets/images/process-icon/MTL.png'
import SC from '../../assets/images/process-icon/SC.png'
import OTD from '../../assets/images/process-icon/OTD.png'
import LTC from '../../assets/images/process-icon/LTC.png'
import ONEN from '../../assets/images/process-icon/ONEN.png'
import ITR from '../../assets/images/process-icon/ITR.png'
import YJ from '../../assets/images/process-icon/YJ.png'
import TAX from '../../assets/images/process-icon/TAX.png'
import DD from '../../assets/images/process-icon/DD.png'
import LP from '../../assets/images/process-icon/LP.png'
import CS from '../../assets/images/process-icon/CS.png'
import STHR from '../../assets/images/process-icon/STHR.png'
import BUSHR from '../../assets/images/process-icon/BUSHR.png'
import OVEHR from '../../assets/images/process-icon/OVEHR.png'
import SVSHR from '../../assets/images/process-icon/SVSHR.png'
import EXP from '../../assets/images/process-icon/EXP.png'
import MA from '../../assets/images/process-icon/MA.png'
import KP from '../../assets/images/process-icon/KP.png'
import DEFAULT from '../../assets/images/process-icon/DEFAULT.png'

enum ProcessType {
  DSTE = 'DSTE',
  IPD = 'IPD',
  MTL = 'MTL',
  SC = 'SC',
  OTD = 'OTD',
  LTC = 'LTC',
  ONEN = 'ONEN',
  ITR = 'ITR',
  YJ = 'YJ',
  TAX = 'TAX',
  DD = 'DD',
  LP = 'LP',
  CS = 'CS',
  STHR = 'STHR',
  BUSHR = 'BUSHR',
  OVEHR = 'OVEHR',
  SVSHR = 'SVSHR',
  EXP = 'EXP',
  MA = 'MA',
  KP = 'KP',
}

const BG_COLOR = {
  [ProcessType.DSTE]: '#F4FAFE',
  [ProcessType.IPD]: '#FEF6EF',
  [ProcessType.MTL]: '#F4FAFE',
  [ProcessType.SC]: '#F7F8FF',
  [ProcessType.OTD]: '#FCF4F4',
  [ProcessType.LTC]: '#FEF6EF',
  [ProcessType.ONEN]: '#F4FAFE',
  [ProcessType.ITR]: '#F7F8FF',
  [ProcessType.YJ]: '#FEF6EF',
  [ProcessType.TAX]: '#FEF6EF',
  [ProcessType.DD]: '#F7F8FF',
  [ProcessType.LP]: '#F7F8FF',
  [ProcessType.CS]: '#F3FDF9',
  [ProcessType.STHR]: '#FCF4F4',
  [ProcessType.BUSHR]: '#FEF6EF',
  [ProcessType.OVEHR]: '#F4FAFE',
  [ProcessType.SVSHR]: '#F4FAFE',
  [ProcessType.EXP]: '#FCF4F4',
  [ProcessType.MA]: '#F3FDF9',
  [ProcessType.KP]: '#F3FDF9',
}

const PROCESS_ICON: Record<ProcessType, string> = {
  DSTE,
  IPD,
  MTL,
  SC,
  OTD,
  LTC,
  ONEN,
  ITR,
  YJ,
  TAX,
  DD,
  LP,
  CS,
  STHR,
  BUSHR,
  OVEHR,
  SVSHR,
  EXP,
  MA,
  KP,
}

interface IProps {
  data: IClassData
}
const props = defineProps<IProps>()
const emits = defineEmits(['collect'])
const process = computed(() => props.data)
const icon = computed(() => (process.value.collect ? 'icontubiao_pingfen' : 'icontubiao_shoucang'))
const tooltip = computed(() => (process.value.collect ? '移除常用流程' : '设为常用流程'))
</script>

<style lang="scss" scoped>
.process {
  float: left;
  display: flex;
  width: 308px;
  height: 86px;
  padding: 12px 16px;
  margin-right: 24px;
  margin-bottom: 12px;
  align-items: center;
  border-radius: 12px;
  box-sizing: border-box;
  cursor: pointer;

  &:hover {
    background-color: #f4fafe;
    .__collect {
      display: block !important;
    }

    &[data-id='DSTE'] {
      background-color: #f4fafe;
    }
    &[data-id='IPD'] {
      background-color: #fef6ef;
    }
    &[data-id='MTL'] {
      background-color: #f4fafe;
    }
    &[data-id='SC'] {
      background-color: #f7f8ff;
    }
    &[data-id='OTD'] {
      background-color: #fcf4f4;
    }
    &[data-id='LTC'] {
      background-color: #fef6ef;
    }
    &[data-id='ONEN'] {
      background-color: #f4fafe;
    }
    &[data-id='ITR'] {
      background-color: #f7f8ff;
    }
    &[data-id='YJ'] {
      background-color: #fef6ef;
    }
    &[data-id='TAX'] {
      background-color: #fef6ef;
    }
    &[data-id='DD'] {
      background-color: #f7f8ff;
    }
    &[data-id='LP'] {
      background-color: #f7f8ff;
    }
    &[data-id='CS'] {
      background-color: #f3fdf9;
    }
    &[data-id='STHR'] {
      background-color: #fcf4f4;
    }
    &[data-id='BUSHR'] {
      background-color: #fef6ef;
    }
    &[data-id='OVEHR'] {
      background-color: #f4fafe;
    }
    &[data-id='SVSHR'] {
      background-color: #f4fafe;
    }
    &[data-id='EXP'] {
      background-color: #fcf4f4;
    }
    &[data-id='MA'] {
      background-color: #f3fdf9;
    }
    &[data-id='KP'] {
      background-color: #f3fdf9;
    }
  }
  .process-icon {
    display: flex;
    flex: 0 0 48px;
    width: 48px;
    height: 48px;
    margin-right: 16px;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background-color: #f4fafe;

    > img {
      width: 24px;
      height: 24px;
    }
  }
  .process-content {
    flex: 1;
    .process-title,
    .process-desc {
      margin: 0;
    }
    .process-title {
      height: 22px;
      line-height: 22px;
      margin-bottom: 4px;
      color: #333333;
      font-size: 14px;
      font-weight: 500;

      .__collect {
        float: right;
        display: none;
        color: #999;

        &:hover {
          color: #333;
        }
      }
    }
    .process-desc {
      height: 36px;
      line-height: 18px;
      color: #999999;
      font-size: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
      word-wrap: break-word;
    }
  }
}
</style>

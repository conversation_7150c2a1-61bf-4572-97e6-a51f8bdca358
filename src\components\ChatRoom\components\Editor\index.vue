<template>
  <div class="editor-container" @click.stop>
    <!-- <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    /> -->
    <Editor
      :style="{ height: props.height + 'px', 'overflow-y': 'hidden' }"
      v-model="valueHtml"
      :default-config="editorConfig"
      :mode="mode"
      @on-created="handleCreated"
      @on-change="handleChange"
    />
    <div class="mention" v-show="isShowMention" :style="{ bottom: `${height}px` }">
      <div class="mention-list">
        <div class="mention-item" v-for="item in mentionList" :key="item.value" @click="handleMentionClick(item)">
          <FAvatar :src="defaultfImg" size="mini" /> {{ item.value }}
        </div>
      </div>
      <div class="mention-not-found" v-show="!mentionList.length">无匹配数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'

import { onBeforeUnmount, ref, Ref, shallowRef, onMounted, computed, inject, reactive } from 'vue'
import { useStore } from 'vuex'
import { Boot } from '@wangeditor/editor'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import mentionModule from '@wangeditor/plugin-mention'
import { StoreUserType } from '@/store/modules/user'
import { IProcessRoleAndUser } from '@/types/request'
import { upload } from '@/api'

import defaultfImg from '@/assets/images/head.png'
import { BPM_MESSAGE_EDITOR, debounce } from '@/utils'

interface IProps {
  height: number
}

// 注册。要在创建编辑器之前注册，且只能注册一次，不可重复注册。
Boot.registerModule(mentionModule)

const store = useStore()
const props = defineProps<IProps>()
const mode = ref('default')
const editorRef = shallowRef()
const isShowMention = ref(false)
const valueHtml = ref('')
const roleList = inject<Ref<IProcessRoleAndUser[]>>('roleList')
const userList = computed<StoreUserType[]>(() => store.state.user.allUser || [])
const mentionInput = ref('')
const mentionStartRangeOffset = ref<number>()
const mentionRange = ref<Range | null>(null)

// mention 列表
const mentionList = computed(() => {
  const _roleList = (roleList?.value || [])
    .filter(data => data.roleName.toUpperCase().includes(mentionInput.value.toUpperCase()) && data.name && data.uuid)
    .slice(0, 30)
    .map(data => ({
      id: data.users.map(user => `${user.name}|${user.uuid}`).join(','),
      value: `角色：${data.roleName}`,
    }))
  const _userList = userList.value
    .filter(user => user.name.toUpperCase().includes(mentionInput.value.toUpperCase()))
    .slice(0, 30)
    .map(user => ({ ...user, value: user.name, id: user.uuid }))

  return [..._roleList, ..._userList]
})

// mention 隐藏
const mentionHideModal = () => {
  mentionInput.value = ''
  isShowMention.value = false
  mentionRange.value = null
  window.removeEventListener('click', mentionHideModal)
  window.removeEventListener('keydown', mentionHandleKeydown)
}

//mention 状态下监听键盘事件
const mentionHandleKeydown = debounce(() => {
  const domSelection = document.getSelection() as Selection
  const domRange = domSelection.getRangeAt(0).cloneRange()

  mentionRange.value?.setStart(domRange.startContainer, mentionStartRangeOffset.value || 0)
  mentionRange.value?.setEnd(domRange.endContainer, domRange.endOffset)

  const str = mentionRange.value?.toString()
  if (!str?.includes('@')) return mentionHideModal()

  mentionInput.value = mentionRange.value?.toString().substring(1) || ''
}, 100)

// 工具栏配置
const toolbarConfig = reactive({
  toolbarKeys: [],
})
// 编辑器配置 - mention 配置
const editorConfig = reactive({
  placeholder: '请输入内容...',
  MENU_CONF: {
    uploadImage: {
      // base64LimitSize: 5 * 1024,
      customUpload: async (file: File, insertFn: any) => {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('isOpen', 'false')
        formData.append('expire', '0')
        const res = await upload(formData)
        insertFn(res)
      },
    },
  },
  EXTEND_CONF: {
    mentionConfig: {
      showModal: () => {
        isShowMention.value = true

        const domSelection = document.getSelection() as Selection
        const domRange = domSelection.getRangeAt(0)
        mentionStartRangeOffset.value = domRange.startOffset - 1

        mentionRange.value = document.createRange()
        mentionRange.value.setStart(domRange.startContainer, mentionStartRangeOffset.value)
        mentionRange.value.setEnd(domRange.endContainer, domRange.endOffset)

        window.addEventListener('click', mentionHideModal)
        window.addEventListener('keydown', mentionHandleKeydown)
      },
    },
  },
})

// 创建编辑器
const handleCreated = (editor: any) => {
  editorRef.value = editor
}

// 编辑器内容变化
const handleChange = (editor: any) => {
  localStorage.setItem(BPM_MESSAGE_EDITOR, editor.getHtml())
}

// 选择 @ 的人
const handleMentionClick = (data: any) => {
  const editor = editorRef.value
  if (editor == null) return

  const str = mentionRange.value?.toString() || ''
  const mentionNode = { type: 'mention', value: data.value, info: { uuid: data.id }, children: [{ text: '' }] }
  editor.restoreSelection()

  for (let i = 0; i < str.length; i++) editor.deleteBackward('character')

  editor.insertNode(mentionNode)
  editor.move(1)

  mentionHideModal()
}

// 销毁
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editorRef.value == null) return
  editor.destroy()
  editorRef.value = null
})

// 暴露给父组件的属性和方法
defineExpose({
  getEditor: () => editorRef.value,
})
</script>

<style scoped lang="scss">
.editor-container {
  position: relative;

  > .mention {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    z-index: 100;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    background-color: white;

    > .mention-list {
      max-height: 140px;
      overflow-y: auto;

      > .mention-item {
        padding: 4px 8px;
        line-height: 24px;

        &:hover {
          background-color: #f0f0f0;
        }
      }
    }

    > .mention-not-found {
      height: 32px;
      line-height: 32px;
      color: #666;
      text-align: center;
    }
  }
}
</style>

<template>
  <div class="milepost-header">
    <div class="milepost-header-content">
      <!-- 标题 -->
      <Icon icon="icontubiao_bushuzhong_mian" :color="milepostStatusColor" :size="22" />
      <span class="__title">{{ milepostName }}</span>

      <!-- 状态 -->
      <Tag class="ml4" :type="(milepostStatus[0] as any)" :text="milepostStatus[1]" />

      <!-- 负责人 -->
      <Member
        class="ml32 m8"
        :id="currtMilepost!.superviserUuid"
        :name="currtMilepost!.superviser"
        :src="ImgUser"
        :flag="[0, 1, 2].includes(currtMilepost!.status) && currtMilepost?.milepostRole == 1"
      />

      <!-- 任务数量 -->
      <Tag class="m8" type="default" v-if="currtMilepost?.taskNum">
        <Icon class="mr2" icon="icontubiao_zhanbitu" :color="milepostTaskColor" />{{
          `${currtMilepost?.taskCompleteNum}/${currtMilepost?.taskNum}`
        }}
      </Tag>

      <!-- 时间 -->
      <ForcastTime v-if="currtMilepost?.invalid" />

      <!-- 催一催 -->
      <NoticeInfo class="m8" v-if="[0,1,2].includes(currtMilepost?.status as number)" :value-id="currtMilepost?.id" />
    </div>
    <div class="milepost-header-extend">
      <!-- <FDropdown
        v-if="currtMilepost && currtMilepost.milepostRole == 1"
        :get-popup-container="(triggerNode: any) => triggerNode.parentNode"
      >
        <FButton class="more-btn w80 mr12">更多<Icon class="ml4" icon="iconjiantouxia" :size="16" /></FButton>
        <template #overlay>
          <FMenu>
            <FMenuItem @click="operate(EmitType.reject, currtMilepost as IProcess)">
              <Icon icon="icontubiao_chexiao" />驳回
            </FMenuItem>
          </FMenu>
        </template>
      </FDropdown> -->
      <FButton
        v-if="currtMilepost?.milepostRole == 1 && [2].includes(currtMilepost?.status as number)"
        class="w80 mr12"
        @click="operate(EmitType.reject, currtMilepost as IProcess)"
        >{{ i18n.t('驳回') }}</FButton
      >
      <FConfigProvider :auto-insert-space-in-button="false">
        <FButton
          v-if="currtMilepost?.milepostRole == 1 && [2].includes(currtMilepost?.status as number)"
          class="w80"
          type="primary"
          @click="handleSubmit"
          >{{ i18n.t('提交') }}</FButton
        >
      </FConfigProvider>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Ref, computed, inject } from 'vue'
import { IProcess } from '@/types/handle'
import { message } from '@fs/smart-design'

import Icon from '@/components/Icon/index.vue'
import NoticeInfo from '@/components/NoticeInfo/index.vue'
import Tag from '../Tag/index.vue'
import Member from '../Member/index.vue'
import ForcastTime from '../ForcastTime/index.vue'

import ImgUser from '@/assets/images/process-detail/user.png'
import { EmitType } from '@/views/process-detail/config'
import { useI18n } from '@/utils'

const i18n = useI18n()
const formRenderRef = inject('formRenderRef') as Ref<any>
const currtMilepost = inject<Ref<IProcess>>('currtMilepost') // 当前里程碑信息
const operate = inject('operate') as (key: EmitType, data: IProcess) => void
const milepostName = computed(() => currtMilepost?.value?.topicName) // 里程碑名称
const milepostStatus = computed(() => {
  if (!currtMilepost?.value) return ['default', i18n.t('未开始')]
  const milepost = currtMilepost.value
  const status = milepost.status
  if ([2].includes(status) && milepost.overdueDuration) return ['danger', i18n.t('已延期')]
  if ([2].includes(status)) return ['warning', i18n.t('进行中')]
  if ([3, 4, 5].includes(status)) return ['success', i18n.t('已完成')]
  // if ([5].includes(status)) return ['danger', '']
  return ['default', i18n.t('未开始')]
})

// 里程碑图标颜色
const milepostStatusColor = computed(() => {
  if (!currtMilepost?.value) return '#bbb'
  const milepost = currtMilepost.value
  const status = milepost.status
  if ([2].includes(status) && milepost.overdueDuration) return '#F04141'
  if ([2].includes(status)) return '#FA8F23'
  if ([3, 4, 5].includes(status)) return '#2FCC83'
  // if ([5].includes(status)) return '#F04141'
  return '#bbb'
})

// 里程碑任务颜色
const milepostTaskColor = computed(() => {
  if (!currtMilepost?.value) return '#bbb'
  const milepost = currtMilepost.value
  if (milepost.taskNum === milepost.taskCompleteNum) return '#2FCC83'
  if (milepost.taskCompleteNum) return '#F04141'
  return '#bbb'
})

// 节点提交
const handleSubmit = async () => {
  const formKey = currtMilepost?.value?.formKey

  if (!formKey) return operate(EmitType.submit, currtMilepost?.value as IProcess)

  try {
    const scoped = formRenderRef.value?.getAmisScoped()
    const $form = scoped?.getComponentByName('page.nodeForm')
    if (!$form) throw new Error(i18n.t(`表单实例获取失败，请联系管理员！`))

    await $form?.submit()
    const res = await $form?.doAction({ actionType: 'submit' })

    if (!res || !res.code) throw new Error(i18n.t('表单提交接口配置异常，请联系管理员！'))
    if (res.code !== 200) throw new Error(i18n.t('表单信息提交失败'))

    message.success(i18n.t('表单信息提交成功'))
    operate(EmitType.submit, currtMilepost?.value as IProcess)
  } catch (error: any) {
    console.log('[milepost header]:', error.message)
    message.error(error.message)
  }
}
</script>

<style scoped lang="scss">
.w80 {
  width: 80px;
}
.m8 {
  margin: 0 8px;
}

.mr2 {
  margin-right: 2px;
}

.mr12 {
  margin-right: 12px;
}

.ml4 {
  margin-left: 4px;
}

.ml32 {
  margin-left: 32px;
}

.milepost-header {
  display: flex;
  height: 56px;
  padding: 0 24px;
  align-items: center;
  border-bottom: 1px solid #eee;
  box-sizing: border-box;

  > .milepost-header-content {
    display: flex;
    align-items: center;

    > .__title {
      margin-left: 8px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  > .milepost-header-extend {
    display: flex;
    align-items: center;
    margin-left: auto;
    .more-btn {
      color: #333;
    }

    :deep(.fs-dropdown) {
      .fs-dropdown-menu {
        padding: 4px;

        .fs-dropdown-menu-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          color: #333;
          font-size: 12px;
          height: 32px;
          &:hover {
            background-color: #f1f4f8;
          }

          .fs-dropdown-menu-title-content {
            line-height: 16px;
          }
        }
      }

      .fs-dropdown-menu-item-selected {
        background-color: white;
      }

      .iconfont {
        margin-right: 8px;
        width: 16px !important;
        height: 16px !important;
        line-height: 16px !important;
        vertical-align: bottom;
      }
    }
  }
}
</style>

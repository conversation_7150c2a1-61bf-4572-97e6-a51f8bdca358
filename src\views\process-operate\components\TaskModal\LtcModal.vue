<template>
  <FModal
    class="f-modal-wrapper"
    width="780px"
    v-model:visible="visible"
    :title="props.title"
    :footer="showFooter"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <FormRender ref="formRef" v-if="props.formKey" :id="props.formKey" type="edit" :data="TFFormData()" />
  </FModal>
</template>

<script lang="ts" setup>
import { IProcess } from '@/types/handle'
import { useI18n } from '@/utils'

const i18n = useI18n()

interface IProps {
  modelValue: boolean
  title: string
  formKey: number
}
import { messageInstance as message } from '@fs/smart-design'
import FormRender from '@/components/FormRender/index.vue'
import { computed, ref, inject, markRaw, Ref } from 'vue'
const emits = defineEmits(['update:modelValue', 'submit'])
const formData = ref<Record<string, unknown>>({})
const confirmLoading = ref(false)
const showFooter = null
const props = defineProps<IProps>()
const processId = inject<number>('processId') // 流程 id
const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emits('update:modelValue', val),
})
const currtMilepost = inject<Ref<IProcess>>('currtMilepost')
const handleCancel = () => {
  visible.value = false
  confirmLoading.value = false
}
const handleOk = async () => {
  visible.value = false
  confirmLoading.value = false
  const amisTask = getAmisTaskFormData()
  if (amisTask.length > 0) {
    const poolForm = amisTask[amisTask.length - 1]
    emits('submit', poolForm)
  } else {
    return message.error(i18n.t('任务表单异常，请联系管理员！'))
  }
}
const getAmisTaskFormData = () => {
  return Object.values(window.amisStore.stores as { storeType: string; data: Record<string, unknown> }[])
    .filter(item => item.storeType === 'FormStore')
    .filter(item => Object.keys(item.data).includes('isPoolForm'))
    .map(item => item.data)
}
const TFFormData = () => {
  return markRaw({
    envData: { instanceId: processId, milepostId: currtMilepost!.value.id },
    envDefaultFormData: formData,
  })
}
</script>

<style lang="scss">
.f-modal-wrapper {
  color: blue;
  .ant-modal-body {
    padding: 24px 0 !important;
  }
  .fs-modal-body {
    padding: 24px 0 !important;
  }
}
</style>

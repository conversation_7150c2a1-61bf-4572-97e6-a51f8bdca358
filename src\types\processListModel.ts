import type { BasicFetchResponse, BasicPageParams } from './common'

export interface ProcessItem {
  // [x: string]: any
  attachmentData?: string
  businessFormKey?: string
  contentData?: string
  createdTime?: string
  creator?: string
  formData?: string
  id?: number
  instanceProcessKey?: string
  processConfigId?: number
  processInstanceCode?: string
  processType?: string
  status: number
  tag?: string
  tags?: string[]
  topicName?: string
  updatedTime?: string
  updatedUser?: string
  mileposts: ProcessChildItem[]
}
export interface ProcessChildItem {
  attachmentData?: string
  completeTime?: string
  contentData?: string
  createdTime?: string
  creator?: string
  duration?: number
  formData?: string
  id?: number
  instanceId?: number
  instanceMilepostId?: number
  instanceProcessKey?: string
  isOverdue?: number
  milepostProcessKey?: number
  nodeInstanceProcessKey?: number
  nodeStartTime?: string
  status?: number
  superviser?: string
  updatedTime?: string
  updatedUser?: string
}
export interface ProcessListResponse extends BasicFetchResponse {
  data: {
    list: ProcessItem[]
    total: number
    [key: string]: any
  }
}
export interface ProcessListParams {
  createUser?: string
  endTime?: string
  isUrgent?: number
  milepostNodeId?: number
  processConfigId?: number
  queryInput?: string
  startTime?: string
  tag?: string
  type?: number
}

interface ISearchObj {
  [key: string]: any
}
export interface ProcessListSearch {
  projectUuidList?: string | string[]
  endTime?: string
  isUrgent?: number
  nodeId?: number
  processConfigId?: number
  queryInput?: string
  sapCode?: string
  startTime?: string
  tags?: string
  type?: number
  searchObj?: ISearchObj
}

export interface ProcessExportParams {
  createUser?: string
  departmentID?: string
  endTime?: string
  isUrgent?: number
  milepostNodeId?: number
  nodeId?: string
  pageNum?: number
  pageSize?: number
  processConfigId?: number
  projectUuidList?: any[]
  queryInput?: string
  startTime?: string
  tag?: string
  type?: number
}

export interface ILocalProjectUuidData {
  selectLists: string[]
  projectUuidList: any[]
}

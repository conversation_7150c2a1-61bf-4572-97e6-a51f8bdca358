import { onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import { IMicroMenu } from '@fs/hooks'
import { getPower, isMicroApp } from '@/utils'
import { IMicroState } from '@/store/modules/micro'

function useMenu() {
  const store = useStore()
  const power = getPower()

  const menus = ref<IMicroMenu[]>([])
  const menusPathMap = new Map<string, IMicroMenu>()

  const currMenu = ref<IMicroMenu[]>([])

  onMounted(() => {
    initMenus()
  })

  const initMenus = async () => {
    const config: IMicroState = store.getters.microConfig
    if (power.power) menus.value = transformToMenuItems(config.menus ?? [])
    else if (isMicroApp) console.warn('微前端环境，不用处理菜单数据！')
    else throw new Error('获取权限异常')
  }

  // 转换菜单数据
  const transformToMenuItems = (menus: IMicroMenu[]): IMicroMenu[] => {
    const menuMap = new Map<number, IMicroMenu>()

    menus.forEach(menu => {
      const currMenu = { ...menu }
      menuMap.set(menu.id, currMenu)
      menu.path && menusPathMap.set(menu.path, currMenu)
    })

    const res: IMicroMenu[] = []
    menus.forEach(menu => {
      const data = menuMap.get(menu.id) as IMicroMenu
      if (menu.parentId && menuMap.has(menu.parentId)) {
        const parent = menuMap.get(menu.parentId) as IMicroMenu
        if (!Array.isArray(parent.children)) parent.children = []
        parent.children.push(data)
      } else res.push(data)
    })

    return res.filter(item => item.name !== '隐藏菜单')
  }

  const setCurrMenu = (menu: IMicroMenu[]) => {
    currMenu.value = menu
  }

  return {
    menus,
    menusPathMap,

    currMenu,
    setCurrMenu,
  }
}

export default useMenu

import { request } from '@/utils'
import {
  RoleListResult,
  ConfigListParams,
  ConfigListParamsResult,
  IAddRole,
  AddRoleResult,
  userAllResult,
  AddUser,
} from '@/types/role'
// IResource, IResourceLabel, IResourceType
// 获取全部角色list
export const getAllRoleList = async (params: string) => {
  const res = await request.get<RoleListResult>(`/api/roleConcern/getRoleById?search=${params}`)
  return res as unknown as RoleListResult
}
// 根据角色查详情

export const getRoleDetails = async (params: ConfigListParams) => {
  const res = await request.post<ConfigListParamsResult>('/api/roleConcern/getRoleStuffByConfigId', params)
  return res as unknown as ConfigListParamsResult
}

//新增角色
export const addRloe = async (params: IAddRole) => {
  const res = await request.post<IAddRole>('/api/roleConcern/addRole', params)
  return res as unknown as AddRoleResult
}
// 编辑角色
export const editRloe = async (params: IAddRole) => {
  const res = await request.post<IAddRole>('/api/roleConcern/updateRole', params)
  return res as unknown as AddRoleResult
}
// 删除角色
export const deleteRloe = async (id: number) => {
  const res = await request.get('/api/roleConcern/deleteaRole', { params: { id: id } })
  return res as unknown as AddRoleResult
}

// 删除角色对应人员
export const deleteRloeUser = async (id: number) => {
  const res = await request.get('/api/roleConcern/deleteaRoleStuff', { params: { id: id } })
  return res as unknown as AddRoleResult
}

//批量新增角色对应人员
export const addUserRloe = async (params: AddUser) => {
  const res = await request.post<IAddRole>('/api/roleConcern/addBatchRoleStuff', params)
  return res as unknown as AddRoleResult
}

//导入人员表格
export const addUserXlse = async (data: any, params: number) => {
  const res = await request.post(`/api/roleConcern/importRoleStuff?roleConfigId=${params}`, data)
  return res as unknown as AddRoleResult
}

//新增角色
export const updateRoleStuff = async (params: { id: number; conditionExpression?: string }) => {
  const res = await request.post<IAddRole>('/api/roleConcern/updateRoleStuff', params)
  return res as unknown as AddRoleResult
}

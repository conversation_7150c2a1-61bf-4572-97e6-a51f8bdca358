<template>
  <div class="process-class-info card-content-shadow">
    <div
      v-sticky="{
        container: '#container',
        offset: 0,
        type: 'sticky',
        top: 0,
        zIndex: 5,
      }"
      class="header flex space-between align-items card-content-header"
    >
      <span class="title color333 fontSize16">流程信息</span>
      <div class="extra-box">
        <FSpace :size="[8]">
          <FButton @click="back" size="small">
            <template #icon>
              <i class="iconfont icontubiao_chehui marginR4 fontSize14"></i>
            </template>
            返回
          </FButton>
          <FButton v-if="isDetail" type="primary" size="small" @click="isDetail = !isDetail">编辑</FButton>
          <FButton v-if="!isDetail" size="small" @click="onCloseFn">取消</FButton>
          <FButton v-if="!isDetail" type="primary" size="small" :loading="processConfigLoading" @click="onSubmitFn"
            >提交</FButton
          >
        </FSpace>
      </div>
    </div>
    <div class="content-box padding24">
      <FForm ref="formRef" :model="fromData" :layout="isDetail ? 'horizontal' : 'vertical'" validateTrigger="none">
        <BaseForm
          v-model:form-data="fromData"
          :components="components"
          :is-detail="isDetail"
          :validatorKeys="validatorKeys"
        />
      </FForm>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted, reactive, ref, toRefs, watch, nextTick, computed, Ref } from 'vue'
import { useStore } from 'vuex'
import { StoreUserType } from '@/store/modules/user'
import { getRootDictionary, getProcess, updateClass } from '@/api'
import BaseForm from '@/views/process-class-detail/components/BaseForm/index.vue'
import CustomInput from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInput/index.vue'
import CustomInputNumber from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInputNumber/index.vue'
import { createTipConfig, tipPresets } from '@/views/process-class-detail/components/BaseForm/tipTypes'
import ProcessTip from '@/views/process-class-detail/components/CustomTipComponents/ProcessTip.vue'
import DynamicTip from '@/views/process-class-detail/components/CustomTipComponents/DynamicTip.vue'
import CustomSelect from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomSelect/index.vue'
import CustomRadioGroup from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomRadioGroup/index.vue'
import CustomInputTextarea from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInputTextarea/index.vue'
import CustomJsonData from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomJsonData/index.vue'
import { deepClone } from '@/utils'
import { isNotEmpty, getValueFn } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'

const props = defineProps<{
  processConfig: any
  classId: string
  processConfigLoading?: boolean
}>()

const { classId, processConfigLoading } = toRefs(props)
const fromData = ref<any>({})
const formRef = ref()
const onGetByIdProcessConfig = inject<() => Promise<void>>('onGetByIdProcessConfig')
const setProcessConfigLoading = inject<(loading: boolean) => void>('setProcessConfigLoading')
const back = inject<() => void>('back')
const processTypeOptions = inject<Ref<any[]>>('processTypeOptions')
const isDetail = ref<boolean>(true)
const dictionary = ref<any[]>([])
const processList = ref<any[]>([])
const isNoticeLists = ref([
  { label: '关闭', value: 0 },
  { label: '延期提醒', value: 1 },
  { label: '即将延期提醒', value: 2 },
  { label: '全部提醒', value: 3 },
])
const sourceLists = ref([
  { label: 'BPM', value: 1 },
  { label: '合同', value: 2 },
  { label: '权限', value: 3 },
  { label: '审批流', value: 4 },
  { label: '外部审批流', value: 5 },
])
const plainOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]
const store = useStore()
const userList = computed<StoreUserType[]>(() => store.state.user.allUser || [])

const validatorKeys = reactive<Record<string, any>>({
  tag: async (rule, value, item) => {
    await nextTick()
    const data = fromData.value?.[rule?.field]
    if (data && Object.keys(JSON.parse(data)).some(key => !key.trim())) {
      return Promise.reject('key不能为空格')
    }
    return Promise.resolve()
  },
  outline: async (rule, value, item) => {
    await nextTick()
    const data = fromData.value?.[rule?.field]
    if (data && Object.keys(JSON.parse(data)).some(key => !key.trim())) {
      return Promise.reject('key不能为空格')
    }
    return Promise.resolve()
  },
})

const components = reactive<Record<string, any>>({
  baseParams: {
    fieldKey: 'baseParams',
    label: '此处为基础参数，设置isShowComponent为返回false，将不在页面显示',
    isShowComponent: () => false,
    onSubmitValueFormatFn: (config, fromData) => {
      return {
        id: classId.value,
      }
    },
  },
  processName: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'processName',
    label: '流程名称',
    required: true,
    // 使用预设配置 - 更优雅简洁
    tipText: tipPresets.withValue(
      '请输入流程的完整名称，建议使用中文描述',
      '当前长度：',
      '字符'
    ),
    componentAttrs: {
      placeholder: '请输入流程名称',
    },
  },
  dictionaryId: {
    component: CustomSelect,
    dataType: 'select',
    fieldKey: 'dictionaryId',
    label: '字典标签',
    componentAttrs: {
      placeholder: '请选择字典标签',
      options: dictionary,
      fieldNames: { label: 'name', value: 'id' },
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'name',
    },
  },
  processDefineKey: {
    component: CustomSelect,
    dataType: 'select',
    fieldKey: 'processDefineKey',
    label: '关联流程图',
    required: true,
    // 使用函数方式 - 动态生成提示文本
    tipText: (fieldValue: any, formData: any, fieldConfig: any) => {
      if (!fieldValue) {
        return '请选择关联的流程图，这将决定流程的执行路径和审批节点'
      }
      const selectedProcess = processList.value.find((p: any) => p.id === fieldValue)
      if (selectedProcess) {
        return `已选择流程图：${selectedProcess.processName}，流程将按此图执行`
      }
      return `已选择流程图ID: ${fieldValue}，当前流程将按此图执行`
    },
    componentAttrs: {
      placeholder: '请选择关联流程图',
      options: processList,
      fieldNames: { label: 'processName', value: 'id' },
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'processName',
    },
    initComponentValueFormatFn: (config: any, value: any) => {
      return (value && Number(value)) || undefined
    },
  },
  readMark: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'readMark',
    label: '普通权限标识',
    componentAttrs: {
      placeholder: '请输入普通权限标识',
    },
  },
  writeMark: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'writeMark',
    label: '高级权限标识',
    componentAttrs: {
      placeholder: '请输入高级权限标识',
    },
  },
  prefix: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'prefix',
    label: '流程编号前缀',
    required: true,
    // 使用工厂函数 - 更优雅简洁
    tipText: createTipConfig.component(DynamicTip, {
      title: '流程编号前缀说明',
      text: '前缀规则',
      type: 'info',
      showValidation: true,
      customSuggestions: [
        '前缀应为2-4个字符的英文或数字',
        '建议使用业务相关的缩写',
        '前缀将用于生成唯一的流程编号'
      ]
    }),
    componentAttrs: {
      placeholder: '请输入流程编号前缀',
    },
  },
  sort: {
    component: CustomInputNumber,
    dataType: 'inputNumber',
    fieldKey: 'sort',
    label: '排序',
    required: true,
    componentAttrs: {
      placeholder: '请输入排序',
    },
  },
  groupCode: {
    component: CustomSelect,
    dataType: 'select',
    fieldKey: 'groupCode',
    label: '流程类型',
    componentAttrs: {
      placeholder: '请选择流程类型',
      options: processTypeOptions,
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'processName',
    },
    onSubmitValueFormatFn: (config, fromData) => {
      return {
        groupCode: fromData?.groupCode,
        businessType: getValueFn(processTypeOptions.value, fromData?.groupCode)?.label || undefined,
      }
    },
  },
  isNotice: {
    component: CustomSelect,
    dataType: 'select',
    fieldKey: 'isNotice',
    label: '延期通知',
    componentAttrs: {
      placeholder: '请选择延期通知',
      options: isNoticeLists,
      fieldNames: { label: 'label', value: 'value' },
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  isDeleteMake: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'isDeleteMake',
    label: '删除权限标识',
    componentAttrs: {
      placeholder: '请输入删除权限标识',
    },
  },
  source: {
    component: CustomSelect,
    dataType: 'select',
    fieldKey: 'source',
    label: '流程来源',
    required: true,
    componentAttrs: {
      placeholder: '请选择流程来源',
      options: sourceLists,
      fieldNames: { label: 'label', value: 'value' },
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  processCode: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'processCode',
    label: '流程编码',
    required: true,
    componentAttrs: {
      placeholder: '请输入流程编码',
    },
  },
  detailsUrl: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'detailsUrl',
    label: '流程链接',
    componentAttrs: {
      placeholder: '请输入流程链接',
    },
  },
  invalid: {
    component: CustomRadioGroup,
    dataType: 'radio',
    fieldKey: 'invalid',
    label: '是否启动时效管理',
    componentAttrs: {
      placeholder: '请选择是否启动时效管理',
      options: plainOptions,
    },
  },
  isSendmsg: {
    component: CustomRadioGroup,
    dataType: 'radio',
    fieldKey: 'isSendmsg',
    label: '飞书消息开关',
    required: true,
    componentAttrs: {
      placeholder: '请选择是否启动飞书消息开关',
      options: plainOptions,
    },
  },
  taskEdit: {
    component: CustomRadioGroup,
    dataType: 'radio',
    fieldKey: 'taskEdit',
    label: '开放任务编辑权限',
    required: true,
    componentAttrs: {
      placeholder: '请选择是否开放任务编辑权限',
      options: plainOptions,
    },
  },
  isDelayReason: {
    component: CustomRadioGroup,
    dataType: 'radio',
    fieldKey: 'isDelayReason',
    label: '是否填写延期原因',
    formItemRuleTrigger: 'blur',
    componentAttrs: {
      placeholder: '请选择是否填写延期原因',
      options: plainOptions,
    },
  },
  isUpdateCompleteSend: {
    component: CustomRadioGroup,
    dataType: 'radio',
    fieldKey: 'isUpdateCompleteSend',
    label: '更新预计完成时间是否发送消息',
    componentAttrs: {
      placeholder: '请选择是否更新预计完成时间是否发送消息',
      options: plainOptions,
    },
  },
  outline: {
    component: CustomJsonData,
    dataType: 'customJsonData',
    fieldKey: 'outline',
    label: '概要信息',
    componentAttrs: {},
  },
  tag: {
    component: CustomJsonData,
    dataType: 'customJsonData',
    fieldKey: 'tag',
    label: '标签',
    componentAttrs: {},
  },
  admins: {
    component: CustomSelect,
    dataType: 'selectMultiple',
    fieldKey: 'admins',
    label: '流程管理员',
    initComponentValueFormatFn: (config, value) => {
      return value?.split?.(',') || undefined
    },
    onSubmitValueFormatFn: (config, fromData) => {
      return {
        admins: fromData?.admins?.join?.(',') || undefined,
      }
    },
    componentAttrs: {
      placeholder: '请选择流程管理员',
      options: userList,
      fieldNames: { label: 'name', value: 'uuid' },
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'name',
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
  },
  finishDesc: {
    component: CustomInputTextarea,
    dataType: 'textarea',
    fieldKey: 'finishDesc',
    label: '流程终止标签',
    colSpan: () => (isDetail.value ? 6 : 24),
    componentAttrs: {
      placeholder: '请输入流程终止标签',
    },
  },
  rejectDesc: {
    component: CustomInputTextarea,
    dataType: 'textarea',
    fieldKey: 'rejectDesc',
    label: '流程驳回标签',
    colSpan: () => (isDetail.value ? 6 : 24),
    componentAttrs: {
      placeholder: '请输入流程驳回标签',
    },
  },
  remark: {
    component: CustomInputTextarea,
    dataType: 'textarea',
    fieldKey: 'remark',
    label: '备注',
    colSpan: () => (isDetail.value ? 6 : 24),
    componentAttrs: {
      placeholder: '请输入备注',
    },
  },
  tabConfig: {
    component: CustomInputTextarea,
    dataType: 'textarea',
    fieldKey: 'tabConfig',
    label: 'tab页配置',
    colSpan: () => (isDetail.value ? 6 : 24),
    componentAttrs: {
      placeholder: '请输入tab页配置',
    },
  },
})

const onCloseFn = () => {
  // fromData.value = deepClone(props.processConfig)
  initData()
  formRef.value?.resetFields()
  isDetail.value = true
}

const onSubmitFn = async () => {
  await formRef?.value?.validate()
  setProcessConfigLoading?.(true)
  try {
    const params = Object.entries(components).reduce((acc, [key, config]) => {
      if (config?.onSubmitValueFormatFn) {
        Object.assign(acc, config?.onSubmitValueFormatFn(config, fromData.value))
      } else if (isNotEmpty(fromData.value[key])) {
        acc[key] = fromData.value[key]
      }
      return acc
    }, {})
    await updateClass(params as any)
    await onGetByIdProcessConfig?.()
    isDetail.value = true
  } finally {
    setProcessConfigLoading?.(false)
  }
}

const initProcess = async () => {
  const res = await getProcess({ currPage: 1, pageSize: 9999, processName: '' })
  processList.value = res?.data?.list ?? []
}

const initDictionary = async () => {
  const { data = [] } = await getRootDictionary()
  dictionary.value = data
}

const initData = () => {
  const data = deepClone(props.processConfig)
  Object.entries(components).forEach(([key, config]) => {
    if (config?.initComponentValueFormatFn) {
      data[key] = config?.initComponentValueFormatFn(config, data[key])
    }
  })
  fromData.value = data
}

watch(
  () => props.processConfig,
  newVal => {
    newVal && initData()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  requestIdleCallback(initDictionary)
  requestIdleCallback(initProcess)
})
</script>

<style lang="scss" scoped>
.process-class-info {
}

.card-content-shadow {
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
.card-content-header {
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
  background-color: #ffffff;
}
.flex {
  display: flex;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.align-items {
  align-items: center;
}
.padding24 {
  padding: 24px;
}
</style>

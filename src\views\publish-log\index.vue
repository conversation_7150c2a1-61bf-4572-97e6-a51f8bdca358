<template>
  <div class="modify-board-container">
    <div class="card-content-shadow pt24 pl24 pr24 mb16">
      <SearchContent v-model:query-data="queryData" />
    </div>
    <div class="card-content-shadow pt24 pl24 pr24">
      <div class="card-btn">
        <div class="fei-su-title">日志列表</div>
      </div>
      <FTable
        class="table-warp"
        :columns="columns"
        :loading="loading"
        :data-source="dataList"
        :row-key="(data:any) => data.id"
        :sticky="{ offsetHeader: 0 }"
        :scroll="{ x: 'min-content' }"
        :pagination="{
          total: paging.total,
          current: paging.pageNum,
          pageSize: paging.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
        }"
        @change="onPaginationChangeFn"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'syncType'">
            {{
              record.syncType == 1
                ? '飞书卡片'
                : record.syncType == 2
                ? '字典'
                : record.syncType == 3
                ? '触发器'
                : record.syncType == 4
                ? '角色'
                : '同步流程配置'
            }}
          </template>
          <template v-if="column.dataIndex === 'syncEnv'">
            {{
              record.syncEnv == 1 ? '生产' : record.syncEnv == 2 ? '预发布' : record.syncEnv == 3 ? '测试' : '中文站'
            }}
          </template>
          <template v-if="column.dataIndex === 'syncTime'">
            {{ dayjs(record.syncTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </template>
      </FTable>
    </div>
  </div>
</template>
<script setup lang="ts">
import dayjs from 'dayjs'
import { ref, watch, reactive, onMounted } from 'vue'
import SearchContent from './SearchContent/index.vue'
import { IProcessClassType } from '@/types/handle'

import { GetTagAndNode, syncProcessLog } from '@/api'
const loading = ref(false)
const paging = reactive<any>({ pageNum: 1, pageSize: 10 })
const queryData = ref<any>({})
const dataList = ref<any[]>([])
const processTypeData = ref<IProcessClassType[]>([])
const columns = ref([
  { title: '配置类型', dataIndex: 'syncType', key: 'syncType' },
  { title: '配置CODE', dataIndex: 'syncCode', key: 'syncCode' },
  { title: '配置名称', dataIndex: 'syncDataName', key: 'syncDataName' },
  { title: 'trace ID', dataIndex: 'traceId', key: 'traceId' },
  { title: '流程类型', dataIndex: 'processConfigName', key: 'processConfigName' },
  { title: '同步环境', dataIndex: 'syncEnv', key: 'syncEnv' },
  { title: '发布人', dataIndex: 'createdUserName', key: 'createdUserName' },
  { title: '发布时间', dataIndex: 'syncTime', key: 'syncTime' },
])
const getProcessTypes = async () => {
  try {
    const res = await GetTagAndNode()
    processTypeData.value = res.data
  } catch (error) {
    throw new Error('流程类型请求失败')
  }
}
// 查询列表
const queryDataList = async () => {
  try {
    loading.value = true
    const data = { ...queryData.value }
    delete data.cacheValue
    const res = await syncProcessLog({
      ...data,
      ...paging,
    })
    const processedFieldList =
      res.data.list.map(item => {
        const obj = processTypeData.value.find(a => a.id === item.processConfigId)
        return {
          ...item,
          processConfigName: obj ? obj.processConfigName : '--',
        }
      }) || []

    dataList.value = processedFieldList || []
    paging.total = res?.data?.totalCount || 0
  } finally {
    loading.value = false
  }
}

const onPaginationChangeFn = (pagination: any) => {
  paging.pageNum = pagination.current
  paging.pageSize = pagination.pageSize
  queryDataList()
}

// 查询列表
const onGetSearchData = async (data: any) => {
  queryData.value = data
  paging.pageNum = data?.cacheValue?.pageNum || 1
  paging.pageSize = data?.cacheValue?.pageSize || 10
  queryDataList()
}
onMounted(() => {
  getProcessTypes()
})
watch(
  () => queryData.value,
  val => {
    onGetSearchData(val)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.modify-board-container {
  .none-container-padding {
    margin-top: -24px;
    margin-left: -4px;
    width: calc(100% + 8px);
  }
  .card-content-shadow {
    background: #ffffff;
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .code-link {
    cursor: pointer;
    color: #378eef;
  }
  .mr6 {
    margin-right: 6px;
  }
  .mr4 {
    margin-right: 4px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .error-color {
    color: #f04141;
  }
  .sucess-color {
    color: #2fcc83;
  }
  .empty-content {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .count-info-content {
    line-height: 18px;
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
  :deep(.fs-table-tbody > tr > td) {
    &.fs-table-cell-row-hover {
      background-color: #f1f4f8;
    }
  }
  :deep(.fs-table-column-sorters) {
    justify-content: flex-start;
    .fs-table-column-title {
      flex: 0;
      white-space: nowrap;
    }
  }
}
</style>

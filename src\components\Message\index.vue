<template>
  <div class="message-box-wrap">
    <slot name="header"></slot>
    <!-- <div class="split-wrapper"> -->
    <Split v-model="split">
      <template #top>
        <!-- <div class="top-pane"> -->
        <div class="message-content wrapper">
          <FSpin class="spin" v-if="loading" />
          <Scroll ref="scrollRef" v-else :on-reach-top="handleRefresh" :distance-to-edge="15">
            <div class="message-item" v-for="(item, index) in messageList" :key="index">
              <MessageItem
                :message-item="item"
                @on-select-role-fn="onSelectRoleFn"
                @on-set-content-fn="onSetContentFn"
                @on-init="handleRefresh"
              ></MessageItem>
            </div>
            <div class="no-data" v-if="messageList.length <= 0">
              <img src="@/assets/images/no-data.png" />
              <span>{{ i18n.t('暂无数据') }}</span>
            </div>
          </Scroll>
        </div>
        <!-- </div> -->
      </template>
      <template #bottom>
        <!-- <div class="bottom-pane"> -->
        <div class="talk-box">
          <TalkPanel @send="handleSend" ref="refTalkPanel"></TalkPanel>
        </div>
        <!-- </div> -->
      </template>
    </Split>
    <!-- </div> -->
  </div>
</template>
<script lang="ts" setup>
import { ref, nextTick, watch, provide, onMounted } from 'vue'
import { getProcessMessage, sendMessage, ISendMessageParams, getProcessRoleAndUser } from '@/api'

import MessageItem from './MessageItem.vue'
import TalkPanel from './TalkPanel.vue'
import Scroll from '@/components/Scroll/index.vue'
import Split from './Split/index.vue'
import { IProcessRoleAndUser } from '@/types/request'
import { useI18n } from '@/utils'

interface IProps {
  processName: string
  processNo: string
  instanceId: number
  paramsWrapper: <T>(data: T) => T
}

const i18n = useI18n()
const props = defineProps<IProps>()

const split = ref<number>(75)
const loading = ref(true)
const messageList = ref<any[]>([])
const processRoleInfo = ref<IProcessRoleAndUser[]>([])
const scrollRef = ref()
const refTalkPanel = ref()

watch(
  () => props.processNo,
  () => {
    onSetContentFn()
    getMessageList()
  }
)

watch(
  () => props.instanceId,
  () => getProcessRoleInfo()
)

onMounted(() => {
  init()
})

const init = async () => {
  loading.value = true
  await getMessageList()
  loading.value = false
  nextTick(resetScrollTop)
  nextTick(getProcessRoleInfo)
}

const resetScrollTop = () => {
  const $scroll = scrollRef.value?.getScrollContainer?.().parentNode
  if ($scroll) {
    $scroll.scrollTop = $scroll.scrollHeight
  }
}

const handleSend = async (params: ISendMessageParams & { callback: () => void }) => {
  const { callback, ...data } = params
  await sendMessage(props.paramsWrapper({ ...data, projectNum: props.processNo, instanceId: props.instanceId }))
  callback()
  await getMessageList()
  nextTick(resetScrollTop)
}

// 处理下拉刷新
const handleRefresh = () => {
  getMessageList()
}

// 获取流程角色信息
const getProcessRoleInfo = async () => {
  const { data = [] } = await getProcessRoleAndUser(props.instanceId)
  processRoleInfo.value = data
}

// 获取消息列表
const getMessageList = async () => {
  if (!props?.processNo) return
  const { data = [] } = await getProcessMessage(props.instanceId)
  messageList.value = data.map(item => {
    item.processName = props.processName || ''
    return item
  })
}
const onSelectRoleFn = (data: any) => {
  refTalkPanel.value.onSelectRoleFn(data)
}

const onSetContentFn = (data: any = {}) => {
  refTalkPanel.value.onSetContentFn(data)
}

provide('processRoleInfo', processRoleInfo)
</script>
<style lang="scss" scoped>
.spin {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
:deep(.send) {
  .fs-btn {
    border: none !important;
    color: #378eef !important;
    font-size: 14px !important;
    cursor: pointer;
    height: 100%;
    line-height: 100%;
    padding: 0 !important;
  }
  .fs-btn:focus {
    box-shadow: none !important;
  }
}
.message-box-wrap {
  position: relative;
  min-width: 330px;
  min-height: 500px;
  max-width: 380px;
  max-height: 840px;
  background-color: #ffffff;
  height: calc(100% - 20px);
  .message-content {
    padding: 6px 24px;
    height: 100%;
    overflow: auto;
    .message-item {
      position: relative;
      &::after {
        content: '';
        width: 1px;
        height: calc(100% - 43px);
        display: inline-block;
        position: absolute;
        left: 16px;
        top: 38px;
        background-color: #eeeeee;
      }
    }
    .message-item:last-child {
      &::after {
        width: 0 !important;
      }
    }
    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 100px;
      & > img {
        width: 153px;
      }
      span {
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #b2b7bf;
        font-size: 12px;
        transform: translateY(-20px);
      }
    }
  }
  .talk-box {
    height: 100%;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    // border-top: 1px solid#EEEEEE;
    background-color: #ffffff;
  }
}
.split-wrapper {
  position: relative;
  width: 100%;
  height: calc(100% - 20px);
  .top-pane {
    position: absolute;
    width: 100%;
    bottom: 27.7425%;
    top: 0;
  }
  .bottom-pane {
    position: absolute;
    width: 100%;
    top: 72.2575%;
    bottom: 0;
  }
}
</style>

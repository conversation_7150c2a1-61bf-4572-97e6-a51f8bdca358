<template>
  <FTooltip
    open
    overlay-class-name="cust-condition-expression-tip-box"
    :overlay-style="{ padding: '0', maxWidth: 'none' }"
    trigger="click"
    placement="bottom"
    color="#fff"
    :get-popup-container="target"
  >
    <template #title>
      <div class="condition-expression-container color333">
        <div class="header">
          <div class="fontSize14 lh22">条件表达式</div>
          <div class="yellow">字段名称必须使用英文ID(非显示名称);</div>
          <div class="yellow">选择型字段的值需填写选项的实际存储值(非显示文本);</div>
          <div class="yellow">集合类运算符的值列表用英文逗号分隔，不要加空格;</div>
        </div>
        <div class="content">
          <ExpandableTree
            class="condition-expression-tree"
            :items="conditionExpression"
            :expandedIds="[conditionExpression[0].id]"
            iconClassOpen="iconjiantouxia1"
            iconClassClosed="iconjiantouyou"
            :indent="26"
          >
            <template #default="{ item }">
              <TipBtn tip-placement="right">
                <template #title>
                  <div class="lh22">[格式]: {{ item.format }}</div>
                  <div class="lh22">[说明]: {{ item.tip }}</div>
                  <div class="lh22">[示例]: {{ item.examples }}</div>
                </template>
                <div class="color333" @click="addCondition(item)">{{ item?.label }}</div>
              </TipBtn>
            </template>
          </ExpandableTree>
        </div>
      </div>
    </template>
    <i class="hover-btn">fx</i>
  </FTooltip>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import ExpandableTree from '@/views/process-ipd-list/components/ExpandableTree/index.vue'

const emits = defineEmits(['change'])
const visibleStasus = ref<boolean>(false)
const target = ref(() => document.querySelector('#container'))
const conditionExpression = ref<any>([
  {
    id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    label: '字符串专用运算符',
    children: [
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '字符串等于 (eq)',
        value: '[default] eq ()',
        format: '[字段ID] eq (字符串)',
        tip: '严格区分大小写的字符串匹配，需完全相同时符合条件',
        examples: '[department] eq (Finance) 表示部门为Finance时分配对应人员',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '字符串不等于 (neq)',
        value: '[default] neq ()',
        format: '[字段ID] neq (字符串)',
        tip: '严格区分大小写的字符串匹配，不完全相同时符合条件',
        examples: '[priority] neq (紧急) 表示优先级非"紧急"时分配对应人员',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '模糊匹配 (like)',
        value: '[default] like ()',
        format: '[字段ID] like (模式)',
        tip: '当字段中存在输入值，则为符合条件',
        examples: '[title] like (重要) 表示标题包含"重要"时分配对应人员',
      },
    ],
  },
  {
    id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    label: '集合操作运算符',
    children: [
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '包含于 (in)',
        value: '[default] in ()',
        format: '[字段ID] in (值1,值2,...)',
        tip: '字段值等于任意一个即为符合条件',
        examples: '[region] in (华东,华北) 表示区域为华东或华北时分配',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '不包含于 (notin)',
        value: '[default] notin ()',
        format: '[字段ID] notin (值1,值2,...)',
        tip: '字段值不等于任意一个即为符合条件',
        examples: '[category] notin (A类,B类) 表示分类既非A类也非B类时分配',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '完全包含 (contains)',
        value: '[default] contains ()',
        format: '[字段ID] contains (值1,值2,...)',
        tip: '字段值（数组）必须包含所有指定值',
        examples: '[skills] contains (Java,Python) 表示同时具备Java和Python技能时分配',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '不完全包含 (notcontains)',
        value: '[default] notcontains ()',
        format: '[字段ID] notcontains (值1,值2,...)',
        tip: '字段值（数组）缺少任意指定值即为符合条件',
        examples: '[tags] notcontains (高优先级) 表示标签中不包含"高优先级"时分配',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '存在交集 (match)',
        value: '[default] match ()',
        format: '[字段ID] match (值1,值2,...)',
        tip: '字段值与指定值有至少一个相同即为真',
        examples: '[projects] match (项目A,项目B)` 表示参与过项目A或B',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '无交集 (nmatch)',
        value: '[default] nmatch ()',
        format: '[字段ID] nmatch (值1,值2,...)',
        tip: '字段值不等于任意一个指定值即位符合条件',
        examples: '[products] nmatch (产品X)` 表示未接触过产品X',
      },
    ],
  },
  {
    id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    label: '空值判断运算符',
    children: [
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '为空 (isnull)',
        value: '[default] isnull',
        format: '[字段ID] isnull',
        tip: '字段值中不存在值则为符合条件',
        examples: '[approver] isnull 表示审批人字段为空时分配',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '不为空 (isnotnull)',
        value: '[default] isnotnull',
        format: '[字段ID] isnotnull',
        tip: '字段值中存在值则为符合条件',
        examples: '[comment] isnotnull 表示备注字段有内容时分配',
      },
    ],
  },
  {
    id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    label: '数值比较运算符',
    children: [
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '大于 (>)',
        value: '[default] > 0',
        format: '[字段ID] > 数值',
        tip: '当字段值大于指定数值时生效',
        examples: '[amount] > 10000 表示金额超过1万时分配对应人员',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '大于等于 (>=)',
        value: '[default] >= 0',
        format: '[字段ID] >= 数值',
        tip: '当字段值大于等于指定数值时生效',
        examples: '[score] >= 60 表示评分60分及以上时分配对应人员',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '小于 (<)',
        value: '[default] < 0',
        format: '[字段ID] < 数值',
        tip: '当字段值小于等于指定数值时生效',
        examples: '[age] < 18 表示年龄小于18岁时分配对应人员',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '小于等于 (<=)',
        value: '[default] <= 0',
        format: '[字段ID] <= 数值',
        tip: '当字段值小于等于指定数值时生效',
        examples: '[level] <= 3 表示等级3级及以下时分配对应人员',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '等于 (==)',
        value: '[default] == 0',
        format: '[字段ID] == 数值',
        tip: '数值和字符串精确匹配，当完全匹配时则是符合条件',
        examples: '[status] == 3 表示状态为"已批准"时分配对应人员',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '不等于 (!=)',
        value: '[default] != 0',
        format: '[字段ID] != 数值',
        tip: '数值和字符串精确匹配，当不完全匹配时则是符合条件',
        examples: '[type] != 3 表示客户类型非VIP时分配对应人员',
      },
    ],
  },
  {
    id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    label: '逻辑判断',
    children: [
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '或 (||)',
        value: '||',
        format: '条件1 || 条件2',
        tip: '多个条件之间满足任意一个即可',
        examples:
          '[level] > 3 || [projects] eq (重点项目) 表示“level”大于3与“projects”等于“重点项目”满足任意一个就会分配至对应人员',
      },
      {
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: '与 (&&)',
        value: '&&',
        format: '条件1 && 条件2',
        tip: '多个条件之间必须全部满足才会生效',
        examples:
          '[level] > 3 && [projects] eq (重点项目) 表示“level”大于3与“projects”等于“重点项目”同时满足时才会分配至对应人员',
      },
    ],
  },
])

const addCondition = item => {
  emits('change', item)
}
</script>

<style lang="scss">
.cust-condition-expression-tip-box {
  .fs-tooltip-inner {
    padding: 0;
  }
}
</style>
<style lang="scss" scoped>
.hover-btn {
  color: #378eef;
  cursor: pointer;
  padding: 2px 4px 2px 4px;
  border-radius: 2px;
  &:hover {
    background-color: #d8d8d8;
  }
}
:deep(.condition-expression-tree) {
  .children {
    .expandable-item {
      margin: 0;
      padding: 8px;
      cursor: pointer;
      &:hover {
        background: #f1f4f8;
      }
    }
  }
  > .expandable-item {
    margin: 0;
    > .info-box {
      padding: 8px;
      &:hover {
        background: #f1f4f8;
      }
    }
  }
}
.condition-expression-container {
  max-width: 325px;
  // max-height: 300px;
  // overflow-y: scroll;

  .header {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
  }
  .content {
    padding: 4px;
    max-height: 260px;
    overflow-y: scroll;
    &::-webkit-scrollbar-track {
      background-image: linear-gradient(180deg, transparent 0%, transparent 100%);
    }
  }
}
</style>

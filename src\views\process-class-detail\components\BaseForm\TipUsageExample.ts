import { createTipConfig, tipPresets, type TipText } from './tipTypes'
import ProcessTip from '../CustomTipComponents/ProcessTip.vue'
import DynamicTip from '../CustomTipComponents/DynamicTip.vue'

// 优雅的使用示例
export const elegantFormConfig = {
  // 1. 简单字符串（最简洁）
  processName: {
    component: 'CustomInput',
    fieldKey: 'processName',
    label: '流程名称',
    required: true,
    tipText: '请输入流程的完整名称，建议使用中文描述',
    componentAttrs: {
      placeholder: '请输入流程名称',
    },
  },

  // 2. 使用预设配置（标准化）
  processDescription: {
    component: 'CustomTextArea',
    fieldKey: 'processDescription',
    label: '流程描述',
    required: false,
    tipText: tipPresets.length(10, 500),
    componentAttrs: {
      placeholder: '请输入流程描述',
      maxlength: 500,
    },
  },

  // 3. 动态文本（智能）
  processDefineKey: {
    component: 'CustomSelect',
    fieldKey: 'processDefineKey',
    label: '关联流程图',
    required: true,
    tipText: tipPresets.withValue(
      '关联流程图将决定流程的执行路径',
      '已选择：',
      ''
    ),
    componentAttrs: {
      placeholder: '请选择关联流程图',
      options: [], // 实际选项
    },
  },

  // 4. 条件提示（智能判断）
  processLevel: {
    component: 'CustomSelect',
    fieldKey: 'processLevel',
    label: '流程级别',
    required: true,
    tipText: tipPresets.conditional(
      (fieldValue) => !!fieldValue,
      tipPresets.dynamic((fieldValue) => {
        const levelMap: Record<number, string> = {
          1: '一级流程：需要最高级别审批',
          2: '二级流程：需要部门级别审批',
          3: '三级流程：普通审批流程'
        }
        return levelMap[fieldValue] || '未知级别'
      }),
      '请选择流程级别，不同级别有不同的审批权限要求'
    ),
    componentAttrs: {
      placeholder: '请选择流程级别',
      options: [
        { label: '一级', value: 1 },
        { label: '二级', value: 2 },
        { label: '三级', value: 3 }
      ],
    },
  },

  // 5. 自定义组件（最灵活）
  processType: {
    component: 'CustomSelect',
    fieldKey: 'processType',
    label: '流程类型',
    required: true,
    tipText: createTipConfig.component(DynamicTip, {
      title: '流程类型说明',
      text: '类型详情',
      type: 'info',
      showValidation: true,
      customSuggestions: [
        '不同类型的流程有不同的审批规则',
        '选择后将自动配置相关参数'
      ]
    }),
    componentAttrs: {
      placeholder: '请选择流程类型',
      options: [
        { label: '审批流程', value: 'approval' },
        { label: '业务流程', value: 'business' },
        { label: '系统流程', value: 'system' }
      ],
    },
  },

  // 6. 复杂动态逻辑（高级用法）
  processConfig: {
    component: 'CustomInput',
    fieldKey: 'processConfig',
    label: '流程配置',
    required: false,
    tipText: createTipConfig.dynamic((fieldValue, formData, fieldConfig) => {
      // 根据流程类型显示不同提示
      const processType = formData.processType
      
      if (!processType) {
        return '请先选择流程类型'
      }
      
      const configTips: Record<string, string> = {
        approval: '审批流程配置：设置审批节点和权限',
        business: '业务流程配置：定义业务规则和流转条件',
        system: '系统流程配置：配置系统集成和自动化规则'
      }
      
      const baseTip = configTips[processType] || '流程配置'
      
      if (fieldValue) {
        try {
          JSON.parse(fieldValue)
          return `${baseTip}（配置有效）`
        } catch {
          return `${baseTip}（配置格式错误，请检查JSON格式）`
        }
      }
      
      return `${baseTip}（留空将使用默认配置）`
    }),
    componentAttrs: {
      placeholder: '请输入JSON格式的配置',
    },
  },

  // 7. 组合使用（展示灵活性）
  processOwner: {
    component: 'CustomSelect',
    fieldKey: 'processOwner',
    label: '流程负责人',
    required: true,
    tipText: createTipConfig.component(ProcessTip, {
      baseText: '流程负责人将负责流程的维护和优化',
      showValue: true,
      valuePrefix: '当前负责人：',
      valueSuffix: ''
    }),
    componentAttrs: {
      placeholder: '请选择流程负责人',
      options: [], // 用户列表
    },
  }
}

// 工具函数示例
export const createFormFieldWithTip = (
  baseConfig: any,
  tipConfig: TipText
) => ({
  ...baseConfig,
  tipText: tipConfig
})

// 批量创建带提示的字段
export const createFieldsWithTips = (
  fields: Record<string, { config: any; tip: TipText }>
) => {
  const result: Record<string, any> = {}
  
  Object.entries(fields).forEach(([key, { config, tip }]) => {
    result[key] = createFormFieldWithTip(config, tip)
  })
  
  return result
}

// 使用示例
export const batchCreatedFields = createFieldsWithTips({
  field1: {
    config: {
      component: 'CustomInput',
      fieldKey: 'field1',
      label: '字段1',
      required: true
    },
    tip: tipPresets.required('字段1')
  },
  field2: {
    config: {
      component: 'CustomSelect',
      fieldKey: 'field2',
      label: '字段2',
      required: false
    },
    tip: tipPresets.select('这是一个选择字段')
  }
})

// 动态提示工厂
export const createDynamicTip = (
  baseText: string,
  valueFormatter?: (value: any) => string
) => createTipConfig.dynamic((fieldValue, formData, fieldConfig) => {
  if (!fieldValue) return baseText
  
  const formattedValue = valueFormatter ? valueFormatter(fieldValue) : String(fieldValue)
  return `${baseText}（当前：${formattedValue}）`
})

// 条件提示工厂
export const createConditionalTip = (
  conditions: Array<{
    condition: (fieldValue: any, formData: any) => boolean
    tip: string
  }>,
  defaultTip: string = '请填写相关信息'
) => createTipConfig.dynamic((fieldValue, formData) => {
  for (const { condition, tip } of conditions) {
    if (condition(fieldValue, formData)) {
      return tip
    }
  }
  return defaultTip
})

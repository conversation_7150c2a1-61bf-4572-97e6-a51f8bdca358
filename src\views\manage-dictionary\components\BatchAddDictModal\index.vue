<template>
  <FModal
    width="720px"
    wrapClassName="bom-modal-wrap-container"
    :bodyStyle="{ maxHeight: '80vh', overflow: 'scroll' }"
    v-model:visible="visible"
    :title="handleData?.[handleType]?.title ?? '新增字典'"
    centered
    :confirm-loading="loading"
    @cancel="onCancelFn"
    @ok="onSubmitFn"
  >
    <FSpin :spinning="modalLoading">
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <div class="title">字段信息</div>
        <FRow :gutter="[24, 0]">
          <FCol :span="12">
            <FFormItem label="标题" name="title">
              <FInput v-model:value="formState.title" placeholder="请输入" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem label="字段名称" name="field">
              <FInput v-model:value="formState.field" placeholder="请输入" />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem label="字典说明" name="describe">
              <FTextarea v-model:value="formState.describe" style="min-height: 88px" placeholder="请输入" />
            </FFormItem>
          </FCol>
        </FRow>
        <div class="title">字典管理</div>
        <DictTreeList v-model:data="treeData">
          <template #actions="{ node, level, index, parentNodeChildren }">
            <FSpace :size="[12]">
              <TipBtn tip-title="新建字典">
                <i
                  class="iconfont icontianjia-01 hover-btn color999"
                  @click="onAddIconClickFn(node, level, index, parentNodeChildren)"
                ></i>
              </TipBtn>
              <TipBtn
                has-pop
                tip-title="删除"
                pop-title="确定删除当前字典吗？"
                @onConfirmFn="onDeleteIconConfirmFn(node, level, index, parentNodeChildren)"
              >
                <i class="iconfont icontubiao_xietongshanchu hover-btn color999"></i>
              </TipBtn>
            </FSpace>
          </template>
        </DictTreeList>
        <span class="cursor color4677C7 in-block marginT8" @click="treeData.push(deepClone(addData))">
          <i class="icon iconfont iconxinzeng fontSize14" />
          新增字典
        </span>
      </FForm>
    </FSpin>
    <div></div>
  </FModal>
</template>

<script setup lang="ts">
import { FSpin, message } from '@fs/smart-design'
import { ref, reactive, onMounted, computed } from 'vue'
import DictTreeList from '../DictTreeList/index.vue'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import { selectFieldDictionaryItem, addBatchFieldDictionary, updateBatchFieldDictionary } from '@/api'
import { deepClone } from '@/utils'
import { processTreeData, isTreeValid } from '../DictTree/utils'

const emits = defineEmits(['updateChange', 'init'])
const visible = ref<boolean>(false)
const loading = ref<boolean>(false)
const modalLoading = ref<boolean>(false)
const formRef = ref()
const formState = reactive<any>({
  title: undefined,
  field: undefined,
  describe: undefined,
})
const deleteIds = ref([])
const treeData = ref<any[]>([])
const addData = {
  originalName: undefined,
  value: undefined,
  id: undefined,
  dict_tree_id: `dict_tree_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
  dict_tree_children: [],
}
const currentRowRecord = ref<any>()
const handleType = ref('')
const handleData = computed(() => ({
  addBatchFieldDictionary: {
    title: '新增字典字段',
    msg: '新增字典字段成功！',
    defaultForm: {},
    baseParams: {},
    apiUrl: addBatchFieldDictionary,
    initFn: async () => {
      treeData.value = [deepClone(addData)]
    },
  },
  updateBatchFieldDictionary: {
    title: '编辑字典字段',
    msg: '编辑字典字段成功！',
    defaultForm: {},
    baseParams: {},
    apiUrl: updateBatchFieldDictionary,
    initFn: async () => {
      await onGetTreeListFn(currentRowRecord?.value)
    },
  },
}))

const rules: Record<string, any[]> = {
  title: [{ required: true, message: '请输入' }],
  field: [{ required: true, message: '请输入' }],
}

const onAddIconClickFn = (node: any, level: number, index: number, parentNodeChildren: any[]) => {
  node.dict_tree_children.push({
    originalName: '',
    value: '',
    id: undefined,
    dict_tree_id: `dict_tree_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    dict_tree_children: [],
  })
}

const transformTreeIds = (tree, ids = []) => {
  tree.forEach(node => {
    node?.id && ids.push(node.id)
    if (node.dict_tree_children && node.dict_tree_children.length) {
      transformTreeIds(node.dict_tree_children, ids)
    }
  })
  return ids
}

const onDeleteIconConfirmFn = async (node: any, level: number, index: number, parentNodeChildren: any[]) => {
  if (treeData?.value.length === 1 && node?.dict_tree_id === treeData?.value?.[0]?.dict_tree_id) {
    message.warning('至少保留一个字典项')
    return
  }
  if (node.id) {
    // const ids = transformTreeIds([node])
    // console.log('ids :>> ', ids)
    deleteIds.value.push(node.id)
  }
  parentNodeChildren.splice(index, 1)
}

const onCancelFn = () => {
  visible.value = false
}

const transformTree = tree => {
  return tree.map((node, index) => {
    const newNode = deepClone({
      id: node?.id,
      name: node?.originalName?.trim?.(),
      value: node?.value?.trim?.(),
      parentId: node?.parentId ?? 0,
      title: formState?.title?.trim?.(),
      field: formState?.field?.trim?.(),
      describe: formState?.describe?.trim?.(),
      status: formState?.status ?? 1,
      sort: index + 1,
    })
    if (node.dict_tree_children && node.dict_tree_children.length) {
      newNode['children'] = transformTree(node.dict_tree_children)
    }
    return newNode
  })
}

const onSubmitFn = async () => {
  try {
    loading.value = true
    if (!formRef.value) return
    await formRef.value.validate()
    const flag = isTreeValid(treeData.value)
    if (!treeData.value.length) {
      message.warning('最少需要添加一项字典项')
      return
    }
    if (!flag) {
      message.warning('请填写完整字典项')
      return
    }
    const list = transformTree(treeData.value)
    const params = {}
    if (handleType.value === 'addBatchFieldDictionary') {
      params['dictionaryList'] = list
    } else {
      params['items'] = list
      params['deleteIds'] = deleteIds.value
    }
    await handleData?.value?.[handleType?.value]?.apiUrl(params)
    message.success(handleData?.value?.[handleType?.value]?.msg ?? '操作成功')
    onCancelFn()
    if (handleType.value === 'addBatchFieldDictionary') {
      emits('init')
    } else {
      emits('updateChange', currentRowRecord.value)
    }
  } finally {
    loading.value = false
  }
}

const onGetTreeListFn = async node => {
  try {
    modalLoading.value = true
    if (!node) return
    if (node?.dict_tree_children?.length) {
      treeData.value = node?.dict_tree_children
      onInitData(treeData.value?.[0] ?? {})
      return
    }
    const res = await selectFieldDictionaryItem(node.field)
    const rawData = res?.data || []
    treeData.value = rawData.length > 0 ? processTreeData(rawData, 'dict_tree_field') : []
    onInitData(treeData.value?.[0] ?? {})
  } finally {
    modalLoading.value = false
  }
}

const onInitData = async data => {
  Object.entries(formState).forEach(([key, value]) => {
    formState[key] = handleData?.value?.[handleType?.value]?.defaultForm?.[key] || (data || {})[key] || undefined
  })
}

const onOpenFn = async (typeValue: string, data: any = {}) => {
  visible.value = true
  currentRowRecord.value = data
  handleType.value = typeValue
  treeData.value = []
  deleteIds.value = []
  await onInitData(currentRowRecord.value)
  handleData?.value?.[handleType?.value]?.initFn?.()
}

defineExpose({ onOpenFn })
</script>

<style scoped lang="scss">
.title {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 16px;
  padding-left: 8px;
  font-size: 14px;
  color: #333333;
  &::before {
    content: '';
    position: absolute;
    width: 2px;
    height: 16px;
    top: 3px;
    left: 0;
    background: #378eef;
  }
}
.hover-btn {
  cursor: pointer;
  &:hover {
    color: #378eef;
  }
}
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
</style>

<template>
  <div class="task-table-list-container">
    <Collapse :header="i18n.t('子节点列表')">
      <template #extra>
        <div class="milepost-task-extra" @click.stop>
          <FCheckbox class="f12" v-model:checked="incompleteChecked" @change="checkedBoxChenge">{{
            i18n.t('我负责的')
          }}</FCheckbox>
          <FCheckbox class="f12" v-model:checked="responsibleChecked" @change="checkedBoxChenge">{{
            i18n.t('未完成的')
          }}</FCheckbox>
          <div v-if="currtMilepost?.isItConfigured">
            <FDropdown
              :trigger="['click']"
              :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
            >
              <FButton @click.prevent.stop>
                <Icon class="mR4" icon="icontubiao_tianjia1" :size="12" />
                <span>{{ i18n.t('添加子节点') }}</span>
              </FButton>
              <template #overlay>
                <FMenu>
                  <FMenuItem key="0">
                    <span @click.prevent="ltcTask(currtMilepost as IProcess)">{{ i18n.t('任务池任务') }}</span>
                  </FMenuItem>
                  <FMenuItem key="1" v-if="currtMilepost && currtMilepost.status !== 4">
                    <span @click.prevent.stop="emitOperate(EmitType.createTask, currtMilepost)">{{
                      i18n.t('自定义任务')
                    }}</span>
                  </FMenuItem>
                </FMenu>
              </template>
            </FDropdown>
          </div>
          <FButton
            v-else-if="currtMilepost && currtMilepost.status !== 4"
            @click="emitOperate(EmitType.createTask, currtMilepost)"
          >
            <Icon class="mR4" icon="icontubiao_tianjia1" :size="12" />
            <span>{{ i18n.t('添加子节点') }}</span>
          </FButton>
          <FButton
            v-if="currtMilepost && currtMilepost.status !== 4"
            :disabled="!selected.length"
            @click="selected.length && emitOperate(EmitType.batchDelTask, { milepost: currtMilepost, tasks: selected })"
          >
            <Icon class="mR4" icon="iconshanchu2" :size="12" />
            <span>{{ i18n.t('批量删除') }}</span>
          </FButton>
          <FButton
            v-if="currtMilepost && currtMilepost.status !== 4"
            :disabled="!selected.length"
            @click="selected.length && emitOperate(EmitType.batchEditTask, { tasks: selected })"
          >
            <Icon class="mR4" icon="iconicon_piliangbianji" :size="12" />
            <span>{{ i18n.t('批量编辑') }}</span>
          </FButton>
          <div class="change-list-view marginL16">
            <span class="sp" @click="changeView">
              <span class="icon iconfont icontubiao_liebiao color333 fontSize14 marginR4"></span>
              <span class="cursor color333 fontSize12">{{ i18n.t('平铺') }}</span>
            </span>
          </div>
        </div>
      </template>
      <div class="milepost-task">
        <FTable
          ref="tableRef"
          v-model:expandedRowKeys="tableExpanded"
          :loading="loading"
          :row-selection="taskRowSelection"
          :columns="taskTableColumn"
          :data-source="taskList"
          :pagination="false"
          :scroll="{ x: 1500 }"
        >
          <template #headerCell="{ column }">
            <template v-if="column.key === 'taskName'">
              <span class="task-table-expanded">
                <PlusSquareOutlined v-show="!tableExpanded.length" @click="handleTaskTableAllExpanded" />
                <MinusSquareOutlined v-show="tableExpanded.length" @click="tableExpanded = []" />
              </span>
              <span>{{ i18n.t('任务名称') }}</span>
            </template>
          </template>
          <template #bodyCell="{ column, record: task }">
            <LinkButton
              v-if="column.key === 'taskName'"
              class="ellipsis"
              :text="task.taskName"
              @click="emitOperate(EmitType.viewTask, task)"
            />
            <template v-if="column.key === 'status'">
              <Icon :icon="task.statusIcon" :color="TFTaskStatusColor(task.status)" />
              {{ task.statusName }}
            </template>
            <template v-if="column.key === 'superviser'">
              {{ task.superviser }}
              <NoticeInfo
                v-if="[0, 1, 4].includes(task.status)"
                class="cust-notice-info"
                style="margin-left: 8px"
                key-id="taskId"
                :value-id="task.id"
              />
            </template>
            <template v-if="column.key === 'approver'">
              {{ task.approver }}
              <NoticeInfo
                v-if="[6].includes(task.status)"
                class="cust-notice-info"
                style="margin-left: 8px"
                key-id="taskId"
                :value-id="task.id"
              />
            </template>
            <template v-if="column.key == 'attachmentData'">
              <div v-if="!task.attachmentData || !JSON.parse(task.attachmentData)?.length">--</div>
              <DownloadFiles
                v-else
                class="color1890ff cursor"
                :data="JSON.parse(task.attachmentData)"
                @download="download"
                @batch-download="batchDownload"
              />
            </template>
            <template v-if="column.key == 'contentData'">
              <div v-if="!task.contentData || !JSON.parse(task.contentData)?.handleTaskDesc">--</div>
              <FPopover
                v-else
                trigger="hover"
                :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
                overlay-class-name="contentDataClass"
              >
                <template #content>
                  <div v-html="JSON.parse(task.contentData)?.handleTaskDesc" @click="handleElementClick"></div>
                </template>
                <a @click="emitOperate(EmitType.viewTask, task)">{{ i18n.t('查看提交内容') }}</a>
              </FPopover>
            </template>
            <template v-if="column.key === 'action'">
              <Icon
                class="marginR12"
                v-bind="iconAttrs"
                :title="i18n.t('处理')"
                icon="icondaiban"
                v-if="task.superviserRole == 1 && [0, 1, 4, 6].includes(task.status)"
                @click="emitOperate(EmitType.handleTask, task)"
              />
              <Icon
                class="marginR12"
                v-bind="iconAttrs"
                :title="i18n.t('审核')"
                icon="iconshenhe1"
                v-if="task.approverRole == 1 && task.status == 6"
                @click="emitOperate(EmitType.judgeTask, task)"
              />
              <!-- <Icon
                class="marginR12"
                v-bind="iconAttrs"
                :title="i18n.t('添加子任务')"
                icon="icontianjiazirenwu"
                v-if="(task.creatorRole == 1 || task.superviserRole == 1) && [0, 1, 4, 6].includes(task.status)"
                @click="emitOperate(EmitType.createChildTask, task)"
              /> -->
              <Icon
                class="marginR12"
                v-bind="iconAttrs"
                :title="i18n.t('编辑')"
                icon="iconicon_piliangbianji"
                v-if="(task.creatorRole == 1 || task.taskEditConfig == 1) && [0, 1, 4, 6].includes(task.status)"
                @click="emitOperate(EmitType.editTask, task)"
              />
              <Icon
                class="marginR12"
                v-bind="iconAttrs"
                :title="i18n.t('回滚')"
                icon="icontubiao_chehui"
                v-if="[2, 3, 5].includes(task.status)"
                @click="emitOperate(EmitType.revokeTask, task)"
              />
              <FPopconfirm
                class="pointer"
                v-if="(task.creatorRole == 1 || task.taskEditConfig == 1) && [0, 1, 4].includes(task.status)"
                overlay-class-name="process-operate-task-delete-popover"
                @confirm="emitOperate(EmitType.delTask, task)"
              >
                <template #title>
                  <p style="color: #333; font-size: 16px; margin-bottom: 8px; font-weight: 500; line-height: 24px">
                    {{ i18n.t('确定删除当前任务吗？') }}
                  </p>
                  <div style="font-size: 14px; font-weight: 400; line-height: 22px; color: #666; margin-bottom: 32px">
                    {{ i18n.t('删除后不可恢复,请谨慎操作') }}
                  </div>
                </template>
                <Icon :title="i18n.t('移除')" size="12px" color="#378eef" icon="iconshanchu2" />
              </FPopconfirm>
            </template>
          </template>
        </FTable>
      </div>
      <SeeImage v-model="seeImageObj.flag" :src="seeImageObj.src" />
    </Collapse>
    <LtcModal
      :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
      v-model="ltcData.flag"
      :form-key="ltcData.formKey"
      :title="i18n.t('添加任务池任务')"
      @submit="handleTaskSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { Ref, computed, inject, reactive, ref, watch } from 'vue'
import { IProcess, ITask } from '@/types/handle'
import { EmitType, TASK_STATUS_COLOR } from '@/views/process-detail/config'
import { deepClone, getUserInfo, useI18n, download, batchDownload } from '@/utils'

import Icon from '@/components/Icon/index.vue'
import LinkButton from '@/views/process-detail/components/HandleInfo/components/LinkButton/index.vue'
import DownloadFiles from '@/views/process-detail/components/DownloadFiles/index.vue'
import Collapse from '../Collapse/index.vue'
import NoticeInfo from '@/components/NoticeInfo/index.vue'
import SeeImage from '@/components/SeeImage/index.vue'
import LtcModal from '@/views/process-operate/components/TaskModal/LtcModal.vue'
import { getLtcForm } from '@/api/handle'
import { messageInstance } from '@fs/smart-design'

const iconAttrs = { class: 'mr10 pointer', size: '12px', color: '#378eef' }
const i18n = useI18n()
const emits = defineEmits(['changeView'])

const instanceId = inject<Ref<number>>('processId') // 流程实例 id
const currtMilepost = inject<Ref<IProcess>>('currtMilepost') // 当前里程碑信息
const operate = inject('operate') as (key: EmitType, data: IProcess) => void

const loading = ref(false)
const userInfo = getUserInfo()
const seeImageObj = reactive({ src: '', flag: false })

const incompleteChecked = ref<boolean>(false)
const responsibleChecked = ref<boolean>(false)
const taskMap = new Map()
const taskOneArr = computed<ITask[]>(() => {
  const data: ITask[] = []
  const df = (arr: ITask[], parentId?: number) => {
    arr.forEach(item => {
      parentId && (item.parentId = parentId)
      if (Array.isArray(item.children) && item.children.length) item.children = df(item.children, item.id) as undefined
      data.push(item)
      taskMap.set(item.id, item)
    })
  }
  df(JSON.parse(JSON.stringify(currtMilepost?.value.children ?? [])))
  return data
})

const tableExpanded = ref<number[]>([])
const tableRef = ref()
const selected = ref<ITask[]>([])
const selectedKeys = ref<string[]>([])
const taskList = ref<ITask[]>(JSON.parse(JSON.stringify(currtMilepost?.value?.children ?? [])))

// 任务表格多选配置
const taskRowSelection = computed(() => ({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[], selectedRows: ITask[]) => {
    selected.value = selectedRows
    selectedKeys.value = selectedRowKeys
  },
  getCheckboxProps: (task: ITask) => ({
    disabled: task.creatorRole !== 1 || ![0, 1, 4, 6].includes(task.status),
    name: task.taskName,
  }),
}))

const taskTableColumn = computed(() => [
  { title: i18n.t('任务名称'), dataIndex: 'taskName', key: 'taskName' },
  { title: i18n.t('状态'), dataIndex: 'status', key: 'status', width: '110px' },
  { title: i18n.t('前置任务'), dataIndex: 'prefixTaskName', key: 'prefixTaskName', width: '80px' },
  { title: i18n.t('负责人'), dataIndex: 'superviser', key: 'superviser', width: '180px' },
  { title: i18n.t('审核人'), dataIndex: 'approver', key: 'approver', width: '180px' },
  { title: i18n.t('当前节点完成'), dataIndex: 'isSysName', key: 'isSysName', width: '60px' },
  { title: i18n.t('预计完成日期'), dataIndex: 'forcastTimeStr', key: 'forcastTimeStr', width: '115px' },
  { title: i18n.t('任务提交内容'), dataIndex: 'contentData', key: 'contentData', width: '115px' },
  { title: i18n.t('实际完成日期'), dataIndex: 'taskCompletedTimeStr', key: 'taskCompletedTimeStr', width: '115px' },
  { title: i18n.t('附件'), dataIndex: 'attachmentData', key: 'attachmentData', width: '150px' },
  { title: i18n.t('操作'), dataIndex: 'action', key: 'action', width: '120px', fixed: 'right' },
])

const milestoneOperate = inject('milestoneOperate') as (key: keyof typeof EmitType, data: unknown) => void
const ltcData = reactive<any>({ flag: false, formKey: 0 })

const ltcTask = (currtMilepost: IProcess) => {
  // 强制触发表单更新
  ltcData.formKey = 0
  ltcData.flag = false
  getLtcForm(currtMilepost.id)
    .then(res => {
      ltcData.formKey = res.data.id
      ltcData.flag = true
    })
    .catch(err => {
      console.log('err:::', err)
    })
}

const handleTaskSubmit = async (data: any) => {
  if (currtMilepost?.value.id) {
    const poolData = {
      formData: { ...data },
      milepostId: currtMilepost!.value.id,
    }
    milestoneOperate(EmitType.createPoolTask, poolData)
  } else {
    messageInstance.error(i18n.t('当前里程碑不存在，无法提交任务池表单'))
  }
}

// 表格全部展开
const handleTaskTableAllExpanded = () => {
  const v: number[] = []
  const s: ITask[] = [...taskList.value]
  while (s.length) {
    const item = s.pop() as ITask
    v.push(item.key as number)
    Array.isArray(item.children) && item.children.length && s.push(...item.children)
  }

  tableExpanded.value = v
}
const changeView = () => {
  emits('changeView')
}
// 图片查看
const handleElementClick = (e: any) => {
  if (e.target.nodeName === 'IMG') {
    seeImageObj.src = e.target.currentSrc
    seeImageObj.flag = true
  }
}

const filterTask = () => {
  let data: ITask[] = taskOneArr.value
  incompleteChecked.value &&
    (data = data.filter((item: ITask) => item.superviserUuid === userInfo.uuid || item.approverUuid === userInfo.uuid))
  responsibleChecked.value && (data = data.filter((item: ITask) => ![2, 3, 4].includes(item.status)))
  return data.map(item => ({ ...item }))
}

const toTaskTree = (data: ITask[]) => {
  const map: Record<string, ITask> = {}
  const res: ITask[] = []
  data.forEach((item: ITask) => (map[item.id] = item))

  for (let i = 0; i < data.length; i++) {
    const item = data[i]
    if (item.parentId) {
      if (!map[item.parentId as string]) handleParentTask(item, map, data)
      const parent = map[item.parentId as string]
      parent.children ? parent.children.push(item) : (parent.children = [item])
    } else {
      res.push(item)
    }
  }
  return res
}

const handleParentTask = (task: ITask, map: Record<string, ITask>, data: ITask[]) => {
  const parentId = task.parentId as number
  const parentTask = deepClone(taskMap.get(parentId))
  if (parentTask) {
    map[parentId] = parentTask
    data.push(parentTask)
  }
  parentTask.parentId && handleParentTask(parentTask, map, data)
}

const checkedBoxChenge = () => {
  loading.value = true
  taskList.value = toTaskTree(filterTask())
  loading.value = false
}
const TFTaskStatusColor = (status: keyof typeof TASK_STATUS_COLOR) => TASK_STATUS_COLOR[status]
const emitOperate = (type: EmitType, data: any) => {
  operate(type, { ...data, instanceId })
}
watch(
  () => currtMilepost,
  () => {
    selected.value = []
    selectedKeys.value = []
    checkedBoxChenge()
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
.task-table-list-container {
  :deep(.fs-collapse-extra) {
    flex: 1;
  }
  .task-delete {
    color: red;
  }
  .milepost-task-extra {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: #333;
    font-size: 12px;
    > * {
      margin-left: 6px;
      margin-right: 6px;
    }
    :deep(.fs-btn) {
      width: 82px;
      height: 24px;
      font-size: 12px !important;
    }
    .mR4 {
      margin-right: 4px;
    }
  }
  .f12 {
    font-size: 12px !important;
  }
}
.change-list-view {
  display: inline-block;
  margin-right: 24px;
  .sp:hover {
    span {
      color: #5fa4f2 !important;
    }
  }
}
.change-list-view {
  display: inline-block;
  margin-right: 24px;
  .sp:hover {
    span {
      color: #5fa4f2 !important;
    }
  }
}
</style>
<style lang="scss">
.process-operate-task-delete-popover {
  min-width: 400px;
  .fs-popover-inner-content {
    padding: 24px;
    .fs-popover-buttons {
      button {
        width: 80px;
        height: 32px;
      }
    }
  }
}
</style>
